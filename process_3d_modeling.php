<?php
header('Content-Type: application/json');

function process3DModeling($imagePath, $hairStyle = null, $faceShape = null) {
    try {
        // Validate input
        if (!file_exists($imagePath)) {
            return ['success' => false, 'error' => 'Image file not found'];
        }
        
        // Prepare Python command
        $pythonScript = __DIR__ . '/face_3d_processor.py';
        $command = "python \"$pythonScript\" \"$imagePath\"";
        
        if ($hairStyle) {
            $command .= " \"$hairStyle\"";
        }
        
        // Execute Python script
        $output = shell_exec($command . ' 2>&1');
        
        if (!$output) {
            return ['success' => false, 'error' => 'Python script execution failed'];
        }
        
        // Parse JSON output
        $result = json_decode($output, true);
        
        if (!$result) {
            return ['success' => false, 'error' => 'Invalid Python script output', 'raw_output' => $output];
        }
        
        return $result;
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function generateHairRecommendations($faceShape) {
    $recommendations = [
        'oval' => ['Textured Crop', 'Pompadour', 'Man Bun', 'Long Layered', 'Classic Undercut'],
        'round' => ['Two Block Hair', 'Taper Fade', 'Fringe Haircut', 'Fluffy with Low Fade', 'Crop Cut'],
        'square' => ['Slick Back', 'Quiff', 'Side Swept', 'Crew Cut', 'Faux Hawk'],
        'heart' => ['Classic Quiff', 'Side Part Fringe', 'Long Fringe', 'Taper Fade', 'Short Faux Hawk'],
        'rectangular' => ['Textured Crop', 'Side Part', 'Crew Cut', 'Short Spiky', 'Messy Fringe']
    ];
    
    return $recommendations[strtolower($faceShape)] ?? $recommendations['oval'];
}

// Handle AJAX request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $imagePath = $input['image_path'] ?? '';
    $hairStyle = $input['hair_style'] ?? null;
    $faceShape = $input['face_shape'] ?? 'oval';
    
    if (empty($imagePath)) {
        echo json_encode(['success' => false, 'error' => 'Image path is required']);
        exit;
    }
    
    // Process 3D modeling
    $result = process3DModeling($imagePath, $hairStyle, $faceShape);
    
    // Add hair recommendations if successful
    if ($result['success']) {
        $result['hair_recommendations'] = generateHairRecommendations($faceShape);
    }
    
    echo json_encode($result);
    exit;
}

// Handle GET request for testing
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['test'])) {
    $testImagePath = __DIR__ . '/uploads/face_detection/contoh.jpg';
    $result = process3DModeling($testImagePath, 'buzz_cut', 'square');
    
    echo json_encode($result, JSON_PRETTY_PRINT);
    exit;
}

echo json_encode(['success' => false, 'error' => 'Invalid request method']);
?>