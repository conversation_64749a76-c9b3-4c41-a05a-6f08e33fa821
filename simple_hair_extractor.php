<?php
header('Content-Type: application/json');

function simpleHairExtraction($userImagePath, $modelImagePath, $hairStyle, $faceShape) {
    try {
        // Create output directory
        $outputDir = 'uploads/3d_results/';
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }
        
        // Load images
        $userImg = @imagecreatefromstring(file_get_contents($userImagePath));
        $modelImg = @imagecreatefromstring(file_get_contents($modelImagePath));
        
        if (!$userImg || !$modelImg) {
            return ['success' => false, 'error' => 'Failed to load images'];
        }
        
        // Get dimensions
        $userWidth = imagesx($userImg);
        $userHeight = imagesy($userImg);
        $modelWidth = imagesx($modelImg);
        $modelHeight = imagesy($modelImg);
        
        // Create result image
        $result = imagecreatetruecolor($userWidth, $userHeight);
        imagecopy($result, $userImg, 0, 0, 0, 0, $userWidth, $userHeight);
        
        // Extract hair region from model (top 40% of image)
        $hairHeight = intval($modelHeight * 0.4);
        $hairRegion = imagecreatetruecolor($modelWidth, $hairHeight);
        imagecopy($hairRegion, $modelImg, 0, 0, 0, 0, $modelWidth, $hairHeight);
        
        // Scale hair to fit user's head
        $scaledWidth = intval($userWidth * 0.8);
        $scaledHeight = intval($hairHeight * ($scaledWidth / $modelWidth));
        $scaledHair = imagecreatetruecolor($scaledWidth, $scaledHeight);
        imagecopyresampled($scaledHair, $hairRegion, 0, 0, 0, 0, 
                          $scaledWidth, $scaledHeight, $modelWidth, $hairHeight);
        
        // Position hair on user's head
        $hairX = intval(($userWidth - $scaledWidth) / 2);
        $hairY = intval($userHeight * 0.05); // 5% from top
        
        // Apply hair with transparency
        imagecopymerge($result, $scaledHair, $hairX, $hairY, 0, 0, 
                      $scaledWidth, $scaledHeight, 75);
        
        // Save result
        $outputPath = $outputDir . uniqid('hair_') . '.png';
        imagepng($result, $outputPath);
        
        // Cleanup
        imagedestroy($userImg);
        imagedestroy($modelImg);
        imagedestroy($hairRegion);
        imagedestroy($scaledHair);
        imagedestroy($result);
        
        return [
            'success' => true,
            'result_url' => $outputPath,
            'hair_style' => $hairStyle
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $userImage = $input['user_image'] ?? '';
    $modelImage = $input['model_image'] ?? '';
    $hairStyle = $input['hair_style'] ?? '';
    $faceShape = $input['face_shape'] ?? 'square';
    
    if (empty($userImage) || empty($modelImage)) {
        echo json_encode(['success' => false, 'error' => 'Missing images']);
        exit;
    }
    
    $result = simpleHairExtraction($userImage, $modelImage, $hairStyle, $faceShape);
    echo json_encode($result);
    exit;
}

echo json_encode(['success' => false, 'error' => 'Invalid request']);
?>