<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Redirect Fixed - Face Detection</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">🚀 Test Redirect FIXED</h1>
                <p class="text-gray-600">Verifikasi perbaikan masalah redirect setelah capture</p>
                <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 class="font-bold text-green-800">✅ Perbaikan yang Diterapkan:</h3>
                    <ul class="text-sm text-green-700 mt-2 space-y-1">
                        <li>• Redirect langsung tanpa delay artificial</li>
                        <li>• Fallback system jika server gagal</li>
                        <li>• Tombol manual redirect sebagai backup</li>
                        <li>• Auto-capture ketika deteksi stabil</li>
                        <li>• Enhanced debugging dan logging</li>
                    </ul>
                </div>
            </div>

            <!-- Quick Test -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">⚡ Quick Test</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="face.php" target="_blank" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg text-center font-bold">
                        <i class="fas fa-camera mr-2"></i>Test Face Detection
                    </a>
                    <button onclick="simulateFullFlow()" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-bold">
                        <i class="fas fa-play mr-2"></i>Simulate Full Flow
                    </button>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">📊 Test Results</h2>
                <div id="testResults" class="space-y-4">
                    <p class="text-gray-500">Klik tombol test untuk memulai...</p>
                </div>
            </div>

            <!-- Expected Flow -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🔄 Expected Flow</h2>
                <div class="space-y-4">
                    <div class="flex items-center space-x-4 p-3 bg-blue-50 rounded-lg">
                        <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">1</div>
                        <div>
                            <h3 class="font-bold text-blue-800">Wajah Terdeteksi</h3>
                            <p class="text-sm text-blue-600">Kotak merah muncul, bentuk wajah ditampilkan</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 p-3 bg-green-50 rounded-lg">
                        <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center font-bold">2</div>
                        <div>
                            <h3 class="font-bold text-green-800">Deteksi Stabil</h3>
                            <p class="text-sm text-green-600">Status berubah hijau, tombol capture beranimasi</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 p-3 bg-purple-50 rounded-lg">
                        <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center font-bold">3</div>
                        <div>
                            <h3 class="font-bold text-purple-800">Capture Diklik</h3>
                            <p class="text-sm text-purple-600">Proses analisis dimulai, status update</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 p-3 bg-orange-50 rounded-lg">
                        <div class="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center font-bold">4</div>
                        <div>
                            <h3 class="font-bold text-orange-800">Redirect Otomatis</h3>
                            <p class="text-sm text-orange-600">Langsung ke output.php tanpa delay</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Troubleshooting -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🔧 Troubleshooting</h2>
                <div class="space-y-4">
                    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h3 class="font-bold text-yellow-800 mb-2">Jika Redirect Tidak Berfungsi:</h3>
                        <ol class="text-sm text-yellow-700 space-y-1">
                            <li>1. Tunggu tombol manual "👁️ Lihat Hasil" (muncul setelah 2 detik)</li>
                            <li>2. Klik tombol manual untuk redirect</li>
                            <li>3. Check console browser untuk error messages</li>
                            <li>4. Gunakan debug_redirect.php untuk diagnosis</li>
                        </ol>
                    </div>
                    <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <h3 class="font-bold text-red-800 mb-2">Jika Server Lambat/Error:</h3>
                        <ol class="text-sm text-red-700 space-y-1">
                            <li>1. Sistem akan menggunakan real-time detection saja</li>
                            <li>2. Redirect tetap berfungsi dengan data fallback</li>
                            <li>3. Check analyze_face_sim.php untuk server issues</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('🚀 Test redirect system initialized', 'info');
            checkCurrentData();
        });

        function simulateFullFlow() {
            addTestResult('🎬 Simulating full detection and redirect flow...', 'info');
            
            // Step 1: Simulate face detection
            addTestResult('📹 Step 1: Face detection started', 'info');
            setTimeout(() => {
                addTestResult('✅ Step 1: Face detected (SQUARE)', 'success');
                
                // Step 2: Simulate stable detection
                addTestResult('🎯 Step 2: Checking detection stability...', 'info');
                setTimeout(() => {
                    addTestResult('✅ Step 2: Detection stable (8/10 matches)', 'success');
                    
                    // Step 3: Simulate capture
                    addTestResult('📸 Step 3: Capture button clicked', 'info');
                    setTimeout(() => {
                        addTestResult('✅ Step 3: Image captured and processed', 'success');
                        
                        // Step 4: Simulate server processing
                        addTestResult('🌐 Step 4: Sending to server...', 'info');
                        setTimeout(() => {
                            addTestResult('✅ Step 4: Server response received', 'success');
                            
                            // Step 5: Simulate data consistency check
                            addTestResult('🎯 Step 5: Checking data consistency...', 'info');
                            setTimeout(() => {
                                addTestResult('✅ Step 5: Real-time priority applied (SQUARE)', 'success');
                                
                                // Step 6: Simulate data storage
                                addTestResult('💾 Step 6: Saving to sessionStorage...', 'info');
                                setTimeout(() => {
                                    addTestResult('✅ Step 6: Data saved successfully', 'success');
                                    
                                    // Step 7: Simulate redirect
                                    addTestResult('🚀 Step 7: Preparing redirect...', 'info');
                                    setTimeout(() => {
                                        addTestResult('✅ Step 7: Redirect to output.php?face_shape=square', 'success');
                                        addTestResult('🎉 Full flow simulation completed successfully!', 'success');
                                        
                                        // Create test data
                                        createTestData();
                                        
                                    }, 500);
                                }, 300);
                            }, 400);
                        }, 800);
                    }, 600);
                }, 700);
            }, 500);
        }

        function createTestData() {
            const testData = {
                shape: 'square',
                confidence: 95,
                alt_shape: 'oval',
                alt_confidence: 85,
                description: 'Bentuk wajah square terdeteksi dengan akurat menggunakan analisis real-time',
                shape_description: 'Bentuk wajah square dengan proporsi yang seimbang',
                recommendations: ['Slick Back', 'Long Layers', 'French Crop'],
                detection_source: 'real_time_priority',
                real_time_shape: 'square',
                server_shape: 'oval',
                timestamp: new Date().toISOString(),
                test_simulation: true
            };

            const testImage = createTestImage('SQUARE');
            
            sessionStorage.setItem('faceAnalysisResult', JSON.stringify(testData));
            sessionStorage.setItem('capturedImage', testImage);
            
            addTestResult('📦 Test data created in sessionStorage', 'success');
            addTestResult('🔗 You can now test output.php manually', 'info');
        }

        function createTestImage(shape) {
            const canvas = document.createElement('canvas');
            canvas.width = 300;
            canvas.height = 300;
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, 300, 300);
            
            // Face outline based on shape
            ctx.strokeStyle = '#22c55e';
            ctx.lineWidth = 3;
            ctx.beginPath();
            
            switch(shape.toLowerCase()) {
                case 'square':
                    ctx.rect(75, 75, 150, 150);
                    break;
                case 'round':
                    ctx.arc(150, 150, 75, 0, 2 * Math.PI);
                    break;
                case 'oval':
                    ctx.ellipse(150, 150, 60, 85, 0, 0, 2 * Math.PI);
                    break;
                case 'heart':
                    // Heart shape approximation
                    ctx.moveTo(150, 75);
                    ctx.lineTo(100, 125);
                    ctx.lineTo(125, 200);
                    ctx.lineTo(150, 225);
                    ctx.lineTo(175, 200);
                    ctx.lineTo(200, 125);
                    ctx.closePath();
                    break;
                case 'rectangular':
                    ctx.rect(100, 50, 100, 200);
                    break;
                default:
                    ctx.ellipse(150, 150, 60, 85, 0, 0, 2 * Math.PI);
            }
            
            ctx.stroke();
            
            // Text
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(shape.toUpperCase(), 150, 150);
            ctx.font = '14px Arial';
            ctx.fillText('Test Image', 150, 170);
            
            return canvas.toDataURL('image/jpeg', 0.8);
        }

        function checkCurrentData() {
            const storedData = sessionStorage.getItem('faceAnalysisResult');
            if (storedData) {
                try {
                    const data = JSON.parse(storedData);
                    addTestResult(`📦 Found existing data: ${data.shape} (${data.detection_source || 'unknown source'})`, 'info');
                } catch (e) {
                    addTestResult('⚠️ Found corrupted data in sessionStorage', 'warning');
                }
            } else {
                addTestResult('📦 No existing data in sessionStorage', 'info');
            }
        }

        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultItem = document.createElement('div');
            
            let colorClass = 'text-gray-700';
            let bgClass = 'bg-gray-100';
            let icon = '📝';
            
            switch (type) {
                case 'success':
                    colorClass = 'text-green-700';
                    bgClass = 'bg-green-100';
                    icon = '✅';
                    break;
                case 'error':
                    colorClass = 'text-red-700';
                    bgClass = 'bg-red-100';
                    icon = '❌';
                    break;
                case 'warning':
                    colorClass = 'text-yellow-700';
                    bgClass = 'bg-yellow-100';
                    icon = '⚠️';
                    break;
                case 'info':
                    colorClass = 'text-blue-700';
                    bgClass = 'bg-blue-100';
                    icon = 'ℹ️';
                    break;
            }
            
            resultItem.className = `p-3 rounded-lg ${bgClass} ${colorClass} font-mono text-sm`;
            resultItem.innerHTML = `${icon} [${new Date().toLocaleTimeString()}] ${message}`;
            
            // Clear "no results" message if it exists
            if (resultsDiv.children.length === 1 && resultsDiv.children[0].textContent.includes('Klik tombol test')) {
                resultsDiv.innerHTML = '';
            }
            
            resultsDiv.appendChild(resultItem);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
    </script>
</body>
</html>
