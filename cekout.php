<?php
session_start();
require_once 'Admin/db_config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$current_pembeli_id = $_SESSION['user_id'];

// Initialize variables
$cart_items = [];
$total_amount = 0;
$selected_address_data = null;

// Check if coming from cart (multiple items) or direct product checkout (single item)
if (isset($_GET['cart_items']) && !empty($_GET['cart_items'])) {
    // Coming from cart - multiple items
    $cart_item_ids = explode(',', $_GET['cart_items']);
    $cart_item_ids = array_map('intval', $cart_item_ids); // Sanitize IDs

    if (!empty($cart_item_ids)) {
        try {
            // Create placeholders for IN clause
            $placeholders = str_repeat('?,', count($cart_item_ids) - 1) . '?';

            // Fetch selected cart items with product and variant details
            $stmt = $conn->prepare("
                SELECT c.id as cart_id, c.quantity, c.product_id, c.variant_id,
                       p.nama_produk, p.gambar_utama, p.deskripsi,
                       v.ukuran_ml, v.harga, v.gambar_varian,
                       (c.quantity * v.harga) as total_price
                FROM cart c
                JOIN produk p ON c.product_id = p.id
                JOIN variasi_produk v ON c.variant_id = v.id
                WHERE c.id IN ($placeholders) AND c.user_id = ?
                ORDER BY c.created_at DESC
            ");

            // Add user_id to the end of cart_item_ids array
            $params = array_merge($cart_item_ids, [$current_pembeli_id]);
            $stmt->execute($params);
            $cart_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Calculate total
            foreach ($cart_items as $item) {
                $total_amount += $item['total_price'];
            }

        } catch (PDOException $e) {
            error_log("Database Error in cekout.php (cart items): " . $e->getMessage());
        }
    }
} else {
    // Coming from direct product checkout - single item
    $product_id = isset($_GET['product_id']) ? $_GET['product_id'] : null;
    $variant_id = isset($_GET['variant_id']) ? $_GET['variant_id'] : null;
    $quantity = isset($_GET['quantity']) ? intval($_GET['quantity']) : 1;

    if ($product_id && $variant_id) {
        try {
            // Fetch product and variant details for single item
            $stmt = $conn->prepare("
                SELECT p.id as product_id, p.nama_produk, p.gambar_utama, p.deskripsi,
                       v.id as variant_id, v.ukuran_ml, v.harga, v.gambar_varian,
                       ? as quantity,
                       (? * v.harga) as total_price
                FROM produk p
                JOIN variasi_produk v ON p.id = v.produk_id
                WHERE p.id = ? AND v.id = ?
            ");
            $stmt->execute([$quantity, $quantity, $product_id, $variant_id]);
            $single_item = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($single_item) {
                $cart_items = [$single_item]; // Convert to array format
                $total_amount = $single_item['total_price'];
            }

        } catch (PDOException $e) {
            error_log("Database Error in cekout.php (single item): " . $e->getMessage());
        }
    }
}

// Redirect back to cart if no items found
if (empty($cart_items)) {
    header('Location: keranjang.php?error=no_items');
    exit();
}

// Fetch default or first available address for the current buyer
try {
    $stmt_default_address = $conn->prepare("SELECT id, address_name, full_address, phone_number FROM user_addresses WHERE pembeli_id = :pembeli_id ORDER BY is_default DESC, created_at DESC LIMIT 1");
    $stmt_default_address->execute(['pembeli_id' => $current_pembeli_id]);
    $selected_address_data = $stmt_default_address->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Database Error in cekout.php (fetching default address): " . $e->getMessage());
}

$upload_dir_display = '/aplikasi-pixel/uploads/'; // Path absolut dari root ke folder uploads
$variant_upload_dir_display = '/aplikasi-pixel/uploads/variants/'; // Path untuk gambar varian

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
        }
        .checkout-step {
            position: relative;
        }
        .checkout-step.active .step-number {
            background-color: #0A5144;
            color: white;
            border-color: #0A5144;
        }
        .checkout-step.completed .step-number {
            background-color: #0A5144;
            color: white;
            border-color: #0A5144;
        }
        .checkout-step.completed .step-number::after {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }
        .checkout-step.inactive .step-number {
            background-color: #E5E7EB;
            color: #9CA3AF;
            border-color: #E5E7EB;
        }
        .payment-method {
            transition: all 0.3s ease;
        }
        .payment-method.selected {
            border-color: #0A5144;
            background-color: #F0F9F8;
        }
        .tab-button-address.active-address-tab {
            color: #0A5144;
            border-bottom-color: #0A5144;
        }
    </style>
</head>
<body class="pb-32">
    <!-- Header -->
    <header class="sticky top-0 z-10 bg-white p-4 shadow-sm">
        <div class="flex items-center justify-between">
            <a href="javascript:history.back()" class="text-gray-600">
                <i class="fas fa-arrow-left text-lg"></i>
            </a>
            <h1 class="text-xl font-bold text-gray-900">Checkout</h1>
            <div class="w-6"></div> <!-- Spacer for balance -->
        </div>
    </header>

    <!-- Checkout Progress -->
    <div class="px-4 py-3 bg-white flex justify-between items-center relative">
        <div class="absolute top-1/2 left-4 right-4 h-0.5 bg-gray-200 -translate-y-1/2 z-0"></div>
        <div class="checkout-step active z-10 flex flex-col items-center">
            <div class="step-number w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center font-bold mb-1">1</div>
            <span class="text-xs font-medium">Delivery</span>
        </div>
        <div class="checkout-step inactive z-10 flex flex-col items-center">
            <div class="step-number w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center font-bold mb-1">2</div>
            <span class="text-xs font-medium">Payment</span>
        </div>
        <div class="checkout-step inactive z-10 flex flex-col items-center">
            <div class="step-number w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center font-bold mb-1">3</div>
            <span class="text-xs font-medium">Confirm</span>
        </div>
    </div>

    <!-- Delivery Address -->
    <div class="bg-white px-4 py-4 mt-1 shadow-sm">
        <div class="flex justify-between items-center mb-3">
            <h2 class="font-bold text-gray-900">Delivery Address</h2>
            <button class="text-[#0A5144] text-sm font-medium" onclick="showAddressModal()">Change</button>
        </div>
        <div id="deliveryAddressDisplay" class="border border-[#0A5144] rounded-lg p-3 bg-[#F0F9F8]">
            <?php if ($selected_address_data): ?>
                <div>
                    <h3 class="font-medium text-gray-900"><?php echo htmlspecialchars($selected_address_data['address_name']); ?></h3>
                    <p class="text-sm text-gray-600 flex items-start mt-0.5">
                        <i class="fas fa-map-marker-alt mr-2 text-[#0A5144]"></i>
                        <span class="flex-1"><?php echo htmlspecialchars($selected_address_data['full_address']); ?></span>
                    </p>
                    <p class="text-sm text-gray-600 mt-1 flex items-center">
                        <i class="fas fa-phone mr-1 text-[#0A5144]"></i>
                        <span class="flex-1"><?php echo htmlspecialchars($selected_address_data['phone_number']); ?></span>
                    </p>
                </div>
            <?php else: ?>
                <div>
                    <h3 class="font-medium text-gray-900">No Address Selected</h3>
                    <p class="text-sm text-gray-600 flex items-start mt-0.5">
                        <i class="fas fa-map-marker-alt mr-2 text-[#0A5144]"></i>
                        <span class="flex-1">Please add or select a delivery address.</span>
                    </p>
                    <p class="text-sm text-gray-600 mt-1 flex items-center">
                        <i class="fas fa-phone mr-1 text-[#0A5144]"></i>
                        <span class="flex-1">N/A</span>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Delivery Service -->
    <div class="bg-white px-4 py-4 mt-4 shadow-sm">
        <h2 class="font-bold text-gray-900 mb-3">Delivery Service</h2>
        <select id="deliveryService" class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0A5144]">
            <option value="0" data-cost="0">Standard Delivery (Free)</option>
            <option value="1" data-cost="15000">Express Delivery (Rp 15.000)</option>
            <option value="2" data-cost="25000">Same-Day Delivery (Rp 25.000)</option>
        </select>
        <div class="flex justify-between mt-3">
            <span class="text-gray-600">Delivery Fee</span>
            <span id="deliveryFeeDisplay" class="text-gray-900">Rp 0</span>
        </div>
    </div>

    <!-- Order Summary -->
    <div class="bg-white px-4 py-4 mt-4 shadow-sm">
        <h2 class="font-bold text-gray-900 mb-3">Order Summary</h2>

        <?php if (!empty($cart_items)): ?>
            <?php foreach ($cart_items as $item): ?>
                <!-- Product Item -->
                <div class="flex justify-between items-start py-3 border-b border-gray-100 last:border-b-0">
                    <div class="flex items-start space-x-3">
                        <!-- Product Image -->
                        <div class="w-16 h-16 flex-shrink-0">
                            <?php
                            // Determine image source - prefer variant image, fallback to product image
                            $image_src = '';
                            if (!empty($item['gambar_varian'])) {
                                $image_src = 'uploads/variants/' . htmlspecialchars($item['gambar_varian']);
                            } elseif (!empty($item['gambar_utama'])) {
                                $image_src = 'uploads/' . htmlspecialchars($item['gambar_utama']);
                            }

                            // Check if image exists
                            if (!empty($image_src) && file_exists($image_src)) {
                                echo '<img src="' . $image_src . '" alt="Product" class="w-full h-full rounded-lg object-cover border border-gray-200">';
                            } else {
                                echo '<div class="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center border border-gray-200">
                                        <i class="fas fa-image text-gray-400"></i>
                                      </div>';
                            }
                            ?>
                        </div>

                        <!-- Product Details -->
                        <div class="flex-1 min-w-0">
                            <h3 class="font-semibold text-gray-900 text-sm mb-1">
                                <?php echo htmlspecialchars($item['nama_produk']); ?>
                            </h3>
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <?php echo htmlspecialchars($item['ukuran_ml']); ?>ml
                                </span>
                            </div>
                            <p class="text-sm text-gray-600">
                                <span class="font-medium">Rp <?php echo number_format($item['harga'], 0, ',', '.'); ?></span>
                                <span class="text-gray-400"> × <?php echo $item['quantity']; ?></span>
                            </p>
                        </div>
                    </div>

                    <!-- Price -->
                    <div class="text-right">
                        <p class="font-bold text-green-600 text-lg">
                            Rp <?php echo number_format($item['total_price'], 0, ',', '.'); ?>
                        </p>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="text-center py-8">
                <i class="fas fa-shopping-cart text-4xl text-gray-300 mb-3"></i>
                <p class="text-gray-500">No items selected for checkout.</p>
                <a href="keranjang.php" class="inline-flex items-center mt-3 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Cart
                </a>
            </div>
        <?php endif; ?>

        <!-- Order Total -->
        <div class="bg-white px-4 py-4 mt-4 shadow-sm">
            <h2 class="font-bold text-gray-900 mb-3">Order Total</h2>

            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-gray-600">Subtotal (<?php echo count($cart_items); ?> item<?php echo count($cart_items) > 1 ? 's' : ''; ?>)</span>
                    <span class="text-gray-900" id="subtotalDisplay">Rp <?php echo number_format($total_amount, 0, ',', '.'); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Delivery Fee</span>
                    <span id="deliveryFeeDisplay" class="text-gray-900">Rp 0</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Service Fee</span>
                    <span id="serviceFeeDisplay" class="text-gray-900">Rp 0</span>
                </div>
                <div class="border-t border-gray-200 pt-2 mt-2 flex justify-between">
                    <span class="font-bold text-gray-900 text-lg">Total</span>
                    <span class="font-bold text-green-600 text-xl" id="grandTotalDisplay">Rp <?php echo number_format($total_amount, 0, ',', '.'); ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Fixed Add to Cart Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-white p-4 shadow-lg border-t border-gray-100">
        <button class="gradient-bg w-full py-3 rounded-lg text-white font-bold shadow-md hover:opacity-90 transition" onclick="goToPayment()">
            Continue to Payment
        </button>
    </div>

    <!-- Address Modal -->
    <div id="addressModal" class="fixed inset-0 bg-black/50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg w-full max-w-md mx-4 p-6 shadow-lg">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-900">Change Delivery Address</h3>
                <button onclick="hideAddressModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <!-- Tabs for Address Selection -->
            <div class="flex border-b border-gray-200 mb-4">
                <button class="tab-button-address py-2 px-4 text-sm font-medium text-center text-gray-600 border-b-2 border-transparent hover:text-[#0A5144] hover:border-[#0A5144] active-address-tab" data-tab="savedAddresses">Saved Addresses</button>
                <button class="tab-button-address py-2 px-4 text-sm font-medium text-center text-gray-600 border-b-2 border-transparent hover:text-[#0A5144] hover:border-[#0A5144]" data-tab="newAddress">Add New Address</button>
            </div>

            <!-- Tab Content -->
            <div id="savedAddresses" class="address-tab-content space-y-3 active">
                <!-- Placeholder for Saved Addresses -->
            </div>

            <div id="newAddress" class="address-tab-content space-y-3 hidden">
                <!-- Form for New Address -->
                <div>
                    <label for="addressName" class="block text-sm font-medium text-gray-700">Address Name</label>
                    <input type="text" id="addressName" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2">
                </div>
                <div>
                    <label for="addressDetails" class="block text-sm font-medium text-gray-700">Full Address</label>
                    <textarea id="addressDetails" rows="3" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"></textarea>
                </div>
                <div>
                    <label for="addressPhone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                    <input type="text" id="addressPhone" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2">
                </div>
                <button onclick="saveNewAddress()" class="gradient-bg w-full py-3 rounded-lg text-white font-bold hover:opacity-90 transition">
                    Save Address
                </button>
            </div>
        </div>
    </div>

</body>
<script>
    let deliveryAddressDisplay; // Declare globally
    let currentTotal = 0; // Global variable for current total
    let selectedAddressId = null; // Global variable for selected address ID
    let deliveryFee = 0; // Global variable for delivery fee
    let serviceFee = 0; // Global variable for service fee

    // Get checkout data from PHP
    const checkoutData = {
        cartItems: <?php echo json_encode($cart_items); ?>,
        totalAmount: <?php echo $total_amount; ?>,
        isFromCart: <?php echo isset($_GET['cart_items']) ? 'true' : 'false'; ?>,
        // For single item checkout
        productId: <?php echo json_encode($product_id ?? null); ?>,
        variantId: <?php echo json_encode($variant_id ?? null); ?>,
        quantity: <?php echo $quantity ?? 1; ?>
    };

    // Initialize currentTotal
    currentTotal = checkoutData.totalAmount;

    // Initialize selectedAddressId from PHP
    selectedAddressId = <?php echo json_encode($selected_address_data['id'] ?? null); ?>;
    console.log('Initial Selected Address ID from PHP:', selectedAddressId);
    console.log('Checkout Data:', checkoutData);

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        deliveryAddressDisplay = document.getElementById('deliveryAddressDisplay');

        // Setup delivery service change handler
        const deliveryService = document.getElementById('deliveryService');
        if (deliveryService) {
            deliveryService.addEventListener('change', updateDeliveryFee);
        }

        // Initial calculation
        updateTotalDisplay();
    });

    // Update delivery fee and total
    function updateDeliveryFee() {
        const deliveryService = document.getElementById('deliveryService');
        const selectedOption = deliveryService.options[deliveryService.selectedIndex];
        deliveryFee = parseInt(selectedOption.dataset.cost) || 0;

        // Update delivery fee display
        document.getElementById('deliveryFeeDisplay').textContent = 'Rp ' + deliveryFee.toLocaleString('id-ID');

        // Update total
        updateTotalDisplay();
    }

    // Update total display
    function updateTotalDisplay() {
        const grandTotal = currentTotal + deliveryFee + serviceFee;
        document.getElementById('grandTotalDisplay').textContent = 'Rp ' + grandTotal.toLocaleString('id-ID');
    }

    // JavaScript for Address Modal
    function showAddressModal() {
        const modal = document.getElementById('addressModal');
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        fetchAndDisplayAddresses(); // Call fetch addresses when modal opens
    }

    function hideAddressModal() {
        const modal = document.getElementById('addressModal');
        modal.classList.add('hidden');
        document.body.style.overflow = '';
    }

    function deleteAddress(event, addressId, buttonElement) {
        event.stopPropagation(); // Prevent the click from bubbling up to the parent card's click listener
        if (confirm('Are you sure you want to delete this address?')) {
            console.log('Deleting address with ID:', addressId);
            // In a real application, you would send an AJAX request to your server to delete the address.
            // Example: fetch('/api/delete-address/' + addressId, { method: 'POST' }).then(response => { ... });

            // Remove the address card from the DOM
            const addressCard = buttonElement.closest('.address-card');
            if (addressCard) {
                addressCard.remove();
                alert('Address deleted successfully!');

                // Optionally, if the deleted address was the currently displayed one, reset the display
                // Use the globally accessible deliveryAddressDisplay
                if (!document.querySelector('.address-card.border-[#0A5144]') && deliveryAddressDisplay) {
                    deliveryAddressDisplay.innerHTML = `
                        <div>
                            <h3 class="font-medium text-gray-900">No Address Selected</h3>
                            <p class="text-sm text-gray-600 flex items-start mt-0.5">
                                <i class="fas fa-map-marker-alt mr-2 text-[#0A5144]"></i>
                                <span class="flex-1">Please add or select a delivery address.</span>
                            </p>
                            <p class="text-sm text-gray-600 mt-1 flex items-center">
                                <i class="fas fa-phone mr-1 text-[#0A5144]"></i>
                                <span class="flex-1">N/A</span>
                            </p>
                        </div>
                    `;
                    selectedAddressId = null; // Clear selected address ID if no addresses
                }
                // After deleting, re-fetch and display addresses to ensure consistency,
                // and potentially select a new default if the current one was deleted.
                fetchAndDisplayAddresses();
            }
        }
    }

    function updateDeliveryAddressDisplay(selectedCard) {
        const name = selectedCard.dataset.addressName;
        const details = selectedCard.dataset.addressDetails;
        const phone = selectedCard.dataset.addressPhone;
        const id = selectedCard.dataset.addressId; // Get the ID from the selected card

        // Use the globally accessible deliveryAddressDisplay
        deliveryAddressDisplay.innerHTML = `
            <div>
                <h3 class="font-medium text-gray-900">${name}</h3>
                <p class="text-sm text-gray-600 flex items-start mt-0.5">
                    <i class="fas fa-map-marker-alt mr-2 text-[#0A5144]"></i>
                    <span class="flex-1">${details}</span>
                </p>
                <p class="text-sm text-gray-600 mt-1 flex items-center">
                    <i class="fas fa-phone mr-1 text-[#0A5144]"></i>
                    <span class="flex-1">${phone}</span>
                </p>
            </div>
        `;
        selectedAddressId = id; // Update the global selectedAddressId
    }

    function saveNewAddress() {
        const name = document.getElementById('addressName').value;
        const details = document.getElementById('addressDetails').value;
        const phone = document.getElementById('addressPhone').value;

        if (name && details && phone) {
            fetch('api/addresses.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    address_name: name,
                    full_address: details,
                    phone_number: phone
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert('New address saved successfully!');
                    hideAddressModal();
                    // Reload addresses from the database
                    fetchAndDisplayAddresses();
                } else {
                    alert('Failed to save address: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error saving new address:', error);
                alert('An error occurred while saving the address.');
            });

            // Clear the form fields
            document.getElementById('addressName').value = '';
            document.getElementById('addressDetails').value = '';
            document.getElementById('addressPhone').value = '';

            // Programmatically click the 'Saved Addresses' tab button
            const savedAddressesTabButton = document.querySelector('.tab-button-address[data-tab="savedAddresses"]');
            if (savedAddressesTabButton) {
                savedAddressesTabButton.click();
            }

        } else {
            alert('Please fill in all address fields.');
        }
    }

    // New function to fetch and display addresses
    function fetchAndDisplayAddresses() {
        const savedAddressesContainer = document.getElementById('savedAddresses');
        savedAddressesContainer.innerHTML = '<p class="text-gray-500 text-center py-4">Loading addresses...</p>';

        fetch('api/addresses.php')
            .then(response => response.json())
            .then(data => {
                savedAddressesContainer.innerHTML = ''; // Clear loading message
                if (data.status === 'success' && data.data.length > 0) {
                    data.data.forEach(address => {
                        const addressCard = `
                            <div class="address-card border border-gray-300 rounded-lg p-3 cursor-pointer hover:border-[#0A5144] hover:bg-[#F0F9F8]" 
                                data-address-id="${address.id}"
                                data-address-name="${address.address_name}"
                                data-address-details="${address.full_address}"
                                data-address-phone="${address.phone_number}">
                                <div class="flex justify-between items-start">
                                    <h4 class="font-medium text-gray-900">${address.address_name}</h4>
                                    <button onclick="deleteAddress(event, ${address.id}, this)" class="text-gray-400 hover:text-red-500 transition-colors focus:outline-none">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                                <p class="text-sm text-gray-600 flex items-start mt-0.5">
                                    <i class="fas fa-map-marker-alt mr-2 text-[#0A5144]"></i>
                                    <span class="flex-1">${address.full_address}</span>
                                </p>
                                <p class="text-xs text-gray-500 mt-1 flex items-center">
                                    <i class="fas fa-phone mr-1 text-[#0A5144]"></i>
                                    <span class="flex-1">${address.phone_number}</span>
                                </p>
                            </div>
                        `;
                        savedAddressesContainer.insertAdjacentHTML('beforeend', addressCard);
                    });
                    // Re-attach event listeners to newly created address cards
                    attachAddressCardListeners();

                    // Select the first address by default if none is selected
                    const currentDisplayedAddressName = deliveryAddressDisplay.querySelector('h3').textContent;
                    const firstAddressCard = savedAddressesContainer.querySelector('.address-card');
                    if (firstAddressCard && currentDisplayedAddressName === 'No Address Selected') {
                        firstAddressCard.classList.add('border-[#0A5144]', 'bg-[#F0F9F8]');
                        updateDeliveryAddressDisplay(firstAddressCard);
                        selectedAddressId = firstAddressCard.dataset.addressId; // Set selected address ID
                        console.log('Selected Address ID:', selectedAddressId); // Debug log
                    }

                } else {
                    savedAddressesContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No saved addresses. Please add a new one.</p>';
                    // If no addresses are found, reset the main display to 'No Address Selected'
                    deliveryAddressDisplay.innerHTML = `
                        <div>
                            <h3 class="font-medium text-gray-900">No Address Selected</h3>
                            <p class="text-sm text-gray-600 flex items-start mt-0.5">
                                <i class="fas fa-map-marker-alt mr-2 text-[#0A5144]"></i>
                                <span class="flex-1">Please add or select a delivery address.</span>
                            </p>
                            <p class="text-sm text-gray-600 mt-1 flex items-center">
                                <i class="fas fa-phone mr-1 text-[#0A5144]"></i>
                                <span class="flex-1">N/A</span>
                            </p>
                        </div>
                    `;
                    selectedAddressId = null; // Clear selected address ID if no addresses
                }
            })
            .catch(error => {
                console.error('Error fetching addresses:', error);
                savedAddressesContainer.innerHTML = '<p class="text-red-500 text-center py-4">Error loading addresses.</p>';
            });
    }

    // Helper function to attach click listeners to address cards
    function attachAddressCardListeners() {
        const addressCards = document.querySelectorAll('#savedAddresses .address-card');
        addressCards.forEach(card => {
            card.addEventListener('click', function(event) {
                // Prevent event propagation to the delete button if clicking the card
                if (event.target.tagName !== 'BUTTON' && event.target.tagName !== 'I') {
                    addressCards.forEach(c => c.classList.remove('border-[#0A5144]', 'bg-[#F0F9F8]'));
                    this.classList.add('border-[#0A5144]', 'bg-[#F0F9F8]');
                    updateDeliveryAddressDisplay(this);
                    selectedAddressId = this.dataset.addressId; // Set selected address ID
                    console.log('Selected Address ID:', selectedAddressId); // Debug log
                    hideAddressModal();
                }
            });
        });
    }

    // Function to go to payment page
    window.goToPayment = function() {
        if (!selectedAddressId) {
            alert('Please select a delivery address first.');
            showAddressModal(); // Re-open modal to select address
            return;
        }

        const deliveryServiceSelect = document.getElementById('deliveryService');
        const grandTotal = currentTotal + deliveryFee + serviceFee;

        // Prepare parameters based on checkout type
        let urlParams;

        if (checkoutData.isFromCart) {
            // From cart - pass cart item IDs
            const cartItemIds = checkoutData.cartItems.map(item => item.cart_id || item.id).join(',');
            urlParams = new URLSearchParams({
                cart_items: cartItemIds,
                delivery_cost: deliveryFee,
                service_fee: serviceFee,
                total_amount: grandTotal,
                address_id: selectedAddressId
            }).toString();
        } else {
            // Single item checkout
            urlParams = new URLSearchParams({
                product_id: checkoutData.productId,
                variant_id: checkoutData.variantId,
                quantity: checkoutData.quantity,
                delivery_cost: deliveryFee,
                service_fee: serviceFee,
                total_amount: grandTotal,
                address_id: selectedAddressId
            }).toString();
        }

        window.location.href = `payment.php?${urlParams}`;
    };

    document.addEventListener('DOMContentLoaded', function() {
        const addressTabButtons = document.querySelectorAll('.tab-button-address');
        const addressTabContents = document.querySelectorAll('.address-tab-content');
        
        // Assign to the global variable
        deliveryAddressDisplay = document.querySelector('#deliveryAddressDisplay');

        // Get references to elements inside DOMContentLoaded for updateOrderTotal
        const deliveryServiceSelect = document.getElementById('deliveryService');
        console.log('deliveryServiceSelect element:', deliveryServiceSelect); // Debug log
        const deliveryFeeDisplay = document.getElementById('deliveryFeeDisplay');
        console.log('deliveryFeeDisplay element:', deliveryFeeDisplay); // Debug log
        const orderSubtotalDisplay = document.querySelector('.space-y-2 div:nth-child(1) span:nth-child(2)');
        console.log('orderSubtotalDisplay element:', orderSubtotalDisplay); // Debug log
        const orderTotalDisplay = document.querySelector('.space-y-2 div:nth-child(3) span:nth-child(2)'); // Corrected selector
        console.log('orderTotalDisplay element:', orderTotalDisplay); // Debug log
        const serviceFeeDisplay = document.getElementById('serviceFeeDisplay'); // New line
        console.log('serviceFeeDisplay element:', serviceFeeDisplay); // Debug log

        function updateOrderTotal() {
            console.log('updateOrderTotal function called!'); // Debug log
            const selectedOption = deliveryServiceSelect.options[deliveryServiceSelect.selectedIndex];
            deliveryFee = parseInt(selectedOption.dataset.cost) || 0;
            serviceFee = 2000; // Fixed service fee
            console.log('Selected Delivery Cost:', deliveryFee); // Debug log

            const subtotal = checkoutData.totalAmount;
            const total = subtotal + serviceFee + deliveryFee;
            console.log('Calculated Total:', total);
            currentTotal = subtotal; // Keep original subtotal separate

            // Update displays
            if (deliveryFeeDisplay) {
                deliveryFeeDisplay.textContent = `Rp ${new Intl.NumberFormat('id-ID').format(deliveryFee)}`;
            }
            if (serviceFeeDisplay) {
                serviceFeeDisplay.textContent = `Rp ${new Intl.NumberFormat('id-ID').format(serviceFee)}`;
            }

            // Update grand total
            const grandTotalDisplay = document.getElementById('grandTotalDisplay');
            if (grandTotalDisplay) {
                grandTotalDisplay.textContent = `Rp ${new Intl.NumberFormat('id-ID').format(total)}`;
            }
        }

        // Call on page load
        updateOrderTotal();

        // Add event listener for delivery service change
        deliveryServiceSelect.addEventListener('change', updateOrderTotal);

        addressTabButtons.forEach(button => {
            button.addEventListener('click', function() {
                addressTabButtons.forEach(btn => btn.classList.remove('active-address-tab', 'border-[#0A5144]', 'text-[#0A5144]'));
                this.classList.add('active-address-tab', 'border-[#0A5144]', 'text-[#0A5144]');

                const targetTab = this.dataset.tab;
                addressTabContents.forEach(content => {
                    if (content.id === targetTab) {
                        content.classList.remove('hidden');
                    } else {
                        content.classList.add('hidden');
                    }
                });
            });
        });

        // Initial fetch on DOMContentLoaded (this will populate addresses and select default if needed)
        fetchAndDisplayAddresses();
    });
</script>
</html>