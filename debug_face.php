<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Detection Debug - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-800">Face Detection Debug</h1>
                        <p class="text-gray-600 mt-2">Debug tools untuk sistem deteksi wajah</p>
                    </div>
                    <div class="space-x-2">
                        <a href="face.php" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                            <i class="fas fa-camera mr-2"></i>Face Detection
                        </a>
                        <button onclick="clearLogs()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded">
                            <i class="fas fa-trash mr-2"></i>Clear Logs
                        </button>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">
                        <i class="fas fa-database text-blue-600 mr-2"></i>Database Status
                    </h3>
                    <div id="databaseStatus">Checking...</div>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">
                        <i class="fas fa-python text-green-600 mr-2"></i>Python Status
                    </h3>
                    <div id="pythonStatus">Checking...</div>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">
                        <i class="fas fa-folder text-yellow-600 mr-2"></i>Files Status
                    </h3>
                    <div id="filesStatus">Checking...</div>
                </div>
            </div>

            <!-- Debug Logs -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">
                        <i class="fas fa-file-alt text-purple-600 mr-2"></i>Debug Logs
                    </h2>
                    <button onclick="refreshLogs()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                </div>
                <div id="debugLogs" class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
                    Loading logs...
                </div>
            </div>

            <!-- Test Tools -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-tools text-orange-600 mr-2"></i>Test Tools
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <button onclick="testDatabase()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        Test Database
                    </button>
                    <button onclick="testPython()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                        Test Python
                    </button>
                    <button onclick="testUpload()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded">
                        Test Upload
                    </button>
                    <button onclick="testSessionStorage()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                        Test SessionStorage
                    </button>
                </div>
                <div id="testResults" class="mt-4 p-4 bg-gray-100 rounded-lg min-h-20">
                    Test results will appear here...
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-history text-indigo-600 mr-2"></i>Recent Activity
                </h2>
                <div id="recentActivity" class="space-y-2">
                    Loading recent activity...
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh every 5 seconds
        let autoRefresh = true;

        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            refreshLogs();
            loadRecentActivity();
            
            // Auto refresh every 5 seconds
            setInterval(() => {
                if (autoRefresh) {
                    refreshLogs();
                    loadRecentActivity();
                }
            }, 5000);
        });

        async function checkSystemStatus() {
            // Check Database
            try {
                const dbResponse = await fetch('test_face_system.php');
                const dbText = await dbResponse.text();
                if (dbText.includes('Database connection: SUCCESS')) {
                    document.getElementById('databaseStatus').innerHTML = '<span class="text-green-600">✅ Connected</span>';
                } else {
                    document.getElementById('databaseStatus').innerHTML = '<span class="text-red-600">❌ Error</span>';
                }
            } catch (error) {
                document.getElementById('databaseStatus').innerHTML = '<span class="text-red-600">❌ Error</span>';
            }

            // Check Python (placeholder)
            document.getElementById('pythonStatus').innerHTML = '<span class="text-yellow-600">⏳ Checking...</span>';
            
            // Check Files (placeholder)
            document.getElementById('filesStatus').innerHTML = '<span class="text-yellow-600">⏳ Checking...</span>';
        }

        async function refreshLogs() {
            try {
                const response = await fetch('debug.log');
                const logs = await response.text();
                
                const logContainer = document.getElementById('debugLogs');
                const lines = logs.split('\n').slice(-50); // Show last 50 lines
                
                logContainer.innerHTML = lines.map(line => {
                    if (line.includes('ERROR') || line.includes('Failed')) {
                        return `<div class="text-red-400">${escapeHtml(line)}</div>`;
                    } else if (line.includes('SUCCESS') || line.includes('berhasil')) {
                        return `<div class="text-green-400">${escapeHtml(line)}</div>`;
                    } else if (line.includes('WARNING') || line.includes('warning')) {
                        return `<div class="text-yellow-400">${escapeHtml(line)}</div>`;
                    } else {
                        return `<div class="text-gray-300">${escapeHtml(line)}</div>`;
                    }
                }).join('');
                
                // Auto scroll to bottom
                logContainer.scrollTop = logContainer.scrollHeight;
                
            } catch (error) {
                document.getElementById('debugLogs').innerHTML = '<div class="text-red-400">Error loading logs: ' + error.message + '</div>';
            }
        }

        async function loadRecentActivity() {
            try {
                const response = await fetch('get_face_history.php');
                const data = await response.json();
                
                const activityContainer = document.getElementById('recentActivity');
                
                if (data.success && data.data.length > 0) {
                    activityContainer.innerHTML = data.data.slice(0, 5).map(item => `
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <span class="font-semibold">${item.face_shape.toUpperCase()}</span>
                                <span class="text-gray-600 ml-2">${item.confidence || 'N/A'}% confidence</span>
                            </div>
                            <div class="text-sm text-gray-500">
                                ${new Date(item.created_at).toLocaleString('id-ID')}
                            </div>
                        </div>
                    `).join('');
                } else {
                    activityContainer.innerHTML = '<div class="text-gray-500 text-center">No recent activity</div>';
                }
            } catch (error) {
                document.getElementById('recentActivity').innerHTML = '<div class="text-red-500">Error loading activity</div>';
            }
        }

        function clearLogs() {
            if (confirm('Are you sure you want to clear all logs?')) {
                fetch('clear_logs.php', { method: 'POST' })
                    .then(() => refreshLogs())
                    .catch(error => console.error('Error clearing logs:', error));
            }
        }

        function testDatabase() {
            showTestResult('Testing database connection...', 'info');
            fetch('test_face_system.php')
                .then(response => response.text())
                .then(data => {
                    if (data.includes('Database connection: SUCCESS')) {
                        showTestResult('✅ Database test passed', 'success');
                    } else {
                        showTestResult('❌ Database test failed', 'error');
                    }
                })
                .catch(error => showTestResult('❌ Database test error: ' + error.message, 'error'));
        }

        function testPython() {
            showTestResult('Testing Python environment...', 'info');
            fetch('analyze_face_sim.php', {
                method: 'POST',
                body: new FormData() // Empty form data to trigger Python check
            })
                .then(response => response.text())
                .then(data => {
                    if (data.includes('Python tidak terinstal')) {
                        showTestResult('❌ Python not installed', 'error');
                    } else {
                        showTestResult('✅ Python environment OK', 'success');
                    }
                })
                .catch(error => showTestResult('❌ Python test error: ' + error.message, 'error'));
        }

        function testUpload() {
            showTestResult('Testing upload functionality...', 'info');
            // Create a small test image
            const canvas = document.createElement('canvas');
            canvas.width = 100;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(0, 0, 100, 100);
            
            canvas.toBlob(blob => {
                const formData = new FormData();
                formData.append('image', blob, 'test.jpg');
                
                fetch('analyze_face_sim.php', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => response.text())
                    .then(data => {
                        if (data.includes('success')) {
                            showTestResult('✅ Upload test passed', 'success');
                        } else {
                            showTestResult('❌ Upload test failed: ' + data.substring(0, 100), 'error');
                        }
                    })
                    .catch(error => showTestResult('❌ Upload test error: ' + error.message, 'error'));
            }, 'image/jpeg', 0.8);
        }

        function testSessionStorage() {
            try {
                const testData = { test: 'data', timestamp: Date.now() };
                sessionStorage.setItem('debugTest', JSON.stringify(testData));
                const retrieved = JSON.parse(sessionStorage.getItem('debugTest'));
                
                if (retrieved.test === 'data') {
                    showTestResult('✅ SessionStorage test passed', 'success');
                } else {
                    showTestResult('❌ SessionStorage test failed', 'error');
                }
                
                sessionStorage.removeItem('debugTest');
            } catch (error) {
                showTestResult('❌ SessionStorage test error: ' + error.message, 'error');
            }
        }

        function showTestResult(message, type) {
            const resultContainer = document.getElementById('testResults');
            const colorClass = type === 'success' ? 'text-green-600' : 
                              type === 'error' ? 'text-red-600' : 'text-blue-600';
            
            resultContainer.innerHTML = `<div class="${colorClass} font-mono">${message}</div>`;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>
