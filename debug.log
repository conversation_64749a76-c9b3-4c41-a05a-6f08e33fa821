[2025-07-10 07:04:03] Logs cleared via clear_logs.php
[2025-07-10 07:05:02] db_config.php berhasil di-include.
[2025-07-10 07:05:03] Path gambar yang dikirim ke Python: uploads/face_detection/686f49ff38cd0.jpg
[2025-07-10 07:05:03] Per<PERSON><PERSON> yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f49ff38cd0.jpg" 2>&1
[2025-07-10 07:05:08] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:05:08] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:05:08] Creating mock result for testing purposes
[2025-07-10 07:05:08] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:05:08] Mencoba koneksi ke database...
[2025-07-10 07:05:08] Database schema updated successfully
[2025-07-10 07:05:08] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:05:08] Parameter: Array
(
    [0] => uploads/face_detection/686f49ff38cd0.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:05:08] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:05:08] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:05:08] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:05:08] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f49ff38cd0.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:05:08] Data berhasil disimpan ke database
[2025-07-10 07:05:30] db_config.php berhasil di-include.
[2025-07-10 07:05:31] Path gambar yang dikirim ke Python: uploads/face_detection/686f4a1b19087.jpg
[2025-07-10 07:05:31] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f4a1b19087.jpg" 2>&1
[2025-07-10 07:05:35] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:05:35] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:05:35] Creating mock result for testing purposes
[2025-07-10 07:05:35] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:05:35] Mencoba koneksi ke database...
[2025-07-10 07:05:35] Database schema updated successfully
[2025-07-10 07:05:35] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:05:35] Parameter: Array
(
    [0] => uploads/face_detection/686f4a1b19087.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:05:35] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:05:35] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:05:35] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:05:35] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f4a1b19087.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:05:35] Data berhasil disimpan ke database
[2025-07-10 07:06:27] db_config.php berhasil di-include.
[2025-07-10 07:06:27] Path gambar yang dikirim ke Python: uploads/face_detection/686f4a53c0210.jpg
[2025-07-10 07:06:27] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f4a53c0210.jpg" 2>&1
[2025-07-10 07:06:31] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:06:31] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:06:31] Creating mock result for testing purposes
[2025-07-10 07:06:31] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:06:31] Mencoba koneksi ke database...
[2025-07-10 07:06:31] Database schema updated successfully
[2025-07-10 07:06:31] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:06:31] Parameter: Array
(
    [0] => uploads/face_detection/686f4a53c0210.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:06:31] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:06:31] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:06:31] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:06:31] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f4a53c0210.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:06:31] Data berhasil disimpan ke database
[2025-07-10 07:08:00] db_config.php berhasil di-include.
[2025-07-10 07:08:01] Path gambar yang dikirim ke Python: uploads/face_detection/686f4ab138a89.jpg
[2025-07-10 07:08:01] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f4ab138a89.jpg" 2>&1
[2025-07-10 07:08:05] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:08:05] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:08:05] Creating mock result for testing purposes
[2025-07-10 07:08:05] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:08:05] Mencoba koneksi ke database...
[2025-07-10 07:08:05] Database schema updated successfully
[2025-07-10 07:08:05] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:08:05] Parameter: Array
(
    [0] => uploads/face_detection/686f4ab138a89.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:08:05] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:08:05] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:08:05] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:08:05] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f4ab138a89.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:08:05] Data berhasil disimpan ke database
[2025-07-10 07:08:28] db_config.php berhasil di-include.
[2025-07-10 07:08:28] Path gambar yang dikirim ke Python: uploads/face_detection/686f4accc7a1a.jpg
[2025-07-10 07:08:28] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f4accc7a1a.jpg" 2>&1
[2025-07-10 07:08:32] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:08:32] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:08:32] Creating mock result for testing purposes
[2025-07-10 07:08:32] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:08:32] Mencoba koneksi ke database...
[2025-07-10 07:08:32] Database schema updated successfully
[2025-07-10 07:08:32] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:08:32] Parameter: Array
(
    [0] => uploads/face_detection/686f4accc7a1a.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:08:32] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:08:32] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:08:32] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:08:32] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f4accc7a1a.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:08:32] Data berhasil disimpan ke database
[2025-07-10 07:09:06] db_config.php berhasil di-include.
[2025-07-10 07:09:06] Path gambar yang dikirim ke Python: uploads/face_detection/686f4af2e70a3.jpg
[2025-07-10 07:09:06] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f4af2e70a3.jpg" 2>&1
[2025-07-10 07:09:10] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:09:10] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:09:10] Creating mock result for testing purposes
[2025-07-10 07:09:10] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:09:10] Mencoba koneksi ke database...
[2025-07-10 07:09:10] Database schema updated successfully
[2025-07-10 07:09:10] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:09:10] Parameter: Array
(
    [0] => uploads/face_detection/686f4af2e70a3.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:09:10] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:09:10] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:09:10] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:09:10] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f4af2e70a3.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:09:10] Data berhasil disimpan ke database
[2025-07-10 07:11:51] db_config.php berhasil di-include.
[2025-07-10 07:11:51] Path gambar yang dikirim ke Python: uploads/face_detection/686f4b9761e81.jpg
[2025-07-10 07:11:51] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f4b9761e81.jpg" 2>&1
[2025-07-10 07:11:55] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:11:55] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:11:55] Creating mock result for testing purposes
[2025-07-10 07:11:55] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:11:55] Mencoba koneksi ke database...
[2025-07-10 07:11:55] Database schema updated successfully
[2025-07-10 07:11:55] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:11:55] Parameter: Array
(
    [0] => uploads/face_detection/686f4b9761e81.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:11:55] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:11:55] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:11:55] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:11:55] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f4b9761e81.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:11:55] Data berhasil disimpan ke database
[2025-07-10 07:17:02] db_config.php berhasil di-include.
[2025-07-10 07:17:03] Path gambar yang dikirim ke Python: uploads/face_detection/686f4ccf2d6eb.jpg
[2025-07-10 07:17:03] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f4ccf2d6eb.jpg" 2>&1
[2025-07-10 07:17:06] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:17:06] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:17:06] Creating mock result for testing purposes
[2025-07-10 07:17:06] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:17:06] Mencoba koneksi ke database...
[2025-07-10 07:17:06] Database schema updated successfully
[2025-07-10 07:17:06] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:17:06] Parameter: Array
(
    [0] => uploads/face_detection/686f4ccf2d6eb.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:17:06] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:17:06] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:17:06] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:17:06] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f4ccf2d6eb.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:17:06] Data berhasil disimpan ke database
[2025-07-10 07:27:43] db_config.php berhasil di-include.
[2025-07-10 07:27:43] Path gambar yang dikirim ke Python: uploads/face_detection/686f4f4fabcfb.jpg
[2025-07-10 07:27:43] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f4f4fabcfb.jpg" 2>&1
[2025-07-10 07:27:47] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:27:47] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:27:47] Creating mock result for testing purposes
[2025-07-10 07:27:47] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:27:47] Mencoba koneksi ke database...
[2025-07-10 07:27:47] Database schema updated successfully
[2025-07-10 07:27:47] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:27:47] Parameter: Array
(
    [0] => uploads/face_detection/686f4f4fabcfb.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:27:47] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:27:47] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:27:47] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:27:47] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f4f4fabcfb.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:27:47] Data berhasil disimpan ke database
[2025-07-10 07:28:07] db_config.php berhasil di-include.
[2025-07-10 07:28:08] Path gambar yang dikirim ke Python: uploads/face_detection/686f4f6831730.jpg
[2025-07-10 07:28:08] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f4f6831730.jpg" 2>&1
[2025-07-10 07:28:11] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:28:11] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:28:11] Creating mock result for testing purposes
[2025-07-10 07:28:11] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:28:11] Mencoba koneksi ke database...
[2025-07-10 07:28:11] Database schema updated successfully
[2025-07-10 07:28:11] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:28:11] Parameter: Array
(
    [0] => uploads/face_detection/686f4f6831730.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:28:11] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:28:11] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:28:11] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:28:11] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f4f6831730.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:28:11] Data berhasil disimpan ke database
[2025-07-10 07:28:27] db_config.php berhasil di-include.
[2025-07-10 07:28:27] Path gambar yang dikirim ke Python: uploads/face_detection/686f4f7bc524e.jpg
[2025-07-10 07:28:27] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f4f7bc524e.jpg" 2>&1
[2025-07-10 07:28:31] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:28:31] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:28:31] Creating mock result for testing purposes
[2025-07-10 07:28:31] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:28:31] Mencoba koneksi ke database...
[2025-07-10 07:28:31] Database schema updated successfully
[2025-07-10 07:28:31] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:28:31] Parameter: Array
(
    [0] => uploads/face_detection/686f4f7bc524e.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:28:31] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:28:31] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:28:31] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:28:31] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f4f7bc524e.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:28:31] Data berhasil disimpan ke database
[2025-07-10 07:49:34] db_config.php berhasil di-include.
[2025-07-10 07:49:34] Path gambar yang dikirim ke Python: uploads/face_detection/686f546ee4520.jpg
[2025-07-10 07:49:34] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f546ee4520.jpg" 2>&1
[2025-07-10 07:49:38] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:49:38] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:49:38] Creating mock result for testing purposes
[2025-07-10 07:49:38] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:49:38] Mencoba koneksi ke database...
[2025-07-10 07:49:38] Database schema updated successfully
[2025-07-10 07:49:38] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:49:38] Parameter: Array
(
    [0] => uploads/face_detection/686f546ee4520.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:49:38] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:49:38] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:49:38] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:49:38] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f546ee4520.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:49:38] Data berhasil disimpan ke database
[2025-07-10 07:49:56] db_config.php berhasil di-include.
[2025-07-10 07:49:56] Path gambar yang dikirim ke Python: uploads/face_detection/686f5484d97b4.jpg
[2025-07-10 07:49:56] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f5484d97b4.jpg" 2>&1
[2025-07-10 07:50:00] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:50:00] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:50:00] Creating mock result for testing purposes
[2025-07-10 07:50:00] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:50:00] Mencoba koneksi ke database...
[2025-07-10 07:50:00] Database schema updated successfully
[2025-07-10 07:50:00] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:50:00] Parameter: Array
(
    [0] => uploads/face_detection/686f5484d97b4.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:50:00] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:50:00] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:50:00] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:50:00] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f5484d97b4.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:50:00] Data berhasil disimpan ke database
[2025-07-10 07:51:14] db_config.php berhasil di-include.
[2025-07-10 07:51:15] Path gambar yang dikirim ke Python: uploads/face_detection/686f54d3389e5.jpg
[2025-07-10 07:51:15] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f54d3389e5.jpg" 2>&1
[2025-07-10 07:51:19] Output mentah dari python: {"error": "Tidak ada wajah yang terdeteksi. Pastikan wajah terlihat jelas dan pencahayaan cukup."}
[2025-07-10 07:51:19] Python script error: Tidak ada wajah yang terdeteksi. Pastikan wajah terlihat jelas dan pencahayaan cukup.
[2025-07-10 07:54:46] db_config.php berhasil di-include.
[2025-07-10 07:54:46] Path gambar yang dikirim ke Python: uploads/face_detection/686f55a69fe66.jpg
[2025-07-10 07:54:46] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f55a69fe66.jpg" 2>&1
[2025-07-10 07:54:50] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:54:50] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:54:50] Creating mock result for testing purposes
[2025-07-10 07:54:50] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:54:50] Mencoba koneksi ke database...
[2025-07-10 07:54:50] Database schema updated successfully
[2025-07-10 07:54:50] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:54:50] Parameter: Array
(
    [0] => uploads/face_detection/686f55a69fe66.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:54:50] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:54:50] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:54:50] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:54:50] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f55a69fe66.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:54:50] Data berhasil disimpan ke database
[2025-07-10 07:55:19] db_config.php berhasil di-include.
[2025-07-10 07:55:19] Path gambar yang dikirim ke Python: uploads/face_detection/686f55c7d17ef.jpg
[2025-07-10 07:55:19] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f55c7d17ef.jpg" 2>&1
[2025-07-10 07:55:23] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:55:23] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:55:23] Creating mock result for testing purposes
[2025-07-10 07:55:23] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:55:23] Mencoba koneksi ke database...
[2025-07-10 07:55:23] Database schema updated successfully
[2025-07-10 07:55:23] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:55:23] Parameter: Array
(
    [0] => uploads/face_detection/686f55c7d17ef.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:55:23] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:55:23] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:55:23] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:55:23] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f55c7d17ef.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:55:23] Data berhasil disimpan ke database
[2025-07-10 07:55:41] db_config.php berhasil di-include.
[2025-07-10 07:55:41] Path gambar yang dikirim ke Python: uploads/face_detection/686f55ddd3327.jpg
[2025-07-10 07:55:41] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f55ddd3327.jpg" 2>&1
[2025-07-10 07:55:45] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:55:45] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 07:55:45] Creating mock result for testing purposes
[2025-07-10 07:55:45] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 07:55:45] Mencoba koneksi ke database...
[2025-07-10 07:55:45] Database schema updated successfully
[2025-07-10 07:55:45] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 07:55:45] Parameter: Array
(
    [0] => uploads/face_detection/686f55ddd3327.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:55:45] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 07:55:45] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 07:55:45] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 07:55:45] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f55ddd3327.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 07:55:45] Data berhasil disimpan ke database
[2025-07-10 08:04:05] db_config.php berhasil di-include.
[2025-07-10 08:04:05] Path gambar yang dikirim ke Python: uploads/face_detection/686f57d5b95bc.jpg
[2025-07-10 08:04:05] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f57d5b95bc.jpg" 2>&1
[2025-07-10 08:04:10] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 08:04:10] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 08:04:10] Creating mock result for testing purposes
[2025-07-10 08:04:10] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 08:04:10] Mencoba koneksi ke database...
[2025-07-10 08:04:10] Database schema updated successfully
[2025-07-10 08:04:10] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 08:04:10] Parameter: Array
(
    [0] => uploads/face_detection/686f57d5b95bc.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 08:04:10] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 08:04:10] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 08:04:10] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 08:04:10] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f57d5b95bc.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 08:04:10] Data berhasil disimpan ke database
[2025-07-10 09:13:37] db_config.php berhasil di-include.
[2025-07-10 09:13:37] Path gambar yang dikirim ke Python: uploads/face_detection/686f6821f3bbb.jpg
[2025-07-10 09:13:37] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686f6821f3bbb.jpg" 2>&1
[2025-07-10 09:13:41] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 09:13:41] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 09:13:41] Creating mock result for testing purposes
[2025-07-10 09:13:41] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 09:13:41] Mencoba koneksi ke database...
[2025-07-10 09:13:41] Database schema updated successfully
[2025-07-10 09:13:41] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 09:13:41] Parameter: Array
(
    [0] => uploads/face_detection/686f6821f3bbb.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 09:13:41] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 09:13:41] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 09:13:41] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 09:13:41] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686f6821f3bbb.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 09:13:41] Data berhasil disimpan ke database
[2025-07-10 15:51:29] db_config.php berhasil di-include.
[2025-07-10 15:51:30] Path gambar yang dikirim ke Python: uploads/face_detection/686fc56225612.jpg
[2025-07-10 15:51:30] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fc56225612.jpg" 2>&1
[2025-07-10 15:51:36] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 15:51:36] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 15:51:36] Creating mock result for testing purposes
[2025-07-10 15:51:36] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 15:51:36] Mencoba koneksi ke database...
[2025-07-10 15:51:36] Database schema updated successfully
[2025-07-10 15:51:36] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 15:51:36] Parameter: Array
(
    [0] => uploads/face_detection/686fc56225612.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 15:51:36] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 15:51:36] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 15:51:36] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 15:51:36] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686fc56225612.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 15:51:36] Data berhasil disimpan ke database
[2025-07-10 15:52:22] db_config.php berhasil di-include.
[2025-07-10 15:52:22] Path gambar yang dikirim ke Python: uploads/face_detection/686fc59647189.jpg
[2025-07-10 15:52:22] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fc59647189.jpg" 2>&1
[2025-07-10 15:52:24] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 15:52:24] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 15:52:24] Creating mock result for testing purposes
[2025-07-10 15:52:24] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 15:52:24] Mencoba koneksi ke database...
[2025-07-10 15:52:24] Database schema updated successfully
[2025-07-10 15:52:24] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 15:52:24] Parameter: Array
(
    [0] => uploads/face_detection/686fc59647189.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 15:52:24] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 15:52:24] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 15:52:24] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 15:52:24] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686fc59647189.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 15:52:24] Data berhasil disimpan ke database
[2025-07-10 16:04:14] db_config.php berhasil di-include.
[2025-07-10 16:04:14] Path gambar yang dikirim ke Python: uploads/face_detection/686fc85eacaf8.jpg
[2025-07-10 16:04:14] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fc85eacaf8.jpg" 2>&1
[2025-07-10 16:04:17] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 16:04:17] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 16:04:17] Creating mock result for testing purposes
[2025-07-10 16:04:17] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 16:04:17] Mencoba koneksi ke database...
[2025-07-10 16:04:17] Database schema updated successfully
[2025-07-10 16:04:17] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 16:04:17] Parameter: Array
(
    [0] => uploads/face_detection/686fc85eacaf8.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 16:04:17] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 16:04:17] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 16:04:17] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 16:04:17] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686fc85eacaf8.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 16:04:17] Data berhasil disimpan ke database
[2025-07-10 17:00:33] db_config.php berhasil di-include.
[2025-07-10 17:00:33] Path gambar yang dikirim ke Python: uploads/face_detection/686fd5914b4b6.jpg
[2025-07-10 17:00:33] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fd5914b4b6.jpg" 2>&1
[2025-07-10 17:00:36] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:00:36] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:00:36] Creating mock result for testing purposes
[2025-07-10 17:00:36] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 17:00:36] Mencoba koneksi ke database...
[2025-07-10 17:00:36] Database schema updated successfully
[2025-07-10 17:00:36] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 17:00:36] Parameter: Array
(
    [0] => uploads/face_detection/686fd5914b4b6.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:00:36] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 17:00:36] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 17:00:36] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 17:00:36] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686fd5914b4b6.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:00:36] Data berhasil disimpan ke database
[2025-07-10 17:00:54] db_config.php berhasil di-include.
[2025-07-10 17:00:55] Path gambar yang dikirim ke Python: uploads/face_detection/686fd5a71a14d.jpg
[2025-07-10 17:00:55] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fd5a71a14d.jpg" 2>&1
[2025-07-10 17:00:58] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:00:58] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:00:58] Creating mock result for testing purposes
[2025-07-10 17:00:58] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 17:00:58] Mencoba koneksi ke database...
[2025-07-10 17:00:58] Database schema updated successfully
[2025-07-10 17:00:58] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 17:00:58] Parameter: Array
(
    [0] => uploads/face_detection/686fd5a71a14d.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:00:58] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 17:00:58] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 17:00:58] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 17:00:58] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686fd5a71a14d.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:00:58] Data berhasil disimpan ke database
[2025-07-10 17:06:12] db_config.php berhasil di-include.
[2025-07-10 17:06:13] Path gambar yang dikirim ke Python: uploads/face_detection/686fd6e523e31.jpg
[2025-07-10 17:06:13] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fd6e523e31.jpg" 2>&1
[2025-07-10 17:06:16] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:06:16] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:06:16] Creating mock result for testing purposes
[2025-07-10 17:06:16] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 17:06:16] Mencoba koneksi ke database...
[2025-07-10 17:06:16] Database schema updated successfully
[2025-07-10 17:06:16] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 17:06:16] Parameter: Array
(
    [0] => uploads/face_detection/686fd6e523e31.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:06:16] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 17:06:16] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 17:06:16] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 17:06:16] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686fd6e523e31.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:06:16] Data berhasil disimpan ke database
[2025-07-10 17:17:27] db_config.php berhasil di-include.
[2025-07-10 17:17:27] Path gambar yang dikirim ke Python: uploads/face_detection/686fd987da348.jpg
[2025-07-10 17:17:27] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fd987da348.jpg" 2>&1
[2025-07-10 17:17:32] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:17:32] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:17:32] Creating mock result for testing purposes
[2025-07-10 17:17:32] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 17:17:32] Mencoba koneksi ke database...
[2025-07-10 17:17:32] Database schema updated successfully
[2025-07-10 17:17:32] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 17:17:32] Parameter: Array
(
    [0] => uploads/face_detection/686fd987da348.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:17:32] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 17:17:32] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 17:17:32] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 17:17:32] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686fd987da348.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:17:32] Data berhasil disimpan ke database
[2025-07-10 17:31:37] db_config.php berhasil di-include.
[2025-07-10 17:31:37] Path gambar yang dikirim ke Python: uploads/face_detection/686fdcd9ca43b.jpg
[2025-07-10 17:31:37] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fdcd9ca43b.jpg" 2>&1
[2025-07-10 17:31:41] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:31:41] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:31:41] Creating mock result for testing purposes
[2025-07-10 17:31:41] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 17:31:41] Mencoba koneksi ke database...
[2025-07-10 17:31:41] Database schema updated successfully
[2025-07-10 17:31:41] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 17:31:41] Parameter: Array
(
    [0] => uploads/face_detection/686fdcd9ca43b.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:31:41] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 17:31:41] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 17:31:41] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 17:31:41] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686fdcd9ca43b.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:31:41] Data berhasil disimpan ke database
[2025-07-10 17:39:26] db_config.php berhasil di-include.
[2025-07-10 17:39:27] Path gambar yang dikirim ke Python: uploads/face_detection/686fdeaf0afd5.jpg
[2025-07-10 17:39:27] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fdeaf0afd5.jpg" 2>&1
[2025-07-10 17:39:33] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:39:33] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:39:33] Creating mock result for testing purposes
[2025-07-10 17:39:33] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 17:39:33] Mencoba koneksi ke database...
[2025-07-10 17:39:33] Database schema updated successfully
[2025-07-10 17:39:33] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 17:39:33] Parameter: Array
(
    [0] => uploads/face_detection/686fdeaf0afd5.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:39:33] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 17:39:33] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 17:39:33] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 17:39:33] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686fdeaf0afd5.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:39:33] Data berhasil disimpan ke database
[2025-07-10 17:39:47] db_config.php berhasil di-include.
[2025-07-10 17:39:47] Path gambar yang dikirim ke Python: uploads/face_detection/686fdec349a2e.jpg
[2025-07-10 17:39:47] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fdec349a2e.jpg" 2>&1
[2025-07-10 17:39:50] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:39:50] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:39:50] Creating mock result for testing purposes
[2025-07-10 17:39:50] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 17:39:50] Mencoba koneksi ke database...
[2025-07-10 17:39:50] Database schema updated successfully
[2025-07-10 17:39:50] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 17:39:50] Parameter: Array
(
    [0] => uploads/face_detection/686fdec349a2e.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:39:50] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 17:39:50] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 17:39:50] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 17:39:50] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686fdec349a2e.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:39:50] Data berhasil disimpan ke database
[2025-07-10 17:40:10] db_config.php berhasil di-include.
[2025-07-10 17:40:10] Path gambar yang dikirim ke Python: uploads/face_detection/686fdeda66a6e.jpg
[2025-07-10 17:40:10] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fdeda66a6e.jpg" 2>&1
[2025-07-10 17:40:13] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:40:13] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-10 17:40:13] Creating mock result for testing purposes
[2025-07-10 17:40:13] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-10 17:40:13] Mencoba koneksi ke database...
[2025-07-10 17:40:13] Database schema updated successfully
[2025-07-10 17:40:13] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-10 17:40:13] Parameter: Array
(
    [0] => uploads/face_detection/686fdeda66a6e.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:40:13] KONSISTENSI CHECK - Original shape: oval
[2025-07-10 17:40:13] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-10 17:40:13] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-10 17:40:13] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/686fdeda66a6e.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-10 17:40:13] Data berhasil disimpan ke database
[2025-07-10 17:41:07] db_config.php berhasil di-include.
[2025-07-10 17:41:07] Path gambar yang dikirim ke Python: uploads/face_detection/686fdf13e99b4.jpg
[2025-07-10 17:41:07] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fdf13e99b4.jpg" 2>&1
[2025-07-10 17:41:11] Output mentah dari python: {"error": "Tidak ada wajah yang terdeteksi. Pastikan wajah terlihat jelas dan pencahayaan cukup."}
[2025-07-10 17:41:11] Python script error: Tidak ada wajah yang terdeteksi. Pastikan wajah terlihat jelas dan pencahayaan cukup.
[2025-07-10 17:41:31] db_config.php berhasil di-include.
[2025-07-10 17:41:32] Path gambar yang dikirim ke Python: uploads/face_detection/686fdf2c1f5cc.jpg
[2025-07-10 17:41:32] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/686fdf2c1f5cc.jpg" 2>&1
[2025-07-10 17:41:37] Output mentah dari python: {"error": "Tidak ada wajah yang terdeteksi. Pastikan wajah terlihat jelas dan pencahayaan cukup."}
[2025-07-10 17:41:37] Python script error: Tidak ada wajah yang terdeteksi. Pastikan wajah terlihat jelas dan pencahayaan cukup.
[2025-07-11 08:54:56] db_config.php berhasil di-include.
[2025-07-11 08:54:57] Path gambar yang dikirim ke Python: uploads/face_detection/6870b54161651.jpg
[2025-07-11 08:54:57] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/6870b54161651.jpg" 2>&1
[2025-07-11 08:55:02] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-11 08:55:02] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-11 08:55:02] Creating mock result for testing purposes
[2025-07-11 08:55:02] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-11 08:55:02] Mencoba koneksi ke database...
[2025-07-11 08:55:02] Database schema updated successfully
[2025-07-11 08:55:02] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-11 08:55:02] Parameter: Array
(
    [0] => uploads/face_detection/6870b54161651.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-11 08:55:02] KONSISTENSI CHECK - Original shape: oval
[2025-07-11 08:55:02] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-11 08:55:02] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-11 08:55:02] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/6870b54161651.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-11 08:55:02] Data berhasil disimpan ke database
[2025-07-14 10:02:07] db_config.php berhasil di-include.
[2025-07-14 10:02:08] Path gambar yang dikirim ke Python: uploads/face_detection/6874b9805f7de.jpg
[2025-07-14 10:02:08] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/6874b9805f7de.jpg" 2>&1
[2025-07-14 10:02:16] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-14 10:02:16] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-14 10:02:16] Creating mock result for testing purposes
[2025-07-14 10:02:16] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-14 10:02:16] Mencoba koneksi ke database...
[2025-07-14 10:02:16] Database schema updated successfully
[2025-07-14 10:02:16] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-14 10:02:16] Parameter: Array
(
    [0] => uploads/face_detection/6874b9805f7de.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-14 10:02:16] KONSISTENSI CHECK - Original shape: oval
[2025-07-14 10:02:16] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-14 10:02:16] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-14 10:02:16] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/6874b9805f7de.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-14 10:02:16] Data berhasil disimpan ke database
[2025-07-14 10:02:37] db_config.php berhasil di-include.
[2025-07-14 10:02:38] Path gambar yang dikirim ke Python: uploads/face_detection/6874b99e1688c.jpg
[2025-07-14 10:02:38] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/6874b99e1688c.jpg" 2>&1
[2025-07-14 10:02:43] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-14 10:02:43] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-14 10:02:43] Creating mock result for testing purposes
[2025-07-14 10:02:43] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-14 10:02:43] Mencoba koneksi ke database...
[2025-07-14 10:02:43] Database schema updated successfully
[2025-07-14 10:02:43] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-14 10:02:43] Parameter: Array
(
    [0] => uploads/face_detection/6874b99e1688c.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-14 10:02:43] KONSISTENSI CHECK - Original shape: oval
[2025-07-14 10:02:43] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-14 10:02:43] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-14 10:02:43] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/6874b99e1688c.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-14 10:02:43] Data berhasil disimpan ke database
[2025-07-14 10:09:08] db_config.php berhasil di-include.
[2025-07-14 10:09:08] Path gambar yang dikirim ke Python: uploads/face_detection/6874bb2488171.jpg
[2025-07-14 10:09:08] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/6874bb2488171.jpg" 2>&1
[2025-07-14 10:09:14] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-14 10:09:14] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-14 10:09:14] Creating mock result for testing purposes
[2025-07-14 10:09:14] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-14 10:09:14] Mencoba koneksi ke database...
[2025-07-14 10:09:14] Database schema updated successfully
[2025-07-14 10:09:14] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-14 10:09:14] Parameter: Array
(
    [0] => uploads/face_detection/6874bb2488171.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-14 10:09:14] KONSISTENSI CHECK - Original shape: oval
[2025-07-14 10:09:14] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-14 10:09:14] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-14 10:09:14] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/6874bb2488171.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-14 10:09:14] Data berhasil disimpan ke database
[2025-07-14 10:11:55] db_config.php berhasil di-include.
[2025-07-14 10:11:55] Path gambar yang dikirim ke Python: uploads/face_detection/6874bbcbb9154.jpg
[2025-07-14 10:11:55] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/6874bbcbb9154.jpg" 2>&1
[2025-07-14 10:12:02] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-14 10:12:02] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-14 10:12:02] Creating mock result for testing purposes
[2025-07-14 10:12:02] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-14 10:12:02] Mencoba koneksi ke database...
[2025-07-14 10:12:02] Database schema updated successfully
[2025-07-14 10:12:02] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-14 10:12:02] Parameter: Array
(
    [0] => uploads/face_detection/6874bbcbb9154.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-14 10:12:02] KONSISTENSI CHECK - Original shape: oval
[2025-07-14 10:12:02] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-14 10:12:02] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-14 10:12:02] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/6874bbcbb9154.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-14 10:12:02] Data berhasil disimpan ke database
[2025-07-14 10:30:34] db_config.php berhasil di-include.
[2025-07-14 10:30:35] Path gambar yang dikirim ke Python: uploads/face_detection/6874c02b85c3c.jpg
[2025-07-14 10:30:35] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/6874c02b85c3c.jpg" 2>&1
[2025-07-14 10:30:42] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-14 10:30:42] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-14 10:30:42] Creating mock result for testing purposes
[2025-07-14 10:30:42] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-14 10:30:42] Mencoba koneksi ke database...
[2025-07-14 10:30:42] Database schema updated successfully
[2025-07-14 10:30:42] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-14 10:30:42] Parameter: Array
(
    [0] => uploads/face_detection/6874c02b85c3c.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-14 10:30:42] KONSISTENSI CHECK - Original shape: oval
[2025-07-14 10:30:42] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-14 10:30:42] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-14 10:30:42] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/6874c02b85c3c.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-14 10:30:42] Data berhasil disimpan ke database
[2025-07-14 10:31:10] db_config.php berhasil di-include.
[2025-07-14 10:31:10] Path gambar yang dikirim ke Python: uploads/face_detection/6874c04eb61c3.jpg
[2025-07-14 10:31:10] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/6874c04eb61c3.jpg" 2>&1
[2025-07-14 10:31:16] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-14 10:31:16] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-14 10:31:16] Creating mock result for testing purposes
[2025-07-14 10:31:16] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-14 10:31:16] Mencoba koneksi ke database...
[2025-07-14 10:31:16] Database schema updated successfully
[2025-07-14 10:31:16] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-14 10:31:16] Parameter: Array
(
    [0] => uploads/face_detection/6874c04eb61c3.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-14 10:31:16] KONSISTENSI CHECK - Original shape: oval
[2025-07-14 10:31:16] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-14 10:31:16] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-14 10:31:16] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/6874c04eb61c3.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-14 10:31:16] Data berhasil disimpan ke database
[2025-07-21 14:48:13] db_config.php berhasil di-include.
[2025-07-21 14:48:13] Path gambar yang dikirim ke Python: uploads/face_detection/687e370d7e722.jpg
[2025-07-21 14:48:13] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/687e370d7e722.jpg" 2>&1
[2025-07-21 14:48:17] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-21 14:48:17] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-21 14:48:17] Creating mock result for testing purposes
[2025-07-21 14:48:17] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-21 14:48:17] Mencoba koneksi ke database...
[2025-07-21 14:48:17] Database schema updated successfully
[2025-07-21 14:48:17] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-21 14:48:17] Parameter: Array
(
    [0] => uploads/face_detection/687e370d7e722.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-21 14:48:17] KONSISTENSI CHECK - Original shape: oval
[2025-07-21 14:48:17] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-21 14:48:17] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-21 14:48:17] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/687e370d7e722.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-21 14:48:17] Data berhasil disimpan ke database
[2025-07-21 14:48:33] db_config.php berhasil di-include.
[2025-07-21 14:48:33] Path gambar yang dikirim ke Python: uploads/face_detection/687e3721a29da.jpg
[2025-07-21 14:48:33] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/687e3721a29da.jpg" 2>&1
[2025-07-21 14:48:36] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-21 14:48:36] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-21 14:48:36] Creating mock result for testing purposes
[2025-07-21 14:48:36] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-21 14:48:36] Mencoba koneksi ke database...
[2025-07-21 14:48:36] Database schema updated successfully
[2025-07-21 14:48:36] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-21 14:48:36] Parameter: Array
(
    [0] => uploads/face_detection/687e3721a29da.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-21 14:48:36] KONSISTENSI CHECK - Original shape: oval
[2025-07-21 14:48:36] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-21 14:48:36] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-21 14:48:36] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/687e3721a29da.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-21 14:48:36] Data berhasil disimpan ke database
[2025-07-21 15:04:08] db_config.php berhasil di-include.
[2025-07-21 15:04:08] Path gambar yang dikirim ke Python: uploads/face_detection/687e3ac8d6730.jpg
[2025-07-21 15:04:08] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/687e3ac8d6730.jpg" 2>&1
[2025-07-21 15:04:12] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-21 15:04:12] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-21 15:04:12] Creating mock result for testing purposes
[2025-07-21 15:04:12] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-21 15:04:12] Mencoba koneksi ke database...
[2025-07-21 15:04:12] Database schema updated successfully
[2025-07-21 15:04:12] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-21 15:04:12] Parameter: Array
(
    [0] => uploads/face_detection/687e3ac8d6730.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-21 15:04:12] KONSISTENSI CHECK - Original shape: oval
[2025-07-21 15:04:12] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-21 15:04:12] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-21 15:04:12] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/687e3ac8d6730.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-21 15:04:12] Data berhasil disimpan ke database
[2025-07-22 04:59:25] db_config.php berhasil di-include.
[2025-07-22 04:59:25] Path gambar yang dikirim ke Python: uploads/face_detection/687efe8dc16ee.jpg
[2025-07-22 04:59:25] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/687efe8dc16ee.jpg" 2>&1
[2025-07-22 04:59:29] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-22 04:59:29] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-22 04:59:29] Creating mock result for testing purposes
[2025-07-22 04:59:29] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-22 04:59:29] Mencoba koneksi ke database...
[2025-07-22 04:59:29] Database schema updated successfully
[2025-07-22 04:59:29] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-22 04:59:29] Parameter: Array
(
    [0] => uploads/face_detection/687efe8dc16ee.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-22 04:59:29] KONSISTENSI CHECK - Original shape: oval
[2025-07-22 04:59:29] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-22 04:59:29] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-22 04:59:29] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/687efe8dc16ee.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-22 04:59:30] Data berhasil disimpan ke database
[2025-07-22 06:18:07] db_config.php berhasil di-include.
[2025-07-22 06:18:08] Path gambar yang dikirim ke Python: uploads/face_detection/687f11000084d.jpg
[2025-07-22 06:18:08] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/687f11000084d.jpg" 2>&1
[2025-07-22 06:18:10] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-22 06:18:10] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-22 06:18:10] Creating mock result for testing purposes
[2025-07-22 06:18:10] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-22 06:18:10] Mencoba koneksi ke database...
[2025-07-22 06:18:10] Database schema updated successfully
[2025-07-22 06:18:10] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-22 06:18:10] Parameter: Array
(
    [0] => uploads/face_detection/687f11000084d.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-22 06:18:10] KONSISTENSI CHECK - Original shape: oval
[2025-07-22 06:18:10] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-22 06:18:10] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-22 06:18:10] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/687f11000084d.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-22 06:18:10] Data berhasil disimpan ke database
[2025-07-22 06:18:40] db_config.php berhasil di-include.
[2025-07-22 06:18:40] Path gambar yang dikirim ke Python: uploads/face_detection/687f11208144f.jpg
[2025-07-22 06:18:40] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/687f11208144f.jpg" 2>&1
[2025-07-22 06:18:43] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-22 06:18:43] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-22 06:18:43] Creating mock result for testing purposes
[2025-07-22 06:18:43] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-22 06:18:43] Mencoba koneksi ke database...
[2025-07-22 06:18:43] Database schema updated successfully
[2025-07-22 06:18:43] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-22 06:18:43] Parameter: Array
(
    [0] => uploads/face_detection/687f11208144f.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-22 06:18:43] KONSISTENSI CHECK - Original shape: oval
[2025-07-22 06:18:43] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-22 06:18:43] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-22 06:18:43] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/687f11208144f.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-22 06:18:43] Data berhasil disimpan ke database
[2025-07-22 06:19:42] db_config.php berhasil di-include.
[2025-07-22 06:19:42] Path gambar yang dikirim ke Python: uploads/face_detection/687f115eaa08d.jpg
[2025-07-22 06:19:42] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/687f115eaa08d.jpg" 2>&1
[2025-07-22 06:19:46] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-22 06:19:46] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-22 06:19:46] Creating mock result for testing purposes
[2025-07-22 06:19:46] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-22 06:19:46] Mencoba koneksi ke database...
[2025-07-22 06:19:46] Database schema updated successfully
[2025-07-22 06:19:46] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-22 06:19:46] Parameter: Array
(
    [0] => uploads/face_detection/687f115eaa08d.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-22 06:19:46] KONSISTENSI CHECK - Original shape: oval
[2025-07-22 06:19:46] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-22 06:19:46] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-22 06:19:46] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/687f115eaa08d.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-22 06:19:46] Data berhasil disimpan ke database
[2025-07-22 06:20:13] db_config.php berhasil di-include.
[2025-07-22 06:20:13] Path gambar yang dikirim ke Python: uploads/face_detection/687f117dc2dea.jpg
[2025-07-22 06:20:13] Perintah yang akan dijalankan: "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "C:\xampp\htdocs\aplikasi-pixel/face_analysis.py" "uploads/face_detection/687f117dc2dea.jpg" 2>&1
[2025-07-22 06:20:17] Output mentah dari python: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-22 06:20:17] Failed to decode JSON output: Traceback (most recent call last):
  File "C:\xampp\htdocs\aplikasi-pixel\face_analysis.py", line 527, in <module>
    print(json.dumps(result))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type int32 is not JSON serializable
[2025-07-22 06:20:17] Creating mock result for testing purposes
[2025-07-22 06:20:17] Mock result created: {"shape":"oval","confidence":85,"alt_shape":"round","alt_confidence":15,"description":"Mock analysis result for testing","shape_description":"Bentuk wajah oval dengan proporsi seimbang","recommendations":["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"],"hairline_analysis":"Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.","hair_type_analysis":"Semua jenis rambut cocok (lurus, gelombang, keriting).","tips":"Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.","ratios":{"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}}
[2025-07-22 06:20:17] Mencoba koneksi ke database...
[2025-07-22 06:20:17] Database schema updated successfully
[2025-07-22 06:20:17] Query yang akan dijalankan: INSERT INTO face_analysis (
            image_path, face_shape, description, recommendation,
            confidence, alt_shape, alt_confidence, shape_description,
            hairline_analysis, hair_type_analysis, tips, ratios
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[2025-07-22 06:20:17] Parameter: Array
(
    [0] => uploads/face_detection/687f117dc2dea.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-22 06:20:17] KONSISTENSI CHECK - Original shape: oval
[2025-07-22 06:20:17] KONSISTENSI CHECK - Normalized shape: oval
[2025-07-22 06:20:17] KONSISTENSI CHECK - Normalized alt_shape: round
[2025-07-22 06:20:17] KONSISTENSI CHECK - Final params for database: Array
(
    [0] => uploads/face_detection/687f117dc2dea.jpg
    [1] => oval
    [2] => Mock analysis result for testing
    [3] => ["Textured Crop - Gaya modern dengan tekstur alami","Pompadour - Klasik dengan volume di atas","Man Bun - Trendy untuk rambut panjang","Classic Undercut - Bersih dan profesional"]
    [4] => 85
    [5] => round
    [6] => 15
    [7] => Bentuk wajah oval dengan proporsi seimbang
    [8] => Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.
    [9] => Semua jenis rambut cocok (lurus, gelombang, keriting).
    [10] => Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.
    [11] => {"width_height_ratio":0.8,"jaw_forehead_ratio":0.95,"chin_ratio":0.3}
)

[2025-07-22 06:20:17] Data berhasil disimpan ke database
