<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Konsistensi Data - Face Detection</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">Test Konsistensi Data Face Detection</h1>
                <p class="text-gray-600">Memastikan data yang terdeteksi di face.php sama dengan yang ditampilkan di output.php</p>
            </div>

            <!-- Test Controls -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Test Controls</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="testSessionStorageConsistency()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-database mr-2"></i>Test SessionStorage
                    </button>
                    <button onclick="testDatabaseConsistency()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-server mr-2"></i>Test Database
                    </button>
                    <button onclick="testFullFlow()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-sync-alt mr-2"></i>Test Full Flow
                    </button>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Test Results</h2>
                <div id="testResults" class="space-y-4">
                    <p class="text-gray-500">Klik tombol test untuk memulai pengujian konsistensi...</p>
                </div>
            </div>

            <!-- Mock Data Generator -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Mock Data Generator</h2>
                <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                    <button onclick="generateMockData('oval')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                        Generate OVAL
                    </button>
                    <button onclick="generateMockData('round')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                        Generate ROUND
                    </button>
                    <button onclick="generateMockData('square')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                        Generate SQUARE
                    </button>
                    <button onclick="generateMockData('heart')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                        Generate HEART
                    </button>
                    <button onclick="generateMockData('rectangular')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                        Generate RECTANGULAR
                    </button>
                </div>
                <div class="flex gap-4">
                    <a href="output.php" target="_blank" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-external-link-alt mr-2"></i>Test di Output.php
                    </a>
                    <button onclick="clearMockData()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-trash mr-2"></i>Clear Data
                    </button>
                </div>
            </div>

            <!-- Current Data Display -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Current SessionStorage Data</h2>
                <div id="currentData" class="bg-gray-100 p-4 rounded-lg font-mono text-sm">
                    <p class="text-gray-500">No data in sessionStorage</p>
                </div>
                <button onclick="refreshCurrentData()" class="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                    <i class="fas fa-sync mr-2"></i>Refresh
                </button>
            </div>
        </div>
    </div>

    <script>
        // Test functions
        function testSessionStorageConsistency() {
            addTestResult('🔍 Testing SessionStorage Consistency...', 'info');
            
            try {
                // Generate test data
                const testShapes = ['oval', 'round', 'square', 'heart', 'rectangular'];
                let allTestsPassed = true;
                
                testShapes.forEach(shape => {
                    // Create test data
                    const testData = {
                        shape: shape,
                        confidence: 85,
                        alt_shape: 'oval',
                        alt_confidence: 15,
                        timestamp: new Date().toISOString()
                    };
                    
                    // Save to sessionStorage
                    sessionStorage.setItem('testFaceAnalysis', JSON.stringify(testData));
                    
                    // Retrieve and verify
                    const retrieved = JSON.parse(sessionStorage.getItem('testFaceAnalysis'));
                    
                    if (retrieved.shape !== shape) {
                        addTestResult(`❌ FAIL: ${shape} - Expected: ${shape}, Got: ${retrieved.shape}`, 'error');
                        allTestsPassed = false;
                    } else {
                        addTestResult(`✅ PASS: ${shape} - Consistent`, 'success');
                    }
                });
                
                if (allTestsPassed) {
                    addTestResult('🎉 All SessionStorage consistency tests passed!', 'success');
                } else {
                    addTestResult('⚠️ Some SessionStorage consistency tests failed!', 'warning');
                }
                
                // Cleanup
                sessionStorage.removeItem('testFaceAnalysis');
                
            } catch (error) {
                addTestResult(`❌ SessionStorage test error: ${error.message}`, 'error');
            }
        }
        
        function testDatabaseConsistency() {
            addTestResult('🔍 Testing Database Consistency...', 'info');
            
            fetch('get_face_history.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data.length > 0) {
                        let consistencyIssues = 0;
                        
                        data.data.forEach((record, index) => {
                            const shape = record.face_shape;
                            const normalizedShape = shape.toLowerCase();
                            
                            if (shape !== normalizedShape && shape !== normalizedShape.toUpperCase()) {
                                addTestResult(`⚠️ Inconsistent case in record ${record.id}: ${shape}`, 'warning');
                                consistencyIssues++;
                            } else {
                                addTestResult(`✅ Record ${record.id}: ${shape} - Consistent`, 'success');
                            }
                        });
                        
                        if (consistencyIssues === 0) {
                            addTestResult('🎉 All database records are consistent!', 'success');
                        } else {
                            addTestResult(`⚠️ Found ${consistencyIssues} consistency issues in database`, 'warning');
                        }
                    } else {
                        addTestResult('ℹ️ No database records to test', 'info');
                    }
                })
                .catch(error => {
                    addTestResult(`❌ Database test error: ${error.message}`, 'error');
                });
        }
        
        function testFullFlow() {
            addTestResult('🔍 Testing Full Flow Consistency...', 'info');
            
            // Test each shape
            const testShapes = ['oval', 'round', 'square', 'heart', 'rectangular'];
            let currentIndex = 0;
            
            function testNextShape() {
                if (currentIndex >= testShapes.length) {
                    addTestResult('🎉 Full flow consistency test completed!', 'success');
                    return;
                }
                
                const shape = testShapes[currentIndex];
                addTestResult(`Testing ${shape.toUpperCase()} flow...`, 'info');
                
                // Generate mock data
                generateMockData(shape);
                
                // Wait a bit then check output
                setTimeout(() => {
                    // Simulate checking output.php
                    const storedData = sessionStorage.getItem('faceAnalysisResult');
                    if (storedData) {
                        const parsed = JSON.parse(storedData);
                        if (parsed.shape === shape) {
                            addTestResult(`✅ ${shape.toUpperCase()} flow: Consistent`, 'success');
                        } else {
                            addTestResult(`❌ ${shape.toUpperCase()} flow: Expected ${shape}, got ${parsed.shape}`, 'error');
                        }
                    }
                    
                    currentIndex++;
                    testNextShape();
                }, 500);
            }
            
            testNextShape();
        }
        
        function generateMockData(shape) {
            const mockData = {
                shape: shape.toLowerCase(),
                confidence: Math.floor(Math.random() * 20) + 80, // 80-99
                alt_shape: getRandomAltShape(shape),
                alt_confidence: Math.floor(Math.random() * 20) + 5, // 5-24
                description: `Mock analysis result for ${shape} face shape`,
                shape_description: `Bentuk wajah ${shape} dengan proporsi yang seimbang`,
                recommendations: getRecommendationsForShape(shape),
                hairline_analysis: `Hairline analysis for ${shape} face shape`,
                hair_type_analysis: `Hair type analysis for ${shape} face shape`,
                tips: `Tips for ${shape} face shape`,
                ratios: {
                    width_height_ratio: Math.random() * 0.3 + 0.7, // 0.7-1.0
                    jaw_forehead_ratio: Math.random() * 0.4 + 0.8, // 0.8-1.2
                    chin_ratio: Math.random() * 0.2 + 0.2 // 0.2-0.4
                },
                timestamp: new Date().toISOString(),
                source: 'mock_generator'
            };
            
            // Create mock image data
            const canvas = document.createElement('canvas');
            canvas.width = 200;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, 200, 200);
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`Mock ${shape.toUpperCase()}`, 100, 100);
            
            const mockImage = canvas.toDataURL('image/jpeg', 0.8);
            
            // Save to sessionStorage
            sessionStorage.setItem('faceAnalysisResult', JSON.stringify(mockData));
            sessionStorage.setItem('capturedImage', mockImage);
            
            addTestResult(`✅ Generated mock data for ${shape.toUpperCase()}`, 'success');
            refreshCurrentData();
        }
        
        function getRandomAltShape(primaryShape) {
            const shapes = ['oval', 'round', 'square', 'heart', 'rectangular'];
            const filtered = shapes.filter(s => s !== primaryShape);
            return filtered[Math.floor(Math.random() * filtered.length)];
        }
        
        function getRecommendationsForShape(shape) {
            const recommendations = {
                'oval': ['Textured Crop', 'Pompadour', 'Man Bun'],
                'round': ['Two Block Hair', 'Taper Fade', 'Fringe Haircut'],
                'square': ['Slick Back', 'Long Layers', 'French Crop'],
                'heart': ['Undercut', 'Textured Crop', 'Quiff'],
                'rectangular': ['Slick Back Hairstyles', 'Short Sides Long Top', 'Quiff']
            };
            return recommendations[shape] || recommendations['oval'];
        }
        
        function clearMockData() {
            sessionStorage.removeItem('faceAnalysisResult');
            sessionStorage.removeItem('capturedImage');
            addTestResult('🗑️ Mock data cleared', 'info');
            refreshCurrentData();
        }
        
        function refreshCurrentData() {
            const currentDataDiv = document.getElementById('currentData');
            const storedData = sessionStorage.getItem('faceAnalysisResult');
            
            if (storedData) {
                try {
                    const parsed = JSON.parse(storedData);
                    currentDataDiv.innerHTML = `
                        <div class="space-y-2">
                            <div><strong>Shape:</strong> ${parsed.shape}</div>
                            <div><strong>Confidence:</strong> ${parsed.confidence}%</div>
                            <div><strong>Alt Shape:</strong> ${parsed.alt_shape || 'None'}</div>
                            <div><strong>Source:</strong> ${parsed.source || 'Unknown'}</div>
                            <div><strong>Timestamp:</strong> ${parsed.timestamp || 'Unknown'}</div>
                        </div>
                    `;
                } catch (e) {
                    currentDataDiv.innerHTML = '<p class="text-red-500">Error parsing stored data</p>';
                }
            } else {
                currentDataDiv.innerHTML = '<p class="text-gray-500">No data in sessionStorage</p>';
            }
        }
        
        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultItem = document.createElement('div');
            
            let colorClass = 'text-gray-700';
            let bgClass = 'bg-gray-100';
            
            switch (type) {
                case 'success':
                    colorClass = 'text-green-700';
                    bgClass = 'bg-green-100';
                    break;
                case 'error':
                    colorClass = 'text-red-700';
                    bgClass = 'bg-red-100';
                    break;
                case 'warning':
                    colorClass = 'text-yellow-700';
                    bgClass = 'bg-yellow-100';
                    break;
                case 'info':
                    colorClass = 'text-blue-700';
                    bgClass = 'bg-blue-100';
                    break;
            }
            
            resultItem.className = `p-3 rounded-lg ${bgClass} ${colorClass} font-mono text-sm`;
            resultItem.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            // Clear "no results" message if it exists
            if (resultsDiv.children.length === 1 && resultsDiv.children[0].textContent.includes('Klik tombol test')) {
                resultsDiv.innerHTML = '';
            }
            
            resultsDiv.appendChild(resultItem);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            refreshCurrentData();
        });
    </script>
</body>
</html>
