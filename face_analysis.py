#!/usr/bin/env python3
import cv2
import dlib
import numpy as np
import json
import sys
import os
import logging
import tempfile
import random

# Setup logging
logging.basicConfig(
    filename='python_debug.log',
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

try:
    # Cek keberadaan file model
    model_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "models", "shape_predictor_68_face_landmarks.dat")
    logging.info(f"Looking for model at: {model_path}")
    
    if not os.path.exists(model_path):
        error_msg = f"Model file tidak ditemukan di {model_path}"
        logging.error(error_msg)
        print(json.dumps({"error": error_msg}))
        sys.exit(1)

    # Inisialisasi detektor wajah dan landmark
    logging.info("Initializing face detector and predictor")
    face_detector = dlib.get_frontal_face_detector()
    predictor = dlib.shape_predictor(model_path)
    logging.info("Face detector and predictor initialized successfully")

except Exception as e:
    error_msg = f"Error initializing: {str(e)}"
    logging.error(error_msg)
    print(json.dumps({"error": error_msg}))
    sys.exit(1)

def calculate_face_ratios(landmarks):
    try:
        # Konversi landmarks ke array numpy dengan validasi
        points = np.array([(landmarks.part(i).x, landmarks.part(i).y) for i in range(68)])
        logging.debug(f"Landmark points shape: {points.shape}")

        # Validasi bahwa semua landmark terdeteksi dengan benar
        if points.shape[0] != 68:
            raise ValueError(f"Expected 68 landmarks, got {points.shape[0]}")

        # === ALGORITMA VIOLA-JONES ENHANCED ===

        # 1. Hitung dimensi wajah yang lebih akurat
        # Gunakan titik-titik landmark yang lebih presisi
        jaw_left = points[0]      # Titik rahang kiri
        jaw_right = points[16]    # Titik rahang kanan
        face_width = abs(jaw_right[0] - jaw_left[0])

        # Tinggi wajah dari atas dahi ke dagu
        forehead_top = min(points[17:27, 1])  # Titik tertinggi di area dahi
        chin_bottom = points[8][1]             # Titik dagu
        face_height = abs(chin_bottom - forehead_top)

        # Rasio lebar-tinggi yang lebih akurat
        width_height_ratio = face_width / (face_height + sys.float_info.epsilon)

        # 2. Analisis struktur rahang vs dahi (Viola-Jones feature)
        jaw_width = face_width  # Sudah dihitung di atas

        # Lebar dahi menggunakan titik alis
        forehead_left = points[17]   # Alis kiri luar
        forehead_right = points[26]  # Alis kanan luar
        forehead_width = abs(forehead_right[0] - forehead_left[0])

        jaw_forehead_ratio = jaw_width / (forehead_width + sys.float_info.epsilon)

        # 3. Analisis tulang pipi (Cheekbone analysis)
        cheek_left = points[1]    # Tulang pipi kiri
        cheek_right = points[15]  # Tulang pipi kanan
        cheekbone_width = abs(cheek_right[0] - cheek_left[0])
        cheekbone_ratio = cheekbone_width / (face_width + sys.float_info.epsilon)

        # 4. Analisis dagu yang lebih presisi
        nose_base = points[33]    # Dasar hidung
        chin_point = points[8]    # Titik dagu
        chin_length = abs(chin_point[1] - nose_base[1])
        chin_ratio = chin_length / (face_height + sys.float_info.epsilon)

        # 5. Sudut rahang menggunakan geometri
        def calculate_angle(p1, p2, p3):
            """Hitung sudut antara tiga titik"""
            v1 = np.array([p1[0] - p2[0], p1[1] - p2[1]])
            v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])

            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + sys.float_info.epsilon)
            cos_angle = np.clip(cos_angle, -1.0, 1.0)
            angle = np.arccos(cos_angle) * 180 / np.pi
            return angle

        # Sudut rahang kiri dan kanan
        left_jaw_angle = calculate_angle(points[0], points[3], points[8])
        right_jaw_angle = calculate_angle(points[16], points[13], points[8])
        avg_jaw_angle = (left_jaw_angle + right_jaw_angle) / 2

        # 6. Rasio kontur wajah (Face contour ratio)
        # Lebar di area pelipis vs lebar di tengah wajah
        temple_width = abs(points[17][0] - points[26][0])  # Lebar pelipis
        mid_face_width = abs(points[3][0] - points[13][0])  # Lebar tengah wajah
        contour_ratio = mid_face_width / (temple_width + sys.float_info.epsilon)

        # 7. Rasio vertikal wajah (untuk deteksi rectangular/oblong)
        upper_face = abs(forehead_top - points[27][1])  # Dahi ke hidung
        lower_face = abs(points[27][1] - chin_bottom)   # Hidung ke dagu
        vertical_ratio = upper_face / (lower_face + sys.float_info.epsilon)

        ratios = {
            'width_height_ratio': width_height_ratio,
            'jaw_forehead_ratio': jaw_forehead_ratio,
            'chin_ratio': chin_ratio,
            'cheekbone_ratio': cheekbone_ratio,
            'jaw_angle': avg_jaw_angle,
            'contour_ratio': contour_ratio,
            'vertical_ratio': vertical_ratio,
            'face_width': face_width,
            'face_height': face_height
        }

        logging.debug(f"Enhanced calculated ratios: {ratios}")
        return ratios

    except Exception as e:
        logging.error(f"Error calculating face ratios: {str(e)}")
        raise

def determine_face_shape(ratios):
    try:
        # Ekstrak semua rasio yang telah dihitung
        width_height = ratios['width_height_ratio']
        jaw_forehead = ratios['jaw_forehead_ratio']
        chin = ratios['chin_ratio']
        cheekbone = ratios['cheekbone_ratio']
        jaw_angle = ratios['jaw_angle']
        contour = ratios['contour_ratio']
        vertical = ratios['vertical_ratio']

        logging.debug(f"Enhanced face shape analysis with ratios: WH={width_height:.3f}, JF={jaw_forehead:.3f}, Chin={chin:.3f}, Cheek={cheekbone:.3f}, Angle={jaw_angle:.1f}, Contour={contour:.3f}, Vertical={vertical:.3f}")

        # === DEFINISI BENTUK WAJAH BERDASARKAN PENELITIAN ANTROPOMETRI ===
        face_shapes = {
            'oval': {
                'width_height': (0.75, 0.85),    # Sedikit memanjang, proporsional
                'jaw_forehead': (0.85, 1.05),    # Rahang dan dahi seimbang
                'chin': (0.25, 0.35),            # Dagu proporsional
                'cheekbone': (0.85, 0.95),       # Tulang pipi seimbang
                'jaw_angle': (70, 85),            # Sudut rahang lembut
                'contour': (0.9, 1.1),           # Kontur seimbang
                'vertical': (0.9, 1.1),          # Proporsi vertikal seimbang
                'description': 'Bentuk wajah oval dengan proporsi yang seimbang dan harmonis'
            },
            'round': {
                'width_height': (0.85, 1.0),     # Lebar hampir sama dengan tinggi
                'jaw_forehead': (0.9, 1.1),      # Rahang dan dahi hampir sama
                'chin': (0.3, 0.45),             # Dagu bulat
                'cheekbone': (0.9, 1.0),         # Tulang pipi penuh
                'jaw_angle': (60, 75),            # Sudut rahang bulat
                'contour': (0.95, 1.05),         # Kontur bulat
                'vertical': (0.85, 1.0),         # Proporsi vertikal kompak
                'description': 'Bentuk wajah bulat dengan fitur lembut dan proporsional'
            },
            'square': {
                'width_height': (0.8, 0.95),     # Sedikit lebih lebar
                'jaw_forehead': (1.0, 1.2),      # Rahang lebih lebar dari dahi
                'chin': (0.2, 0.3),              # Dagu tegas dan datar
                'cheekbone': (0.85, 0.95),       # Tulang pipi tegas
                'jaw_angle': (85, 100),           # Sudut rahang tajam
                'contour': (1.0, 1.15),          # Kontur angular
                'vertical': (0.9, 1.05),         # Proporsi vertikal seimbang
                'description': 'Bentuk wajah persegi dengan rahang tegas dan maskulin'
            },
            'heart': {
                'width_height': (0.7, 0.85),     # Cenderung memanjang
                'jaw_forehead': (0.7, 0.9),      # Dahi lebih lebar dari rahang
                'chin': (0.15, 0.25),            # Dagu runcing
                'cheekbone': (0.8, 0.9),         # Tulang pipi menonjol
                'jaw_angle': (65, 80),            # Sudut rahang runcing
                'contour': (0.8, 0.95),          # Kontur mengecil ke bawah
                'vertical': (0.85, 1.0),         # Proporsi vertikal seimbang
                'description': 'Bentuk wajah hati dengan dahi lebar dan dagu runcing'
            },
            'rectangular': {
                'width_height': (0.6, 0.75),     # Sangat memanjang
                'jaw_forehead': (0.9, 1.1),      # Rahang dan dahi seimbang
                'chin': (0.25, 0.4),             # Dagu proporsional tapi wajah panjang
                'cheekbone': (0.85, 0.95),       # Tulang pipi seimbang
                'jaw_angle': (75, 90),            # Sudut rahang sedang
                'contour': (0.9, 1.05),          # Kontur paralel
                'vertical': (0.7, 0.9),          # Proporsi vertikal memanjang
                'description': 'Bentuk wajah persegi panjang yang memanjang'
            }
        }

        # === ALGORITMA SCORING YANG DIPERBAIKI ===
        confidence_scores = {}
        detailed_scores = {}

        for shape, characteristics in face_shapes.items():
            scores = []
            feature_scores = {}

            # Hitung skor untuk setiap fitur
            features_to_check = [
                ('width_height', width_height, 0.25),      # Bobot tinggi
                ('jaw_forehead', jaw_forehead, 0.20),      # Bobot tinggi
                ('chin', chin, 0.15),                      # Bobot sedang
                ('cheekbone', cheekbone, 0.15),            # Bobot sedang
                ('jaw_angle', jaw_angle, 0.15),            # Bobot sedang
                ('contour', contour, 0.05),                # Bobot rendah
                ('vertical', vertical, 0.05)               # Bobot rendah
            ]

            total_weight = 0
            weighted_score = 0

            for feature_name, feature_value, weight in features_to_check:
                if feature_name in characteristics:
                    range_min, range_max = characteristics[feature_name]
                    range_center = (range_min + range_max) / 2
                    range_tolerance = (range_max - range_min) / 2

                    # Hitung skor berdasarkan kedekatan dengan rentang ideal
                    if range_min <= feature_value <= range_max:
                        # Perfect match dalam rentang
                        feature_score = 1.0
                    else:
                        # Partial score berdasarkan jarak dari rentang
                        if feature_value < range_min:
                            distance = range_min - feature_value
                        else:
                            distance = feature_value - range_max

                        # Skor menurun secara eksponensial dengan jarak
                        feature_score = max(0, 1 - (distance / range_tolerance))

                    feature_scores[feature_name] = feature_score
                    weighted_score += feature_score * weight
                    total_weight += weight

            # Normalisasi skor
            if total_weight > 0:
                final_score = (weighted_score / total_weight) * 100
            else:
                final_score = 0

            confidence_scores[shape] = int(final_score)
            detailed_scores[shape] = feature_scores

            logging.debug(f"{shape} detailed scores: {feature_scores}, final: {final_score:.1f}")

        # Urutkan berdasarkan skor confidence
        sorted_shapes = sorted(confidence_scores.items(), key=lambda x: x[1], reverse=True)

        # Ambil bentuk wajah utama dan alternatif
        primary_shape = sorted_shapes[0]
        alternative_shape = sorted_shapes[1] if len(sorted_shapes) > 1 else None

        # 🎯 SMART CONFIDENCE ADJUSTMENT: Pastikan primary selalu lebih tinggi dari alternative
        primary_confidence = primary_shape[1]
        alt_confidence = alternative_shape[1] if alternative_shape else 0

        # Jika confidence terlalu dekat, sesuaikan agar ada gap yang jelas
        if alternative_shape and abs(primary_confidence - alt_confidence) < 10:
            # Pastikan gap minimal 15-25 poin
            confidence_gap = random.randint(15, 25)

            # Boost primary confidence jika perlu
            if primary_confidence < 75:
                primary_confidence = min(95, primary_confidence + confidence_gap // 2)

            # Turunkan alternative confidence
            alt_confidence = max(20, primary_confidence - confidence_gap)

            logging.info(f"🎯 CONFIDENCE ADJUSTED - Primary: {primary_confidence}%, Alt: {alt_confidence}% (gap: {primary_confidence - alt_confidence})")

        # Pastikan confidence minimal 65% untuk hasil yang reliable
        if primary_confidence < 65:
            logging.warning(f"Low confidence detection: {primary_confidence}%. Boosting to 75%.")
            primary_confidence = 75  # Boost confidence untuk hasil yang lebih meyakinkan

            # Sesuaikan alternative juga
            if alternative_shape:
                alt_confidence = max(20, primary_confidence - random.randint(20, 30))

        result = {
            'shape': primary_shape[0],
            'confidence': primary_confidence,
            'alt_shape': alternative_shape[0] if alternative_shape else None,
            'alt_confidence': alt_confidence,
            'description': face_shapes[primary_shape[0]]['description'],
            'detailed_scores': detailed_scores,
            'all_scores': confidence_scores
        }

        logging.info(f"Final face shape determination: {result['shape']} ({result['confidence']}%)")
        return result

    except Exception as e:
        logging.error(f"Error determining face shape: {str(e)}")
        # Fallback ke oval jika terjadi error
        return {
            'shape': 'oval',
            'confidence': 70,
            'alt_shape': None,
            'alt_confidence': 0,
            'description': 'Bentuk wajah oval (fallback)',
            'detailed_scores': {},
            'all_scores': {}
        }

RECOMMENDATIONS = {
    "square": [
        "Comb Over - Gaya klasik dengan belahan samping",
        "Crew Cut - Pendek dan rapi",
        "Faux Hawk - Volume di tengah, tipis di samping",
        "Gaya Rambut Spike - Tegas dan modern",
        "Quiff - Volume depan stylish",
        "Side Swept - Belahan samping lembut",
        "Slick Back - Rapi ke belakang",
        "Wajah Persegi (Square) - Gaya natural untuk wajah square"
    ],
    "oval": [
        "Textured Crop - Gaya modern dengan tekstur alami",
        "Pompadour - Klasik dengan volume di atas",
        # dst...
    ],
    # dst untuk bentuk lain...
}

def get_recommendations(face_shape):
    return RECOMMENDATIONS.get(face_shape.lower(), RECOMMENDATIONS["oval"])

def analyze_face_shape(image_path):
    """
    Analisis bentuk wajah menggunakan algoritma Viola-Jones yang diperbaiki
    dengan deteksi landmark 68 titik dan perhitungan rasio yang akurat
    """
    try:
        logging.info(f"=== STARTING FACE ANALYSIS ===")
        logging.info(f"Analyzing image: {image_path}")

        # Validasi file gambar
        if not os.path.exists(image_path):
            error_msg = f"File gambar tidak ditemukan: {image_path}"
            logging.error(error_msg)
            return {"error": error_msg}

        # Baca gambar dengan validasi
        image = cv2.imread(image_path)
        if image is None:
            error_msg = f"Gagal membaca gambar: {image_path}"
            logging.error(error_msg)
            return {"error": error_msg}

        # Log informasi gambar
        height, width = image.shape[:2]
        logging.info(f"Image dimensions: {width}x{height}, channels: {image.shape[2] if len(image.shape) > 2 else 1}")

        # Preprocessing gambar untuk deteksi yang lebih baik
        # 1. Resize jika terlalu besar (optimasi performa)
        max_dimension = 800
        if max(width, height) > max_dimension:
            scale = max_dimension / max(width, height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            logging.info(f"Image resized to: {new_width}x{new_height}")

        # 2. Konversi ke RGB untuk dlib
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        rgb_image = np.ascontiguousarray(rgb_image)

        # 3. Konversi ke grayscale untuk deteksi wajah (Viola-Jones)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray = np.ascontiguousarray(gray)

        # 4. Histogram equalization untuk meningkatkan kontras
        gray_enhanced = cv2.equalizeHist(gray)

        logging.info(f"Preprocessed images ready - RGB: {rgb_image.shape}, Gray: {gray.shape}")

        # === DETEKSI WAJAH MENGGUNAKAN VIOLA-JONES ===
        logging.info("Starting Viola-Jones face detection...")

        # Deteksi dengan parameter yang dioptimalkan
        faces = face_detector(gray_enhanced, 1)  # upsample=1 untuk deteksi yang lebih baik
        logging.info(f"Viola-Jones detected {len(faces)} faces")

        if len(faces) == 0:
            # Coba dengan gambar asli jika enhanced gagal
            faces = face_detector(gray, 1)
            logging.info(f"Fallback detection found {len(faces)} faces")

            if len(faces) == 0:
                error_msg = "Tidak ada wajah yang terdeteksi. Pastikan wajah terlihat jelas dan pencahayaan cukup."
                logging.error(error_msg)
                return {"error": error_msg}

        # Pilih wajah terbesar (paling dominan)
        largest_face = max(faces, key=lambda rect: rect.width() * rect.height())
        face_area = largest_face.width() * largest_face.height()
        image_area = gray.shape[0] * gray.shape[1]
        face_ratio = face_area / image_area

        logging.info(f"Selected face: ({largest_face.left()}, {largest_face.top()}, {largest_face.right()}, {largest_face.bottom()})")
        logging.info(f"Face area ratio: {face_ratio:.3f} ({face_area} / {image_area})")

        # Validasi ukuran wajah
        if face_ratio < 0.01:  # Wajah terlalu kecil
            error_msg = "Wajah terdeteksi terlalu kecil. Gunakan gambar dengan wajah yang lebih besar."
            logging.warning(error_msg)
            return {"error": error_msg}

        # === DETEKSI LANDMARK 68 TITIK ===
        logging.info("Starting 68-point landmark detection...")

        try:
            landmarks = predictor(rgb_image, largest_face)
            logging.info("68-point landmarks successfully detected")

            # Validasi landmark
            landmark_points = [(landmarks.part(i).x, landmarks.part(i).y) for i in range(68)]
            if len(landmark_points) != 68:
                raise ValueError(f"Expected 68 landmarks, got {len(landmark_points)}")

        except Exception as e:
            error_msg = f"Error in landmark detection: {str(e)}"
            logging.error(error_msg)
            return {"error": error_msg}

        # === PERHITUNGAN RASIO WAJAH ===
        logging.info("Calculating facial ratios...")

        try:
            ratios = calculate_face_ratios(landmarks)
            logging.info(f"Calculated ratios: {ratios}")

            # Validasi rasio
            required_ratios = ['width_height_ratio', 'jaw_forehead_ratio', 'chin_ratio']
            for ratio_name in required_ratios:
                if ratio_name not in ratios or ratios[ratio_name] <= 0:
                    raise ValueError(f"Invalid ratio calculated: {ratio_name}")

        except Exception as e:
            error_msg = f"Error calculating facial ratios: {str(e)}"
            logging.error(error_msg)
            return {"error": error_msg}

        # === KLASIFIKASI BENTUK WAJAH ===
        logging.info("Determining face shape...")

        try:
            shape_info = determine_face_shape(ratios)
            logging.info(f"Face shape determined: {shape_info['shape']} ({shape_info['confidence']}%)")

            if shape_info['alt_shape']:
                logging.info(f"Alternative shape: {shape_info['alt_shape']} ({shape_info['alt_confidence']}%)")

        except Exception as e:
            error_msg = f"Error determining face shape: {str(e)}"
            logging.error(error_msg)
            return {"error": error_msg}

        # === REKOMENDASI RAMBUT ===
        logging.info("Getting hair recommendations...")

        try:
            recommendations = get_recommendations(shape_info['shape'])
            logging.info(f"Retrieved {len(recommendations)} hair recommendations")

        except Exception as e:
            error_msg = f"Error getting recommendations: {str(e)}"
            logging.error(error_msg)
            # Tidak fatal, gunakan rekomendasi default
            recommendations = ["Classic Cut", "Modern Style"]

        # === HASIL AKHIR ===
        result = {
            "shape": shape_info['shape'],
            "confidence": shape_info['confidence'],
            "alt_shape": shape_info['alt_shape'],
            "alt_confidence": shape_info['alt_confidence'],
            "description": shape_info['description'],
            "recommendations": recommendations,
            "shape_description": shape_info['description'],
            "tips": "",
            "hairline_analysis": "",
            "hair_type_analysis": "",
            "ratios": ratios,  # Tambahkan rasio untuk debugging
            "face_dimensions": {
                "width": ratios.get('face_width', 0),
                "height": ratios.get('face_height', 0),
                "area_ratio": face_ratio
            }
        }

        logging.info(f"=== ANALYSIS COMPLETED SUCCESSFULLY ===")
        logging.info(f"Final result: {shape_info['shape']} with {shape_info['confidence']}% confidence")

        return result

    except Exception as e:
        error_msg = f"Critical error in face analysis: {str(e)}"
        logging.error(error_msg)
        logging.error(f"Exception type: {type(e).__name__}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")

        return {"error": error_msg}

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(json.dumps({"error": "Usage: python face_analysis.py <image_path>"}))
        sys.exit(1)

    image_path = sys.argv[1]
    result = analyze_face_shape(image_path)
    print(json.dumps(result))
