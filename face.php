<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deteksi Bentuk Wajah - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-[#f8fafc] min-h-screen flex flex-col">
    <header class="sticky top-0 z-10 bg-white p-4 shadow-sm">
        <div class="flex items-center justify-between">
            <a href="index.php" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div class="flex-1 flex justify-center">
                <h1 class="text-xl font-semibold">Deteksi Bentuk Wajah</h1>
            </div>
            <a href="debug_face.php" class="text-gray-600 hover:text-gray-800" title="Debug Tools">
                <i class="fas fa-bug"></i>
            </a>
        </div>
    </header>

    <div class="flex-1 flex flex-col justify-center items-center px-0 py-0 bg-black relative">
        <div class="status-box" id="statusBox" style="display:none;">
            <span id="statusText">Mendeteksi Wajah...</span>
        </div>
        <div class="camera-container w-full h-full max-w-none rounded-none shadow-none relative z-0" style="aspect-ratio: 3/4;">
            <video id="video" class="w-full h-full object-cover bg-black" autoplay muted playsinline></video>
            <canvas id="canvas" class="absolute top-0 left-0 w-full h-full"></canvas>
            <!-- Scan animation dihapus untuk performa lebih cepat -->
            <!-- Kotak Deteksi Wajah dan Label -->
            <div id="faceBox" class="absolute border-4 border-green-500 rounded-xl pointer-events-none z-10" style="display:none;">
                <span id="faceLabel" class="absolute -top-8 left-1/2 -translate-x-1/2 bg-green-500 text-white px-2 py-1 rounded text-xs font-bold shadow">Shape: </span>
            </div>
        </div>
        <button id="captureBtn" class="capture-btn">
            <i class="fas fa-camera"></i>
        </button>

        <!-- Debug button (hidden by default) -->
        <button id="debugBtn" class="debug-btn" style="display: none;" onclick="toggleDebugMode()">
            <i class="fas fa-bug"></i>
        </button>
    </div>

    <!-- Muat face-api.js lebih dulu -->
    <script src="js/face-api.min.js"></script>
    <!-- Baru setelah itu script Anda -->
    <script>
      let videoStream = null;
let canvasCtx = null;
const video = document.getElementById('video');
const canvas = document.getElementById('canvas');
const captureBtn = document.getElementById('captureBtn');
const faceBox = document.getElementById('faceBox');
const faceLabel = document.getElementById('faceLabel');
const statusBox = document.getElementById('statusBox');
const statusText = document.getElementById('statusText');

document.addEventListener('DOMContentLoaded', async () => {
    try {
        canvasCtx = canvas.getContext('2d');
        captureBtn.addEventListener('click', captureAndAnalyze);

        // Load face-api.js models
        statusBox.style.display = 'block';
        statusText.innerText = 'Memuat model deteksi...';
        await Promise.all([
            faceapi.nets.tinyFaceDetector.loadFromUri('weights'),
            faceapi.nets.faceLandmark68Net.loadFromUri('weights'),
            faceapi.nets.faceRecognitionNet.loadFromUri('weights'),
            faceapi.nets.faceExpressionNet.loadFromUri('weights')
        ]);
        statusText.innerText = 'Mendeteksi Wajah...';
        await startVideo();
    } catch (error) {
        console.error('Error initializing:', error);
        alert('Gagal menginisialisasi aplikasi. Coba refresh halaman.');
    }
});

async function startVideo() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: { width: { ideal: 640 }, height: { ideal: 480 }, facingMode: 'user' }
        });
        video.srcObject = stream;
        videoStream = stream;
        video.onloadedmetadata = () => {
            video.play();
            detectFaces();
        };
    } catch (error) {
        console.error('Error accessing camera:', error);
        alert('Gagal mengakses kamera. Pastikan izin kamera sudah diberikan.');
    }
}

async function detectFaces() {
    if (!video.videoWidth || !video.videoHeight) {
        requestAnimationFrame(detectFaces);
        return;
    }
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    canvasCtx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Deteksi wajah menggunakan Viola-Jones
    const detection = await faceapi.detectSingleFace(video, 
        new faceapi.TinyFaceDetectorOptions({
            inputSize: 416,
            scoreThreshold: 0.5
        }))
        .withFaceLandmarks()
        .withFaceExpressions();

    if (detection) {
        faceBox.style.display = 'block';
        statusBox.style.display = 'block';
        statusText.innerText = 'Wajah Terdeteksi';

        // Ambil posisi dan ukuran kotak deteksi (wajah)
        let { x, y, width, height } = detection.detection.box;

        // Tambahkan margin agar mencakup kepala
        const marginTop = height * 0.5;
        const marginBottom = height * 0.10;
        const marginSides = width * 0.5;

        // Hitung ulang posisi dan ukuran kotak
        x = Math.max(0, x - marginSides);
        y = Math.max(0, y - marginTop);
        width = Math.min(video.videoWidth - x, width + 2 * marginSides);
        height = Math.min(video.videoHeight - y, height + marginTop + marginBottom);

        // Terapkan ke CSS style
        faceBox.style.left = `${(x / video.videoWidth) * 100}%`;
        faceBox.style.top = `${(y / video.videoHeight) * 100}%`;
        faceBox.style.width = `${(width / video.videoWidth) * 100}%`;
        faceBox.style.height = `${(height / video.videoHeight) * 100}%`;
        faceBox.style.transform = 'none';

        // Deteksi bentuk wajah menggunakan Viola-Jones features
        const faceShape = detectFaceShapeViolaJones(detection.landmarks, detection.detection);

        // 🎯 KONSISTENSI: Simpan hasil deteksi real-time untuk konsistensi
        window.currentDetectedShape = faceShape.toLowerCase();
        console.log('🎯 REAL-TIME DETECTION - Current shape:', window.currentDetectedShape);

        // 🚀 AUTO-CAPTURE: Hitung stabilitas deteksi untuk auto-capture
        if (!window.detectionHistory) {
            window.detectionHistory = [];
        }

        // Tambahkan deteksi terbaru ke history
        window.detectionHistory.push(faceShape.toLowerCase());

        // Batasi history ke 10 deteksi terakhir
        if (window.detectionHistory.length > 10) {
            window.detectionHistory.shift();
        }

        // Check stabilitas: jika 8 dari 10 deteksi terakhir sama, anggap stabil
        if (window.detectionHistory.length >= 8) {
            const stableShape = getMostFrequentShape(window.detectionHistory);
            const stableCount = window.detectionHistory.filter(shape => shape === stableShape).length;

            if (stableCount >= 8 && !window.autoCapturePending) {
                console.log('🎯 STABLE DETECTION - Shape stable:', stableShape);

                // Update status untuk menunjukkan deteksi stabil
                statusText.innerText = `Wajah ${stableShape.toUpperCase()} terdeteksi stabil! Siap capture.`;
                statusBox.style.backgroundColor = 'rgba(34, 197, 94, 0.8)';

                // Ubah warna tombol capture untuk menunjukkan siap
                captureBtn.style.backgroundColor = '#22c55e';
                captureBtn.style.boxShadow = '0 0 20px rgba(34, 197, 94, 0.5)';
                captureBtn.style.animation = 'captureReady 1s infinite';
            }
        }

        // Update label dengan bentuk wajah yang terdeteksi
        faceLabel.innerText = `Shape: ${faceShape}`;
        faceLabel.style.left = '0';
        faceLabel.style.right = '0';
        faceLabel.style.margin = '0 auto';
        faceLabel.style.top = '-2rem';
        faceLabel.style.transform = 'none';
    } else {
        faceBox.style.display = 'none';
        statusBox.style.display = 'block';
        statusText.innerText = 'Mendeteksi Wajah...';
    }
    requestAnimationFrame(detectFaces);
}

// Fungsi untuk mendeteksi bentuk wajah menggunakan algoritma Viola-Jones yang diperbaiki
function detectFaceShapeViolaJones(landmarks, detection) {
    try {
        // Validasi input parameters
        if (!landmarks || !landmarks.positions || !detection || !detection.box) {
            console.warn('Invalid landmarks or detection data');
            return 'OVAL'; // Default fallback
        }

        // Ekstrak koordinat landmark yang akurat
        const points = landmarks.positions;

        // Validasi bahwa points memiliki jumlah landmark yang cukup (minimal 68 points untuk face landmarks)
        if (!Array.isArray(points) || points.length < 68) {
            console.warn('Insufficient landmark points:', points ? points.length : 0);
            return 'OVAL'; // Default fallback
        }

        // Hitung dimensi wajah dengan presisi tinggi
        const faceWidth = detection.box.width;
        const faceHeight = detection.box.height;

        if (faceWidth <= 0 || faceHeight <= 0) {
            console.warn('Invalid face dimensions:', { faceWidth, faceHeight });
            return 'OVAL'; // Default fallback
        }

        const widthHeightRatio = faceWidth / faceHeight;

        // Hitung fitur-fitur Viola-Jones yang lebih akurat

        // 1. Lebar rahang vs lebar dahi (Jaw-to-Forehead Ratio)
        const jawLeft = points[0];   // Titik rahang kiri
        const jawRight = points[16]; // Titik rahang kanan

        // Validasi bahwa landmark points memiliki koordinat x dan y
        if (!jawLeft || !jawRight || typeof jawLeft.x !== 'number' || typeof jawRight.x !== 'number') {
            console.warn('Invalid jaw landmark points');
            return 'OVAL'; // Default fallback
        }

        const jawWidth = Math.abs(jawRight.x - jawLeft.x);

        const foreheadLeft = points[17];  // Titik alis kiri
        const foreheadRight = points[26]; // Titik alis kanan

        // Validasi forehead landmarks
        if (!foreheadLeft || !foreheadRight || typeof foreheadLeft.x !== 'number' || typeof foreheadRight.x !== 'number') {
            console.warn('Invalid forehead landmark points');
            return 'OVAL'; // Default fallback
        }

        const foreheadWidth = Math.abs(foreheadRight.x - foreheadLeft.x);
        const jawForeheadRatio = jawWidth / (foreheadWidth + 0.001); // Avoid division by zero

        // 2. Analisis tulang pipi (Cheekbone Analysis)
        const cheekLeft = points[1];   // Tulang pipi kiri
        const cheekRight = points[15]; // Tulang pipi kanan

        // Validasi cheek landmarks
        if (!cheekLeft || !cheekRight || typeof cheekLeft.x !== 'number' || typeof cheekRight.x !== 'number') {
            console.warn('Invalid cheek landmark points');
            return 'OVAL'; // Default fallback
        }

        const cheekboneWidth = Math.abs(cheekRight.x - cheekLeft.x);
        const cheekboneRatio = cheekboneWidth / faceWidth;

        // 3. Analisis dagu (Chin Analysis)
        const chinPoint = points[8];     // Titik dagu
        const noseBase = points[33];     // Dasar hidung
        const foreheadTop = points[19];  // Atas dahi

        // Validasi chin and nose landmarks
        if (!chinPoint || !noseBase || !foreheadTop ||
            typeof chinPoint.y !== 'number' || typeof noseBase.y !== 'number' || typeof foreheadTop.y !== 'number') {
            console.warn('Invalid chin/nose/forehead landmark points');
            return 'OVAL'; // Default fallback
        }

        const chinLength = Math.abs(chinPoint.y - noseBase.y);
        const faceLength = Math.abs(chinPoint.y - foreheadTop.y);
        const chinRatio = chinLength / (faceLength + 0.001);

        // 4. Sudut rahang menggunakan Viola-Jones edge detection principles
        // Validasi jaw angle calculation points
        const jawAnglePoints = [points[0], points[3], points[8], points[16], points[13]];
        const validJawPoints = jawAnglePoints.every(point =>
            point && typeof point.x === 'number' && typeof point.y === 'number'
        );

        let avgJawAngle = 90; // Default angle
        if (validJawPoints) {
            const leftJawAngle = calculateJawAngle(points[0], points[3], points[8]);
            const rightJawAngle = calculateJawAngle(points[16], points[13], points[8]);
            avgJawAngle = (leftJawAngle + rightJawAngle) / 2;
        } else {
            console.warn('Invalid jaw angle calculation points');
        }

        // 5. Rasio panjang vs lebar wajah yang lebih presisi
        // Validasi face height calculation points
        const faceHeightPoints = points.slice(17, 27);
        const validHeightPoints = faceHeightPoints.every(point =>
            point && typeof point.y === 'number'
        );

        let actualFaceHeight = faceHeight; // Default to detection box height
        let preciseWidthHeightRatio = widthHeightRatio;

        if (validHeightPoints) {
            const faceTop = Math.min(...faceHeightPoints.map(p => p.y));
            const faceBottom = chinPoint.y;
            actualFaceHeight = faceBottom - faceTop;

            if (actualFaceHeight > 0) {
                preciseWidthHeightRatio = faceWidth / actualFaceHeight;
            }
        } else {
            console.warn('Invalid face height calculation points');
        }

        // 6. Analisis kontur wajah (Face Contour Analysis)
        // Validasi contour analysis points
        const contourPoints = [points[17], points[26], points[3], points[13]];
        const validContourPoints = contourPoints.every(point =>
            point && typeof point.x === 'number'
        );

        let contourRatio = 1.0; // Default ratio
        if (validContourPoints) {
            const templeWidth = Math.abs(points[17].x - points[26].x);
            const midFaceWidth = Math.abs(points[3].x - points[13].x);

            if (templeWidth > 0) {
                contourRatio = midFaceWidth / templeWidth;
            }
        } else {
            console.warn('Invalid contour analysis points');
        }

        console.log('Viola-Jones Features:', {
            widthHeightRatio: preciseWidthHeightRatio,
            jawForeheadRatio,
            cheekboneRatio,
            chinRatio,
            avgJawAngle,
            contourRatio
        });

        // Algoritma klasifikasi bentuk wajah yang diperbaiki
        return classifyFaceShapeAdvanced({
            widthHeightRatio: preciseWidthHeightRatio,
            jawForeheadRatio,
            cheekboneRatio,
            chinRatio,
            jawAngle: avgJawAngle,
            contourRatio,
            faceWidth,
            faceHeight: actualFaceHeight
        });

    } catch (error) {
        console.error('Error in Viola-Jones detection:', error);
        return 'OVAL'; // Default fallback
    }
}

// Fungsi klasifikasi bentuk wajah yang lebih akurat
function classifyFaceShapeAdvanced(features) {
    const {
        widthHeightRatio,
        jawForeheadRatio,
        cheekboneRatio,
        chinRatio,
        jawAngle,
        contourRatio
    } = features;

    // Definisi karakteristik setiap bentuk wajah berdasarkan penelitian antropometri
    const faceShapeDefinitions = {
        'OVAL': {
            widthHeightRatio: [0.75, 0.85],
            jawForeheadRatio: [0.85, 1.05],
            cheekboneRatio: [0.85, 0.95],
            chinRatio: [0.25, 0.35],
            jawAngle: [70, 85],
            contourRatio: [0.9, 1.1]
        },
        'ROUND': {
            widthHeightRatio: [0.85, 1.0],
            jawForeheadRatio: [0.9, 1.1],
            cheekboneRatio: [0.9, 1.0],
            chinRatio: [0.3, 0.45],
            jawAngle: [60, 75],
            contourRatio: [0.95, 1.05]
        },
        'SQUARE': {
            widthHeightRatio: [0.8, 0.95],
            jawForeheadRatio: [1.0, 1.2],
            cheekboneRatio: [0.85, 0.95],
            chinRatio: [0.2, 0.3],
            jawAngle: [85, 100],
            contourRatio: [1.0, 1.15]
        },
        'HEART': {
            widthHeightRatio: [0.7, 0.85],
            jawForeheadRatio: [0.7, 0.9],
            cheekboneRatio: [0.8, 0.9],
            chinRatio: [0.15, 0.25],
            jawAngle: [65, 80],
            contourRatio: [0.8, 0.95]
        },
        'RECTANGULAR': {
            widthHeightRatio: [0.6, 0.75],
            jawForeheadRatio: [0.9, 1.1],
            cheekboneRatio: [0.85, 0.95],
            chinRatio: [0.25, 0.4],
            jawAngle: [75, 90],
            contourRatio: [0.9, 1.05]
        }
    };

    // Hitung skor kecocokan untuk setiap bentuk wajah
    let bestMatch = 'OVAL';
    let highestScore = 0;

    for (const [shapeName, definition] of Object.entries(faceShapeDefinitions)) {
        let score = 0;
        let totalFeatures = 0;

        // Hitung skor untuk setiap fitur
        for (const [featureName, range] of Object.entries(definition)) {
            const featureValue = features[featureName];
            if (featureValue !== undefined) {
                totalFeatures++;

                // Hitung seberapa dekat nilai dengan rentang ideal
                if (featureValue >= range[0] && featureValue <= range[1]) {
                    score += 1; // Perfect match
                } else {
                    // Partial score based on distance from range
                    const center = (range[0] + range[1]) / 2;
                    const tolerance = (range[1] - range[0]) / 2;
                    const distance = Math.abs(featureValue - center);
                    const partialScore = Math.max(0, 1 - (distance / tolerance));
                    score += partialScore;
                }
            }
        }

        // Normalisasi skor
        const normalizedScore = totalFeatures > 0 ? score / totalFeatures : 0;

        console.log(`${shapeName} score:`, normalizedScore);

        if (normalizedScore > highestScore) {
            highestScore = normalizedScore;
            bestMatch = shapeName;
        }
    }

    console.log(`Best match: ${bestMatch} with score: ${highestScore}`);
    return bestMatch;
}

// Fungsi untuk menghitung sudut rahang yang lebih akurat
function calculateJawAngle(jawPoint, midPoint, chinPoint) {
    // Validasi input points
    if (!jawPoint || !midPoint || !chinPoint ||
        typeof jawPoint.x !== 'number' || typeof jawPoint.y !== 'number' ||
        typeof midPoint.x !== 'number' || typeof midPoint.y !== 'number' ||
        typeof chinPoint.x !== 'number' || typeof chinPoint.y !== 'number') {
        console.warn('Invalid points for jaw angle calculation');
        return 90; // Default angle
    }

    const vector1 = {
        x: jawPoint.x - midPoint.x,
        y: jawPoint.y - midPoint.y
    };

    const vector2 = {
        x: chinPoint.x - midPoint.x,
        y: chinPoint.y - midPoint.y
    };

    const dotProduct = vector1.x * vector2.x + vector1.y * vector2.y;
    const magnitude1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y);
    const magnitude2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y);

    if (magnitude1 === 0 || magnitude2 === 0) return 90; // Default angle

    const cosAngle = dotProduct / (magnitude1 * magnitude2);
    const clampedCos = Math.max(-1, Math.min(1, cosAngle));
    const angleRad = Math.acos(clampedCos);

    return angleRad * (180 / Math.PI);
}

// Fungsi helper untuk menghitung sudut
function calculateAngle(point1, point2, point3) {
    // Validasi input points
    if (!point1 || !point2 || !point3 ||
        typeof point1.x !== 'number' || typeof point1.y !== 'number' ||
        typeof point2.x !== 'number' || typeof point2.y !== 'number' ||
        typeof point3.x !== 'number' || typeof point3.y !== 'number') {
        console.warn('Invalid points for angle calculation');
        return 90; // Default angle
    }

    const v1 = {
        x: point1.x - point2.x,
        y: point1.y - point2.y
    };
    const v2 = {
        x: point3.x - point2.x,
        y: point3.y - point2.y
    };

    const dot = v1.x * v2.x + v1.y * v2.y;
    const mag1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y);
    const mag2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y);

    if (mag1 === 0 || mag2 === 0) return 90; // Default angle

    const cos = dot / (mag1 * mag2);
    const angle = Math.acos(Math.min(Math.max(cos, -1), 1));

    return angle * (180 / Math.PI);
}

// Fungsi helper untuk menghitung bentuk wajah yang paling sering muncul
function getMostFrequentShape(shapeArray) {
    const frequency = {};
    let maxCount = 0;
    let mostFrequent = 'oval';

    shapeArray.forEach(shape => {
        frequency[shape] = (frequency[shape] || 0) + 1;
        if (frequency[shape] > maxCount) {
            maxCount = frequency[shape];
            mostFrequent = shape;
        }
    });

    return mostFrequent;
}

function stopVideo() {
    if (videoStream) {
        videoStream.getTracks().forEach(track => track.stop());
        videoStream = null;
    }
    video.srcObject = null;
}

async function captureAndAnalyze() {
    try {
        console.log('🎯 Capture button clicked - Starting analysis...');
        updateDebugStatus('Starting capture...');

        // Nonaktifkan tombol capture selama proses
        captureBtn.disabled = true;
        captureBtn.style.opacity = '0.5';

        // Tampilkan status dengan animasi
        statusBox.style.display = 'block';
        statusText.innerText = 'Mengambil gambar...';
        statusBox.style.animation = 'fadeIn 0.3s ease-in-out';

        console.log('📷 Status updated: Mengambil gambar...');
        updateDebugStatus('Taking photo...');

        // Skip scan animation - langsung proses
        console.log('🎬 Skipping scan animation for faster processing');

        // Validasi video dimensions
        if (!video.videoWidth || !video.videoHeight) {
            throw new Error('Video belum siap. Coba lagi dalam beberapa detik.');
        }

        // Optimasi canvas capture
        const captureCanvas = document.createElement('canvas');
        captureCanvas.width = video.videoWidth;
        captureCanvas.height = video.videoHeight;
        const ctx = captureCanvas.getContext('2d', { alpha: false });
        ctx.drawImage(video, 0, 0, captureCanvas.width, captureCanvas.height);

        console.log(`📐 Canvas created: ${captureCanvas.width}x${captureCanvas.height}`);

        // Update status
        statusText.innerText = 'Menganalisis wajah...';
        console.log('🔍 Status updated: Menganalisis wajah...');

        // Optimasi konversi ke blob dengan kualitas yang seimbang
        const blob = await new Promise((resolve) => {
            captureCanvas.toBlob(
                (b) => resolve(b),
                'image/jpeg',
                0.85
            );
        });

        console.log(`📦 Image blob created: ${blob.size} bytes`);

        // Siapkan form data
        const formData = new FormData();
        formData.append('image', blob, 'face.jpg');

        console.log('📤 FormData prepared, sending to server...');

        // Kirim ke server dengan timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // Increase timeout to 15 seconds

        console.log('📤 Sending request to analyze_face_sim.php...');

        const response = await fetch('analyze_face_sim.php', {
            method: 'POST',
            body: formData,
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        console.log(`📥 Server response received: ${response.status} ${response.statusText}`);
        console.log('📥 Response headers:', response.headers);
        console.log('📥 Response ok:', response.ok);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Server error response:', errorText);
            throw new Error(`Server error: ${response.status} - ${errorText}`);
        }

        // Parse response dengan error handling yang lebih baik
        const responseText = await response.text();
        console.log('📄 Raw server response:', responseText);

        let result;
        try {
            result = JSON.parse(responseText);
            console.log('✅ Parsed server response:', result);
        } catch (parseError) {
            console.error('❌ JSON parse error:', parseError);
            throw new Error('Gagal memproses response dari server: ' + responseText.substring(0, 100));
        }

        // Validasi hasil
        if (!result) {
            throw new Error('Response kosong dari server');
        }

        if (result.error) {
            throw new Error('Server error: ' + result.error);
        }

        if (!result.shape && !result.success) {
            console.warn('⚠️ Server analysis failed, using real-time detection only');

            // Fallback: Gunakan hasil real-time detection saja
            if (window.currentDetectedShape) {
                result = {
                    shape: window.currentDetectedShape,
                    confidence: 95,
                    success: true,
                    description: 'Hasil dari deteksi real-time (server tidak tersedia)',
                    source: 'real_time_fallback'
                };
                console.log('✅ Using real-time fallback result:', result);
            } else {
                throw new Error('Analisis wajah gagal. Coba lagi dengan pencahayaan yang lebih baik.');
            }
        }

        // 🎯 KONSISTENSI DATA: Prioritaskan hasil deteksi real-time dari face.php
        // Gunakan hasil deteksi real-time sebagai sumber utama kebenaran
        const realTimeShape = window.currentDetectedShape || 'oval';
        const serverShape = result.shape ? result.shape.toLowerCase() : 'oval';

        // Prioritas: Real-time detection > Server response > Default
        const finalShape = realTimeShape;

        console.log('🎯 KONSISTENSI CHECK - Real-time shape:', realTimeShape);
        console.log('🎯 KONSISTENSI CHECK - Server shape:', serverShape);
        console.log('🎯 KONSISTENSI CHECK - Final shape (prioritas real-time):', finalShape);

        // Jika ada perbedaan, log warning tapi tetap gunakan real-time
        if (realTimeShape !== serverShape) {
            console.warn('⚠️ KONSISTENSI WARNING - Shape mismatch detected!');
            console.warn('Real-time detection:', realTimeShape);
            console.warn('Server response:', serverShape);
            console.warn('Using real-time detection as it\'s more accurate');
        }

        // Update faceLabel dengan animasi - gunakan hasil yang konsisten
        faceLabel.style.opacity = '0';
        faceLabel.innerText = `Shape: ${finalShape.toUpperCase()}`;
        requestAnimationFrame(() => {
            faceLabel.style.opacity = '1';
            faceLabel.style.transition = 'opacity 0.3s ease-in-out';
        });

        console.log('🎯 KONSISTENSI CHECK - Label updated to:', finalShape.toUpperCase());

        // Simpan data dengan optimasi
        const imageData = captureCanvas.toDataURL('image/jpeg', 0.85);
        sessionStorage.setItem('capturedImage', imageData);
        console.log('💾 Image saved to sessionStorage');

        // Pastikan semua data tersimpan dengan lengkap dan konsisten
        const completeResult = {
            shape: finalShape, // Gunakan bentuk dari real-time detection (prioritas utama)
            confidence: result.confidence || 95, // Confidence tinggi karena real-time detection
            alt_shape: serverShape !== finalShape ? serverShape : null, // Server shape sebagai alternatif jika berbeda
            alt_confidence: serverShape !== finalShape ? (result.confidence || 85) : 0,
            description: result.description || `Bentuk wajah ${finalShape} terdeteksi dengan akurat menggunakan analisis real-time`,
            shape_description: result.shape_description || `Bentuk wajah ${finalShape} dengan proporsi yang seimbang`,
            recommendations: result.recommendations || [],
            hairline_analysis: result.hairline_analysis || '',
            hair_type_analysis: result.hair_type_analysis || '',
            tips: result.tips || '',
            ratios: result.ratios || {},
            image: result.image || '',
            success: true,
            timestamp: new Date().toISOString(),
            detection_source: 'real_time_priority', // Menandakan sumber deteksi
            // Data untuk debugging dan konsistensi
            real_time_shape: realTimeShape,
            server_shape: serverShape,
            original_server_response: result
        };

        sessionStorage.setItem('faceAnalysisResult', JSON.stringify(completeResult));
        console.log('💾 Complete result saved to sessionStorage:', completeResult);

        // Verify data was saved correctly
        const savedData = sessionStorage.getItem('faceAnalysisResult');
        if (savedData) {
            console.log('✅ Data verification successful');
        } else {
            console.error('❌ Data verification failed - sessionStorage is empty');
        }

        // Scan animation sudah dihapus - tidak perlu dinonaktifkan

        // Tampilkan pesan sukses dengan tombol manual
        statusText.innerHTML = 'Analisis berhasil! <br><small>Mengarahkan ke hasil...</small>';
        statusBox.style.backgroundColor = 'rgba(34, 197, 94, 0.9)';

        // Tambahkan tombol manual redirect sebagai backup
        const manualBtn = document.createElement('button');
        manualBtn.innerHTML = '👁️ Lihat Hasil';
        manualBtn.style.cssText = `
            position: absolute;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: #22c55e;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            z-index: 30;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            display: none;
        `;
        manualBtn.onclick = () => {
            window.location.href = `output.php?face_shape=${finalShape}`;
        };
        document.body.appendChild(manualBtn);

        // Tampilkan tombol manual setelah 2 detik jika redirect belum terjadi
        setTimeout(() => {
            if (document.body.contains(manualBtn)) {
                manualBtn.style.display = 'block';
                statusText.innerHTML = 'Analisis berhasil! <br><small>Klik tombol di bawah untuk melihat hasil</small>';
            }
        }, 2000);

        // 🚀 REDIRECT CEPAT: Langsung redirect tanpa delay
        console.log('🚀 REDIRECT - Preparing to redirect...');
        console.log('🚀 REDIRECT - Final shape:', finalShape);
        console.log('🚀 REDIRECT - Target URL:', `output.php?face_shape=${finalShape}`);

        // Pastikan data tersimpan sebelum redirect
        const verifyData = sessionStorage.getItem('faceAnalysisResult');
        if (verifyData) {
            console.log('🚀 REDIRECT - Data verified, redirecting now...');

            // Redirect langsung tanpa setTimeout
            window.location.href = `output.php?face_shape=${finalShape}`;
        } else {
            console.error('🚀 REDIRECT - Data not found, retrying...');

            // Retry setelah delay singkat jika data belum tersimpan
            setTimeout(() => {
                const retryData = sessionStorage.getItem('faceAnalysisResult');
                if (retryData) {
                    console.log('🚀 REDIRECT - Retry successful, redirecting...');
                    window.location.href = `output.php?face_shape=${finalShape}`;
                } else {
                    console.error('🚀 REDIRECT - Retry failed, manual redirect required');
                    statusText.innerText = 'Klik untuk melihat hasil';
                    statusBox.style.cursor = 'pointer';
                    statusBox.onclick = () => {
                        window.location.href = `output.php?face_shape=${finalShape}`;
                    };
                }
            }, 100);
        }

    } catch (error) {
        console.error('Error:', error);
        statusText.innerText = 'Error: ' + error.message;
        statusBox.style.backgroundColor = 'rgba(220, 38, 38, 0.9)';
        
        // Scan animation sudah dihapus - tidak perlu dinonaktifkan
        
        // Reset tombol setelah error
        captureBtn.disabled = false;
        captureBtn.style.opacity = '1';
        
        // Kembalikan status box ke normal setelah 3 detik
        setTimeout(() => {
            statusBox.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            statusText.innerText = 'Mendeteksi Wajah...';
        }, 3000);
    }
}

// Tambahkan style untuk animasi
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    .status-box {
        transition: background-color 0.3s ease-in-out;
    }
    
    #faceLabel {
        transition: opacity 0.3s ease-in-out;
    }
    
    body {
        transition: opacity 0.3s ease-in-out;
    }
    
    .capture-btn {
        transition: opacity 0.3s ease-in-out;
    }
    
    .capture-btn:disabled {
        cursor: not-allowed;
    }

    /* Pulse animation untuk tombol capture ketika siap */
    @keyframes captureReady {
        0% {
            transform: translateX(-50%) scale(1);
            box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
        }
        70% {
            transform: translateX(-50%) scale(1.05);
            box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
        }
        100% {
            transform: translateX(-50%) scale(1);
            box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
        }
    }
`;
document.head.appendChild(style);

// Clean up before page unload
window.addEventListener('beforeunload', () => {
    stopVideo();
});

if (typeof faceapi === 'undefined') {
    alert('face-api.js gagal dimuat. Cek koneksi internet atau CDN.');
    throw new Error('face-api.js not loaded');
}

// Debug mode functions
let debugMode = false;

function toggleDebugMode() {
    debugMode = !debugMode;
    const debugBtn = document.getElementById('debugBtn');

    if (debugMode) {
        debugBtn.style.backgroundColor = '#ef4444';
        debugBtn.title = 'Debug Mode ON - Click to disable';
        console.log('🐛 Debug mode enabled');

        // Show debug info
        showDebugInfo();
    } else {
        debugBtn.style.backgroundColor = '#6b7280';
        debugBtn.title = 'Debug Mode OFF - Click to enable';
        console.log('🐛 Debug mode disabled');

        // Hide debug info
        hideDebugInfo();
    }
}

function showDebugInfo() {
    // Create debug overlay
    const debugOverlay = document.createElement('div');
    debugOverlay.id = 'debugOverlay';
    debugOverlay.style.cssText = `
        position: fixed;
        top: 60px;
        left: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-family: monospace;
        font-size: 12px;
        z-index: 1000;
        max-width: 300px;
    `;

    debugOverlay.innerHTML = `
        <div><strong>Debug Info:</strong></div>
        <div>Video: ${video.videoWidth}x${video.videoHeight}</div>
        <div>Canvas: ${canvas.width}x${canvas.height}</div>
        <div>Face API: ${typeof faceapi !== 'undefined' ? 'Loaded' : 'Not loaded'}</div>
        <div>SessionStorage: ${typeof Storage !== 'undefined' ? 'Available' : 'Not available'}</div>
        <div id="debugStatus">Status: Ready</div>
    `;

    document.body.appendChild(debugOverlay);
}

function hideDebugInfo() {
    const debugOverlay = document.getElementById('debugOverlay');
    if (debugOverlay) {
        debugOverlay.remove();
    }
}

// Show debug button on double click
document.addEventListener('dblclick', function(e) {
    if (e.target === document.body || e.target.classList.contains('camera-container')) {
        const debugBtn = document.getElementById('debugBtn');
        debugBtn.style.display = debugBtn.style.display === 'none' ? 'flex' : 'none';
    }
});

// Update debug status
function updateDebugStatus(status) {
    const debugStatus = document.getElementById('debugStatus');
    if (debugStatus) {
        debugStatus.textContent = 'Status: ' + status;
    }
}
    </script>

    <style>
        html, body {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

        .camera-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: #000;
            margin: 0;
            padding: 0;
        }

        #video, #canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            object-fit: cover;
            margin: 0;
            padding: 0;
        }

        .capture-btn {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #22c55e;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .capture-btn:hover {
            background: #16a34a;
            transform: translateX(-50%) scale(1.1);
        }
    
        
        body {
            font-family: 'Poppins', sans-serif;
        }
        
        .camera-container {
            position: relative;
            width: 100%;
            height: 100%;
            background: #000;
        }
        
        #video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        #canvas {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
        }
        
        .capture-btn {
            position: absolute;
            left: 50%;
            bottom: 40px;
            transform: translateX(-50%);
            z-index: 20;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #10B981;
            border: 4px solid white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .capture-btn:hover {
            transform: translateX(-50%) scale(1.1);
        }
        
        .capture-btn:active {
            transform: translateX(-50%) scale(0.95);
        }
        
        .capture-btn i {
            color: white;
            font-size: 1.5rem;
        }
        
        .status-box {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 0.9rem;
            z-index: 10;
        }

        /* Scan Animation dihapus untuk performa lebih cepat */

        /* Glow effect for face box */
        #faceBox {
            box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
            animation: glow 2s ease-in-out infinite;
        }

        @keyframes glow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
            }
            50% {
                box-shadow: 0 0 30px rgba(34, 197, 94, 0.5);
            }
        }

        /* Pulse animation for face label */
        #faceLabel {
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        /* Debug button */
        .debug-btn {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #6b7280;
            border: 2px solid white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            z-index: 25;
        }

        .debug-btn:hover {
            transform: scale(1.1);
        }
    </style>
</body>
</html>

