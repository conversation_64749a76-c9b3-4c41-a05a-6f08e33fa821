<?php
header('Content-Type: application/json');

function processDirectOverlay($userImagePath, $overlayImagePath) {
    try {
        if (!file_exists($userImagePath)) {
            return ['success' => false, 'error' => 'User image not found'];
        }
        
        if (!file_exists($overlayImagePath)) {
            return ['success' => false, 'error' => 'Overlay image not found'];
        }
        
        // Call Python script for direct overlay
        $pythonScript = __DIR__ . '/direct_image_overlay.py';
        $command = sprintf('python "%s" "%s" "%s"', $pythonScript, $userImagePath, $overlayImagePath);
        
        $output = shell_exec($command . ' 2>&1');
        
        if (!$output) {
            return ['success' => false, 'error' => 'Direct overlay processing failed'];
        }
        
        $result = json_decode($output, true);
        
        if (!$result) {
            return ['success' => false, 'error' => 'Invalid processing output', 'debug' => $output];
        }
        
        return $result;
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $userImage = $input['user_image'] ?? '';
    $overlayImage = $input['overlay_image'] ?? '';
    
    if (empty($userImage) || empty($overlayImage)) {
        echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
        exit;
    }
    
    $result = processDirectOverlay($userImage, $overlayImage);
    echo json_encode($result);
    exit;
}

echo json_encode(['success' => false, 'error' => 'Invalid request method']);
?>