<?php
class Hair3DApplier {
    private $hair_segments_dir = 'hair_segments/';
    private $output_dir = 'uploads/3d_results/';
    
    public function __construct() {
        if (!is_dir($this->hair_segments_dir)) {
            mkdir($this->hair_segments_dir, 0755, true);
        }
        if (!is_dir($this->output_dir)) {
            mkdir($this->output_dir, 0755, true);
        }
    }
    
    public function extractHairFrom3DModel($modelImagePath, $hairStyle) {
        try {
            $pythonScript = __DIR__ . '/hair_3d_extractor.py';
            $command = "python \"$pythonScript\" \"$modelImagePath\" \"$hairStyle\"";
            
            $output = shell_exec($command . ' 2>&1');
            $result = json_decode($output, true);
            
            if (!$result || !$result['success']) {
                return ['success' => false, 'error' => 'Hair extraction failed'];
            }
            
            return $result;
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    public function apply3DHairToUser($userImagePath, $extractedHairPath, $faceShape) {
        try {
            // Load user image and extracted hair
            $userImg = imagecreatefromstring(file_get_contents($userImagePath));
            $hairImg = imagecreatefrompng($extractedHairPath);
            
            if (!$userImg || !$hairImg) {
                return ['success' => false, 'error' => 'Failed to load images'];
            }
            
            // Get dimensions
            $userWidth = imagesx($userImg);
            $userHeight = imagesy($userImg);
            $hairWidth = imagesx($hairImg);
            $hairHeight = imagesy($hairImg);
            
            // Calculate positioning based on face shape
            $position = $this->calculateHairPosition($faceShape, $userWidth, $userHeight);
            
            // Scale hair to fit user's head
            $scaledHairWidth = intval($userWidth * 0.8);
            $scaledHairHeight = intval($hairHeight * ($scaledHairWidth / $hairWidth));
            
            $scaledHair = imagecreatetruecolor($scaledHairWidth, $scaledHairHeight);
            imagealphablending($scaledHair, false);
            imagesavealpha($scaledHair, true);
            
            imagecopyresampled($scaledHair, $hairImg, 0, 0, 0, 0, 
                             $scaledHairWidth, $scaledHairHeight, $hairWidth, $hairHeight);
            
            // Create result image
            $result = imagecreatetruecolor($userWidth, $userHeight);
            imagecopy($result, $userImg, 0, 0, 0, 0, $userWidth, $userHeight);
            
            // Apply hair with blending
            imagecopymerge($result, $scaledHair, 
                          $position['x'], $position['y'], 
                          0, 0, $scaledHairWidth, $scaledHairHeight, 85);
            
            // Save result
            $outputPath = $this->output_dir . uniqid('3d_hair_') . '.png';
            imagepng($result, $outputPath);
            
            // Cleanup
            imagedestroy($userImg);
            imagedestroy($hairImg);
            imagedestroy($scaledHair);
            imagedestroy($result);
            
            return [
                'success' => true,
                'result_path' => $outputPath,
                'result_url' => $outputPath
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    private function calculateHairPosition($faceShape, $width, $height) {
        $positions = [
            'oval' => ['x' => 0.1, 'y' => 0.05],
            'round' => ['x' => 0.1, 'y' => 0.03],
            'square' => ['x' => 0.1, 'y' => 0.08],
            'heart' => ['x' => 0.1, 'y' => 0.06],
            'rectangular' => ['x' => 0.1, 'y' => 0.04]
        ];
        
        $pos = $positions[strtolower($faceShape)] ?? $positions['oval'];
        
        return [
            'x' => intval($width * $pos['x']),
            'y' => intval($height * $pos['y'])
        ];
    }
    
    public function process3DHairModeling($userImagePath, $modelImagePath, $hairStyle, $faceShape) {
        // Step 1: Extract hair from 3D model
        $extractResult = $this->extractHairFrom3DModel($modelImagePath, $hairStyle);
        
        if (!$extractResult['success']) {
            return $extractResult;
        }
        
        // Step 2: Apply extracted hair to user
        $applyResult = $this->apply3DHairToUser(
            $userImagePath, 
            $extractResult['hair_segment_path'], 
            $faceShape
        );
        
        return array_merge($extractResult, $applyResult);
    }
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $userImagePath = $input['user_image'] ?? '';
    $modelImagePath = $input['model_image'] ?? '';
    $hairStyle = $input['hair_style'] ?? '';
    $faceShape = $input['face_shape'] ?? 'oval';
    
    if (empty($userImagePath) || empty($modelImagePath) || empty($hairStyle)) {
        echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
        exit;
    }
    
    $applier = new Hair3DApplier();
    $result = $applier->process3DHairModeling($userImagePath, $modelImagePath, $hairStyle, $faceShape);
    
    echo json_encode($result);
    exit;
}
?>