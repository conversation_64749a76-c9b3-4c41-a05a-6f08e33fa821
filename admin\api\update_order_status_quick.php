<?php
session_start();
require_once '../db_config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit();
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['order_id']) || !isset($input['status'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Order ID and status are required'
    ]);
    exit();
}

$order_id = (int)$input['order_id'];
$new_status = $input['status'];

// Validate status
$valid_statuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];
if (!in_array($new_status, $valid_statuses)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid status'
    ]);
    exit();
}

try {
    // Start transaction
    $conn->beginTransaction();
    
    // Check if order exists
    $check_stmt = $conn->prepare("SELECT id, order_status FROM orders WHERE id = ?");
    $check_stmt->execute([$order_id]);
    $order = $check_stmt->fetch();
    
    if (!$order) {
        $conn->rollback();
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Pesanan tidak ditemukan'
        ]);
        exit();
    }
    
    // Update order status
    $update_stmt = $conn->prepare("
        UPDATE orders 
        SET order_status = ?, updated_at = NOW() 
        WHERE id = ?
    ");
    $update_stmt->execute([$new_status, $order_id]);
    
    // Add order history log
    try {
        $history_stmt = $conn->prepare("
            INSERT INTO order_history (order_id, status, notes, admin_id, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        $history_stmt->execute([
            $order_id, 
            $new_status, 
            "Status diubah melalui quick update", 
            $_SESSION['admin_id']
        ]);
    } catch (PDOException $e) {
        // If order_history table doesn't exist, continue without logging
        error_log("Order history logging failed: " . $e->getMessage());
    }
    
    // Commit transaction
    $conn->commit();
    
    // Log the activity
    error_log("Admin {$_SESSION['admin_id']} updated order {$order_id} status from {$order['order_status']} to {$new_status}");
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Status pesanan berhasil diperbarui',
        'data' => [
            'order_id' => $order_id,
            'old_status' => $order['order_status'],
            'new_status' => $new_status
        ]
    ]);
    
} catch (PDOException $e) {
    // Rollback transaction on error
    $conn->rollback();
    
    // Log the error
    error_log("Error updating order status {$order_id}: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan database'
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    
    // Log the error
    error_log("Unexpected error updating order status {$order_id}: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan tidak terduga'
    ]);
}
?>
