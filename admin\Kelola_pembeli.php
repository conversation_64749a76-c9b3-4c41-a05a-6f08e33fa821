<?php
session_start();
require_once 'db_config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Fetch all pembeli (buyers) with aggregated order data
$stmt = $conn->prepare("
    SELECT 
        p.id, 
        p.nama_pembeli, 
        p.created_at, 
        COUNT(o.id) AS total_orders, 
        SUM(o.total_amount) AS total_spending,
        SUM(CASE WHEN o.order_status = 'pending' THEN 1 ELSE 0 END) AS pending_orders_count
    FROM 
        pembeli p
    LEFT JOIN 
        orders o ON p.id = o.pembeli_id
    GROUP BY 
        p.id, p.nama_pembeli, p.created_at
    ORDER BY 
        p.nama_pembeli ASC
");
$stmt->execute();
$pembeli = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ke<PERSON><PERSON> Pembeli - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            background: #2d3748;
            color: #fff;
        }
        .sidebar .logo {
            background: #1a202c;
            padding: 1rem;
        }
        .sidebar .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .sidebar nav ul li {
            transition: all 0.3s ease;
        }
        .sidebar nav ul li a {
            color: #fff;
        }
        .sidebar nav ul li.active {
            background:rgb(35, 71, 250);
        }
        .sidebar nav ul li.active a {
            color: #fff;
        }
        .sidebar nav ul li i {
            color: #4299e1;
        }
        .sidebar nav ul li.active i {
            color: #fff;
        }

        /* Delete button animations */
        .delete-btn {
            transition: all 0.2s ease;
        }
        .delete-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .delete-btn:disabled {
            transform: none !important;
            box-shadow: none !important;
        }

        /* Row animation */
        tbody tr {
            transition: all 0.3s ease;
        }
        tbody tr:hover {
            background-color: #f9fafb;
        }

        /* Notification animations */
        .notification {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Loading spinner */
        .fa-spin {
            animation: fa-spin 1s infinite linear;
        }
        @keyframes fa-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-white">
    <div class="flex h-screen bg-white">
        <!-- Sidebar -->
        <aside class="w-64 sidebar">
            <div class="logo">
                <h1 class="text-2xl font-bold">Admin Panel</h1>
            </div>
            <nav class="mt-4">
                <ul>
                    <li class="px-4 py-3">
                        <a href="index.php" class="flex items-center space-x-3">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <!-- Manajemen Booking Menu -->
                    <li>
                        <button onclick="toggleBookingSubmenu()" class="w-full flex items-center justify-between px-4 py-3 rounded-lg text-white focus:outline-none">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-calendar-check"></i>
                                <span>Manajemen Booking</span>
                            </span>
                            <i id="arrowBookingIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>

                        <!-- Submenu -->
                        <ul id="bookingSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="data_booking.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Data Booking</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_lokasi.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Kelola Lokasi</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_barber.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cut"></i>
                                    <span>Kelola Barber</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <script>
                        function toggleBookingSubmenu() {
                            const submenu = document.getElementById('bookingSubmenu');
                            const icon = document.getElementById('arrowBookingIcon');
                            submenu.classList.toggle('hidden');
                            icon.classList.toggle('rotate-180');
                        }
                    </script>

                    <li class="mb-2">
                        <button onclick="toggleSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-box"></i>
                                <span>Manajemen Produk</span>
                            </span>
                            <i id="arrowIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="submenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_produk.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cube"></i>
                                    <span>Produk</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_pembeli.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pembeli</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_metode_pembayaran.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-money-check-alt"></i>
                                    <span>Metode Pembayaran</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="mb-2">
                        <button onclick="toggleLowonganSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-briefcase"></i>
                                <span>Manajemen Lowongan</span>
                            </span>
                            <i id="arrowLowonganIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="lowonganSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_lowongan.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Kelola Lowongan</span>
                                </a>
                            </li>
                            <li>
                                <a href="data_pelamar.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-user-tie"></i>
                                    <span>Data Pelamar</span>
                                </a>
                            </li>
                            <li>
                                <a href="whatsapp_manager.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>WhatsApp Manager</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="px-4 py-3">
                        <a href="kelola_admin.php" class="flex items-center space-x-3">
                            <i class="fas fa-user-shield"></i>
                            <span>Kelola Admin</span>
                        </a>
                    </li>

                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col">
            <header class="flex items-center justify-between px-6 py-4 bg-white border-b-4 border-indigo-600">
                <div class="flex items-center">
                    <button class="text-gray-500 focus:outline-none">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-xl font-semibold ml-4">Kelola Pembeli</h1>
                </div>
                <div class="flex items-center">
                    <span class="mr-4 text-gray-700 hidden sm:block"><?php echo $_SESSION['admin_full_name'] ?? 'Admin'; ?></span>
                    <div class="relative">
                        <button id="profileDropdown" class="flex items-center focus:outline-none">
                            <?php
                            // Get admin profile photo
                            $stmt = $conn->prepare("SELECT profile_photo FROM admin WHERE id = ?");
                            $stmt->execute([$_SESSION['admin_id']]);
                            $profile_photo = $stmt->fetchColumn();

                            if (!empty($profile_photo) && file_exists('uploads/profiles/' . $profile_photo)):
                            ?>
                                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-300 hover:border-blue-500 transition-colors"
                                     src="uploads/profiles/<?= htmlspecialchars($profile_photo) ?>"
                                     alt="Admin avatar">
                            <?php else: ?>
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-300 hover:border-blue-500 transition-colors">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                            <?php endif; ?>
                            <i class="fas fa-chevron-down ml-2 text-gray-500 text-sm"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="profileDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="profile_settings.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-3 text-gray-500"></i>
                                Profile Settings
                            </a>
                            <hr class="my-1">
                            <a href="logout.php" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-3 text-red-500"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200 p-6">
                <!-- Pembeli List Section -->
                <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
                <h2 class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600">Daftar Pembeli</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border-collapse border">
                            <thead>
                                <tr>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">NO</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Nama Pembeli</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Tanggal Daftar</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Jumlah Pesanan</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Total Pengeluaran</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Status</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($pembeli)): ?>
                                    <?php $no = 1; // Initialize counter ?>
                                    <?php foreach ($pembeli as $buyer): ?>
                                        <tr>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo $no++; ?></td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo htmlspecialchars($buyer['nama_pembeli']); ?></td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo htmlspecialchars(date('d M Y', strtotime($buyer['created_at']))); ?></td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo (int)$buyer['total_orders']; ?></td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700">Rp <?php echo number_format($buyer['total_spending'], 0, ',', '.'); ?></td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700">
                                                <select class="block w-full rounded-md border-gray-300 shadow-sm text-sm focus:ring-0 focus:border-gray-300" disabled>
                                                    <?php
                                                        $status_text = 'Tidak Ada Pesanan';
                                                        if ($buyer['total_orders'] > 0) {
                                                            if ($buyer['pending_orders_count'] > 0) {
                                                                $status_text = 'Memiliki Pesanan Tertunda';
                                                            } else {
                                                                $status_text = 'Semua Pesanan Selesai';
                                                            }
                                                        }
                                                    ?>
                                                    <option value="<?php echo $status_text; ?>" selected><?php echo $status_text; ?></option>
                                                </select>
                                            </td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700">
                                                <div class="flex flex-col sm:flex-row gap-1 justify-center">
                                                    <a href="view_pembeli_orders.php?pembeli_id=<?php echo $buyer['id']; ?>"
                                                       class="bg-green-500 hover:bg-green-700 text-white text-xs font-bold py-1 px-2 rounded inline-flex items-center justify-center"
                                                       title="Kelola pesanan dari konfirmasi hingga pengiriman">
                                                        <i class="fas fa-shopping-cart mr-1"></i>Kelola Pesanan
                                                    </a>
                                                    <button onclick="deletePembeli(<?php echo $buyer['id']; ?>, '<?php echo htmlspecialchars($buyer['nama_pembeli'], ENT_QUOTES); ?>')"
                                                            class="bg-red-500 hover:bg-red-700 text-white text-xs font-bold py-1 px-2 rounded inline-flex items-center justify-center delete-btn"
                                                            title="Hapus pembeli dan semua data terkait"
                                                            data-pembeli-id="<?php echo $buyer['id']; ?>">
                                                        <i class="fas fa-trash mr-1"></i>Hapus
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="py-4 text-center text-gray-500 border">
                                            Tidak ada data pembeli yang tersedia.
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

<script>
    function toggleSubmenu() {
        const submenu = document.getElementById('submenu');
        const icon = document.getElementById('arrowIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleLowonganSubmenu() {
        const submenu = document.getElementById('lowonganSubmenu');
        const icon = document.getElementById('arrowLowonganIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleBookingSubmenu() {
        const submenu = document.getElementById('bookingSubmenu');
        const icon = document.getElementById('arrowBookingIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Set active class for current page in sidebar
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.sidebar nav ul li a');
        navLinks.forEach(link => {
            // Remove active class from all direct links initially to ensure only one is active
            link.closest('li').classList.remove('active');

            if (currentPath.includes(link.getAttribute('href'))) { // Use includes for partial match
                link.closest('li').classList.add('active'); // Make the specific link active

                // If this active link is inside a submenu, open the submenu and activate its parent button's LI
                const parentUl = link.closest('ul');
                if (parentUl && (parentUl.id === 'submenu' || parentUl.id === 'bookingSubmenu' || parentUl.id === 'lowonganSubmenu')) {
                    parentUl.classList.remove('hidden'); // Show the submenu

                    // Get the button element that controls this submenu
                    const parentButton = parentUl.previousElementSibling;
                    if (parentButton) {
                        parentButton.querySelector('i').classList.add('rotate-180'); // Rotate arrow for parent button
                        // IMPORTANT: Ensure the parent LI (which contains the button) does NOT get the 'active' background
                        const parentLiWithButton = parentButton.closest('li');
                        if (parentLiWithButton) {
                            parentLiWithButton.classList.remove('active');
                        }
                    }
                }
            }
        });

        // Special handling for kelola_pembeli.php and view_pembeli_orders.php to keep 'Data Pembeli' active
        if (currentPath.includes('kelola_pembeli.php') || currentPath.includes('view_pembeli_orders.php')) {
            const dataPembeliLink = document.querySelector('a[href="kelola_pembeli.php"]');
            if (dataPembeliLink) {
                dataPembeliLink.closest('li').classList.add('active');
                // Also ensure the parent submenu is open
                const submenu = document.getElementById('submenu');
                const arrowIcon = document.getElementById('arrowIcon');
                if (submenu && submenu.classList.contains('hidden')) {
                    submenu.classList.remove('hidden');
                    arrowIcon.classList.add('rotate-180');
                }
            }
        }

        // Profile dropdown functionality
        const profileDropdown = document.getElementById('profileDropdown');
        const profileDropdownMenu = document.getElementById('profileDropdownMenu');

        if (profileDropdown && profileDropdownMenu) {
            profileDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdownMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdownMenu.classList.add('hidden');
                }
            });
        }
    });

    // Delete pembeli function with AJAX
    async function deletePembeli(pembeliId, pembeliName) {
        // Show confirmation dialog
        const confirmed = confirm(
            `Apakah Anda yakin ingin menghapus pembeli "${pembeliName}" dan semua data terkaitnya?\n\n` +
            `Data yang akan dihapus:\n` +
            `• Data pembeli\n` +
            `• Semua pesanan\n` +
            `• Alamat pengiriman\n` +
            `• Riwayat pesanan\n\n` +
            `Penghapusan ini bersifat PERMANEN dan tidak dapat dibatalkan!`
        );

        if (!confirmed) {
            return;
        }

        // Get the button element
        const deleteBtn = document.querySelector(`[data-pembeli-id="${pembeliId}"]`);
        const originalContent = deleteBtn.innerHTML;

        try {
            // Show loading state
            deleteBtn.disabled = true;
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Menghapus...';
            deleteBtn.classList.add('opacity-50', 'cursor-not-allowed');

            // Send AJAX request
            const response = await fetch('api/delete_pembeli.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    pembeli_id: pembeliId
                })
            });

            const result = await response.json();

            if (result.success) {
                // Show detailed success message
                let detailMessage = result.message;
                if (result.data && result.data.total_deleted > 0) {
                    detailMessage += `\n\nData yang dihapus:`;
                    if (result.data.deleted_orders > 0) detailMessage += `\n• ${result.data.deleted_orders} pesanan`;
                    if (result.data.deleted_items > 0) detailMessage += `\n• ${result.data.deleted_items} item pesanan`;
                    if (result.data.deleted_addresses > 0) detailMessage += `\n• ${result.data.deleted_addresses} alamat`;
                    if (result.data.deleted_history > 0) detailMessage += `\n• ${result.data.deleted_history} riwayat pesanan`;
                    if (result.data.deleted_notifications > 0) detailMessage += `\n• ${result.data.deleted_notifications} notifikasi`;
                    if (result.data.deleted_other > 0) detailMessage += `\n• ${result.data.deleted_other} data lainnya`;
                }

                showNotification(detailMessage, 'success');

                // Remove the row from table with animation
                const row = deleteBtn.closest('tr');
                row.style.transition = 'all 0.3s ease';
                row.style.backgroundColor = '#fee2e2';
                row.style.transform = 'scale(0.95)';
                row.style.opacity = '0.5';

                setTimeout(() => {
                    row.remove();

                    // Check if table is empty
                    const tbody = document.querySelector('tbody');
                    const remainingRows = tbody.querySelectorAll('tr').length;

                    if (remainingRows === 0) {
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="7" class="py-4 text-center text-gray-500 border">
                                    Tidak ada data pembeli yang tersedia.
                                </td>
                            </tr>
                        `;
                    } else {
                        // Update row numbers
                        updateRowNumbers();
                    }
                }, 300);

            } else {
                // Show error message
                showNotification(result.message || 'Gagal menghapus pembeli', 'error');

                // Restore button state
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = originalContent;
                deleteBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }

        } catch (error) {
            console.error('Error deleting pembeli:', error);
            showNotification('Terjadi kesalahan jaringan. Silakan coba lagi.', 'error');

            // Restore button state
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = originalContent;
            deleteBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }

    // Show notification function
    function showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification fixed top-4 right-4 z-50 px-6 py-4 rounded-lg shadow-lg max-w-md transition-all duration-300 transform translate-x-full`;

        // Format message (convert \n to <br> for HTML display)
        const formattedMessage = message.replace(/\n/g, '<br>');

        // Set colors based on type
        if (type === 'success') {
            notification.classList.add('bg-green-500', 'text-white');
            notification.innerHTML = `
                <div class="flex items-start">
                    <i class="fas fa-check-circle mr-3 mt-1 flex-shrink-0"></i>
                    <div class="flex-1">
                        <div class="font-medium mb-1">Berhasil!</div>
                        <div class="text-sm opacity-90">${formattedMessage}</div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        } else if (type === 'error') {
            notification.classList.add('bg-red-500', 'text-white');
            notification.innerHTML = `
                <div class="flex items-start">
                    <i class="fas fa-exclamation-circle mr-3 mt-1 flex-shrink-0"></i>
                    <div class="flex-1">
                        <div class="font-medium mb-1">Error!</div>
                        <div class="text-sm opacity-90">${formattedMessage}</div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        } else {
            notification.classList.add('bg-blue-500', 'text-white');
            notification.innerHTML = `
                <div class="flex items-start">
                    <i class="fas fa-info-circle mr-3 mt-1 flex-shrink-0"></i>
                    <div class="flex-1">
                        <div class="font-medium mb-1">Info</div>
                        <div class="text-sm opacity-90">${formattedMessage}</div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }

        // Add to page
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 8 seconds (longer for detailed messages)
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 8000);
    }

    // Update row numbers after deletion
    function updateRowNumbers() {
        const rows = document.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            const numberCell = row.querySelector('td:first-child');
            if (numberCell && !numberCell.getAttribute('colspan')) {
                numberCell.textContent = index + 1;
            }
        });
    }
</script>
</body>
</html>
