import cv2
import numpy as np
import dlib
import json
import sys
import os
import base64
from PIL import Image, ImageFilter, ImageEnhance, ImageDraw
import mediapipe as mp

class Professional3DFaceSystem:
    def __init__(self):
        # Initialize MediaPipe
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5
        )
        
        # Initialize dlib (fallback)
        self.detector = dlib.get_frontal_face_detector()
        try:
            self.predictor = dlib.shape_predictor('models/shape_predictor_68_face_landmarks.dat')
        except:
            self.predictor = None
    
    def detect_face_landmarks_advanced(self, image):
        """Advanced face landmark detection using MediaPipe + dlib"""
        landmarks = None
        method_used = "none"
        
        # Try MediaPipe first (more accurate)
        try:
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(rgb_image)
            
            if results.multi_face_landmarks:
                face_landmarks = results.multi_face_landmarks[0]
                h, w = image.shape[:2]
                
                # Convert to pixel coordinates
                landmarks = []
                for landmark in face_landmarks.landmark:
                    x = int(landmark.x * w)
                    y = int(landmark.y * h)
                    z = landmark.z
                    landmarks.append([x, y, z])
                
                landmarks = np.array(landmarks)
                method_used = "mediapipe"
        except:
            pass
        
        # Fallback to dlib if MediaPipe fails
        if landmarks is None and self.predictor:
            try:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                faces = self.detector(gray)
                
                if len(faces) > 0:
                    shape = self.predictor(gray, faces[0])
                    landmarks = []
                    for i in range(68):
                        landmarks.append([shape.part(i).x, shape.part(i).y, 0])
                    landmarks = np.array(landmarks)
                    method_used = "dlib"
            except:
                pass
        
        return landmarks, method_used
    
    def create_professional_3d_face(self, image, landmarks, method_used):
        """Create professional 3D face model"""
        # Stage 1: Base enhancement
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # Professional contrast enhancement
        enhancer = ImageEnhance.Contrast(pil_image)
        pil_image = enhancer.enhance(2.2)  # Professional level
        
        # Ultra-sharp details
        enhancer = ImageEnhance.Sharpness(pil_image)
        pil_image = enhancer.enhance(2.0)  # Maximum sharpness
        
        # Vibrant professional colors
        enhancer = ImageEnhance.Color(pil_image)
        pil_image = enhancer.enhance(1.8)  # High saturation
        
        # Professional brightness
        enhancer = ImageEnhance.Brightness(pil_image)
        pil_image = enhancer.enhance(1.3)  # Bright and clear
        
        # Convert back to OpenCV
        result = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        # Stage 2: Advanced lighting based on landmarks
        if landmarks is not None:
            result = self.apply_professional_lighting(result, landmarks, method_used)
        else:
            result = self.apply_studio_lighting(result)
        
        # Stage 3: Professional depth and shadows
        result = self.apply_professional_depth_effects(result, landmarks)
        
        # Stage 4: Final professional polish
        result = self.apply_professional_finish(result)
        
        return result
    
    def apply_professional_lighting(self, image, landmarks, method_used):
        """Apply professional studio lighting based on face structure"""
        h, w = image.shape[:2]
        
        # Get key facial features
        if method_used == "mediapipe" and len(landmarks) > 100:
            # MediaPipe landmarks (468 points)
            nose_tip = landmarks[1]
            left_cheek = landmarks[234]
            right_cheek = landmarks[454]
            forehead = landmarks[10]
            chin = landmarks[152]
        elif method_used == "dlib" and len(landmarks) >= 68:
            # dlib landmarks (68 points)
            nose_tip = landmarks[30]
            left_cheek = landmarks[1]
            right_cheek = landmarks[15]
            forehead = [landmarks[27][0], landmarks[27][1] - 40]
            chin = landmarks[8]
        else:
            # Generic positioning
            nose_tip = [w//2, h//2]
            left_cheek = [w//4, h//2]
            right_cheek = [3*w//4, h//2]
            forehead = [w//2, h//4]
            chin = [w//2, 3*h//4]
        
        # Create professional lighting setup
        lighting_map = np.ones((h, w), dtype=np.float32)
        y_coords, x_coords = np.ogrid[:h, :w]
        
        # Key light (main light from top-left)
        key_light_pos = [w * 0.2, h * 0.1]
        key_distances = np.sqrt((x_coords - key_light_pos[0])**2 + (y_coords - key_light_pos[1])**2)
        key_intensity = 1.0 - (key_distances / np.sqrt(w**2 + h**2)) * 0.5
        key_intensity = np.clip(key_intensity, 0.5, 1.4)
        
        # Fill light (softer light from right)
        fill_light_pos = [w * 0.8, h * 0.3]
        fill_distances = np.sqrt((x_coords - fill_light_pos[0])**2 + (y_coords - fill_light_pos[1])**2)
        fill_intensity = 1.0 - (fill_distances / np.sqrt(w**2 + h**2)) * 0.3
        fill_intensity = np.clip(fill_intensity, 0.7, 1.1)
        
        # Rim light (edge lighting)
        rim_light_pos = [w * 0.9, h * 0.2]
        rim_distances = np.sqrt((x_coords - rim_light_pos[0])**2 + (y_coords - rim_light_pos[1])**2)
        rim_intensity = np.exp(-rim_distances / 100) * 0.3
        
        # Combine all lights
        combined_lighting = (key_intensity * 0.6 + fill_intensity * 0.3 + rim_intensity * 0.1)
        combined_lighting = np.clip(combined_lighting, 0.6, 1.5)
        
        # Apply lighting
        result = image.copy().astype(np.float32)
        for i in range(3):
            result[:, :, i] *= combined_lighting
        
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def apply_studio_lighting(self, image):
        """Apply generic studio lighting"""
        h, w = image.shape[:2]
        y_coords, x_coords = np.ogrid[:h, :w]
        
        # Professional 3-point lighting setup
        # Key light
        key_x, key_y = w * 0.25, h * 0.15
        key_dist = np.sqrt((x_coords - key_x)**2 + (y_coords - key_y)**2)
        key_light = 1.0 - (key_dist / np.sqrt(w**2 + h**2)) * 0.4
        
        # Fill light
        fill_x, fill_y = w * 0.75, h * 0.25
        fill_dist = np.sqrt((x_coords - fill_x)**2 + (y_coords - fill_y)**2)
        fill_light = 1.0 - (fill_dist / np.sqrt(w**2 + h**2)) * 0.2
        
        # Back light
        back_x, back_y = w * 0.5, h * 0.1
        back_dist = np.sqrt((x_coords - back_x)**2 + (y_coords - back_y)**2)
        back_light = np.exp(-back_dist / 80) * 0.2
        
        # Combine lights
        combined = np.clip((key_light * 0.5 + fill_light * 0.3 + back_light * 0.2), 0.6, 1.4)
        
        # Apply to image
        result = image.copy().astype(np.float32)
        for i in range(3):
            result[:, :, i] *= combined
        
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def apply_professional_depth_effects(self, image, landmarks):
        """Apply professional depth and shadow effects"""
        h, w = image.shape[:2]
        
        # Create multiple shadow layers for realistic depth
        shadows = []
        
        # Deep shadow (8px offset)
        if h > 8 and w > 8:
            shadow1 = np.zeros_like(image)
            shadow1[8:, 8:] = image[:-8, :-8]
            shadow1 = (shadow1 * 0.1).astype(np.uint8)
            shadows.append((shadow1, 0.25))
        
        # Medium shadow (4px offset)
        if h > 4 and w > 4:
            shadow2 = np.zeros_like(image)
            shadow2[4:, 4:] = image[:-4, :-4]
            shadow2 = (shadow2 * 0.2).astype(np.uint8)
            shadows.append((shadow2, 0.2))
        
        # Soft shadow (2px offset)
        if h > 2 and w > 2:
            shadow3 = np.zeros_like(image)
            shadow3[2:, 2:] = image[:-2, :-2]
            shadow3 = (shadow3 * 0.4).astype(np.uint8)
            shadows.append((shadow3, 0.15))
        
        # Subtle shadow (1px offset)
        if h > 1 and w > 1:
            shadow4 = np.zeros_like(image)
            shadow4[1:, 1:] = image[:-1, :-1]
            shadow4 = (shadow4 * 0.6).astype(np.uint8)
            shadows.append((shadow4, 0.1))
        
        # Blend all shadows
        result = image.copy().astype(np.float32)
        for shadow, weight in shadows:
            result = result * (1 - weight) + shadow.astype(np.float32) * weight
        
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def apply_professional_finish(self, image):
        """Apply final professional finishing touches"""
        # Convert to PIL for advanced effects
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # Professional glow effect
        glow = pil_image.filter(ImageFilter.GaussianBlur(4))
        
        # Edge enhancement for 3D pop
        edges = pil_image.filter(ImageFilter.EDGE_ENHANCE_MORE)
        
        # Unsharp mask for professional sharpening
        unsharp = pil_image.filter(ImageFilter.UnsharpMask(radius=2, percent=150, threshold=3))
        
        # Combine all effects
        result_array = np.array(pil_image).astype(np.float32)
        glow_array = np.array(glow).astype(np.float32)
        edges_array = np.array(edges).astype(np.float32)
        unsharp_array = np.array(unsharp).astype(np.float32)
        
        # Professional blend: 60% original + 20% glow + 10% edges + 10% unsharp
        final_result = (result_array * 0.6 + 
                       glow_array * 0.2 + 
                       edges_array * 0.1 + 
                       unsharp_array * 0.1)
        
        final_result = np.clip(final_result, 0, 255).astype(np.uint8)
        
        return cv2.cvtColor(final_result, cv2.COLOR_RGB2BGR)
    
    def apply_hairstyle_with_landmarks(self, face_3d, hairstyle_path, landmarks, method_used):
        """Apply hairstyle using precise landmark positioning"""
        try:
            # Load hairstyle
            hairstyle = cv2.imread(hairstyle_path, cv2.IMREAD_UNCHANGED)
            if hairstyle is None:
                return face_3d
            
            h, w = face_3d.shape[:2]
            
            # Calculate hair positioning based on landmarks
            if landmarks is not None:
                if method_used == "mediapipe" and len(landmarks) > 100:
                    # Use MediaPipe landmarks for precise positioning
                    forehead_top = landmarks[10]
                    left_temple = landmarks[234]
                    right_temple = landmarks[454]
                    
                    # Calculate hair region
                    hair_center_x = int((left_temple[0] + right_temple[0]) / 2)
                    hair_top_y = max(0, int(forehead_top[1] - h * 0.3))
                    hair_width = int(abs(right_temple[0] - left_temple[0]) * 1.4)
                    hair_height = int(h * 0.5)
                    
                elif method_used == "dlib" and len(landmarks) >= 68:
                    # Use dlib landmarks
                    forehead_center = landmarks[27]
                    left_face = landmarks[0]
                    right_face = landmarks[16]
                    
                    hair_center_x = int(forehead_center[0])
                    hair_top_y = max(0, int(forehead_center[1] - h * 0.25))
                    hair_width = int(abs(right_face[0] - left_face[0]) * 1.2)
                    hair_height = int(h * 0.45)
                else:
                    # Generic positioning
                    hair_center_x = w // 2
                    hair_top_y = int(h * 0.1)
                    hair_width = int(w * 0.8)
                    hair_height = int(h * 0.4)
            else:
                # Fallback positioning
                hair_center_x = w // 2
                hair_top_y = int(h * 0.1)
                hair_width = int(w * 0.8)
                hair_height = int(h * 0.4)
            
            # Resize hairstyle
            hair_resized = cv2.resize(hairstyle, (hair_width, hair_height))
            
            # Calculate position
            start_x = max(0, hair_center_x - hair_width // 2)
            start_y = max(0, hair_top_y)
            end_x = min(w, start_x + hair_width)
            end_y = min(h, start_y + hair_height)
            
            # Adjust if needed
            actual_width = end_x - start_x
            actual_height = end_y - start_y
            
            if actual_width > 0 and actual_height > 0:
                hair_final = cv2.resize(hair_resized, (actual_width, actual_height))
                
                # Apply with alpha blending if available
                if hair_final.shape[2] == 4:
                    hair_rgb = hair_final[:, :, :3]
                    alpha = hair_final[:, :, 3] / 255.0
                    
                    # Professional alpha blending
                    result = face_3d.copy().astype(np.float32)
                    for c in range(3):
                        result[start_y:end_y, start_x:end_x, c] = (
                            alpha * hair_rgb[:, :, c] + 
                            (1 - alpha) * result[start_y:end_y, start_x:end_x, c]
                        )
                    
                    return np.clip(result, 0, 255).astype(np.uint8)
                else:
                    # Simple overlay
                    result = face_3d.copy()
                    result[start_y:end_y, start_x:end_x] = cv2.addWeighted(
                        result[start_y:end_y, start_x:end_x], 0.3, 
                        hair_final, 0.7, 0
                    )
                    return result
            
            return face_3d
            
        except Exception as e:
            print(f"Hairstyle application error: {e}")
            return face_3d
    
    def process_professional_3d_face(self, user_image_path, hairstyle_path=None):
        """Complete professional 3D face processing"""
        try:
            # Load image
            image = cv2.imread(user_image_path)
            if image is None:
                return {'success': False, 'error': 'Failed to load user image'}
            
            # Detect landmarks with advanced method
            landmarks, method_used = self.detect_face_landmarks_advanced(image)
            
            # Create professional 3D face
            face_3d = self.create_professional_3d_face(image, landmarks, method_used)
            
            # Apply hairstyle with landmark-based positioning
            if hairstyle_path and os.path.exists(hairstyle_path):
                face_3d = self.apply_hairstyle_with_landmarks(face_3d, hairstyle_path, landmarks, method_used)
            
            # Save result
            output_dir = 'uploads/3d_results/'
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, f'professional_3d_{hash(user_image_path)}.png')
            cv2.imwrite(output_path, face_3d)
            
            # Convert to base64
            _, buffer = cv2.imencode('.png', face_3d)
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return {
                'success': True,
                'result_url': output_path,
                'result_base64': f"data:image/png;base64,{img_base64}",
                'landmarks_detected': landmarks is not None,
                'detection_method': method_used,
                'landmark_count': len(landmarks) if landmarks is not None else 0,
                'processing_quality': 'Professional'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

def main():
    if len(sys.argv) < 2:
        print(json.dumps({'success': False, 'error': 'No image path provided'}))
        return
    
    user_image = sys.argv[1]
    hairstyle_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    processor = Professional3DFaceSystem()
    result = processor.process_professional_3d_face(user_image, hairstyle_path)
    
    print(json.dumps(result))

if __name__ == "__main__":
    main()