<?php
session_start();
require_once 'Admin/db_config.php';

echo "<h2>Upload Debug Information</h2>";

// Check session
echo "<h3>1. Session Check:</h3>";
if (isset($_SESSION['user_id'])) {
    echo "✅ User logged in with ID: " . $_SESSION['user_id'] . "<br>";
} else {
    echo "❌ User not logged in<br>";
}

// Check database connection
echo "<h3>2. Database Connection:</h3>";
try {
    $stmt = $conn->query("SELECT 1");
    echo "✅ Database connection successful<br>";
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

// Check table structure
echo "<h3>3. Table Structure:</h3>";
try {
    $stmt = $conn->query("DESCRIBE pembeli");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $has_profile_picture = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'profile_picture') {
            $has_profile_picture = true;
            break;
        }
    }
    
    if ($has_profile_picture) {
        echo "✅ profile_picture column exists<br>";
    } else {
        echo "❌ profile_picture column missing<br>";
        echo "Adding column...<br>";
        $conn->exec("ALTER TABLE pembeli ADD COLUMN profile_picture VARCHAR(255)");
        echo "✅ Column added<br>";
    }
} catch (PDOException $e) {
    echo "❌ Table check failed: " . $e->getMessage() . "<br>";
}

// Check upload directory
echo "<h3>4. Upload Directory:</h3>";
$upload_dir = 'uploads/profile/';
if (!is_dir($upload_dir)) {
    echo "❌ Upload directory doesn't exist. Creating...<br>";
    if (mkdir($upload_dir, 0755, true)) {
        echo "✅ Upload directory created<br>";
    } else {
        echo "❌ Failed to create upload directory<br>";
    }
} else {
    echo "✅ Upload directory exists<br>";
}

// Check directory permissions
if (is_writable($upload_dir)) {
    echo "✅ Upload directory is writable<br>";
} else {
    echo "❌ Upload directory is not writable<br>";
    echo "Attempting to fix permissions...<br>";
    chmod($upload_dir, 0755);
    if (is_writable($upload_dir)) {
        echo "✅ Permissions fixed<br>";
    } else {
        echo "❌ Could not fix permissions<br>";
    }
}

// Check PHP upload settings
echo "<h3>5. PHP Upload Settings:</h3>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "post_max_size: " . ini_get('post_max_size') . "<br>";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "<br>";
echo "file_uploads: " . (ini_get('file_uploads') ? 'Enabled' : 'Disabled') . "<br>";

// Test upload form
echo "<h3>6. Test Upload Form:</h3>";
?>
<form action="api/upload_profile_picture.php" method="post" enctype="multipart/form-data">
    <input type="file" name="profile_picture" accept="image/*" required>
    <button type="submit">Test Upload</button>
</form>

<h3>7. API Test:</h3>
<button onclick="testAPI()">Test API Connection</button>
<div id="api-result"></div>

<script>
function testAPI() {
    fetch('api/upload_profile_picture.php', {
        method: 'POST',
        credentials: 'same-origin'
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('api-result').innerHTML = '<pre>' + data + '</pre>';
    })
    .catch(error => {
        document.getElementById('api-result').innerHTML = 'Error: ' + error;
    });
}
</script>

<br><br><a href="profil.php">← Back to Profile</a>
