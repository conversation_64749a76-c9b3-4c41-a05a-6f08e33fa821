<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

try {
    require_once '../Admin/db_config.php';
} catch (Exception $e) {
    $response = ['success' => false, 'message' => 'Database config error: ' . $e->getMessage()];
    echo json_encode($response);
    exit();
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$response = [
    'success' => false,
    'message' => '',
    'image_url' => ''
];

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $response['message'] = 'User not logged in';
    echo json_encode($response);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Method not allowed';
    echo json_encode($response);
    exit();
}

$user_id = $_SESSION['user_id'];

// Debug: Log all received data
error_log("POST data: " . print_r($_POST, true));
error_log("FILES data: " . print_r($_FILES, true));

// Check if file was uploaded
if (!isset($_FILES['profile_picture'])) {
    $response['message'] = 'No file uploaded. Expected field name: profile_picture';
    echo json_encode($response);
    exit();
}

if ($_FILES['profile_picture']['error'] !== UPLOAD_ERR_OK) {
    $upload_errors = [
        UPLOAD_ERR_INI_SIZE => 'File too large (exceeds upload_max_filesize)',
        UPLOAD_ERR_FORM_SIZE => 'File too large (exceeds MAX_FILE_SIZE)',
        UPLOAD_ERR_PARTIAL => 'File upload was only partial',
        UPLOAD_ERR_NO_FILE => 'No file was uploaded',
        UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
        UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
        UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
    ];

    $error_code = $_FILES['profile_picture']['error'];
    $response['message'] = 'Upload error: ' . ($upload_errors[$error_code] ?? 'Unknown error');
    echo json_encode($response);
    exit();
}

$file = $_FILES['profile_picture'];

// Validate file type
$allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
$file_type = $file['type'];

if (!in_array($file_type, $allowed_types)) {
    $response['message'] = 'File type not allowed. Please upload JPG, PNG, or GIF images only.';
    echo json_encode($response);
    exit();
}

// Validate file size (max 5MB)
$max_size = 5 * 1024 * 1024; // 5MB
if ($file['size'] > $max_size) {
    $response['message'] = 'File size too large. Maximum size is 5MB.';
    echo json_encode($response);
    exit();
}

try {
    // Ensure profile_picture column exists
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(255)");
    
    // Create upload directory if it doesn't exist
    $upload_dir = '../uploads/profile/';
    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            $response['message'] = 'Failed to create upload directory';
            echo json_encode($response);
            exit();
        }
    }

    // Check if directory is writable
    if (!is_writable($upload_dir)) {
        $response['message'] = 'Upload directory is not writable';
        echo json_encode($response);
        exit();
    }
    
    // Generate unique filename
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'profile_' . $user_id . '_' . time() . '.' . $file_extension;
    $file_path = $upload_dir . $filename;
    
    // Get current profile picture to delete old one
    $stmt = $conn->prepare("SELECT profile_picture FROM pembeli WHERE id = ?");
    $stmt->execute([$user_id]);
    $current_user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Debug file info
    error_log("Temp file: " . $file['tmp_name']);
    error_log("Target file: " . $file_path);
    error_log("File exists in temp: " . (file_exists($file['tmp_name']) ? 'Yes' : 'No'));

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        error_log("File moved successfully to: " . $file_path);

        // Delete old profile picture if exists
        if ($current_user && !empty($current_user['profile_picture'])) {
            $old_file_path = '../uploads/profile/' . $current_user['profile_picture'];
            if (file_exists($old_file_path)) {
                unlink($old_file_path);
                error_log("Old file deleted: " . $old_file_path);
            }
        }

        // Update database
        $update_stmt = $conn->prepare("UPDATE pembeli SET profile_picture = ?, updated_at = NOW() WHERE id = ?");
        $update_stmt->execute([$filename, $user_id]);

        $response['success'] = true;
        $response['message'] = 'Profile picture uploaded successfully';
        $response['image_url'] = 'uploads/profile/' . $filename;

    } else {
        $error = error_get_last();
        $response['message'] = 'Failed to move uploaded file. Error: ' . ($error['message'] ?? 'Unknown error');
        error_log("Move file failed. Last error: " . print_r($error, true));
    }
    
} catch (PDOException $e) {
    $response['message'] = 'Database error: ' . $e->getMessage();
    error_log("Upload profile picture error: " . $e->getMessage());
} catch (Exception $e) {
    $response['message'] = 'Upload error: ' . $e->getMessage();
    error_log("Upload profile picture error: " . $e->getMessage());
}

echo json_encode($response);
?>
