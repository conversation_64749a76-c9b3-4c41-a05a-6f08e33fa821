import sys
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont

def detect_face_box(image_path):
    img = cv2.imread(image_path)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
    if len(faces) > 0:
        x, y, w, h = faces[0]
        return x, y, w, h
    else:
        h_img, w_img = img.shape[:2]
        w = int(w_img * 0.4)
        h = int(h_img * 0.4)
        x = (w_img - w) // 2
        y = int(h_img * 0.18)
        return x, y, w, h

if len(sys.argv) < 7:
    print("Usage: python apply_hairstyle.py <user_image_path> <hairstyle_overlay_path> <score> <hairstyle_name> <model_reference_path> <output_path>")
    sys.exit(1)

user_image_path = sys.argv[1]
hairstyle_overlay_path = sys.argv[2]
score = int(sys.argv[3])
hairstyle_name = sys.argv[4]
model_reference_path = sys.argv[5]
output_path = sys.argv[6]

# Deteksi wajah otomatis
x, y, w, h = detect_face_box(user_image_path)

# Load images
face_img = Image.open(user_image_path).convert("RGBA")
hair_img = Image.open(hairstyle_overlay_path).convert("RGBA")
model_img = Image.open(model_reference_path).convert("RGBA")

# Resize hair overlay to match face width
hair_aspect = hair_img.width / hair_img.height
new_hair_width = w
new_hair_height = int(new_hair_width / hair_aspect)
hair_img_resized = hair_img.resize((new_hair_width, new_hair_height), Image.LANCZOS)

# Calculate overlay position (above face, with slight offset)
offset_y = y - int(new_hair_height * 0.7)
offset_y = max(0, offset_y)
offset_x = x

# Paste hair overlay on face
result_img = face_img.copy()
result_img.paste(hair_img_resized, (offset_x, offset_y), hair_img_resized)

# Prepare canvas for side-by-side display
margin = 30
canvas_width = result_img.width * 2 + margin
canvas_height = max(result_img.height, model_img.height) + 80
canvas = Image.new("RGBA", (canvas_width, canvas_height), (255, 255, 255, 255))

# Paste result and model reference
canvas.paste(result_img, (0, 40))
canvas.paste(model_img.resize((result_img.width, result_img.height), Image.LANCZOS), (result_img.width + margin, 40))

draw = ImageDraw.Draw(canvas)
try:
    font = ImageFont.truetype("arial.ttf", 28)
    font_small = ImageFont.truetype("arial.ttf", 20)
except:
    font = font_small = None
if font:
    draw.text((result_img.width // 2 - 40, 10), hairstyle_name, fill=(30, 30, 30), font=font)
    draw.text((result_img.width + margin + result_img.width // 2 - 40, 10), "Referensi Model", fill=(30, 30, 30), font=font_small)
    draw.text((result_img.width // 2 - 40, canvas_height - 35), f"Skor: {score}/10", fill=(0, 128, 0), font=font_small)
else:
    draw.text((result_img.width // 2 - 40, 10), hairstyle_name, fill=(30, 30, 30))
    draw.text((result_img.width + margin + result_img.width // 2 - 40, 10), "Referensi Model", fill=(30, 30, 30))
    draw.text((result_img.width // 2 - 40, canvas_height - 35), f"Skor: {score}/10", fill=(0, 128, 0))

canvas = canvas.convert("RGB")
canvas.save(output_path) 