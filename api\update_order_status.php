<?php
ini_set('display_errors', 0); // Temporary: Hide PHP errors from output
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING); // Temporary: Suppress notices and warnings
require_once '../Admin/db_config.php';

header('Content-Type: application/json');

$response = [
    'success' => false,
    'message' => ''
];

// Get the raw POST data
$input = file_get_contents('php://input');
$data = json_decode($input, true);

$orderId = $data['order_id'] ?? null;
$newStatus = $data['status'] ?? null;

if (!$orderId || !$newStatus) {
    $response['message'] = 'Invalid request: order_id or status missing.';
    echo json_encode($response);
    exit();
}

// Validate newStatus to prevent arbitrary input
$allowedStatuses = ['pending', 'confirmed'];
if (!in_array($newStatus, $allowedStatuses)) {
    $response['message'] = 'Invalid status value.';
    echo json_encode($response);
    exit();
}

try {
    // Prepare the base update statement
    $sql = "UPDATE orders SET order_status = :order_status";
    $params = ['order_status' => $newStatus, 'order_id' => $orderId];

    // If status is confirmed, also mark as not notified yet
    if ($newStatus === 'confirmed') {
        $sql .= ", user_notified = 0"; // Mark as not notified yet
    }

    $sql .= " WHERE id = :order_id";

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);

    if ($stmt->rowCount() > 0) {
        $response['success'] = true;
        $response['message'] = 'Order status updated successfully.';
    } else {
        $response['message'] = 'No order found with the given ID or status is already the same.';
    }
} catch (PDOException $e) {
    $response['message'] = 'Database Error: ' . $e->getMessage();
    error_log("Database Error in update_order_status.php: " . $e->getMessage());
}

echo json_encode($response);