<?php
session_start();
require_once '../db_config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit();
}

// Check if order_id is provided
if (!isset($_GET['order_id']) || empty($_GET['order_id'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Order ID is required'
    ]);
    exit();
}

$order_id = (int)$_GET['order_id'];

try {
    // Get order details
    $order_stmt = $conn->prepare("
        SELECT o.*, ua.address_name, ua.full_address, pm.name as payment_method_name,
               p.nama_pembeli
        FROM orders o 
        LEFT JOIN user_addresses ua ON o.address_id = ua.id
        LEFT JOIN payment_methods pm ON o.payment_method_id = pm.id
        LEFT JOIN pembeli p ON o.pembeli_id = p.id
        WHERE o.id = ?
    ");
    $order_stmt->execute([$order_id]);
    $order = $order_stmt->fetch();

    if (!$order) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Pesanan tidak ditemukan'
        ]);
        exit();
    }

    // Get order items
    $items_stmt = $conn->prepare("
        SELECT oi.*, p.nama_produk, p.gambar_utama
        FROM order_items oi 
        LEFT JOIN produk p ON oi.product_id = p.id 
        WHERE oi.order_id = ?
    ");
    $items_stmt->execute([$order_id]);
    $order['items'] = $items_stmt->fetchAll();

    // Get order history
    try {
        $history_stmt = $conn->prepare("
            SELECT oh.*, a.full_name as admin_name
            FROM order_history oh 
            LEFT JOIN admin a ON oh.admin_id = a.id
            WHERE oh.order_id = ? 
            ORDER BY oh.created_at DESC
        ");
        $history_stmt->execute([$order_id]);
        $order['history'] = $history_stmt->fetchAll();
    } catch (PDOException $e) {
        // If order_history table doesn't exist yet, set empty array
        $order['history'] = [];
    }

    // Return success response
    echo json_encode([
        'success' => true,
        'data' => $order
    ]);

} catch (PDOException $e) {
    // Log the error
    error_log("Error fetching order details {$order_id}: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan database'
    ]);
    
} catch (Exception $e) {
    // Log the error
    error_log("Unexpected error fetching order details {$order_id}: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan tidak terduga'
    ]);
}
?>
