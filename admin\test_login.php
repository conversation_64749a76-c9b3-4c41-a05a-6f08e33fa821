<?php
session_start();
require_once 'db_config.php';

echo "<h2>Test Login System</h2>";

// Test database connection
try {
    $stmt = $conn->query("SELECT 1");
    echo "<p style='color: green;'>✅ Database connection OK</p>";
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Test login process
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    echo "<h3>Testing Login for: $username</h3>";
    
    if (!empty($username) && !empty($password)) {
        try {
            // Check admin credentials from database
            $stmt = $conn->prepare("SELECT id, username, password, full_name, email, role, is_active FROM admin WHERE username = ?");
            $stmt->execute([$username]);
            $admin = $stmt->fetch();
            
            if ($admin) {
                echo "<p style='color: blue;'>ℹ️ Admin found in database</p>";
                echo "<p><strong>Username:</strong> " . $admin['username'] . "</p>";
                echo "<p><strong>Full Name:</strong> " . $admin['full_name'] . "</p>";
                echo "<p><strong>Email:</strong> " . $admin['email'] . "</p>";
                echo "<p><strong>Role:</strong> " . $admin['role'] . "</p>";
                echo "<p><strong>Is Active:</strong> " . ($admin['is_active'] ? 'Yes' : 'No') . "</p>";
                
                if ($admin['is_active'] != 1) {
                    echo "<p style='color: red;'>❌ Admin account is not active!</p>";
                } else {
                    // Test password verification
                    if (password_verify($password, $admin['password'])) {
                        echo "<p style='color: green;'>✅ Password verification successful!</p>";
                        echo "<p style='color: green;'>✅ Login should work!</p>";
                        
                        // Set session (for testing)
                        $_SESSION['admin_logged_in'] = true;
                        $_SESSION['admin_id'] = $admin['id'];
                        $_SESSION['admin_username'] = $admin['username'];
                        $_SESSION['admin_full_name'] = $admin['full_name'];
                        $_SESSION['admin_email'] = $admin['email'];
                        $_SESSION['admin_role'] = $admin['role'];
                        
                        echo "<p><a href='kelola_lowongan.php' style='color: blue;'>→ Go to Admin Dashboard</a></p>";
                        
                    } else {
                        echo "<p style='color: red;'>❌ Password verification failed!</p>";
                        echo "<p>Stored password hash: " . substr($admin['password'], 0, 50) . "...</p>";
                        
                        // Try to reset password
                        $new_password = password_hash($password, PASSWORD_DEFAULT);
                        $stmt = $conn->prepare("UPDATE admin SET password = ? WHERE id = ?");
                        $stmt->execute([$new_password, $admin['id']]);
                        echo "<p style='color: orange;'>🔄 Password has been reset. Try logging in again.</p>";
                    }
                }
            } else {
                echo "<p style='color: red;'>❌ No admin found with username: $username</p>";
                
                // Show all available usernames
                $stmt = $conn->query("SELECT username FROM admin WHERE is_active = 1");
                $usernames = $stmt->fetchAll(PDO::FETCH_COLUMN);
                echo "<p><strong>Available usernames:</strong> " . implode(', ', $usernames) . "</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Username and password are required!</p>";
    }
    
    echo "<hr>";
}

// Show login form
?>
<h3>Test Login Form</h3>
<form method="POST" style="border: 1px solid #ccc; padding: 20px; max-width: 400px;">
    <div style="margin-bottom: 15px;">
        <label for="username">Username:</label><br>
        <input type="text" id="username" name="username" value="admin" style="width: 100%; padding: 8px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="password">Password:</label><br>
        <input type="password" id="password" name="password" value="password" style="width: 100%; padding: 8px;">
    </div>
    
    <button type="submit" style="background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer;">
        Test Login
    </button>
</form>

<p><strong>Default credentials to try:</strong></p>
<ul>
    <li>Username: admin, Password: password</li>
    <li>Username: manager, Password: password</li>
    <li>Username: staff, Password: password</li>
</ul>

<p><a href="login.php">→ Go to actual login page</a></p>
