import cv2
import numpy as np
import dlib
import json
import sys
import os
import base64
from PIL import Image, ImageFilter, ImageEnhance

class Complete3DFaceProcessor:
    def __init__(self):
        # Initialize dlib face detector
        self.detector = dlib.get_frontal_face_detector()
        try:
            self.predictor = dlib.shape_predictor('models/shape_predictor_68_face_landmarks.dat')
        except:
            self.predictor = None
    
    def detect_face_landmarks(self, image):
        """Detect 68 face landmarks using dlib"""
        if not self.predictor:
            return None
            
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.detector(gray)
        
        if len(faces) > 0:
            landmarks = self.predictor(gray, faces[0])
            points = []
            for i in range(68):
                points.append([landmarks.part(i).x, landmarks.part(i).y])
            return np.array(points)
        return None
    
    def create_3d_face_model(self, image, landmarks):
        """Create 3D face model with proper depth and lighting"""
        h, w = image.shape[:2]
        
        # Convert to PIL for advanced processing
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # Stage 1: Dramatic contrast enhancement
        enhancer = ImageEnhance.Contrast(pil_image)
        pil_image = enhancer.enhance(2.0)  # Very strong contrast
        
        # Stage 2: Sharp details
        enhancer = ImageEnhance.Sharpness(pil_image)
        pil_image = enhancer.enhance(1.8)  # Very sharp
        
        # Stage 3: Vibrant colors
        enhancer = ImageEnhance.Color(pil_image)
        pil_image = enhancer.enhance(1.6)  # Vibrant saturation
        
        # Stage 4: Brightness adjustment
        enhancer = ImageEnhance.Brightness(pil_image)
        pil_image = enhancer.enhance(1.2)  # Slightly brighter
        
        # Convert back to OpenCV
        result = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        # Stage 5: Advanced 3D lighting based on face structure
        if landmarks is not None:
            result = self.apply_face_structure_lighting(result, landmarks)
        else:
            result = self.apply_generic_3d_lighting(result)
        
        # Stage 6: Multi-layer depth shadows
        result = self.apply_3d_depth_shadows(result, landmarks)
        
        # Stage 7: Final 3D enhancement
        result = self.apply_final_3d_effects(result)
        
        return result
    
    def apply_face_structure_lighting(self, image, landmarks):
        """Apply lighting based on actual face structure"""
        h, w = image.shape[:2]
        
        # Get key facial points
        nose_tip = landmarks[30]  # Nose tip
        left_cheek = landmarks[1]   # Left face contour
        right_cheek = landmarks[15] # Right face contour
        forehead_center = [(landmarks[19][0] + landmarks[24][0]) // 2, landmarks[19][1] - 30]
        
        # Create lighting map based on face geometry
        lighting_map = np.ones((h, w), dtype=np.float32)
        
        # Main light from top-left (simulating studio lighting)
        light_source = [w * 0.2, h * 0.15]
        
        # Calculate lighting intensity for each pixel
        y_coords, x_coords = np.ogrid[:h, :w]
        
        # Distance from light source
        distances = np.sqrt((x_coords - light_source[0])**2 + (y_coords - light_source[1])**2)
        max_distance = np.sqrt(w**2 + h**2)
        
        # Create gradient lighting
        base_lighting = 1.0 - (distances / max_distance) * 0.4
        base_lighting = np.clip(base_lighting, 0.6, 1.3)
        
        # Enhance lighting around facial features
        for point in [nose_tip, forehead_center]:
            feature_distances = np.sqrt((x_coords - point[0])**2 + (y_coords - point[1])**2)
            feature_enhancement = np.exp(-feature_distances / 50) * 0.3
            base_lighting += feature_enhancement
        
        # Apply lighting to image
        result = image.copy().astype(np.float32)
        for i in range(3):
            result[:, :, i] *= base_lighting
        
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def apply_generic_3d_lighting(self, image):
        """Apply generic 3D lighting when no landmarks available"""
        h, w = image.shape[:2]
        
        # Create multiple light sources
        y_coords, x_coords = np.ogrid[:h, :w]
        
        # Main light (top-left)
        light1_x, light1_y = w * 0.25, h * 0.15
        dist1 = np.sqrt((x_coords - light1_x)**2 + (y_coords - light1_y)**2)
        light1_intensity = 1.0 - (dist1 / np.sqrt(w**2 + h**2)) * 0.4
        
        # Fill light (right)
        light2_x, light2_y = w * 0.75, h * 0.3
        dist2 = np.sqrt((x_coords - light2_x)**2 + (y_coords - light2_y)**2)
        light2_intensity = 1.0 - (dist2 / np.sqrt(w**2 + h**2)) * 0.2
        
        # Combine lighting
        combined_lighting = np.clip((light1_intensity + light2_intensity * 0.4) / 1.4, 0.6, 1.3)
        
        # Apply to image
        result = image.copy().astype(np.float32)
        for i in range(3):
            result[:, :, i] *= combined_lighting
        
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def apply_3d_depth_shadows(self, image, landmarks):
        """Apply realistic depth shadows"""
        h, w = image.shape[:2]
        
        # Create multiple shadow layers
        shadow_layers = []
        
        # Primary shadow (strong)
        shadow1 = np.zeros_like(image)
        if h > 6 and w > 6:
            shadow1[6:, 6:] = image[:-6, :-6]
            shadow1 = (shadow1 * 0.15).astype(np.uint8)
            shadow_layers.append((shadow1, 0.2))
        
        # Secondary shadow (medium)
        shadow2 = np.zeros_like(image)
        if h > 3 and w > 3:
            shadow2[3:, 3:] = image[:-3, :-3]
            shadow2 = (shadow2 * 0.3).astype(np.uint8)
            shadow_layers.append((shadow2, 0.15))
        
        # Soft shadow (subtle)
        shadow3 = np.zeros_like(image)
        if h > 1 and w > 1:
            shadow3[1:, 1:] = image[:-1, :-1]
            shadow3 = (shadow3 * 0.5).astype(np.uint8)
            shadow_layers.append((shadow3, 0.1))
        
        # Blend all shadows with original
        result = image.copy().astype(np.float32)
        total_weight = 1.0
        
        for shadow, weight in shadow_layers:
            result = result * (1 - weight) + shadow.astype(np.float32) * weight
            total_weight -= weight * 0.5
        
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def apply_final_3d_effects(self, image):
        """Apply final 3D enhancement effects"""
        # Convert to PIL for final processing
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # Create subtle glow effect
        glow = pil_image.filter(ImageFilter.GaussianBlur(3))
        
        # Enhance edges for 3D pop
        edges = pil_image.filter(ImageFilter.EDGE_ENHANCE_MORE)
        
        # Combine effects
        result_array = np.array(pil_image).astype(np.float32)
        glow_array = np.array(glow).astype(np.float32)
        edges_array = np.array(edges).astype(np.float32)
        
        # Blend: 70% original + 20% glow + 10% edge enhancement
        final_result = (result_array * 0.7 + 
                       glow_array * 0.2 + 
                       edges_array * 0.1)
        
        final_result = np.clip(final_result, 0, 255).astype(np.uint8)
        
        return cv2.cvtColor(final_result, cv2.COLOR_RGB2BGR)
    
    def apply_hairstyle_overlay(self, face_3d, hairstyle_path):
        """Apply hairstyle overlay to 3D face"""
        try:
            # Load hairstyle image
            hairstyle = cv2.imread(hairstyle_path, cv2.IMREAD_UNCHANGED)
            if hairstyle is None:
                return face_3d
            
            h, w = face_3d.shape[:2]
            
            # Resize hairstyle to match face
            hairstyle_resized = cv2.resize(hairstyle, (w, h))
            
            # Apply overlay based on image type
            if hairstyle_resized.shape[2] == 4:  # Has alpha channel
                # Extract alpha channel
                hair_rgb = hairstyle_resized[:, :, :3]
                alpha = hairstyle_resized[:, :, 3] / 255.0
                
                # Apply alpha blending
                result = face_3d.copy().astype(np.float32)
                for c in range(3):
                    result[:, :, c] = (alpha * hair_rgb[:, :, c] + 
                                     (1 - alpha) * result[:, :, c])
                
                return np.clip(result, 0, 255).astype(np.uint8)
            else:
                # Simple overlay without alpha
                return cv2.addWeighted(face_3d, 0.4, hairstyle_resized, 0.6, 0)
                
        except Exception as e:
            print(f"Hairstyle overlay error: {e}")
            return face_3d
    
    def process_complete_3d_face(self, user_image_path, hairstyle_path=None):
        """Complete 3D face processing pipeline"""
        try:
            # Load user image
            image = cv2.imread(user_image_path)
            if image is None:
                return {'success': False, 'error': 'Failed to load user image'}
            
            # Detect face landmarks
            landmarks = self.detect_face_landmarks(image)
            
            # Create 3D face model
            face_3d = self.create_3d_face_model(image, landmarks)
            
            # Apply hairstyle if provided
            if hairstyle_path and os.path.exists(hairstyle_path):
                face_3d = self.apply_hairstyle_overlay(face_3d, hairstyle_path)
            
            # Create output directory
            output_dir = 'uploads/3d_results/'
            os.makedirs(output_dir, exist_ok=True)
            
            # Save result
            output_path = os.path.join(output_dir, f'complete_3d_{hash(user_image_path)}.png')
            cv2.imwrite(output_path, face_3d)
            
            # Convert to base64
            _, buffer = cv2.imencode('.png', face_3d)
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return {
                'success': True,
                'result_url': output_path,
                'result_base64': f"data:image/png;base64,{img_base64}",
                'has_landmarks': landmarks is not None,
                'processing_method': 'Complete_3D_Face_Model'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

def main():
    if len(sys.argv) < 2:
        print(json.dumps({'success': False, 'error': 'No image path provided'}))
        return
    
    user_image = sys.argv[1]
    hairstyle_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    processor = Complete3DFaceProcessor()
    result = processor.process_complete_3d_face(user_image, hairstyle_path)
    
    print(json.dumps(result))

if __name__ == "__main__":
    main()