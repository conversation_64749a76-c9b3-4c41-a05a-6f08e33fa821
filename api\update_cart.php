<?php
session_start();
header('Content-Type: application/json');
require_once '../Admin/db_config.php';

$response = [
    'success' => false,
    'message' => '',
    'cart_count' => 0
];

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $response['message'] = 'User not logged in';
    echo json_encode($response);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Method not allowed';
    echo json_encode($response);
    exit();
}

$user_id = $_SESSION['user_id'];
$cart_id = $_POST['cart_id'] ?? null;
$action = $_POST['action'] ?? null; // 'update' or 'remove'
$quantity = (int)($_POST['quantity'] ?? 0);

// Validation
if (!$cart_id || !$action) {
    $response['message'] = 'Invalid request data';
    echo json_encode($response);
    exit();
}

try {
    if ($action === 'remove') {
        // Remove item from cart
        $stmt = $conn->prepare("DELETE FROM cart WHERE id = ? AND user_id = ?");
        $stmt->execute([$cart_id, $user_id]);
        
        if ($stmt->rowCount() > 0) {
            $response['success'] = true;
            $response['message'] = 'Item removed from cart';
        } else {
            $response['message'] = 'Item not found or unauthorized';
        }
        
    } elseif ($action === 'update') {
        if ($quantity <= 0) {
            $response['message'] = 'Invalid quantity';
            echo json_encode($response);
            exit();
        }
        
        // Check stock availability
        $stmt = $conn->prepare("
            SELECT v.stok FROM cart c
            JOIN variasi_produk v ON c.variant_id = v.id
            WHERE c.id = ? AND c.user_id = ?
        ");
        $stmt->execute([$cart_id, $user_id]);
        $stock = $stmt->fetchColumn();
        
        if ($stock === false) {
            $response['message'] = 'Cart item not found';
            echo json_encode($response);
            exit();
        }
        
        if ($quantity > $stock) {
            $response['message'] = 'Insufficient stock. Available: ' . $stock;
            echo json_encode($response);
            exit();
        }
        
        // Update quantity
        $stmt = $conn->prepare("
            UPDATE cart SET quantity = ?, updated_at = NOW() 
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$quantity, $cart_id, $user_id]);
        
        if ($stmt->rowCount() > 0) {
            $response['success'] = true;
            $response['message'] = 'Cart updated successfully';
        } else {
            $response['message'] = 'Failed to update cart';
        }
        
    } elseif ($action === 'clear') {
        // Clear all cart items for user
        $stmt = $conn->prepare("DELETE FROM cart WHERE user_id = ?");
        $stmt->execute([$user_id]);
        
        $response['success'] = true;
        $response['message'] = 'Cart cleared successfully';
        
    } else {
        $response['message'] = 'Invalid action';
        echo json_encode($response);
        exit();
    }
    
    // Get updated cart count
    $stmt = $conn->prepare("SELECT SUM(quantity) as total FROM cart WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $cart_count = $stmt->fetchColumn() ?: 0;
    
    // Update session cart count
    $_SESSION['cart_count'] = $cart_count;
    $response['cart_count'] = $cart_count;
    
} catch (PDOException $e) {
    $response['message'] = 'Database error: ' . $e->getMessage();
    error_log("Update cart error: " . $e->getMessage());
}

echo json_encode($response);
?>
