from flask import Flask, request, jsonify
import base64
import cv2
import numpy as np
import os
import time
import subprocess
import json
from werkzeug.utils import secure_filename

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads/'
app.config['ALLOWED_EXTENSIONS'] = {'png', 'jpg', 'jpeg'}

# Utility: cek ekstensi file
def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

@app.route('/')
def index():
    return 'API Ready'

@app.route('/analyze', methods=['POST'])
def analyze():
    # 1. Terima dan simpan gambar user
    if 'image' not in request.files:
        return jsonify({'error': 'No image uploaded'}), 400
    image_file = request.files['image']
    if not allowed_file(image_file.filename):
        return jsonify({'error': 'Invalid file type'}), 400
    filename = secure_filename('capture_' + str(int(time.time())) + '_' + image_file.filename)
    save_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    image_file.save(save_path)

    # 2. Panggil face_analysis.py untuk deteksi bentuk wajah & rekomendasi
    try:
        result = subprocess.run([
            'python', 'face_analysis.py', save_path
        ], capture_output=True, text=True, check=True)
        analysis = json.loads(result.stdout)
    except Exception as e:
        return jsonify({'error': f'Face analysis failed: {str(e)}'}), 500

    face_shape = analysis.get('shape', 'oval')
    recommendations = analysis.get('recommendations', [])

    # 3. Ambil gambar rambut model (PNG/JPG) sesuai rekomendasi[0]
    if recommendations:
        hair_name = recommendations[0].split(' - ')[0]
        hair_path = f'rekomendasi/{face_shape}/{hair_name}.png'
        if not os.path.exists(hair_path):
            hair_path = f'rekomendasi/{face_shape}/{hair_name}.jpg'
    else:
        hair_path = None

    # 4. Overlay rambut ke wajah user (2D)
    overlay_result_path = None
    if hair_path and os.path.exists(hair_path):
        try:
            # Panggil face_overlay.py (atau transparent_hair_applier.py)
            overlay_proc = subprocess.run([
                'python', 'face_overlay.py', save_path, hair_path
            ], capture_output=True, text=True, check=True)
            overlay_result_path = overlay_proc.stdout.strip()
        except Exception as e:
            overlay_result_path = None
    
    # 5. Encode hasil overlay ke base64
    overlay_b64 = None
    if overlay_result_path and os.path.exists(overlay_result_path):
        with open(overlay_result_path, "rb") as img_file:
            overlay_b64 = base64.b64encode(img_file.read()).decode('utf-8')

    # 6. Return hasil ke frontend
    return jsonify({
        'shape': face_shape,
        'recommendations': recommendations,
        'overlay_image_base64': overlay_b64,
        'analysis': analysis
    })

if __name__ == '__main__':
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    app.run(debug=True)