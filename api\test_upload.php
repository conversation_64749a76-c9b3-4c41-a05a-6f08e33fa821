<?php
// Simple test upload API
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

// Start session
session_start();

$response = [
    'success' => false,
    'message' => '',
    'debug' => []
];

// Add debug info
$response['debug']['session_status'] = session_status();
$response['debug']['user_id'] = $_SESSION['user_id'] ?? 'not set';
$response['debug']['method'] = $_SERVER['REQUEST_METHOD'];
$response['debug']['files'] = $_FILES;
$response['debug']['post'] = $_POST;

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $response['message'] = 'User not logged in';
    echo json_encode($response);
    exit();
}

// Check method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Method not allowed';
    echo json_encode($response);
    exit();
}

// Check if file was uploaded
if (!isset($_FILES['profile_picture'])) {
    $response['message'] = 'No file uploaded';
    echo json_encode($response);
    exit();
}

$file = $_FILES['profile_picture'];

// Check upload error
if ($file['error'] !== UPLOAD_ERR_OK) {
    $response['message'] = 'Upload error code: ' . $file['error'];
    echo json_encode($response);
    exit();
}

// Check file type
$allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
if (!in_array($file['type'], $allowed_types)) {
    $response['message'] = 'File type not allowed: ' . $file['type'];
    echo json_encode($response);
    exit();
}

// Check file size (5MB)
if ($file['size'] > 5 * 1024 * 1024) {
    $response['message'] = 'File too large: ' . $file['size'] . ' bytes';
    echo json_encode($response);
    exit();
}

// Try to create upload directory
$upload_dir = dirname(__DIR__) . '/uploads/profile/';
$response['debug']['upload_dir'] = $upload_dir;
$response['debug']['upload_dir_exists'] = is_dir($upload_dir);

if (!is_dir($upload_dir)) {
    if (!mkdir($upload_dir, 0755, true)) {
        $response['message'] = 'Cannot create upload directory: ' . $upload_dir;
        echo json_encode($response);
        exit();
    }
}

// Check if writable
if (!is_writable($upload_dir)) {
    $response['message'] = 'Upload directory not writable';
    echo json_encode($response);
    exit();
}

// Generate filename
$user_id = $_SESSION['user_id'];
$file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
$filename = 'profile_' . $user_id . '_' . time() . '.' . $file_extension;
$file_path = $upload_dir . $filename;

// Try to move file
if (move_uploaded_file($file['tmp_name'], $file_path)) {
    $response['success'] = true;
    $response['message'] = 'File uploaded successfully';
    $response['filename'] = $filename;
    $response['file_path'] = $file_path;
    $response['image_url'] = 'uploads/profile/' . $filename;
} else {
    $response['message'] = 'Failed to move uploaded file';
    $response['debug']['last_error'] = error_get_last();
}

echo json_encode($response);
?>
