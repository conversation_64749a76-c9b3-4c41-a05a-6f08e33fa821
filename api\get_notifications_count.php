<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../admin/db_config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$response = [
    'success' => false,
    'count' => 0,
    'notifications' => []
];

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $response['message'] = 'User not logged in';
    $response['session_status'] = session_status();
    $response['session_data'] = $_SESSION ?? [];
    echo json_encode($response);
    exit();
}

$user_id = $_SESSION['user_id'];

try {
    // Get unread notifications count
    $count_stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM notifications 
        WHERE user_id = ? AND is_read = FALSE
    ");
    $count_stmt->execute([$user_id]);
    $count_result = $count_stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get recent notifications (last 5)
    $notif_stmt = $conn->prepare("
        SELECT n.*, o.id as order_id 
        FROM notifications n 
        LEFT JOIN orders o ON n.order_id = o.id 
        WHERE n.user_id = ? 
        ORDER BY n.created_at DESC 
        LIMIT 5
    ");
    $notif_stmt->execute([$user_id]);
    $notifications = $notif_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $response['success'] = true;
    $response['count'] = $count_result['count'];
    $response['notifications'] = $notifications;
    
} catch (PDOException $e) {
    $response['message'] = 'Database error: ' . $e->getMessage();
    error_log("Error in get_notifications_count.php: " . $e->getMessage());
}

echo json_encode($response);
?>
