<?php
require_once dirname(__FILE__) . '/../vendor/autoload.php';
require_once dirname(__FILE__) . '/../Admin/db_config.php'; // Assuming db_config is needed for user_id or order saving

use Midtrans\Snap;
use Midtrans\Config;

header('Content-Type: application/json');

// Set Midtrans Configuration
Config::$serverKey = 'SB-Mid-server-YOUR_SERVER_KEY'; // GANTI DENGAN SERVER KEY ANDA
Config::$isProduction = false; // Set to true for production environment
Config::$isSanitized = true;
Config::$is3ds = true;

// Get data from POST request
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid JSON input']);
    exit();
}

$order_id = $data['order_id'] ?? uniqid(); // Use existing order ID or generate new one
$total_amount = $data['total_amount'] ?? 0;
$item_details = $data['item_details'] ?? [];
$customer_details = $data['customer_details'] ?? [];
$selected_payment_method = $data['payment_method'] ?? 'all'; // Default to 'all' if not specified

// Basic validation
if ($total_amount <= 0 || empty($item_details)) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid total amount or item details']);
    exit();
}

try {
    // Transaction details
    $transaction_details = [
        'order_id' => $order_id,
        'gross_amount' => $total_amount,
    ];

    // Enabled payments for Snap. You can customize this based on the selected method.
    $enabled_payments = [];
    switch ($selected_payment_method) {
        case 'bca_mbanking':
            $enabled_payments = ['bca_va']; // Or 'bca_klikbca', etc.
            break;
        case 'mandiri_mbanking':
            $enabled_payments = ['mandiri_va']; // Or 'mandiri_bill', etc.
            break;
        case 'gopay':
            $enabled_payments = ['gopay'];
            break;
        case 'ovo':
            $enabled_payments = ['ovo'];
            break;
        default:
            $enabled_payments = ['credit_card', 'gopay', 'permata_va', 'bca_va', 'bni_va', 'bri_va', 'echannel', 'mandiri_bill', 'bca_klikbca', 'bca_klikpay', 'cimb_clicks', 'danamon_online', 'indomaret', 'alfamart', 'akulaku'];
            break;
    }

    $customer_details = [
        'first_name' => $customer_details['first_name'] ?? 'Guest',
        'last_name' => $customer_details['last_name'] ?? '',
        'email' => $customer_details['email'] ?? '<EMAIL>',
        'phone' => $customer_details['phone'] ?? '***********',
    ];

    $params = [
        'transaction_details' => $transaction_details,
        'item_details' => $item_details,
        'customer_details' => $customer_details,
        'enabled_payments' => $enabled_payments,
        // 'callbacks' => [ // Uncomment and configure if you need specific redirect after payment
        //     'finish' => 'http://yourwebsite.com/payment-finish.php?order_id=' . $order_id,
        //     'error' => 'http://yourwebsite.com/payment-error.php?order_id=' . $order_id,
        //     'pending' => 'http://yourwebsite.com/payment-pending.php?order_id=' . $order_id,
        // ]
    ];

    $snapToken = Snap::getSnapToken($params);

    echo json_encode(['status' => 'success', 'snap_token' => $snapToken, 'order_id' => $order_id]);

} catch (Exception $e) {
    error_log("Midtrans Transaction Error: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
?> 