<?php
session_start();
require_once 'Admin/db_config.php';

$registration_error = '';
$full_name_input = '';
$email_input = '';
$phone_number_input = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone_number = trim($_POST['phone_number'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // Retain input values
    $full_name_input = $full_name;
    $email_input = $email;
    $phone_number_input = $phone_number;

    if (empty($full_name) || empty($email) || empty($phone_number) || empty($password) || empty($confirm_password)) {
        $registration_error = 'Semua kolom harus diisi.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $registration_error = 'Format email tidak valid.';
    } elseif ($password !== $confirm_password) {
        $registration_error = 'Kata sandi dan konfirmasi kata sandi tidak cocok.';
    } elseif (strlen($password) < 6) {
        $registration_error = 'Kata sandi minimal 6 karakter.';
    } else {
        try {
            // Check if email already exists
            $stmt_check_email = $conn->prepare("SELECT id FROM pembeli WHERE email = :email");
            $stmt_check_email->execute(['email' => $email]);
            if ($stmt_check_email->fetch()) {
                $registration_error = 'Email sudah terdaftar. Silakan gunakan email lain atau login.';
            } else {
                // Hash the password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // First ensure the table has the required columns
                $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS password VARCHAR(255)");
                $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS alamat TEXT");

                // Insert new user into pembeli table
                $stmt_insert_user = $conn->prepare("
                    INSERT INTO pembeli (nama_pembeli, email, phone, password, created_at)
                    VALUES (:nama_pembeli, :email, :phone, :password, NOW())
                ");

                $stmt_insert_user->execute([
                    'nama_pembeli' => $full_name,
                    'email' => $email,
                    'phone' => $phone_number,
                    'password' => $hashed_password
                ]);

                // Redirect to login page after successful registration
                header('Location: login.php?registration=success');
                exit();
            }
        } catch (PDOException $e) {
            error_log("Registration error: " . $e->getMessage());
            $registration_error = 'Terjadi kesalahan saat pendaftaran. Silakan coba lagi nanti.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Sign Up</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
</head>
<body class="bg-[#006A63] flex items-center justify-center min-h-screen font-sans px-4">
  <div class="bg-white px-8 py-6 rounded-2xl shadow-lg w-full max-w-sm">
    <h2 class="text-2xl font-bold text-center text-[#006A63] mb-6">Daftar Akun</h2>

    <?php if ($registration_error): ?>
        <div class="bg-red-500 text-white text-sm p-3 rounded-lg mb-4 text-center">
            <?php echo htmlspecialchars($registration_error); ?>
        </div>
    <?php endif; ?>

    <form action="registrasi.php" method="POST">
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1" for="full_name">Nama Lengkap</label>
        <input type="text" id="full_name" name="full_name" placeholder="Masukkan nama lengkap Anda"
               class="w-full px-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#006A63] bg-white"
               value="<?php echo htmlspecialchars($full_name_input); ?>" required />
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1" for="email">Email</label>
        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email_input); ?>" placeholder="<EMAIL>"
               class="w-full px-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#006A63] bg-gray-100" required />
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1" for="phone_number">Nomor Telepon</label>
        <input type="tel" id="phone_number" name="phone_number" placeholder="08123456789"
               class="w-full px-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#006A63] bg-white"
               value="<?php echo htmlspecialchars($phone_number_input); ?>" required />
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1" for="password">Kata Sandi</label>
        <div class="relative">
          <input type="password" id="password" name="password" placeholder="Minimal 6 karakter"
                 class="w-full px-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#006A63] bg-gray-100" required />
          <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500" onclick="togglePassword('password')">
            <i class="fa-solid fa-eye" id="password-icon"></i>
          </button>
        </div>
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1" for="confirm_password">Konfirmasi Kata Sandi</label>
        <div class="relative">
          <input type="password" id="confirm_password" name="confirm_password" placeholder="Ulangi kata sandi"
                 class="w-full px-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#006A63] bg-white" required />
          <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500" onclick="togglePassword('confirm_password')">
            <i class="fa-solid fa-eye" id="confirm_password-icon"></i>
          </button>
        </div>
      </div>

      <p class="text-xs text-gray-600 text-center mt-2 mb-6">
        Dengan mendaftar, Anda menyetujui
        <a href="#" class="text-[#006A63] font-semibold">Syarat & Ketentuan</a>
        dan
        <a href="#" class="text-[#006A63] font-semibold">Kebijakan Privasi</a> kami
      </p>

      <button type="submit" class="w-full bg-[#006A63] hover:bg-[#064f45] text-white font-semibold py-2 rounded-xl mb-4">
        Daftar Sekarang
      </button>

      <p class="text-center text-sm text-gray-700">
        Sudah punya akun? <a href="login.php" class="text-[#006A63] font-semibold hover:underline">Masuk</a>
      </p>
    </form>
  </div>

  <script>
    function togglePassword(fieldId) {
      const field = document.getElementById(fieldId);
      const icon = document.getElementById(fieldId + '-icon');

      if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      }
    }

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirm_password').value;

      if (password !== confirmPassword) {
        e.preventDefault();
        alert('Konfirmasi kata sandi tidak cocok!');
        return false;
      }

      if (password.length < 6) {
        e.preventDefault();
        alert('Kata sandi minimal 6 karakter!');
        return false;
      }
    });
  </script>
</body>
</html>
