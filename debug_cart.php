<?php
session_start();
require_once 'Admin/db_config.php';

echo "<h2>Debug Cart Data</h2>";

// Check session
echo "<h3>1. Session Check:</h3>";
if (isset($_SESSION['user_id'])) {
    echo "✅ User logged in with ID: " . $_SESSION['user_id'] . "<br>";
    $user_id = $_SESSION['user_id'];
} else {
    echo "❌ User not logged in<br>";
    exit();
}

// Check cart table
echo "<h3>2. Cart Table Structure:</h3>";
try {
    $stmt = $conn->query("DESCRIBE cart");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage();
}

// Check cart items
echo "<h3>3. Cart Items for User:</h3>";
try {
    $cart_stmt = $conn->prepare("
        SELECT c.*, p.nama_produk, p.gambar_utama, p.deskripsi,
               v.ukuran_ml, v.harga, v.stok,
               (c.quantity * v.harga) as total_price
        FROM cart c
        JOIN produk p ON c.product_id = p.id
        JOIN variasi_produk v ON c.variant_id = v.id
        WHERE c.user_id = ?
        ORDER BY c.created_at DESC
    ");
    $cart_stmt->execute([$user_id]);
    $cart_items = $cart_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($cart_items)) {
        echo "❌ No cart items found for user ID: $user_id<br>";
    } else {
        echo "✅ Found " . count($cart_items) . " cart items:<br>";
        echo "<pre>";
        print_r($cart_items);
        echo "</pre>";
    }
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage();
}

// Check products table
echo "<h3>4. All Products:</h3>";
try {
    $stmt = $conn->query("SELECT id, nama_produk, gambar_utama FROM produk LIMIT 5");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($products);
    echo "</pre>";
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage();
}

// Check variants table
echo "<h3>5. Product Variants:</h3>";
try {
    $stmt = $conn->query("SELECT id, produk_id, ukuran_ml, harga FROM variasi_produk LIMIT 5");
    $variants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($variants);
    echo "</pre>";
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage();
}

// Check image files
echo "<h3>6. Image Files Check:</h3>";
$upload_dirs = ['uploads/', 'uploads/products/', 'Admin/uploads/'];
foreach ($upload_dirs as $dir) {
    echo "<h4>Directory: $dir</h4>";
    if (is_dir($dir)) {
        $files = scandir($dir);
        $image_files = array_filter($files, function($file) {
            return in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']);
        });
        
        if (!empty($image_files)) {
            echo "✅ Found images: " . implode(', ', $image_files) . "<br>";
        } else {
            echo "❌ No image files found<br>";
        }
    } else {
        echo "❌ Directory does not exist<br>";
    }
}

// Test add to cart
echo "<h3>7. Test Add to Cart:</h3>";
echo "<form method='POST'>";
echo "Product ID: <input type='number' name='product_id' value='1'><br>";
echo "Variant ID: <input type='number' name='variant_id' value='1'><br>";
echo "Quantity: <input type='number' name='quantity' value='1'><br>";
echo "<button type='submit' name='add_to_cart'>Add to Cart</button>";
echo "</form>";

if (isset($_POST['add_to_cart'])) {
    $product_id = $_POST['product_id'];
    $variant_id = $_POST['variant_id'];
    $quantity = $_POST['quantity'];
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO cart (user_id, product_id, variant_id, quantity) 
            VALUES (?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE quantity = quantity + VALUES(quantity)
        ");
        $stmt->execute([$user_id, $product_id, $variant_id, $quantity]);
        echo "✅ Item added to cart successfully!<br>";
        echo "<a href='debug_cart.php'>Refresh</a>";
    } catch (PDOException $e) {
        echo "❌ Error adding to cart: " . $e->getMessage();
    }
}

echo "<br><br><a href='keranjang.php'>← Back to Cart</a>";
?>
