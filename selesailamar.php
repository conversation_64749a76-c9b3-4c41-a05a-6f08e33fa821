<?php
session_start();

// Get application result from session
$result = $_SESSION['application_result'] ?? null;
if ($result) {
    unset($_SESSION['application_result']);
} else {
    // If no result, redirect to jobs page
    header("Location: jobs.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@500;700&display=swap" rel="stylesheet">
    <style>
        .checkmark {
            width: 80px;
            height: 80px;
            display: block;
            margin: 0 auto 20px auto;
        }
        .checkmark__circle {
            stroke: #1cc88a;
            stroke-width: 5;
            fill: none;
            animation: circle 0.6s ease-in-out;
        }
        .checkmark__check {
            stroke: #1cc88a;
            stroke-width: 5;
            fill: none;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: check 0.4s 0.6s forwards;
        }
        @keyframes circle {
            0% { stroke-dasharray: 0 251.2; }
            100% { stroke-dasharray: 251.2 0; }
        }
        @keyframes check {
            to { stroke-dashoffset: 0; }
        }
    </style>
</head>
<body class="bg-[#f8f8f8] min-h-screen flex flex-col justify-center items-center">
    <div class="bg-white rounded-xl shadow-lg p-8 max-w-lg w-full mt-24 text-center animate-fade-in">
        <?php if ($result['status'] === 'accepted'): ?>
            <!-- Accepted Status -->
            <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h2 class="text-2xl font-bold text-green-600 mb-2">Quiz Selesai!</h2>
            <p class="text-gray-700 mb-4">Terima kasih telah mengikuti quiz. Berikut adalah hasil Anda.</p>

        <?php elseif ($result['status'] === 'rejected'): ?>
            <!-- Rejected Status -->
            <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h2 class="text-2xl font-bold text-blue-600 mb-2">Quiz Selesai!</h2>
            <p class="text-gray-700 mb-4">Terima kasih telah mengikuti quiz. Berikut adalah hasil Anda.</p>

        <?php else: ?>
            <!-- Under Review Status -->
            <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h2 class="text-2xl font-bold text-blue-600 mb-2">Quiz Selesai!</h2>
            <p class="text-gray-700 mb-4">Terima kasih telah mengikuti quiz. Berikut adalah hasil Anda.</p>
        <?php endif; ?>

        <!-- Evaluation Results -->
        <?php if (isset($result['evaluation_score'])): ?>
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-6 border border-blue-200">
            <h3 class="font-semibold text-gray-800 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Hasil Evaluasi Anda
            </h3>

            <!-- Overall Score -->
            <div class="bg-white rounded-lg p-4 mb-4 border border-blue-100">
                <div class="flex justify-center items-center space-x-8">
                    <div class="text-center">
                        <div class="text-3xl font-bold <?php echo $result['evaluation_score'] >= 80 ? 'text-green-600' : ($result['evaluation_score'] >= 60 ? 'text-yellow-600' : 'text-blue-600'); ?>">
                            <?php echo round($result['evaluation_score']); ?>/100
                        </div>
                        <div class="text-sm text-gray-600">Skor Evaluasi</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold <?php echo $result['score'] >= 70 ? 'text-green-600' : 'text-blue-600'; ?>">
                            <?php echo round($result['score']); ?>%
                        </div>
                        <div class="text-sm text-gray-600">Skor Quiz</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-800">
                            <?php echo $result['correct_answers']; ?>/<?php echo $result['total_questions']; ?>
                        </div>
                        <div class="text-sm text-gray-600">Jawaban Benar</div>
                    </div>
                </div>
            </div>

            <!-- Evaluation Breakdown -->
            <?php if (isset($result['evaluation_details'])): ?>
            <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
                <?php
                $category_names = [
                    'cv' => ['name' => 'CV', 'icon' => '📄'],
                    'certificate' => ['name' => 'Sertifikat', 'icon' => '🏆'],
                    'experience' => ['name' => 'Pengalaman', 'icon' => '💼'],
                    'skills' => ['name' => 'Keahlian', 'icon' => '⚡'],
                    'quiz' => ['name' => 'Quiz', 'icon' => '🧠']
                ];
                foreach ($result['evaluation_details'] as $category => $details):
                ?>
                <div class="bg-white p-3 rounded-lg border border-gray-200 text-center">
                    <div class="text-lg mb-1"><?= $category_names[$category]['icon'] ?></div>
                    <div class="text-xs font-medium text-gray-700 mb-1"><?= $category_names[$category]['name'] ?></div>
                    <div class="text-sm font-bold <?php echo $details['score'] >= ($details['max'] * 0.8) ? 'text-green-600' : ($details['score'] >= ($details['max'] * 0.5) ? 'text-yellow-600' : 'text-gray-600'); ?>">
                        <?= $details['score'] ?>/<?= $details['max'] ?>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-1 mt-1">
                        <div class="<?php echo $details['score'] >= ($details['max'] * 0.8) ? 'bg-green-500' : ($details['score'] >= ($details['max'] * 0.5) ? 'bg-yellow-500' : 'bg-gray-400'); ?> h-1 rounded-full" style="width: <?= ($details['score'] / $details['max']) * 100 ?>%"></div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <p class="text-sm text-gray-600 mt-4 text-center">Lamaran Anda telah berhasil dikirim dan akan diproses lebih lanjut.</p>
        </div>
        <?php else: ?>
        <!-- Fallback for quiz-only results -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 class="font-semibold text-gray-800 mb-2">Hasil Quiz Anda:</h3>
            <div class="flex justify-center items-center space-x-4">
                <div class="text-center">
                    <div class="text-2xl font-bold <?php echo $result['score'] >= 70 ? 'text-green-600' : 'text-red-600'; ?>">
                        <?php echo round($result['score']); ?>%
                    </div>
                    <div class="text-sm text-gray-600">Skor</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-800">
                        <?php echo $result['correct_answers']; ?>/<?php echo $result['total_questions']; ?>
                    </div>
                    <div class="text-sm text-gray-600">Benar</div>
                </div>
            </div>
            <p class="text-sm text-gray-600 mt-3">Lamaran Anda telah berhasil dikirim dan akan diproses lebih lanjut.</p>
        </div>
        <?php endif; ?>

        <a href="jobs.php" class="inline-block bg-[#0A5144] text-white px-6 py-2 rounded-lg font-semibold shadow hover:bg-[#179b6b] transition">
            Kembali ke Lowongan
        </a>
    </div>
    <script>
        // Fade in animation
        document.querySelector('.animate-fade-in').style.opacity = 0;
        setTimeout(() => {
            document.querySelector('.animate-fade-in').style.transition = 'opacity 0.7s';
            document.querySelector('.animate-fade-in').style.opacity = 1;
        }, 200);
    </script>
</body>
</html>