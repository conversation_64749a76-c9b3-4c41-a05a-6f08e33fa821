<?php
session_start();
header('Content-Type: application/json');
require_once 'Admin/db_config.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Cek koneksi database
    if (!isset($conn) || !($conn instanceof PDO)) {
        throw new PDOException("Koneksi database tidak tersedia");
    }
    
    // Jika ada parameter ID, ambil data spesifik
    if (isset($_GET['id']) && !empty($_GET['id'])) {
        $id = intval($_GET['id']);
        
        $stmt = $conn->prepare("SELECT * FROM face_analysis WHERE id = ?");
        $stmt->execute([$id]);
        $result = $stmt->fetch();
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Data tidak ditemukan'
            ]);
        }
    } else {
        // Ambil semua riwayat deteksi (10 terbaru)
        $stmt = $conn->prepare("SELECT * FROM face_analysis ORDER BY created_at DESC LIMIT 10");
        $stmt->execute();
        $results = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'data' => $results,
            'count' => count($results)
        ]);
    }
    
} catch (PDOException $e) {
    error_log("Database error in get_face_history.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Gagal mengambil data dari database: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    error_log("General error in get_face_history.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}
?>
