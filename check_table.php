<?php
require_once 'Admin/db_config.php';

try {
    // Get table structure
    $stmt = $conn->query("DESCRIBE produk");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Table structure for 'produk':\n";
    print_r($columns);
    
    // Check if table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'produk'");
    if ($stmt->rowCount() == 0) {
        echo "\nTable 'produk' does not exist!\n";
    } else {
        echo "\nTable 'produk' exists.\n";
    }
    
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?> 