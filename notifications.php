<?php
require_once 'Admin/db_config.php';
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Mark notifications as read
if (isset($_SESSION['user_id'])) {
    try {
        $stmt = $conn->prepare("UPDATE notifications SET is_read = TRUE WHERE user_id = :user_id");
        $stmt->execute(['user_id' => $_SESSION['user_id']]);
    } catch (PDOException $e) {
        error_log("Error marking notifications as read: " . $e->getMessage());
    }
}

// Get all notifications for the user
$notifications = [];
try {
    $stmt = $conn->prepare("
        SELECT n.*, o.id as order_id 
        FROM notifications n 
        LEFT JOIN orders o ON n.order_id = o.id 
        WHERE n.user_id = :user_id 
        ORDER BY n.created_at DESC
    ");
    $stmt->execute(['user_id' => $_SESSION['user_id']]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error getting notifications: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifikasi - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="gradient-bg fixed top-0 left-0 right-0 z-20 p-4 shadow-md">
        <div class="flex items-center justify-between">
            <a href="javascript:history.back()" class="text-white">
                <i class="fas fa-arrow-left text-lg"></i>
            </a>
            <h1 class="text-xl font-bold text-white">Notifikasi</h1>
            <div class="w-6"></div> <!-- Spacer for balance -->
        </div>
    </header>

    <!-- Main Content -->
    <div class="pt-20 pb-4 px-4">
        <?php if (empty($notifications)): ?>
            <div class="text-center py-8">
                <i class="fas fa-bell text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">Belum ada notifikasi</p>
            </div>
        <?php else: ?>
            <div class="space-y-4">
                <?php foreach ($notifications as $notif): ?>
                    <div class="bg-white rounded-lg shadow p-4">
                        <div class="flex items-start">
                            <div class="flex-1">
                                <p class="text-gray-800"><?php echo htmlspecialchars($notif['message']); ?></p>
                                <p class="text-sm text-gray-500 mt-1">
                                    <?php echo date('d M Y H:i', strtotime($notif['created_at'])); ?>
                                </p>
                            </div>
                            <?php if ($notif['order_id']): ?>
                                <a href="konfirmasi_pembayaran.php?order_id=<?php echo $notif['order_id']; ?>" 
                                   class="ml-4 text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</body>
</html> 