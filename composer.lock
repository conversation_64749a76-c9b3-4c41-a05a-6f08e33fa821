{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "09895849f12de276db226c9272500f53", "packages": [{"name": "midtrans/midtrans-php", "version": "2.6.2", "source": {"type": "git", "url": "https://github.com/Midtrans/midtrans-php.git", "reference": "8ed7fc58ff1ababe675da17acf8233f4028eb3be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Midtrans/midtrans-php/zipball/8ed7fc58ff1ababe675da17acf8233f4028eb3be", "reference": "8ed7fc58ff1ababe675da17acf8233f4028eb3be", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-openssl": "*", "php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "5.7.*", "psy/psysh": "0.4.*"}, "type": "library", "autoload": {"psr-4": {"SnapBi\\": "SnapBi/", "Midtrans\\": "Midtrans/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Wrapper for Midtrans Payment API.", "homepage": "https://midtrans.com", "support": {"issues": "https://github.com/Midtrans/midtrans-php/issues", "source": "https://github.com/Midtrans/midtrans-php/tree/v2.6.2"}, "time": "2025-03-18T06:30:17+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "plugin-api-version": "2.6.0"}