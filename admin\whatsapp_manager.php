<?php
session_start();
require_once 'db_config.php';
require_once 'whatsapp_notification.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$message = '';
$message_type = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'test_connection':
                $whatsapp = new WhatsAppNotification();
                $test_phone = $_POST['test_phone'] ?? '+6281234567890';
                $result = $whatsapp->testConnection($test_phone);
                
                if ($result['success']) {
                    $message = "Test message berhasil dikirim ke {$test_phone}!";
                    $message_type = 'success';
                } else {
                    $message = "Gagal mengirim test message: " . json_encode($result['response']);
                    $message_type = 'error';
                }
                break;
                
            case 'send_notification':
                $application_id = $_POST['application_id'];
                try {
                    $result = sendWhatsAppNotification($application_id);

                    if ($result && $result['success']) {
                        $message = "Notifikasi WhatsApp berhasil dikirim!";
                        $message_type = 'success';
                    } else {
                        $message = "Gagal mengirim notifikasi: " . ($result['message'] ?? 'Unknown error');
                        $message_type = 'error';
                    }
                } catch (Exception $e) {
                    $message = "Error mengirim notifikasi: " . $e->getMessage();
                    $message_type = 'error';
                }
                break;
                
            case 'bulk_notify':
                $status_filter = $_POST['status_filter'] ?? 'all';
                
                // Get applications based on filter
                $sql = "SELECT a.*, j.position FROM applications a JOIN jobs j ON a.job_id = j.id";
                if ($status_filter !== 'all') {
                    $sql .= " WHERE a.status = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->execute([$status_filter]);
                } else {
                    $stmt = $conn->prepare($sql);
                    $stmt->execute();
                }
                
                $applications = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $whatsapp = new WhatsAppNotification();
                $results = $whatsapp->sendBulkNotifications($applications);
                
                $success_count = count(array_filter($results, function($r) { return $r['sent']; }));
                $total_count = count($results);
                
                $message = "Bulk notification selesai: {$success_count}/{$total_count} berhasil dikirim.";
                $message_type = $success_count > 0 ? 'success' : 'error';
                break;

            case 'create_table':
                $result = createNotificationLogsTable();
                if ($result) {
                    $message = "Tabel notification_logs berhasil dibuat!";
                    $message_type = 'success';
                } else {
                    $message = "Gagal membuat tabel notification_logs. Cek log untuk detail.";
                    $message_type = 'error';
                }
                break;
        }
    }
}

// Get notification logs with status filter
$status_filter = $_GET['status_filter'] ?? 'all';
$notification_logs = [];
try {
    $sql = "
        SELECT nl.*, a.full_name, a.status as application_status, j.position
        FROM notification_logs nl
        JOIN applications a ON nl.application_id = a.id
        JOIN jobs j ON a.job_id = j.id
    ";

    if ($status_filter !== 'all') {
        $sql .= " WHERE a.status = ? ";
    }

    $sql .= " ORDER BY nl.sent_at DESC LIMIT 50";

    $logs_stmt = $conn->prepare($sql);
    if ($status_filter !== 'all') {
        $logs_stmt->execute([$status_filter]);
    } else {
        $logs_stmt->execute();
    }
    $notification_logs = $logs_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Table might not exist yet
    $notification_logs = [];
}

// Get applications for manual notification
$apps_stmt = $conn->prepare("
    SELECT a.id, a.full_name, a.phone, a.status, j.position
    FROM applications a
    JOIN jobs j ON a.job_id = j.id
    ORDER BY a.applied_at DESC
");
$apps_stmt->execute();
$applications = $apps_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get statistics
$stats = [];
try {
    // Total notifications sent today
    $today_stmt = $conn->prepare("SELECT COUNT(*) as count FROM notification_logs WHERE DATE(sent_at) = CURDATE()");
    $today_stmt->execute();
    $stats['today'] = $today_stmt->fetchColumn();

    // Success rate today
    $success_stmt = $conn->prepare("SELECT COUNT(*) as count FROM notification_logs WHERE DATE(sent_at) = CURDATE() AND success = 1");
    $success_stmt->execute();
    $stats['success_today'] = $success_stmt->fetchColumn();

    // Status breakdown
    $status_stmt = $conn->prepare("
        SELECT a.status, COUNT(nl.id) as notification_count
        FROM applications a
        LEFT JOIN notification_logs nl ON a.id = nl.application_id
        GROUP BY a.status
    ");
    $status_stmt->execute();
    $stats['by_status'] = $status_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

} catch (PDOException $e) {
    $stats = ['today' => 0, 'success_today' => 0, 'by_status' => []];
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Notification Manager - Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            background: #2d3748;
            color: #fff;
            transition: transform 0.3s ease-in-out;
            width: 250px;
            min-width: 250px;
            max-width: 250px;
        }
        .sidebar .logo {
            background: #1a202c;
            padding: 1rem;
        }
        .sidebar .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .sidebar nav ul li {
            transition: all 0.3s ease;
        }
        .sidebar nav ul li a {
            color: #fff;
            text-decoration: none;
        }
        .sidebar nav ul li.active {
            background: rgb(35, 71, 250);
        }
        .sidebar nav ul li.active a,
        .sidebar nav ul li.active i {
            color: #fff;
        }
        .sidebar nav ul li i {
            color: #4299e1;
        }
        .sidebar nav ul li.active i {
            color: #fff;
        }
        .sidebar nav ul li:hover {
            background: rgba(66, 153, 225, 0.1);
        }
        .sidebar nav ul li.active:hover {
            background: rgb(35, 71, 250);
        }

        .main-content {
            flex: 1;
            min-width: 0;
            overflow-x: auto;
        }

        /* Compact table styling */
        .compact-table th,
        .compact-table td {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
        }

        /* Responsive cards */
        .responsive-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        @media (max-width: 640px) {
            .responsive-grid {
                grid-template-columns: 1fr;
            }
            .compact-table th,
            .compact-table td {
                padding: 0.375rem 0.5rem;
                font-size: 0.8rem;
            }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 50;
                transform: translateX(-100%);
                width: 250px;
            }
            .sidebar.open {
                transform: translateX(0);
            }
            .main-content {
                width: 100%;
                margin-left: 0;
            }
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 40;
                display: none;
            }
            .overlay.show {
                display: block;
            }
        }

        @media (min-width: 769px) {
            .sidebar {
                position: relative;
                transform: translateX(0);
                flex-shrink: 0;
            }
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile Overlay -->
    <div id="overlay" class="overlay"></div>

    <div class="flex h-screen bg-gray-100 overflow-hidden">
        <!-- Sidebar -->
        <aside id="sidebar" class="sidebar">
            <div class="logo">
                <h1 class="text-2xl font-bold">Admin Panel</h1>
            </div>
            <nav class="mt-4">
                <ul>
                    <li class="px-4 py-3 hover:bg-gray-600 rounded-lg mx-2">
                        <a href="index.php" class="flex items-center space-x-3">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <!-- Manajemen Booking -->
                    <li class="mb-2 mx-2">
                        <button onclick="toggleBookingSubmenu(event)" class="w-full flex items-center justify-between px-4 py-3 rounded-lg text-white focus:outline-none hover:bg-gray-600 transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-calendar-check"></i>
                                <span>Manajemen Booking</span>
                            </span>
                            <i id="arrowBookingIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="bookingSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'data_booking.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="data_booking.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Data Booking</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_lokasi.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_lokasi.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Kelola Lokasi</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_barber.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_barber.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-cut"></i>
                                    <span>Kelola Barber</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Manajemen Produk -->
                    <li class="mb-2 mx-2">
                        <button onclick="toggleProdukSubmenu(event)" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none hover:bg-gray-600 transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-box"></i>
                                <span>Manajemen Produk</span>
                            </span>
                            <i id="arrowProdukIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="produkSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_produk.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_produk.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-cube"></i>
                                    <span>Produk</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_pembeli.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_pembeli.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pembeli</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_metode_pembayaran.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_metode_pembayaran.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-money-check-alt"></i>
                                    <span>Metode Pembayaran</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- Manajemen Lowongan -->
                    <li class="mb-2 mx-2">
                        <button onclick="toggleLowonganSubmenu(event)" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none hover:bg-gray-600 transition-colors duration-200">
<div class="flex items-center">
    <i class="fas fa-briefcase mr-2"></i>
    <span>Manajemen Lowongan</span>
</div>

                            <i id="arrowLowonganIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="lowonganSubmenu" class="ml-8 mt-2 space-y-2 <?php echo (basename($_SERVER['PHP_SELF']) === 'kelola_lowongan.php' || basename($_SERVER['PHP_SELF']) === 'data_pelamar.php' || basename($_SERVER['PHP_SELF']) === 'whatsapp_manager.php') ? '' : 'hidden'; ?>">
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_lowongan.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_lowongan.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-tasks"></i>
                                    <span>Kelola Lowongan</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'data_pelamar.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="data_pelamar.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pelamar</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'whatsapp_manager.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="whatsapp_manager.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>Interview Schedulingr</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="px-4 py-3 hover:bg-gray-600 rounded-lg mx-2 <?php echo basename($_SERVER['PHP_SELF']) === 'kelola_admin.php' ? 'bg-blue-600' : ''; ?>">
                        <a href="kelola_admin.php" class="flex items-center space-x-3">
                            <i class="fas fa-user-shield"></i>
                            <span>Kelola Admin</span>
                        </a>
                    </li>

                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col main-content">
            <header class="flex items-center justify-between px-6 py-4 bg-white border-b-4 border-indigo-600">
                <div class="flex items-center">
                    <button id="sidebarToggle" class="text-gray-500 focus:outline-none md:hidden mr-4">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <h1 class="text-xl font-semibold">Interview Scheduling</h1>
                </div>
                <div class="flex items-center">
                    <span class="mr-4 text-gray-700 hidden sm:block"><?php echo $_SESSION['admin_full_name'] ?? 'Admin'; ?></span>
                    <div class="relative">
                        <button id="profileDropdown" class="flex items-center focus:outline-none">
                            <?php
                            // Get admin profile photo
                            $stmt = $conn->prepare("SELECT profile_photo FROM admin WHERE id = ?");
                            $stmt->execute([$_SESSION['admin_id']]);
                            $profile_photo = $stmt->fetchColumn();

                            if (!empty($profile_photo) && file_exists('uploads/profiles/' . $profile_photo)):
                            ?>
                                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-300 hover:border-blue-500 transition-colors"
                                     src="uploads/profiles/<?= htmlspecialchars($profile_photo) ?>"
                                     alt="Admin avatar">
                            <?php else: ?>
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-300 hover:border-blue-500 transition-colors">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                            <?php endif; ?>
                            <i class="fas fa-chevron-down ml-2 text-gray-500 text-sm"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="profileDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="profile_settings.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-3 text-gray-500"></i>
                                Profile Settings
                            </a>
                            <hr class="my-1">
                            <a href="logout.php" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-3 text-red-500"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 p-6 overflow-y-auto">
                <div class="max-w-7xl mx-auto">
                    <h1 class="text-3xl font-bold text-gray-800 mb-6">Interview Scheduling</h1>

                    <!-- Statistics Dashboard -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-paper-plane text-2xl text-blue-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-blue-600">Hari Ini</p>
                                    <p class="text-2xl font-bold text-blue-900"><?= $stats['today'] ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-2xl text-green-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-green-600">Berhasil</p>
                                    <p class="text-2xl font-bold text-green-900"><?= $stats['success_today'] ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-percentage text-2xl text-yellow-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-yellow-600">Success Rate</p>
                                    <p class="text-2xl font-bold text-yellow-900">
                                        <?= $stats['today'] > 0 ? round(($stats['success_today'] / $stats['today']) * 100) : 0 ?>%
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-users text-2xl text-purple-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-purple-600">Total Pelamar</p>
                                    <p class="text-2xl font-bold text-purple-900"><?= count($applications) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                <!-- Success/Error Messages -->
                <?php if ($message): ?>
                    <div class="mb-4 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <!-- Setup & Test -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold mb-4">🔧 Setup & Test</h2>

                    <!-- API Configuration -->
                    <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h3 class="font-medium text-blue-800 mb-2">API Configuration</h3>
                        <p class="text-sm text-blue-700 mb-3">Konfigurasi API WhatsApp provider untuk mengirim notifikasi.</p>
                        <a href="whatsapp_setup_guide.php" class="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                            <i class="fas fa-cog mr-2"></i>Setup API WhatsApp
                        </a>
                    </div>

                    <!-- Create Table -->
                    <div class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h3 class="font-medium text-yellow-800 mb-2">Database Setup</h3>
                        <p class="text-sm text-yellow-700 mb-3">Jika ini pertama kali menggunakan WhatsApp notification, buat tabel database terlebih dahulu.</p>
                        <form method="POST" class="inline">
                            <input type="hidden" name="action" value="create_table">
                            <button type="submit" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 text-sm">
                                <i class="fas fa-database mr-2"></i>Create Notification Table
                            </button>
                        </form>
                    </div>

                    <!-- Test Connection -->
                    <div>
                        <h3 class="font-medium text-gray-800 mb-3">Test WhatsApp Connection</h3>
                        <form method="POST" class="flex gap-4 mb-4">
                            <input type="hidden" name="action" value="test_connection">
                            <input type="text" name="test_phone" placeholder="+6281234567890"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                                <i class="fas fa-paper-plane mr-2"></i>Send Test
                            </button>
                        </form>

                        <div class="text-center">
                            <a href="whatsapp_diagnostic.php" class="inline-block px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 text-sm">
                                <i class="fas fa-stethoscope mr-2"></i>Diagnostic Tool
                            </a>
                            <p class="text-xs text-gray-500 mt-1">Jika pesan tidak sampai, gunakan diagnostic tool untuk troubleshooting</p>
                        </div>
                    </div>
                </div>

                <!-- Bulk Notification -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold mb-4">📢 Bulk Notification</h2>
                    <form method="POST" class="flex gap-4">
                        <input type="hidden" name="action" value="bulk_notify">
                        <select name="status_filter" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">Semua Status</option>
                            <option value="pending">Pending</option>
                            <option value="reviewed">Reviewed</option>
                            <option value="interview">Interview</option>
                            <option value="accepted">Accepted</option>
                            <option value="rejected">Rejected</option>
                        </select>
                        <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                onclick="return confirm('Kirim notifikasi ke semua pelamar dengan status yang dipilih?')">
                            <i class="fas fa-broadcast-tower mr-2"></i>Send Bulk
                        </button>
                    </form>
                </div>

                <!-- Manual Notification -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold mb-4">👤 Manual Notification</h2>
                    <div class="overflow-x-auto -mx-6 px-6">
                        <table class="min-w-full table-auto compact-table">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th class="px-4 py-2 text-left">Nama</th>
                                    <th class="px-4 py-2 text-left">Posisi</th>
                                    <th class="px-4 py-2 text-left">Telepon</th>
                                    <th class="px-4 py-2 text-left">Status</th>
                                    <th class="px-4 py-2 text-left">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($applications, 0, 10) as $app): ?>
                                <tr class="border-b">
                                    <td class="px-4 py-2"><?= htmlspecialchars($app['full_name']) ?></td>
                                    <td class="px-4 py-2"><?= htmlspecialchars($app['position']) ?></td>
                                    <td class="px-4 py-2"><?= htmlspecialchars($app['phone']) ?></td>
                                    <td class="px-4 py-2">
                                        <span class="px-2 py-1 rounded text-xs font-semibold
                                            <?php
                                            switch($app['status']) {
                                                case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                                case 'reviewed': echo 'bg-blue-100 text-blue-800'; break;
                                                case 'interview': echo 'bg-purple-100 text-purple-800'; break;
                                                case 'accepted': echo 'bg-green-100 text-green-800'; break;
                                                case 'rejected': echo 'bg-red-100 text-red-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800';
                                            }
                                            ?>">
                                            <?= ucfirst($app['status']) ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-2">
                                        <form method="POST" class="inline">
                                            <input type="hidden" name="action" value="send_notification">
                                            <input type="hidden" name="application_id" value="<?= $app['id'] ?>">
                                            <button type="submit" class="px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700">
                                                <i class="fab fa-whatsapp mr-1"></i>Send
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Notification Logs -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                        <h2 class="text-xl font-semibold mb-2 sm:mb-0">📋 Notification Logs</h2>
                        <div class="flex items-center space-x-2">
                            <label class="text-sm text-gray-600">Filter Status:</label>
                            <select onchange="filterLogs(this.value)" class="px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all" <?= $status_filter === 'all' ? 'selected' : '' ?>>Semua</option>
                                <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>Pending</option>
                                <option value="reviewed" <?= $status_filter === 'reviewed' ? 'selected' : '' ?>>Reviewed</option>
                                <option value="interview" <?= $status_filter === 'interview' ? 'selected' : '' ?>>Interview</option>
                                <option value="accepted" <?= $status_filter === 'accepted' ? 'selected' : '' ?>>Accepted</option>
                                <option value="rejected" <?= $status_filter === 'rejected' ? 'selected' : '' ?>>Rejected</option>
                            </select>
                        </div>
                    </div>
                    <?php if (empty($notification_logs)): ?>
                        <div class="text-center py-8">
                            <div class="text-gray-400 mb-4">
                                <i class="fas fa-inbox text-4xl"></i>
                            </div>
                            <p class="text-gray-600 mb-4">Belum ada log notifikasi.</p>
                            <p class="text-sm text-gray-500">Log akan muncul setelah mengirim notifikasi WhatsApp pertama.</p>
                        </div>
                    <?php else: ?>
                    <div class="overflow-x-auto -mx-6 px-6">
                        <table class="min-w-full table-auto compact-table">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th class="px-4 py-2 text-left">Waktu</th>
                                    <th class="px-4 py-2 text-left">Nama</th>
                                    <th class="px-4 py-2 text-left">Posisi</th>
                                    <th class="px-4 py-2 text-left">Telepon</th>
                                    <th class="px-4 py-2 text-left">Status App</th>
                                    <th class="px-4 py-2 text-left">Status WA</th>
                                    <th class="px-4 py-2 text-left">Pesan</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($notification_logs as $log): ?>
                                <tr class="border-b">
                                    <td class="px-4 py-2 text-sm"><?= date('d/m/Y H:i', strtotime($log['sent_at'])) ?></td>
                                    <td class="px-4 py-2"><?= htmlspecialchars($log['full_name']) ?></td>
                                    <td class="px-4 py-2"><?= htmlspecialchars($log['position']) ?></td>
                                    <td class="px-4 py-2"><?= htmlspecialchars($log['recipient']) ?></td>
                                    <td class="px-4 py-2">
                                        <span class="px-2 py-1 rounded text-xs font-semibold
                                            <?php
                                            switch($log['application_status']) {
                                                case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                                case 'reviewed': echo 'bg-blue-100 text-blue-800'; break;
                                                case 'interview': echo 'bg-purple-100 text-purple-800'; break;
                                                case 'accepted': echo 'bg-green-100 text-green-800'; break;
                                                case 'rejected': echo 'bg-red-100 text-red-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800';
                                            }
                                            ?>">
                                            <?= ucfirst($log['application_status']) ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-2">
                                        <?php if ($log['success']): ?>
                                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">✅ Sent</span>
                                        <?php else: ?>
                                            <span class="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">❌ Failed</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 py-2 text-sm max-w-xs truncate" title="<?= htmlspecialchars($log['message_sent']) ?>">
                                        <?= htmlspecialchars(substr($log['message_sent'], 0, 50)) ?>...
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
                </div>
            </main>
        </div>
    </div>

    <script>
    // Sidebar Toggle for Mobile
    document.addEventListener('DOMContentLoaded', function() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('open');
                overlay.classList.toggle('show');
            });
        }

        if (overlay) {
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
            });
        }

        // Auto-open current submenu
        const currentPage = '<?php echo basename($_SERVER['PHP_SELF']); ?>';
        if (currentPage === 'kelola_lowongan.php' || currentPage === 'data_pelamar.php' || currentPage === 'whatsapp_manager.php') {
            const lowonganSubmenu = document.getElementById('lowonganSubmenu');
            const lowonganIcon = document.getElementById('arrowLowonganIcon');
            if (lowonganSubmenu && lowonganIcon) {
                lowonganSubmenu.classList.remove('hidden');
                lowonganIcon.classList.add('rotate-180');
            }
        }
    });

    function toggleBookingSubmenu(event) {
        event.preventDefault();
        event.stopPropagation();
        const submenu = document.getElementById('bookingSubmenu');
        const icon = document.getElementById('arrowBookingIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleProdukSubmenu(event) {
        event.preventDefault();
        event.stopPropagation();
        const submenu = document.getElementById('produkSubmenu');
        const icon = document.getElementById('arrowProdukIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleLowonganSubmenu(event) {
        event.preventDefault();
        event.stopPropagation();
        const submenu = document.getElementById('lowonganSubmenu');
        const icon = document.getElementById('arrowLowonganIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    // Profile dropdown functionality
    document.addEventListener('DOMContentLoaded', function() {
        const profileDropdown = document.getElementById('profileDropdown');
        const profileDropdownMenu = document.getElementById('profileDropdownMenu');

        if (profileDropdown && profileDropdownMenu) {
            profileDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdownMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdownMenu.classList.add('hidden');
                }
            });
        }
    });

    function filterLogs(status) {
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('status_filter', status);
        window.location.href = currentUrl.toString();
    }
    </script>
</body>
</html>
