<?php
/**
 * Setup Profile System - Add profile photo functionality
 */

require_once 'db_config.php';

echo "<h2>Setup Profile System</h2>";

try {
    // Add profile_photo column to admin table if not exists
    $alterQueries = [
        "ALTER TABLE admin ADD COLUMN IF NOT EXISTS profile_photo VARCHAR(255) DEFAULT NULL COMMENT 'Profile photo filename'",
        "ALTER TABLE admin ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
        "ALTER TABLE admin ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ];
    
    foreach ($alterQueries as $query) {
        try {
            $conn->exec($query);
            echo "<p style='color: green;'>✅ Database structure updated successfully!</p>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<p style='color: orange;'>⚠️ Column already exists.</p>";
            } else {
                throw $e;
            }
        }
    }
    
    // Create uploads directory for profile photos
    $upload_dir = __DIR__ . '/uploads/profiles';
    if (!file_exists($upload_dir)) {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<p style='color: green;'>✅ Profile uploads directory created!</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create uploads directory!</p>";
        }
    } else {
        echo "<p style='color: blue;'>📁 Profile uploads directory already exists.</p>";
    }
    
    // Create .htaccess for security
    $htaccess_content = "# Prevent direct access to PHP files\n<Files \"*.php\">\nOrder Deny,Allow\nDeny from all\n</Files>\n\n# Allow image files\n<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">\nOrder Allow,Deny\nAllow from all\n</FilesMatch>";
    
    $htaccess_file = $upload_dir . '/.htaccess';
    if (!file_exists($htaccess_file)) {
        if (file_put_contents($htaccess_file, $htaccess_content)) {
            echo "<p style='color: green;'>✅ Security .htaccess created!</p>";
        }
    }
    
    echo "<h3>✅ Profile System Setup Complete!</h3>";
    echo "<p>You can now:</p>";
    echo "<ul>";
    echo "<li>Upload profile photos for admin users</li>";
    echo "<li>Use dropdown menu in header</li>";
    echo "<li>Access profile settings</li>";
    echo "</ul>";
    
    echo "<p><a href='data_pelamar.php' style='background: #3b82f6; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Go to Admin Panel</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Unexpected error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Profile System Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        h2 { color: #333; border-bottom: 2px solid #3b82f6; padding-bottom: 10px; }
        h3 { color: #555; margin-top: 30px; }
        p { line-height: 1.6; }
        ul { margin-left: 20px; }
        li { margin-bottom: 5px; }
    </style>
</head>
<body>
    <!-- Content is generated by PHP above -->
</body>
</html>
