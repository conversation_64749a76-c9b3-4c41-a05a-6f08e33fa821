<?php
session_start();
require_once 'admin/db_config.php';

// Check if job ID is provided
if (!isset($_GET['id'])) {
    header("Location: jobs.php");
    exit();
}

$job_id = $_GET['id'];

// Ambil data lowongan dari database
try {
    $stmt = $conn->prepare("SELECT * FROM jobs WHERE id = ?");
    $stmt->execute([$job_id]);
    $job = $stmt->fetch();
    
    if (!$job) {
        header("Location: jobs.php");
        exit();
    }
    
    $_SESSION['job_application'] = [
        'job_id' => $job_id,
        'position' => $job['position'],
        'location' => $job['location']
    ];
    
} catch (PDOException $e) {
    header("Location: jobs.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $job['position']; ?> - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding-bottom: 100px;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .content-container {
            min-height: calc(100vh - 200px);
        }
        .fixed-bottom-button {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 50;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem;
            box-shadow: 0 -10px 25px -5px rgba(0, 0, 0, 0.1);
        }
        .floating-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .job-header-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
        .info-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            transition: all 0.3s ease;
        }
        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px -5px rgba(0, 0, 0, 0.15);
        }
        .badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .salary-highlight {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }
        .btn-apply {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px 0 rgba(102, 126, 234, 0.3);
        }
        .btn-apply:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px 0 rgba(102, 126, 234, 0.4);
        }
        .requirement-item, .benefit-item {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 8px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        .requirement-item:hover, .benefit-item:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateX(5px);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="sticky top-0 z-40 floating-header">
 <div class="relative flex items-center justify-center h-16 border-b">
    <!-- Ikon Kembali -->
<a href="jobs.php" class="absolute left-4 text-gray-600 text-xl">
    <i class="fas fa-arrow-left"></i>
</a>


    <!-- Judul di Tengah -->
    <div class="text-center">
        <h1 class="text-lg font-bold text-gray-800">Detail Lowongan</h1>
        <p class="text-sm text-gray-600">Pixel Barbershop</p>
    </div>
</div>

    </header>

    <!-- Job Header dengan Informasi Tambahan -->
<div class="px-4 py-6 bg-white rounded-xl shadow">
    <div class="flex flex-col md:flex-row md:items-center">
        <!-- Gambar Posisi -->
        <img src="assets/<?= htmlspecialchars($job['image']) ?>" alt="<?= htmlspecialchars($job['position']) ?>" 
             class="w-16 h-16 rounded-lg object-cover mb-4 md:mb-0 md:mr-6">

        <!-- Detail Posisi -->
        <div class="flex-1">
            <h2 class="text-2xl font-bold text-gray-900"><?= htmlspecialchars($job['position']) ?></h2>

            <!-- Informasi Lokasi, Tipe, dan Tanggal -->
            <div class="flex flex-wrap items-center mt-2 gap-x-6 gap-y-2 text-sm text-gray-600">
                <div class="flex items-center gap-1">
                    <i class="fas fa-map-marker-alt text-[#0A5144]"></i>
                    <span><?= htmlspecialchars($job['location']) ?></span>
                </div>
                <div class="flex items-center gap-1">
                    <i class="fas fa-clock text-[#0A5144]"></i>
                    <span><?= htmlspecialchars($job['type']) ?></span>
                </div>
                <div class="flex items-center gap-1">
                    <i class="fas fa-calendar text-[#0A5144]"></i>
                    <span>Diposting: <?= htmlspecialchars($job['posted']) ?></span>
                </div>
            </div>

            <!-- Gaji -->
          
<div class="mt-4 text-[#0A5144] font-semibold text-lg">
    Rate Gaji: <?= htmlspecialchars($job['salary']) ?>
</div>


            <!-- Label Tambahan -->
            <div class="flex flex-wrap gap-2 mt-3">
                <?php if(!empty($job['experience_level'])): ?>
                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                        <?= htmlspecialchars($job['experience_level']) ?>
                    </span>
                <?php endif; ?>
                <?php if(!empty($job['education'])): ?>
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                        <?= htmlspecialchars($job['education']) ?>
                    </span>
                <?php endif; ?>
                <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">
                    <?= htmlspecialchars($job['type']) ?>
                </span>
            </div>
        </div>
    </div>
</div>


    <!-- Informasi Tambahan -->
    <div class="px-4 py-4 max-w-4xl mx-auto content-container">
        <!-- Deskripsi Pekerjaan -->
        <div class="bg-white rounded-xl shadow-sm p-5 mb-6">
            <h3 class="text-lg font-bold text-gray-900 mb-3">
                <i class="fas fa-file-alt text-[#0A5144] mr-2"></i>
                Deskripsi Pekerjaan
            </h3>
            <div class="text-gray-700 leading-relaxed">
                <?= nl2br(htmlspecialchars($job['description'])) ?>
            </div>
        </div>

        <!-- Kualifikasi -->
        <div class="bg-white rounded-xl shadow-sm p-5 mb-6">
            <h3 class="text-lg font-bold text-gray-900 mb-3">
                <i class="fas fa-clipboard-check text-[#0A5144] mr-2"></i>
                Kualifikasi & Persyaratan
            </h3>
            <?php if (!empty($job['requirements'])): ?>
                <?php
                // Handle both JSON array and plain text
                $requirements_text = $job['requirements'];
                if (json_decode($requirements_text) !== null) {
                    $requirements = json_decode($requirements_text, true);
                } else {
                    // Split by newlines if it's plain text
                    $requirements = array_filter(explode("\n", $requirements_text));
                }
                ?>
                <ul class="space-y-3">
                    <?php foreach ($requirements as $requirement): ?>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-[#0A5144] mt-1 mr-3 flex-shrink-0"></i>
                        <span class="text-gray-700"><?= htmlspecialchars(trim($requirement)) ?></span>
                    </li>
                    <?php endforeach; ?>
                </ul>
            <?php else: ?>
                <p class="text-gray-500 italic">Tidak ada kualifikasi khusus yang disebutkan.</p>
            <?php endif; ?>
        </div>

        <!-- Benefit -->
        <div class="bg-white rounded-xl shadow-sm p-5 mb-8">
            <h3 class="text-lg font-bold text-gray-900 mb-3">
                <i class="fas fa-gift text-[#0A5144] mr-2"></i>
                Benefit & Fasilitas
            </h3>
            <?php if (!empty($job['benefits'])): ?>
                <?php
                // Handle both JSON array and plain text
                $benefits_text = $job['benefits'];
                if (json_decode($benefits_text) !== null) {
                    $benefits = json_decode($benefits_text, true);
                } else {
                    // Split by newlines if it's plain text
                    $benefits = array_filter(explode("\n", $benefits_text));
                }
                ?>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <?php foreach ($benefits as $benefit): ?>
                    <div class="flex items-start">
                        <i class="fas fa-check-circle text-[#0A5144] mt-1 mr-3 flex-shrink-0"></i>
                        <span class="text-gray-700"><?= htmlspecialchars(trim($benefit)) ?></span>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p class="text-gray-500 italic">Informasi benefit akan dijelaskan lebih lanjut saat interview.</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- Tombol Lamar - Fixed Bottom -->
    <div class="fixed-bottom-button">
        <div class="max-w-4xl mx-auto">
            <a href="application_form.php?id=<?= $job_id ?>"
               class="block w-full bg-[#0A5144] text-white text-center py-4 rounded-lg font-semibold hover:bg-[#094239] transition-all duration-200 shadow-lg">
                <i class="fas fa-paper-plane mr-2"></i>
                Lamar Sekarang
            </a>
        </div>
    </div>
</body>
</html>