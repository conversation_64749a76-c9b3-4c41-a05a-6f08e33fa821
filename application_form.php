<?php
session_start();

// Check if job application is in session
if (!isset($_SESSION['job_application'])) {
    header("Location: jobs.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Form - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="sticky top-0 z-10 bg-white p-4 shadow-sm">
        <div class="flex items-center justify-between">
            <a href="job_details.php?id=<?php echo $_SESSION['job_application']['job_id']; ?>" class="text-gray-600">
                <i class="fas fa-arrow-left text-lg"></i>
            </a>
            <h1 class="text-xl font-bold text-gray-900">Application Form</h1>
            <div class="w-6"></div>
        </div>
    </header>

    <!-- Application Info -->
    <div class="px-4 py-4 bg-white shadow-sm mb-1">
        <div class="flex items-center">
            <div class="bg-[#0A5144] text-white rounded-full w-10 h-10 flex items-center justify-center mr-3">
                <i class="fas fa-briefcase"></i>
            </div>
            <div>
                <h2 class="font-bold text-gray-900"><?php echo $_SESSION['job_application']['position']; ?></h2>
                <p class="text-sm text-gray-600"><?php echo $_SESSION['job_application']['location']; ?></p>
            </div>
        </div>
    </div>

    <!-- Application Form -->
    <form action="application_confirm.php" method="post" enctype="multipart/form-data" class="px-4 py-4 max-w-4xl mx-auto">
        <div class="space-y-5">
            <!-- Personal Information -->
            <div class="bg-white rounded-xl shadow-sm p-5">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Personal Information</h3>
                
                <div class="space-y-4">
                    <div>
                        <label for="full_name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                        <input type="text" id="full_name" name="full_name" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0A5144] focus:border-transparent">
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" id="email" name="email" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0A5144] focus:border-transparent">
                        </div>
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                            <input type="tel" id="phone" name="phone" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0A5144] focus:border-transparent">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                            <input type="text" id="address" name="address" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0A5144] focus:border-transparent">
                        </div>
                        <div>
                            <label for="city" class="block text-sm font-medium text-gray-700 mb-1">City</label>
                            <input type="text" id="city" name="city" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0A5144] focus:border-transparent">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Professional Information -->
            <div class="bg-white rounded-xl shadow-sm p-5">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Professional Information</h3>
                
                <div class="space-y-4">
                    <div>
                        <label for="experience" class="block text-sm font-medium text-gray-700 mb-1">Years of Experience</label>
                        <select id="experience" name="experience" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0A5144] focus:border-transparent">
                            <option value="">Select</option>
                            <option value="0-1">0-1 years</option>
                            <option value="1-3">1-3 years</option>
                            <option value="3-5">3-5 years</option>
                            <option value="5+">5+ years</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="skills" class="block text-sm font-medium text-gray-700 mb-1">Skills (comma separated)</label>
                        <textarea id="skills" name="skills" rows="2" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0A5144] focus:border-transparent" placeholder="e.g., Fade cuts, Straight razor shaves, Beard grooming"></textarea>
                    </div>

                    <div>
                        <label for="cv" class="block text-sm font-medium text-gray-700 mb-1">Upload CV</label>
                        <div class="flex items-center">
                            <input type="file" id="cv" name="cv" accept=".pdf,.doc,.docx" required class="hidden">
                            <label for="cv" class="px-4 py-2 border border-gray-300 rounded-lg cursor-pointer bg-white hover:bg-gray-50 transition-colors">
                                <i class="fas fa-upload mr-2 text-[#0A5144]"></i>Upload CV
                            </label>
                            <span id="cv-name" class="ml-3 text-sm text-gray-500">No file selected</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Accepted formats: PDF, DOC, DOCX (Max 5MB)</p>
                    </div>

                    <div>
                        <label for="certificate" class="block text-sm font-medium text-gray-700 mb-1">Upload Sertifikat Keahlian (Opsional)</label>
                        <div class="flex items-center">
                            <input type="file" id="certificate" name="certificate" accept=".pdf,.jpg,.jpeg,.png" class="hidden">
                            <label for="certificate" class="px-4 py-2 border border-gray-300 rounded-lg cursor-pointer bg-white hover:bg-gray-50 transition-colors">
                                <i class="fas fa-certificate mr-2 text-[#0A5144]"></i>Upload Certificate
                            </label>
                            <span id="certificate-name" class="ml-3 text-sm text-gray-500">No file selected</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Accepted formats: PDF, JPG, PNG (Max 5MB)</p>
                    </div>
                </div>
            </div>
            

            <!-- Cover Letter -->
            <div class="bg-white rounded-xl shadow-sm p-5">
                <h3 class="text-lg font-bold text-gray-900 mb-4">Cover Letter</h3>

                <div>
                    <label for="cover_letter" class="block text-sm font-medium text-gray-700 mb-1">Why do you want to work at Pixel Barbershop?</label>
                    <textarea id="cover_letter" name="cover_letter" rows="5" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0A5144] focus:border-transparent"></textarea>
                </div>
            </div>


        </div>
        
        <!-- Submit Button -->
        <div class="mt-6">
            <button type="submit" class="gradient-bg w-full py-3 rounded-lg text-white font-bold shadow-md hover:opacity-90 transition">
                Submit Application
            </button>
        </div>
    </form>

    <script>
        // Show selected file names for CV upload
        document.getElementById('cv').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'No file selected';
            document.getElementById('cv-name').textContent = fileName;
        });

        // Show selected file names for Certificate upload
        document.getElementById('certificate').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'No file selected';
            document.getElementById('certificate-name').textContent = fileName;
        });
    </script>
</body>
</html>