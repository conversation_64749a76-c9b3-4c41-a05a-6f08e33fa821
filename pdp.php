<?php
error_log("DEBUG: pdp.php loaded at " . date('Y-m-d H:i:s') . ".\n", 3, "Admin/debug.log");

// Sertikan file konfigurasi database
require_once 'Admin/db_config.php';

// Path untuk folder uploads (digunakan untuk menampilkan gambar)
$upload_dir_display = '/aplikasi-pixel/uploads/'; // Path absolut dari root ke folder uploads
$variant_upload_dir_display = '/aplikasi-pixel/uploads/variants/'; // Path untuk gambar varian

$product = null;
$error_message = '';
$product_variants = [];

if (isset($_GET['id']) && !empty($_GET['id'])) {
    try {
        $product_id = $_GET['id'];
        
        // Fetch product details
        $stmt = $conn->prepare("SELECT 
            p.id, 
            p.nama_produk as nama_produk, 
            p.deskrip<PERSON> as deskripsi_produk, 
            p.harga as harga_produk, 
            p.gambar_utama as gambar_utama 
        FROM produk p 
        WHERE p.id = :id");
        $stmt->execute(['id' => $product_id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            $error_message = "Produk tidak ditemukan";
        } else {
            // Debug: Tampilkan detail produk
            error_log("Product ID: " . $product['id']);
            error_log("Product Name: " . $product['nama_produk']);
            error_log("Product Image: " . $product['gambar_utama']);
            error_log("Upload Directory: " . $upload_dir_display);
            error_log("Full Image Path: " . $upload_dir_display . $product['gambar_utama']);
        }

        // Get product variations from database
        $stmt = $conn->prepare("SELECT * FROM variasi_produk WHERE produk_id = :id");
        $stmt->execute(['id' => $product_id]);
        $product_variants = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (isset($product_id)) {
            // DEBUG: Log the query and product_id
            error_log("DEBUG: Fetching additional images for product_id: " . $product_id . " with query: SELECT gambar, label FROM gambar_produk WHERE produk_id = ?");

            $stmt = $conn->prepare("SELECT gambar, label FROM gambar_produk WHERE produk_id = ?");
            $stmt->execute([$product_id]);
            $additionalImages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
    } catch (PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
        error_log("Database Error: " . $e->getMessage());
    }
} else {
    $error_message = "ID produk tidak valid";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $product ? htmlspecialchars($product['nama_produk']) : 'Detail Produk'; ?> - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
        }
        .image-gallery-thumbnail {
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .image-gallery-thumbnail.active, .image-gallery-thumbnail:hover {
            border-color: #0A5144;
        }
        .variant-option .variant-thumbnail.active-variant {
            border-color: #0A5144; /* Emerald-700 like */
            box-shadow: 0 0 0 2px #0A5144;
        }
        .tab-button {
            transition: all 0.3s ease;
        }
        .tab-button.active {
            color: #0A5144;
            border-bottom: 2px solid #0A5144;
        }
    </style>
</head>
<body class="pb-32">
    <!-- Header -->
    <header class="sticky top-0 z-10 gradient-bg text-white p-4 shadow-md">
        <div class="flex items-center justify-between">
            <a href="javascript:history.back()" class="text-white">
                <i class="fas fa-arrow-left text-lg"></i>
            </a>
            <div class="flex items-center space-x-4">
                <!-- Share Button with Dropdown -->
                <div class="relative">
                    <!-- Share Button - Responsive -->
                    <button id="shareButton"
                            class="text-white flex items-center justify-center space-x-1 sm:space-x-2
                                   bg-gray-800 bg-opacity-50 hover:bg-opacity-70
                                   rounded-full p-2 sm:p-3 transition-all duration-200
                                   backdrop-blur-sm border border-white border-opacity-20">
                        <i class="fas fa-share-alt text-sm sm:text-base"></i>
                        <span class="hidden sm:inline text-sm font-medium">Share</span>
                    </button>

                    <!-- Share Options Dropdown - Responsive -->
                    <div id="shareDropdown"
                         class="hidden absolute right-0 mt-2 w-44 sm:w-52 bg-white rounded-xl shadow-xl
                                border border-gray-200 z-50 overflow-hidden">

                        <!-- WhatsApp Share -->
                        <a href="https://wa.me/?text=<?php echo urlencode('Lihat produk ini: ' . (isset($product['nama_produk']) ? $product['nama_produk'] : 'Produk Menarik') . ' - ' . 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>"
                           target="_blank"
                           class="block px-3 py-3 sm:px-4 sm:py-3 hover:bg-green-50 flex items-center space-x-3 text-gray-700 transition-colors duration-200 group">
                            <div class="flex-shrink-0">
                                <i class="fab fa-whatsapp text-green-500 text-lg sm:text-xl group-hover:scale-110 transition-transform duration-200"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <span class="text-sm sm:text-base font-medium">WhatsApp</span>
                                <p class="text-xs text-gray-500 hidden sm:block">Bagikan ke WhatsApp</p>
                            </div>
                        </a>

                        <!-- Instagram Share -->
                        <a href="javascript:void(0)"
                           onclick="shareToInstagram()"
                           class="block px-3 py-3 sm:px-4 sm:py-3 hover:bg-pink-50 flex items-center space-x-3 text-gray-700 transition-colors duration-200 group">
                            <div class="flex-shrink-0">
                                <i class="fab fa-instagram text-pink-500 text-lg sm:text-xl group-hover:scale-110 transition-transform duration-200"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <span class="text-sm sm:text-base font-medium">Instagram</span>
                                <p class="text-xs text-gray-500 hidden sm:block">Bagikan ke Instagram</p>
                            </div>
                        </a>

                        <!-- Facebook Share -->
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode('http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>"
                           target="_blank"
                           class="block px-3 py-3 sm:px-4 sm:py-3 hover:bg-blue-50 flex items-center space-x-3 text-gray-700 transition-colors duration-200 group">
                            <div class="flex-shrink-0">
                                <i class="fab fa-facebook text-blue-600 text-lg sm:text-xl group-hover:scale-110 transition-transform duration-200"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <span class="text-sm sm:text-base font-medium">Facebook</span>
                                <p class="text-xs text-gray-500 hidden sm:block">Bagikan ke Facebook</p>
                            </div>
                        </a>

                        <!-- Copy Link -->
                        <a href="javascript:void(0)"
                           onclick="copyToClipboard()"
                           class="block px-3 py-3 sm:px-4 sm:py-3 hover:bg-gray-50 flex items-center space-x-3 text-gray-700 transition-colors duration-200 group border-t border-gray-100">
                            <div class="flex-shrink-0">
                                <i class="fas fa-copy text-gray-600 text-lg sm:text-xl group-hover:scale-110 transition-transform duration-200"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <span class="text-sm sm:text-base font-medium">Copy Link</span>
                                <p class="text-xs text-gray-500 hidden sm:block">Salin link produk</p>
                            </div>
                        </a>
                    </div>
                </div>
                <!-- Cart Button -->
                <a href="keranjang.php" class="text-white relative">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        <?php 
                        // Get cart count from session or database
                        $cartCount = isset($_SESSION['cart_count']) ? $_SESSION['cart_count'] : 0;
                        echo $cartCount;
                        ?>
                    </span>
                </a>
            </div>
        </div>
    </header>

    <!-- Product Gallery -->
    <div class="relative">
        <div class="swiper-container">
            <div class="swiper-wrapper">
                <div class="swiper-slide">
                    <img src="<?php echo $product && !empty($product['gambar_utama']) ? $upload_dir_display . htmlspecialchars($product['gambar_utama']) : 'https://via.placeholder.com/1000x800.png?text=No+Image'; ?>" 
                         alt="<?php echo $product ? htmlspecialchars($product['nama_produk']) : 'Gambar Produk'; ?>" 
                         id="main-product-image" 
                         class="w-full h-80 object-cover">
                </div>
            </div>
            <div class="swiper-pagination"></div>
        </div>
        <div class="absolute top-4 right-4 bg-white/80 text-[#0A5144] text-xs font-bold px-2 py-1 rounded-full">
            <i class="fas fa-camera mr-1"></i> <?php echo ($product && !empty($product['gambar_utama'])) ? '1/1' : '0/0'; ?>
        </div>
        <div class="absolute bottom-4 left-4 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
            -20%
        </div>
    </div>

  <!-- Thumbnail Gallery -->
    <div class="flex space-x-2 px-4 py-2 overflow-x-auto">
        <!-- Gambar utama -->
        <div class="image-gallery-thumbnail active flex-shrink-0 w-20 h-20 rounded-md overflow-hidden">
            <img src="<?php 
                $gambarPath = !empty($product['gambar_utama']) ? 
                    '/aplikasi-pixel/uploads/' . htmlspecialchars($product['gambar_utama']) : 
                    'https://via.placeholder.com/160x160.png?text=N/A';
                
                // Debug: Check if image exists
                $fullPath = $_SERVER['DOCUMENT_ROOT'] . $gambarPath;
                if (!empty($product['gambar_utama']) && !file_exists($fullPath)) {
                    error_log("Error: Image not found at path: " . $fullPath);
                    error_log("Database filename: " . $product['gambar_utama']);
                    $gambarPath = 'https://via.placeholder.com/160x160.png?text=Image%20Not%20Found';
                }
                
                echo $gambarPath; ?>" 
                 alt="<?php echo $product ? htmlspecialchars($product['nama_produk']) : 'Gambar Produk'; ?>" 
                 class="w-full h-full object-cover">
        </div>

        <!-- Gambar tambahan -->
        <?php
        if ($product) {
            $stmt = $conn->prepare("SELECT gambar FROM gambar_produk WHERE produk_id = ?");
            $stmt->execute([$product['id']]);
            $additionalImages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($additionalImages) {
                foreach ($additionalImages as $image) {
                    $gambarPath = '/aplikasi-pixel/uploads/' . htmlspecialchars($image['gambar']);
                    
                    // Debug: Check if additional image exists
                    $fullPath = $_SERVER['DOCUMENT_ROOT'] . $gambarPath;
                    if (!file_exists($fullPath)) {
                        error_log("Error: Additional image not found at path: " . $fullPath);
                        error_log("Database filename: " . $image['gambar']);
                        $gambarPath = 'https://via.placeholder.com/160x160.png?text=Image%20Not%20Found';
                    }
                    ?>
                    <div class="image-gallery-thumbnail flex-shrink-0 w-20 h-20 rounded-md overflow-hidden">
                        <img src="<?php echo $gambarPath; ?>" 
                             alt="<?php echo $product ? htmlspecialchars($product['nama_produk']) : 'Gambar Produk'; ?>" 
                             class="w-full h-full object-cover">
                    </div>
                    <?php
                }
            }
        }
        ?>
    </div>

    <!-- Product Info -->
    <div class="px-4 py-3">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-xl font-bold text-gray-900"><?php echo $product ? htmlspecialchars($product['nama_produk']) : 'Nama Produk Tidak Tersedia'; ?></h1>
                <p class="text-sm text-gray-500"><?php echo $product ? htmlspecialchars(substr($product['deskripsi_produk'], 0, 50)) . (strlen($product['deskripsi_produk']) > 50 ? '...' : '') : 'Deskripsi singkat tidak tersedia'; ?></p>
            </div>
            <div class="flex items-center text-yellow-400 text-sm">
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
                <i class="fas fa-star"></i>
                <i class="fas fa-star-half-alt"></i>
                <span class="text-gray-500 ml-1">(128)</span>
            </div>
        </div>

        <!-- Variasi Produk -->
        <div id="variant-options-container" class="mt-4">
            <?php if (!empty($product_variants)): ?>
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">Pilih Ukuran</h3>
                    <div class="flex flex-wrap gap-2">
                        <?php foreach ($product_variants as $variant): ?>
                            <div class="variant-option flex flex-col items-center border-2 border-gray-300 rounded-lg p-2 cursor-pointer hover:border-emerald-500 transition-colors
                                   <?php echo $variant['stok'] <= 0 ? 'opacity-50 cursor-not-allowed bg-red-50' : ''; ?>"
                                   data-variant-id="<?php echo $variant['id']; ?>"
                                   data-variant-price="<?php echo $variant['harga']; ?>"
                                   data-variant-stock="<?php echo $variant['stok']; ?>"
                                   title="<?php echo $variant['stok'] <= 0 ? 'Habis stok' : ''; ?>">
                                <span class="mr-2"><?php echo htmlspecialchars($variant['ukuran_ml']); ?></span>
                                <span class="text-sm text-gray-500">(Stok: <?php echo $variant['stok']; ?>)</span>
                                <span class="ml-2 text-sm"><?php echo 'Rp ' . number_format($variant['harga'], 0, ',', '.'); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <div class="mt-3">
            <div class="flex items-center">
                <span id="product-unit-price" class="text-[#0A5144] font-bold text-xl">Rp <?php echo $product ? number_format($product['harga_produk'], 0, ',', '.') : '0'; ?></span>
                <span class="text-gray-400 text-sm line-through ml-2">Rp 150.000</span>
                <span class="bg-red-100 text-red-600 text-xs font-medium ml-2 px-2 py-0.5 rounded">20% OFF</span>
            </div>
        </div>

        <div class="mt-4">
            <div class="flex items-center space-x-4 text-sm">
              
            </div>
        </div>
    </div>

    <!-- Product Tabs -->
    <div class="px-4 py-4">


        <!-- Tab Content -->
        <div class="space-y-6">
            <!-- Description Tab Content -->
            <div id="description-content" class="tab-content active">
                <h3 class="text-lg font-medium mb-4">Product Description</h3>
                <p class="text-gray-600 leading-relaxed">
                    <?php echo $product ? nl2br(htmlspecialchars($product['deskripsi_produk'])) : 'No description available'; ?>
                </p>
            </div>

            <!-- Ingredients Tab Content -->
            <div id="ingredients-content" class="tab-content hidden">
                <h3 class="text-lg font-medium mb-4">Ingredients</h3>
                <p class="text-gray-600 leading-relaxed">
                    <?php echo $product && isset($product['ingredients']) ? nl2br(htmlspecialchars($product['ingredients'])) : 'Ingredients information coming soon'; ?>
                </p>
            </div>

            <!-- Reviews Tab Content -->
            <div id="reviews-content" class="tab-content hidden">
                <h3 class="text-lg font-medium mb-4">Customer Reviews</h3>
                <p class="text-gray-600 leading-relaxed">
                    <?php if ($product && isset($product['reviews'])): ?>
                        <?php echo nl2br(htmlspecialchars($product['reviews'])); ?>
                    <?php else: ?>
                        Be the first to review this product!
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </div>




    <!-- Tab Content -->
    <div class="px-4 py-4">
        <h3 class="font-bold text-gray-900 mb-2">Product Description</h3>
        <p class="text-gray-600 text-sm mb-4">
            <?php echo $product ? nl2br(htmlspecialchars($product['deskripsi_produk'])) : 'Deskripsi produk tidak tersedia.'; ?>
        </p>
        
        <h3 class="font-bold text-gray-900 mb-2">Benefits</h3>
        <ul class="text-gray-600 text-sm space-y-2 mb-4">
            <li class="flex items-start">
                <i class="fas fa-check text-[#0A5144] mr-2 mt-0.5"></i>
                <span>Softens and conditions beard hair</span>
            </li>
            <li class="flex items-start">
                <i class="fas fa-check text-[#0A5144] mr-2 mt-0.5"></i>
                <span>Reduces itchiness and beardruff</span>
            </li>
            <li class="flex items-start">
                <i class="fas fa-check text-[#0A5144] mr-2 mt-0.5"></i>
                <span>Promotes healthy beard growth</span>
            </li>
            <li class="flex items-start">
                <i class="fas fa-check text-[#0A5144] mr-2 mt-0.5"></i>
                <span>100% natural ingredients</span>
            </li>
        </ul>
        
        <h3 class="font-bold text-gray-900 mb-2">How To Use</h3>
        <p class="text-gray-600 text-sm">
            Apply 3-5 drops to palm, rub hands together, then massage into beard and skin. Use daily for best results.
        </p>
    </div>

    <!-- Fixed Add to Cart Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-white shadow-xl border-t px-6 py-4 z-30">
      <div class="flex items-center justify-between max-w-xl mx-auto">
        <!-- Total Price -->
        <div class="text-left">
          <div class="text-xs text-gray-500 uppercase tracking-wide">Total</div>
          <div id="total-price-display" class="text-[#0A5144] font-extrabold text-xl mt-1">
            Rp <?php echo $product ? number_format($product['harga_produk'], 0, ',', '.') : '0'; ?>
          </div>
        </div>

        <!-- Buttons -->
        <div class="flex items-center gap-x-4">
          <!-- Add to Cart Button -->
          <button onclick="showCheckoutModal()" class="gradient-bg text-white p-3 rounded-lg font-semibold hover:opacity-90 transition-opacity flex items-center justify-center shadow-lg">
            <i class="fas fa-cart-plus"></i>
          </button>

          <!-- Checkout Button -->
          <button onclick="showCheckoutModal()" class="gradient-bg text-white px-8 py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity flex items-center gap-2 shadow-lg">
            Checkout
          </button>
        </div>
      </div>
    </div>


            <!-- Checkout Modal -->
            <div id="checkoutModal" class="fixed inset-0 bg-black/50 hidden flex items-end justify-center z-40">
                <div class="bg-white rounded-lg w-full max-w-md mx-4 transform transition-all duration-300 ease-in-out mb-4">
                    <div class="p-4">
                        <div class="flex justify-between items-center mb-6">
                            <div class="flex items-center space-x-3">
                                <img src="<?php 
                                    $gambarPath = $upload_dir_display . ($product ? htmlspecialchars($product['gambar_utama']) : '');
                                    if (!file_exists($_SERVER['DOCUMENT_ROOT'] . '/aplikasi-pixel/uploads/' . ($product ? htmlspecialchars($product['gambar_utama']) : '')) && $product) {
                                        $fileParts = pathinfo($product['gambar_utama']);
                                        $baseName = $fileParts['filename'];
                                        $gambarPath = '/aplikasi-pixel/uploads/' . $baseName . '_23.jpeg';
                                    }
                                    echo !file_exists($_SERVER['DOCUMENT_ROOT'] . '/aplikasi-pixel/uploads/' . ($product ? htmlspecialchars($product['gambar_utama']) : '')) ? 'https://via.placeholder.com/300x200.png?text=No+Image' : $gambarPath;
                                ?>" alt="<?php echo $product ? htmlspecialchars($product['nama_produk']) : ''; ?>" class="w-20 h-20 object-cover rounded" id="checkout-product-image-modal">
                                <div>
                                    <h3 class="font-semibold text-lg"><?php echo $product ? htmlspecialchars($product['nama_produk']) : ''; ?></h3>
                                    <p class="text-gray-600 mt-1" id="checkout-product-price-modal">Rp <?php echo $product ? number_format($product['harga_produk'], 0, ',', '.') : '0'; ?></p>
                                </div>
                            </div>
                            <button onclick="hideCheckoutModal()" class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <!-- Variant Selection -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700">Pilih Varian</label>
                            <div class="grid grid-cols-2 sm:grid-cols-4 gap-2">
                                <?php if (!empty($product_variants)): ?>
                                    <?php foreach ($product_variants as $variant): ?>
                                        <?php
                                        $variantImageFilename = htmlspecialchars($variant['gambar_varian']);
                                        $fullDisplayPath = $variant_upload_dir_display . $variantImageFilename;
                                        
                                        // Construct the server file path for checking existence
                                        $serverFilePath = $_SERVER['DOCUMENT_ROOT'] . $fullDisplayPath;
                                        
                                        $imageSrc = $fullDisplayPath;
                                        $altText = htmlspecialchars($variant['ukuran_ml']);

                                        error_log("Modal Variant Image Filename: " . $variant['gambar_varian']);
                                        error_log("Modal Full Display Path: " . $fullDisplayPath);
                                        error_log("Modal Full Server Path for check: " . $serverFilePath);

                                        if (empty($variantImageFilename) || !file_exists($serverFilePath)) {
                                            $imageSrc = 'https://via.placeholder.com/64x64.png?text=No+Variant+Image';
                                            error_log("Modal Variant image NOT FOUND or path empty: " . $serverFilePath);
                                        } else {
                                            error_log("Modal Variant image FOUND: " . $serverFilePath);
                                        }
                                        ?>
                                        <div class="variant-option flex flex-col items-center border-2 border-gray-300 rounded-lg p-2 cursor-pointer hover:border-emerald-500 transition-colors
                                               <?php echo $variant['stok'] <= 0 ? 'opacity-50 cursor-not-allowed bg-red-50' : ''; ?>"
                                               data-variant-id="<?php echo $variant['id']; ?>"
                                               data-variant-price="<?php echo $variant['harga']; ?>"
                                               data-variant-stock="<?php echo $variant['stok']; ?>"
                                               onclick="selectVariant(this, '<?php echo $imageSrc; ?>', '<?php echo $altText; ?>', '<?php echo $variant['harga']; ?>', '<?php echo $variant['stok']; ?>');">
                                            <div class="w-16 h-16 aspect-square overflow-hidden rounded-lg mb-1">
                                                <img src="<?php echo $imageSrc; ?>"
                                                     alt="<?php echo $altText; ?>"
                                                     class="w-full h-full object-cover">
                                            </div>
                                            <p class="text-center text-sm font-medium m-0"><?php echo htmlspecialchars($variant['ukuran_ml']); ?> ml</p>
                                            <p class="text-center text-xs text-gray-600 m-0 mt-0.5">Rp <?php echo number_format($variant['harga'], 0, ',', '.'); ?></p>
                                            <p class="text-xs text-center <?php echo $variant['stok'] <= 0 ? 'text-red-500' : 'text-gray-500'; ?> m-0 mt-0.5">
                                                Stok: <?php echo $variant['stok']; ?>
                                            </p>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <p class="text-gray-500 col-span-4">Tidak ada varian tersedia untuk produk ini.</p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Quantity Selection -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Jumlah</label>
                            <div class="flex items-center">
                                <button id="btn-minus" class="px-3 py-2 border rounded-l-lg hover:bg-gray-100 focus:outline-none">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="quantity-value" class="w-20 px-4 py-2 border-t border-b border-gray-300 text-center" value="1" min="1">
                                <button id="btn-plus" class="px-3 py-2 border rounded-r-lg hover:bg-gray-100 focus:outline-none">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Total and Action Buttons -->
                        <div class="border-t pt-4">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <span class="text-sm text-gray-500">Total</span>
                                    <div id="total-price-display-modal" class="text-[#0A5144] font-bold text-xl">
                                        Rp <?php echo $product ? number_format($product['harga_produk'], 0, ',', '.') : '0'; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <button id="addToCartBtn" disabled
                                    class="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg flex items-center justify-center opacity-50 cursor-not-allowed"
                                    onclick="addToCart()">
                                    <i class="fas fa-shopping-cart mr-2"></i> Keranjang
                                </button>
                                <button id="buyNowBtn" 
                                    class="flex-1 bg-[#0A5144] text-white px-6 py-3 rounded-lg flex items-center justify-center"
                                    onclick="goToCheckout()">
                                    <i class="fas fa-shopping-bag mr-2"></i> Beli
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white shadow-lg flex justify-around py-3 z-20 border-t border-gray-100" id="mainNavBar">
        <a href="#" class="flex flex-col items-center text-xs text-gray-600">
            <i class="fas fa-home text-lg mb-1"></i>
            <span>Home</span>
        </a>
        <a href="#" class="flex flex-col items-center text-xs text-[#0A5144] font-bold">
            <i class="fas fa-shopping-bag text-lg mb-1"></i>
            <span>Products</span>
        </a>
        <a href="#" class="flex flex-col items-center text-xs text-gray-600">
            <i class="fas fa-cut text-lg mb-1"></i>
            <span>Services</span>
        </a>
        <a href="#" class="flex flex-col items-center text-xs text-gray-600">
            <i class="fas fa-user text-lg mb-1"></i>
            <span>Profile</span>
        </a>
    </nav>
</body>
<script>
    // Global variables for modal state
    let selectedVariantId = null;
    let selectedVariantPrice = <?php echo $product ? $product['harga_produk'] : 0; ?>;
    let selectedVariantStock = 0;
    let quantity = 1;
    const productId = <?php echo $product ? $product['id'] : 'null'; ?>;

    // Helper function to format currency
    function formatRupiah(number) {
        return 'Rp ' + number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
    }

    // Function to show the checkout modal
    function showCheckoutModal() {
        const modal = document.getElementById('checkoutModal');
        if (modal.classList.contains('hidden')) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
            
            // Add animation classes
            const modalContent = modal.querySelector('.bg-white');
            modalContent.classList.add('scale-95', 'opacity-0');
            
            // Use requestAnimationFrame for smoother animation
            requestAnimationFrame(() => {
                modalContent.classList.remove('scale-95', 'opacity-0');
                modalContent.classList.add('scale-100', 'opacity-100');
            });

            // Hide the main navigation bar and checkout bar
            document.getElementById('mainNavBar').classList.add('hidden');
            document.querySelector('.fixed.bottom-0.bg-white.shadow-xl').classList.add('hidden');

            // Initialize values when modal opens
            const initialProductPrice = <?php echo $product ? $product['harga_produk'] : '0'; ?>;
            
            // Reset selected variant and quantity when modal opens
            selectedVariantId = null;
            selectedVariantPrice = initialProductPrice;
            selectedVariantStock = 0;
            quantity = 1;

            // Reset UI elements
            document.getElementById('checkout-product-image-modal').src = '<?php echo $upload_dir_display . ($product ? htmlspecialchars($product['gambar_utama']) : ''); ?>';
            document.getElementById('checkout-product-image-modal').alt = '<?php echo $product ? htmlspecialchars($product['nama_produk']) : ''; ?>';
            document.getElementById('checkout-product-price-modal').textContent = formatRupiah(initialProductPrice);
            document.getElementById('total-price-display-modal').textContent = formatRupiah(initialProductPrice);
            document.getElementById('quantity-value').value = 1;

            // Remove active class from all variants
            const variants = document.querySelectorAll('#checkoutModal .variant-option');
            variants.forEach(variant => variant.classList.remove('border-emerald-500', 'active-variant'));

            validateButtons();
        }
    }

    // Function to hide the checkout modal
    function hideCheckoutModal() {
        const modal = document.getElementById('checkoutModal');
        const modalContent = modal.querySelector('.bg-white');
        
        // Add closing animation
        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');
        
        // Use requestAnimationFrame for smoother animation
        requestAnimationFrame(() => {
            setTimeout(() => {
                modal.classList.add('hidden');
                document.body.style.overflow = '';
                document.getElementById('mainNavBar').classList.remove('hidden');
                document.querySelector('.fixed.bottom-0.bg-white.shadow-xl').classList.remove('hidden');
            }, 300);
        });
    }

    // Function to validate buttons
    function validateButtons() {
        const addToCartBtn = document.getElementById('addToCartBtn');
        const buyNowBtn = document.getElementById('buyNowBtn');
        
        const isVariantSelected = selectedVariantId !== null;
        const isQuantityValid = quantity > 0 && quantity <= selectedVariantStock;

        const shouldEnable = isVariantSelected && isQuantityValid;

        [addToCartBtn, buyNowBtn].forEach(btn => {
            if (btn) {
                btn.disabled = !shouldEnable;
                btn.classList.toggle('opacity-50', !shouldEnable);
                btn.classList.toggle('cursor-not-allowed', !shouldEnable);
                if (shouldEnable) {
                    btn.classList.remove('bg-gray-100', 'text-gray-700');
                    btn.classList.add('bg-[#0A5144]', 'text-white');
                } else {
                    btn.classList.remove('bg-[#0A5144]', 'text-white');
                    btn.classList.add('bg-gray-100', 'text-gray-700');
                }
            }
        });
    }

    // Function to select variant
    function selectVariant(element, imageSrc, sizeText, price, stock) {
        // Remove active class from all variants
        const variants = document.querySelectorAll('#checkoutModal .variant-option');
        variants.forEach(variant => {
            variant.classList.remove('border-emerald-500', 'active-variant');
            variant.classList.add('border-gray-300');
        });
        
        // Add active class to selected variant
        element.classList.remove('border-gray-300');
        element.classList.add('border-emerald-500', 'active-variant');

        // Update global state
        selectedVariantId = element.dataset.variantId;
        selectedVariantPrice = parseFloat(price);
        selectedVariantStock = parseInt(stock);
        quantity = 1;

        // Update UI elements
        document.getElementById('checkout-product-image-modal').src = imageSrc;
        document.getElementById('checkout-product-image-modal').alt = sizeText;
        document.getElementById('quantity-value').value = quantity;
        
        updateModalHeaderPrice();
        updateTotalPriceDisplay();
        validateButtons();
    }

    // Function to update modal header price
    function updateModalHeaderPrice() {
        document.getElementById('checkout-product-price-modal').textContent = formatRupiah(selectedVariantPrice);
    }

    // Function to update total price display
    function updateTotalPriceDisplay() {
        const total = selectedVariantPrice * quantity;
        document.getElementById('total-price-display-modal').textContent = formatRupiah(total);
        document.getElementById('total-price-display').textContent = formatRupiah(total);
    }

    // Function to redirect to checkout page with selected product and variant data
    function goToCheckout() {
        if (selectedVariantId === null || quantity === 0) {
            alert('Silakan pilih varian dan masukkan jumlah yang valid.');
            return;
        }

        const baseUrl = 'cekout.php';
        const params = new URLSearchParams({
            product_id: productId,
            variant_id: selectedVariantId,
            quantity: quantity
        });

        window.location.href = `${baseUrl}?${params.toString()}`;
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Event listeners for quantity buttons
        const btnMinus = document.getElementById('btn-minus');
        const btnPlus = document.getElementById('btn-plus');
        const quantityValueInput = document.getElementById('quantity-value');

        if (btnMinus && btnPlus && quantityValueInput) {
            btnMinus.addEventListener('click', function() {
                if (quantity > 1) {
                    quantity--;
                    quantityValueInput.value = quantity;
                    updateTotalPriceDisplay();
                    validateButtons();
                }
            });

            btnPlus.addEventListener('click', function() {
                if (selectedVariantId === null) {
                    alert('Silakan pilih varian terlebih dahulu.');
                    return;
                }
                if (quantity < selectedVariantStock) {
                    quantity++;
                    quantityValueInput.value = quantity;
                    updateTotalPriceDisplay();
                    validateButtons();
                } else {
                    alert('Maaf, stok tidak mencukupi.');
                }
            });

            quantityValueInput.addEventListener('input', function() {
                let val = parseInt(this.value);
                if (isNaN(val) || val < 1) {
                    val = 1;
                }
                if (selectedVariantId !== null && val > selectedVariantStock) {
                    alert('Maaf, stok tidak mencukupi.');
                    val = selectedVariantStock;
                }
                quantity = val;
                this.value = quantity;
                updateTotalPriceDisplay();
                validateButtons();
            });
        }

        // Modal click handling
        const modal = document.getElementById('checkoutModal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    hideCheckoutModal();
                }
            });

            const modalContent = modal.querySelector('.bg-white');
            if (modalContent) {
                modalContent.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
        }

        // Initialize button state
        validateButtons();

        // Share functionality
        const shareButton = document.getElementById('shareButton');
        const shareDropdown = document.getElementById('shareDropdown');

        if (shareButton && shareDropdown) {
            // Toggle share dropdown
            shareButton.addEventListener('click', function(e) {
                e.stopPropagation();
                shareDropdown.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!shareButton.contains(e.target) && !shareDropdown.contains(e.target)) {
                    shareDropdown.classList.add('hidden');
                }
            });

            // Close dropdown when pressing escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    shareDropdown.classList.add('hidden');
                }
            });
        }
    });

    // Share to Instagram function
    function shareToInstagram() {
        const url = window.location.href;
        const productName = '<?php echo $product ? addslashes($product['nama_produk']) : 'Produk Menarik'; ?>';

        // For mobile devices, try to open Instagram app
        if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
            // Try Instagram app first
            window.location.href = 'instagram://camera';

            // Fallback to web version after a delay
            setTimeout(() => {
                window.open('https://www.instagram.com/', '_blank');
            }, 1000);
        } else {
            // For desktop, open Instagram web
            window.open('https://www.instagram.com/', '_blank');
        }

        // Copy URL to clipboard for easy sharing
        copyToClipboard();
        trackShare('Instagram');
    }

    // Copy link to clipboard function
    function copyToClipboard() {
        const url = window.location.href;

        if (navigator.clipboard && window.isSecureContext) {
            // Modern clipboard API
            navigator.clipboard.writeText(url).then(() => {
                showToast('Link berhasil disalin!', 'success');
            }).catch(() => {
                fallbackCopyTextToClipboard(url);
            });
        } else {
            // Fallback for older browsers
            fallbackCopyTextToClipboard(url);
        }
        trackShare('Copy Link');
    }

    // Fallback copy function
    function fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        textArea.style.opacity = "0";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            showToast('Link berhasil disalin!', 'success');
        } catch (err) {
            showToast('Gagal menyalin link', 'error');
        }

        document.body.removeChild(textArea);
    }

    // Toast notification function
    function showToast(message, type = 'info') {
        // Remove existing toast
        const existingToast = document.getElementById('toast');
        if (existingToast) {
            existingToast.remove();
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.id = 'toast';
        toast.className = `fixed top-20 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white text-sm font-medium transform transition-all duration-300 translate-x-full`;

        // Set color based on type
        if (type === 'success') {
            toast.className += ' bg-green-500';
        } else if (type === 'error') {
            toast.className += ' bg-red-500';
        } else {
            toast.className += ' bg-blue-500';
        }

        toast.textContent = message;
        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);

        // Auto hide after 3 seconds
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, 3000);
    }

    // Enhanced share tracking
    function trackShare(platform) {
        console.log(`Shared to ${platform}`);

        // Close dropdown after sharing
        setTimeout(() => {
            const shareDropdown = document.getElementById('shareDropdown');
            if (shareDropdown) {
                shareDropdown.classList.add('hidden');
            }
        }, 100);
    }

    // Add to cart function
    async function addToCart() {
        if (selectedVariantId === null || quantity === 0) {
            showToast('Silakan pilih varian dan masukkan jumlah yang valid.', 'error');
            return;
        }

        // Show loading state
        const addToCartBtn = document.getElementById('addToCartBtn');
        const originalText = addToCartBtn.innerHTML;
        addToCartBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Menambahkan...';
        addToCartBtn.disabled = true;

        try {
            const formData = new FormData();
            formData.append('product_id', productId);
            formData.append('variant_id', selectedVariantId);
            formData.append('quantity', quantity);

            const response = await fetch('api/add_to_cart.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            });

            const data = await response.json();

            if (data.success) {
                showToast(`${data.product_name} (${data.variant_size}ml) berhasil ditambahkan ke keranjang!`, 'success');

                // Update cart count in header
                updateCartCount(data.cart_count);

                // Close modal after successful add
                setTimeout(() => {
                    hideCheckoutModal();
                }, 1500);

            } else {
                showToast('Error: ' + data.message, 'error');
            }

        } catch (error) {
            console.error('Add to cart error:', error);
            showToast('Terjadi kesalahan saat menambahkan ke keranjang.', 'error');
        } finally {
            // Restore button state
            addToCartBtn.innerHTML = originalText;
            validateButtons(); // This will re-enable if conditions are met
        }
    }

    // Update cart count in header
    function updateCartCount(count) {
        const cartCountElement = document.querySelector('.fa-shopping-cart').parentElement.querySelector('span');
        if (cartCountElement) {
            cartCountElement.textContent = count;

            // Add animation
            cartCountElement.classList.add('animate-pulse');
            setTimeout(() => {
                cartCountElement.classList.remove('animate-pulse');
            }, 1000);
        }
    }
</script>
</html>