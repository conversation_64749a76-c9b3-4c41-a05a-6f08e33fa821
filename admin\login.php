<?php
session_start();
require_once 'db_config.php';

// Admin authentication with database
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';

    if (!empty($username) && !empty($password)) {
        try {
            // Debug: Log login attempt
            error_log("Login attempt for username: " . $username);

            // Check admin credentials from database
            $stmt = $conn->prepare("SELECT id, username, password, full_name, email, role, is_active FROM admin WHERE username = ?");
            $stmt->execute([$username]);
            $admin = $stmt->fetch();

            // Debug: Log what we found
            if ($admin) {
                error_log("Admin found: " . $admin['username'] . ", is_active: " . $admin['is_active']);
            } else {
                error_log("No admin found with username: " . $username);
            }

            if ($admin) {
                // Check if admin is active
                if ($admin['is_active'] != 1) {
                    $error = "Akun admin tidak aktif!";
                } else {
                    // Verify password
                    if (password_verify($password, $admin['password'])) {
                        // Login successful
                        $_SESSION['admin_logged_in'] = true;
                        $_SESSION['admin_id'] = $admin['id'];
                        $_SESSION['admin_username'] = $admin['username'];
                        $_SESSION['admin_full_name'] = $admin['full_name'];
                        $_SESSION['admin_email'] = $admin['email'];
                        $_SESSION['admin_role'] = $admin['role'];

                        // Update last login
                        $stmt = $conn->prepare("UPDATE admin SET last_login = CURRENT_TIMESTAMP WHERE id = ?");
                        $stmt->execute([$admin['id']]);

                        header("Location: kelola_lowongan.php");
                        exit();
                    } else {
                        error_log("Password verification failed for user: " . $username);
                        $error = "Username atau password salah!";
                    }
                }
            } else {
                $error = "Username atau password salah!";
            }
        } catch (PDOException $e) {
            error_log("Login error: " . $e->getMessage());
            $error = "Terjadi kesalahan sistem. Silakan coba lagi.";
        }
    } else {
        $error = "Username dan password harus diisi!";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-xl shadow-lg w-full max-w-md">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Admin Login</h1>
            <p class="text-gray-600">Pixel Barbershop Admin Panel</p>
        </div>

        <?php if (isset($error)): ?>
            <div class="mb-4 p-4 bg-red-100 text-red-700 rounded-lg">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <form method="POST" class="space-y-6">
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                <input type="text" id="username" name="username" required
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>

            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                <input type="password" id="password" name="password" required
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>

            <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200">
                <i class="fas fa-sign-in-alt mr-2"></i>
                Login
            </button>
        </form>
    </div>
</body>
</html>