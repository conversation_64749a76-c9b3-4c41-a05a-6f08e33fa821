<?php
require_once dirname(__FILE__) . '/../vendor/autoload.php';
require_once dirname(__FILE__) . '/../Admin/db_config.php'; // Sesuaikan path jika perlu

use Midtrans\Notification;
use Midtrans\Config;

Config::$serverKey = 'SB-Mid-server-YOUR_SERVER_KEY'; // GANTI DENGAN SERVER KEY ANDA
Config::$isProduction = false; // Set to true for production environment
Config::$isSanitized = true;
Config::$is3ds = true;

header('Content-Type: application/json');

try {
    $notif = new Notification();

    $transaction = $notif->transaction_status;
    $type = $notif->payment_type;
    $order_id = $notif->order_id;
    $fraud = $notif->fraud_status;

    // Get gross amount to be sure, although usually not used for validation here
    $gross_amount = $notif->gross_amount;

    // Prepare update query for order status
    $sql_update_order = "UPDATE orders SET payment_status = :payment_status, order_status = :order_status, midtrans_transaction_id = :midtrans_id WHERE id = :order_id";
    $stmt_update_order = $conn->prepare($sql_update_order);

    // Default status to log
    $log_message = "Notification received for order_id: $order_id, transaction: $transaction, type: $type, fraud: $fraud.";

    $new_order_status = 'pending'; // Default order status

    if ($transaction == 'capture') {
        // For credit card transaction, we need to check 'fraud_status'
        if ($type == 'credit_card') {
            if ($fraud == 'challenge') {
                // TODO set payment status in merchant's database to 'challenge'
                $new_payment_status = 'challenge';
                $new_order_status = 'pending'; // Keep order as pending if challenged
                $log_message .= " Status: CHALLENGE.";
            } else if ($fraud == 'accept') {
                // TODO set payment status in merchant's database to 'success'
                $new_payment_status = 'settlement';
                $new_order_status = 'processing'; // Order can be processed
                $log_message .= " Status: SETTLEMENT (Credit Card).";
            }
        }
    } else if ($transaction == 'settlement') {
        // TODO set payment status in merchant's database to 'success'
        $new_payment_status = 'settlement';
        $new_order_status = 'processing'; // Order can be processed
        $log_message .= " Status: SETTLEMENT.";
    } else if ($transaction == 'pending') {
        // TODO set payment status in merchant's database to 'pending'
        $new_payment_status = 'pending';
        $new_order_status = 'pending';
        $log_message .= " Status: PENDING.";
    } else if ($transaction == 'deny') {
        // TODO set payment status in merchant's database to 'deny'
        $new_payment_status = 'deny';
        $new_order_status = 'cancelled'; // Or 'failed'
        $log_message .= " Status: DENY.";
    } else if ($transaction == 'expire') {
        // TODO set payment status in merchant's database to 'expire'
        $new_payment_status = 'expire';
        $new_order_status = 'cancelled'; // Or 'expired'
        $log_message .= " Status: EXPIRE.";
    } else if ($transaction == 'cancel') {
        // TODO set payment status in merchant's database to 'cancel'
        $new_payment_status = 'cancel';
        $new_order_status = 'cancelled';
        $log_message .= " Status: CANCEL.";
    } else if ($transaction == 'refund' || $transaction == 'partial_refund') {
        // TODO set payment status in merchant's database to 'refund' or 'partial_refund'
        $new_payment_status = 'refund';
        $new_order_status = 'refunded'; // Or 'partially_refunded'
        $log_message .= " Status: REFUND.";
    } else {
        // Default to pending for any other status not explicitly handled
        $new_payment_status = 'pending';
        $new_order_status = 'pending';
        $log_message .= " Status: UNKNOWN/DEFAULT PENDING.";
    }

    // Execute update if new_payment_status is determined
    if (isset($new_payment_status)) {
        $stmt_update_order->execute([
            ':payment_status' => $new_payment_status,
            ':order_status' => $new_order_status, // Add order_status parameter
            ':midtrans_id' => $notif->transaction_id, // Use Midtrans transaction ID
            ':order_id' => $order_id
        ]);
        error_log("Order $order_id status updated to $new_payment_status / $new_order_status. Midtrans TX ID: {$notif->transaction_id}");
        echo json_encode(['status' => 'success', 'message' => 'Notification processed', 'order_id' => $order_id, 'payment_status' => $new_payment_status, 'order_status' => $new_order_status]);
    } else {
        error_log("No specific payment status determined for order_id: $order_id");
        echo json_encode(['status' => 'error', 'message' => 'No specific payment status determined', 'order_id' => $order_id]);
    }

} catch (Exception $e) {
    error_log("Midtrans Notification Error: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
?> 