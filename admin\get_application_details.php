<?php
require_once 'db_config.php';

if (!isset($_GET['id'])) {
    echo '<p class="text-red-500">ID aplikasi tidak ditemukan.</p>';
    exit;
}

$application_id = $_GET['id'];

try {
    $stmt = $conn->prepare("
        SELECT a.*, j.position, j.location, j.type, j.salary 
        FROM applications a 
        JOIN jobs j ON a.job_id = j.id 
        WHERE a.id = ?
    ");
    $stmt->execute([$application_id]);
    $app = $stmt->fetch();
    
    if (!$app) {
        echo '<p class="text-red-500">Data aplikasi tidak ditemukan.</p>';
        exit;
    }
    
    // Parse days available if it's JSON
    $days_available = [];
    if (!empty($app['days_available'])) {
        $days_available = json_decode($app['days_available'], true) ?? [];
    }

    // Parse quiz answers if available
    $quiz_answers = null;
    $quiz_questions = [
        'q1' => 'What is the most important factor when performing a skin fade?',
        'q2' => 'How often should you sanitize your clippers during use?',
        'q3' => 'Which technique is best for creating a natural-looking beard line?',
        'q4' => 'What is the purpose of using a pre-shave oil?'
    ];

    $quiz_options = [
        'q1' => ['A' => 'Using the right clipper guard', 'B' => 'Maintaining consistent pressure', 'C' => 'Proper blending between lengths', 'D' => 'Starting with clean, dry hair'],
        'q2' => ['A' => 'After each client', 'B' => 'Every 2-3 clients', 'C' => 'Only at the beginning and end of the day', 'D' => 'When switching between different hair types'],
        'q3' => ['A' => 'Using a straight razor for sharp lines', 'B' => 'Following the natural jawline with slight fading', 'C' => 'Creating a perfectly straight line under the chin', 'D' => 'Using clippers without any guard'],
        'q4' => ['A' => 'To disinfect the skin before shaving', 'B' => 'To soften the hair and prepare the skin', 'C' => 'To add shine to the finished haircut', 'D' => 'To make the hair easier to cut with clippers']
    ];

    $correct_answers = ['q1' => 'C', 'q2' => 'A', 'q3' => 'B', 'q4' => 'B'];

    if (!empty($app['quiz_answers'])) {
        $quiz_answers = json_decode($app['quiz_answers'], true);
    }
    
} catch (PDOException $e) {
    echo '<p class="text-red-500">Terjadi kesalahan: ' . htmlspecialchars($e->getMessage()) . '</p>';
    exit;
}
?>

<div class="space-y-6">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-lg shadow-lg">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold"><?= htmlspecialchars($app['full_name']) ?></h3>
                <p class="text-blue-100 mt-1">Melamar untuk posisi <?= htmlspecialchars($app['position']) ?></p>
            </div>
            <div class="text-right">
                <div class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">
                    <?= date('d M Y', strtotime($app['applied_at'])) ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Job Information -->
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h4 class="text-lg font-semibold text-gray-800 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2m-8 0V6a2 2 0 00-2 2v6"></path>
                </svg>
                Informasi Lowongan
            </h4>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="w-24 text-sm font-medium text-gray-600">Posisi:</div>
                        <div class="flex-1 text-gray-900 font-medium"><?= htmlspecialchars($app['position']) ?></div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-24 text-sm font-medium text-gray-600">Tipe:</div>
                        <div class="flex-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <?= htmlspecialchars($app['type']) ?>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="w-24 text-sm font-medium text-gray-600">Lokasi:</div>
                        <div class="flex-1 text-gray-900"><?= htmlspecialchars($app['location']) ?></div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-24 text-sm font-medium text-gray-600">Gaji:</div>
                        <div class="flex-1 text-green-600 font-semibold"><?= htmlspecialchars($app['salary']) ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Personal Information -->
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h4 class="text-lg font-semibold text-gray-800 flex items-center">
                <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                Informasi Personal
            </h4>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="w-28 text-sm font-medium text-gray-600">Nama Lengkap:</div>
                        <div class="flex-1 text-gray-900 font-medium"><?= htmlspecialchars($app['full_name']) ?></div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-28 text-sm font-medium text-gray-600">Email:</div>
                        <div class="flex-1">
                            <a href="mailto:<?= htmlspecialchars($app['email']) ?>" class="text-blue-600 hover:text-blue-800">
                                <?= htmlspecialchars($app['email']) ?>
                            </a>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-28 text-sm font-medium text-gray-600">Telepon:</div>
                        <div class="flex-1">
                            <a href="tel:<?= htmlspecialchars($app['phone']) ?>" class="text-blue-600 hover:text-blue-800">
                                <?= htmlspecialchars($app['phone']) ?>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="space-y-4">
                    <?php if (!empty($app['city'])): ?>
                    <div class="flex items-start">
                        <div class="w-20 text-sm font-medium text-gray-600">Kota:</div>
                        <div class="flex-1 text-gray-900"><?= htmlspecialchars($app['city']) ?></div>
                    </div>
                    <?php endif; ?>
                    <?php if (!empty($app['address'])): ?>
                    <div class="flex items-start">
                        <div class="w-20 text-sm font-medium text-gray-600">Alamat:</div>
                        <div class="flex-1 text-gray-900 leading-relaxed"><?= htmlspecialchars($app['address']) ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Information -->
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h4 class="text-lg font-semibold text-gray-800 flex items-center">
                <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                Informasi Profesional
            </h4>
        </div>
        <div class="p-6">
            <div class="space-y-6">
                <?php if (!empty($app['experience'])): ?>
                <div class="border-l-4 border-purple-400 pl-4">
                    <h5 class="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Pengalaman Kerja
                    </h5>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-gray-800 leading-relaxed whitespace-pre-wrap"><?= htmlspecialchars($app['experience']) ?></p>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($app['license'])): ?>
                <div class="border-l-4 border-blue-400 pl-4">
                    <h5 class="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                        Lisensi & Sertifikat
                    </h5>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-gray-800 leading-relaxed whitespace-pre-wrap"><?= htmlspecialchars($app['license']) ?></p>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($app['skills'])): ?>
                <div class="border-l-4 border-green-400 pl-4">
                    <h5 class="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Keahlian & Skill
                    </h5>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-gray-800 leading-relaxed whitespace-pre-wrap"><?= htmlspecialchars($app['skills']) ?></p>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (empty($app['experience']) && empty($app['license']) && empty($app['skills'])): ?>
                <div class="text-center py-8 text-gray-500">
                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p>Tidak ada informasi profesional yang disediakan</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Comprehensive Evaluation -->
    <?php if (isset($app['evaluation_score']) && $app['evaluation_score'] !== null): ?>
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200 shadow-sm">
        <h4 class="font-bold text-xl mb-4 text-blue-800 flex items-center">
            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            📊 Evaluasi Komprehensif Kandidat
        </h4>

        <!-- Overall Score -->
        <div class="mb-6 p-4 bg-white rounded-lg border border-blue-100">
            <div class="flex items-center justify-between mb-3">
                <h5 class="font-semibold text-gray-800">Skor Keseluruhan</h5>
                <div class="text-right">
                    <div class="text-3xl font-bold <?php echo $app['evaluation_score'] >= 80 ? 'text-green-600' : ($app['evaluation_score'] >= 60 ? 'text-yellow-600' : 'text-red-600'); ?>">
                        <?= round($app['evaluation_score']) ?>/100
                    </div>
                    <div class="text-sm text-gray-600">
                        <?php if ($app['evaluation_score'] >= 80): ?>
                            Sangat Baik 🌟
                        <?php elseif ($app['evaluation_score'] >= 60): ?>
                            Cukup Baik ✅
                        <?php else: ?>
                            Perlu Perbaikan ⚠️
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-4">
                <div class="<?php echo $app['evaluation_score'] >= 80 ? 'bg-green-500' : ($app['evaluation_score'] >= 60 ? 'bg-yellow-500' : 'bg-red-500'); ?> h-4 rounded-full transition-all duration-500" style="width: <?= $app['evaluation_score'] ?>%"></div>
            </div>
        </div>

        <!-- Detailed Breakdown -->
        <?php if (!empty($app['evaluation_details'])): ?>
        <?php $eval_details = json_decode($app['evaluation_details'], true); ?>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <?php foreach ($eval_details as $category => $details): ?>
            <div class="bg-white p-4 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between mb-2">
                    <h6 class="font-medium text-gray-800 capitalize">
                        <?php
                        $category_names = [
                            'cv' => '📄 CV/Resume',
                            'certificate' => '🏆 Sertifikat',
                            'experience' => '💼 Pengalaman',
                            'skills' => '⚡ Keahlian',
                            'quiz' => '🧠 Quiz'
                        ];
                        echo $category_names[$category] ?? ucfirst($category);
                        ?>
                    </h6>
                    <span class="text-sm font-semibold <?php echo $details['score'] >= ($details['max'] * 0.8) ? 'text-green-600' : ($details['score'] >= ($details['max'] * 0.5) ? 'text-yellow-600' : 'text-red-600'); ?>">
                        <?= $details['score'] ?>/<?= $details['max'] ?>
                    </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div class="<?php echo $details['score'] >= ($details['max'] * 0.8) ? 'bg-green-500' : ($details['score'] >= ($details['max'] * 0.5) ? 'bg-yellow-500' : 'bg-red-500'); ?> h-2 rounded-full" style="width: <?= ($details['score'] / $details['max']) * 100 ?>%"></div>
                </div>
                <p class="text-xs text-gray-600"><?= htmlspecialchars($details['status']) ?></p>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- Quiz Details (if available) -->
        <?php if (isset($app['quiz_score']) && $app['quiz_score'] !== null): ?>
        <div class="bg-white p-4 rounded-lg border border-gray-200">
            <h5 class="font-semibold text-gray-800 mb-3 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Detail Quiz Barber
            </h5>
            <div class="flex items-center space-x-6 mb-4">
                <div class="text-center">
                    <div class="text-2xl font-bold <?php echo $app['quiz_score'] >= 70 ? 'text-green-600' : 'text-red-600'; ?>">
                        <?= round($app['quiz_score']) ?>%
                    </div>
                    <div class="text-sm text-gray-600">Skor Quiz</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-800">
                        <?php
                        $correct_count = 0;
                        if ($quiz_answers) {
                            foreach ($correct_answers as $q => $correct) {
                                if (isset($quiz_answers[$q]) && $quiz_answers[$q] === $correct) {
                                    $correct_count++;
                                }
                            }
                        }
                        echo $correct_count . '/' . count($correct_answers);
                        ?>
                    </div>
                    <div class="text-sm text-gray-600">Jawaban Benar</div>
                </div>
            </div>
            <?php if ($quiz_answers): ?>
            <div class="space-y-3 mt-4">
                <h6 class="font-medium text-gray-700 border-b pb-2">📝 Detail Jawaban Quiz:</h6>
                <?php foreach ($quiz_questions as $q_id => $question): ?>
                <div class="border-l-4 <?php echo (isset($quiz_answers[$q_id]) && $quiz_answers[$q_id] === $correct_answers[$q_id]) ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'; ?> pl-4 py-3 rounded-r">
                    <p class="font-medium text-sm mb-2"><?= $question ?></p>
                    <div class="text-sm">
                        <p class="text-gray-700">
                            <strong>Jawaban:</strong> <?= isset($quiz_answers[$q_id]) ? $quiz_options[$q_id][$quiz_answers[$q_id]] : 'Tidak dijawab' ?>
                        </p>
                        <?php if (isset($quiz_answers[$q_id]) && $quiz_answers[$q_id] === $correct_answers[$q_id]): ?>
                            <p class="text-green-600 font-medium mt-1">✅ Benar</p>
                        <?php else: ?>
                            <p class="text-red-600 font-medium mt-1">❌ Salah</p>
                            <p class="text-gray-500 text-xs mt-1">
                                <strong>Jawaban yang benar:</strong> <?= $quiz_options[$q_id][$correct_answers[$q_id]] ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <?php if (!empty($app['status_reason'])): ?>
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h6 class="font-semibold text-blue-800 mb-2 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                🤖 Alasan Status Otomatis
            </h6>
            <p class="text-blue-700 text-sm"><?= htmlspecialchars($app['status_reason']) ?></p>
        </div>
        <?php endif; ?>
    </div>
    <?php elseif (isset($app['quiz_score']) && $app['quiz_score'] !== null): ?>
    <!-- Fallback for old quiz-only data -->
    <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
        <h4 class="font-bold text-lg mb-3">🧠 Hasil Quiz Barber</h4>
        <div class="mb-4">
            <div class="flex items-center space-x-6">
                <div class="text-center">
                    <div class="text-4xl font-bold <?php echo $app['quiz_score'] >= 70 ? 'text-green-600' : 'text-red-600'; ?>">
                        <?= round($app['quiz_score']) ?>%
                    </div>
                    <div class="text-sm text-gray-600 font-medium">Skor Quiz</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-gray-800">
                        <?php
                        $correct_count = 0;
                        if ($quiz_answers) {
                            foreach ($correct_answers as $q => $correct) {
                                if (isset($quiz_answers[$q]) && $quiz_answers[$q] === $correct) {
                                    $correct_count++;
                                }
                            }
                        }
                        echo $correct_count . '/' . count($correct_answers);
                        ?>
                    </div>
                    <div class="text-sm text-gray-600 font-medium">Jawaban Benar</div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>



    <!-- Cover Letter -->
    <?php if (!empty($app['cover_letter'])): ?>
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h4 class="text-lg font-semibold text-gray-800 flex items-center">
                <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Cover Letter
            </h4>
        </div>
        <div class="p-6">
            <div class="bg-indigo-50 p-4 rounded-lg border border-indigo-200">
                <p class="text-gray-800 leading-relaxed whitespace-pre-wrap"><?= htmlspecialchars($app['cover_letter']) ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Resume -->
    <?php if (!empty($app['resume_file'])): ?>
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h4 class="text-lg font-semibold text-gray-800 flex items-center">
                <svg class="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Resume/CV
            </h4>
        </div>
        <div class="p-6">
            <div class="flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-900">Resume File</p>
                        <p class="text-sm text-gray-600"><?= htmlspecialchars($app['resume_file']) ?></p>
                    </div>
                </div>
                <a href="../uploads/resumes/<?= htmlspecialchars($app['resume_file']) ?>" target="_blank"
                   class="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Download
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Application Status -->
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h4 class="text-lg font-semibold text-gray-800 flex items-center">
                <svg class="w-5 h-5 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                </svg>
                Status Lamaran
            </h4>
        </div>
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <?php
                        $status_config = [
                            'pending' => ['bg' => 'bg-yellow-100', 'text' => 'text-yellow-800', 'border' => 'border-yellow-200', 'icon' => 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'],
                            'reviewed' => ['bg' => 'bg-blue-100', 'text' => 'text-blue-800', 'border' => 'border-blue-200', 'icon' => 'M15 12a3 3 0 11-6 0 3 3 0 016 0z'],
                            'interview' => ['bg' => 'bg-purple-100', 'text' => 'text-purple-800', 'border' => 'border-purple-200', 'icon' => 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z'],
                            'accepted' => ['bg' => 'bg-green-100', 'text' => 'text-green-800', 'border' => 'border-green-200', 'icon' => 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'],
                            'rejected' => ['bg' => 'bg-red-100', 'text' => 'text-red-800', 'border' => 'border-red-200', 'icon' => 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z']
                        ];
                        $config = $status_config[$app['status']] ?? $status_config['pending'];
                        ?>
                        <div class="w-12 h-12 <?= $config['bg'] ?> <?= $config['border'] ?> border rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 <?= $config['text'] ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?= $config['icon'] ?>"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-lg font-semibold text-gray-900"><?= ucfirst($app['status']) ?></p>
                        <p class="text-sm text-gray-600">Status saat ini</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-sm font-medium text-gray-900"><?= date('d M Y', strtotime($app['applied_at'])) ?></p>
                    <p class="text-sm text-gray-600"><?= date('H:i', strtotime($app['applied_at'])) ?> WIB</p>
                </div>
            </div>
        </div>
    </div>
</div>
