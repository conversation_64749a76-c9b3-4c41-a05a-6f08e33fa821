<?php
// Check Image Files - Debug gambar model
header('Content-Type: application/json');

// Path ke folder rekomendasi
$baseDir = __DIR__ . '/rekomendasi';

// Fungsi untuk scan folder
function scanImageFolder($dir) {
    $result = [];
    
    if (!is_dir($dir)) {
        return ['error' => 'Directory not found: ' . $dir];
    }
    
    $shapes = ['oval', 'round', 'square', 'heart', 'rectangular'];
    
    foreach ($shapes as $shape) {
        $shapeDir = $dir . '/' . $shape;
        $result[$shape] = [];
        
        if (is_dir($shapeDir)) {
            $files = scandir($shapeDir);
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..' && strtolower(pathinfo($file, PATHINFO_EXTENSION)) === 'jpg') {
                    $filePath = $shapeDir . '/' . $file;
                    $webPath = 'rekomendasi/' . $shape . '/' . $file;
                    
                    $result[$shape][] = [
                        'filename' => $file,
                        'filepath' => $filePath,
                        'webpath' => $webPath,
                        'exists' => file_exists($filePath),
                        'readable' => is_readable($filePath),
                        'size' => file_exists($filePath) ? filesize($filePath) : 0,
                        'modified' => file_exists($filePath) ? date('Y-m-d H:i:s', filemtime($filePath)) : null
                    ];
                }
            }
        } else {
            $result[$shape] = ['error' => 'Shape directory not found: ' . $shapeDir];
        }
    }
    
    return $result;
}

// Fungsi untuk test specific files
function testSpecificFiles() {
    $testFiles = [
        'rekomendasi/oval/Textured Crop.jpg',
        'rekomendasi/oval/Pompadour.jpg',
        'rekomendasi/round/Two Block Hair.jpg',
        'rekomendasi/square/Slick Back.jpg',
        'rekomendasi/heart/Classic Quiff.jpg'
    ];
    
    $results = [];
    
    foreach ($testFiles as $webPath) {
        $fullPath = __DIR__ . '/' . $webPath;
        
        $results[] = [
            'webpath' => $webPath,
            'fullpath' => $fullPath,
            'exists' => file_exists($fullPath),
            'readable' => is_readable($fullPath),
            'size' => file_exists($fullPath) ? filesize($fullPath) : 0,
            'mime' => file_exists($fullPath) ? mime_content_type($fullPath) : null,
            'url_accessible' => checkUrlAccessible($webPath)
        ];
    }
    
    return $results;
}

// Fungsi untuk check URL accessibility
function checkUrlAccessible($webPath) {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . '://' . $host . dirname($_SERVER['REQUEST_URI']);
    $fullUrl = $baseUrl . '/' . $webPath;
    
    $headers = @get_headers($fullUrl);
    return $headers && strpos($headers[0], '200') !== false;
}

// Main execution
try {
    $action = $_GET['action'] ?? 'scan';
    
    switch ($action) {
        case 'scan':
            $result = [
                'success' => true,
                'action' => 'scan',
                'base_dir' => $baseDir,
                'base_dir_exists' => is_dir($baseDir),
                'data' => scanImageFolder($baseDir)
            ];
            break;
            
        case 'test':
            $result = [
                'success' => true,
                'action' => 'test',
                'data' => testSpecificFiles()
            ];
            break;
            
        case 'info':
            $result = [
                'success' => true,
                'action' => 'info',
                'server_info' => [
                    'php_version' => phpversion(),
                    'document_root' => $_SERVER['DOCUMENT_ROOT'],
                    'script_path' => __FILE__,
                    'current_dir' => __DIR__,
                    'base_url' => (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']),
                    'rekomendasi_path' => $baseDir,
                    'rekomendasi_exists' => is_dir($baseDir),
                    'permissions' => is_dir($baseDir) ? substr(sprintf('%o', fileperms($baseDir)), -4) : 'N/A'
                ]
            ];
            break;
            
        default:
            $result = [
                'success' => false,
                'error' => 'Invalid action. Use: scan, test, or info'
            ];
    }
    
} catch (Exception $e) {
    $result = [
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ];
}

echo json_encode($result, JSON_PRETTY_PRINT);
?>
