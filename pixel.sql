-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 11, 2025 at 05:45 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.0.30

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `pixel`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin`
--

CREATE TABLE `admin` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `profile_photo` varchar(255) DEFAULT NULL COMMENT 'Profile photo filename'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin`
--

INSERT INTO `admin` (`id`, `username`, `password`, `full_name`, `email`, `role`, `is_active`, `last_login`, `created_at`, `updated_at`, `profile_photo`) VALUES
(9, 'admin', '$2y$10$Z.klYBjU8Lbv6DOo0rh8keU2M4ANA1GU6ZYGzUggTumiqoVsatyiC', 'Super Administrator', '<EMAIL>', 'super_admin', 1, '2025-07-10 12:30:40', '2025-06-15 19:50:12', '2025-07-10 12:30:40', 'profile_9_1750635073.jpeg'),
(10, 'manager', '$2y$10$Z.klYBjU8Lbv6DOo0rh8keU2M4ANA1GU6ZYGzUggTumiqoVsatyiC', 'Manager', '<EMAIL>', 'admin', 1, NULL, '2025-06-15 19:50:12', '2025-06-15 19:50:12', NULL),
(11, 'staff', '$2y$10$Z.klYBjU8Lbv6DOo0rh8keU2M4ANA1GU6ZYGzUggTumiqoVsatyiC', 'Staff Admin', '<EMAIL>', 'moderator', 1, NULL, '2025-06-15 19:50:12', '2025-06-15 19:50:12', NULL),
(12, 'lukman', '$2y$10$Z.klYBjU8Lbv6DOo0rh8keU2M4ANA1GU6ZYGzUggTumiqoVsatyiC', 'Lukmanul Hakim Jayadi', '<EMAIL>', 'admin', 1, '2025-06-17 10:09:19', '2025-06-15 19:50:12', '2025-06-17 10:09:19', 'profile_12_1750074929.jpeg');

-- --------------------------------------------------------

--
-- Table structure for table `applications`
--

CREATE TABLE `applications` (
  `id` int(11) NOT NULL,
  `job_id` int(11) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `experience` text DEFAULT NULL,
  `license` varchar(255) DEFAULT NULL,
  `skills` text DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `days_available` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`days_available`)),
  `cover_letter` text DEFAULT NULL,
  `resume_file` varchar(255) DEFAULT NULL,
  `status` enum('pending','reviewed','interview','accepted','rejected') DEFAULT 'pending',
  `applied_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `quiz_score` decimal(5,2) DEFAULT NULL,
  `quiz_answers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`quiz_answers`)),
  `status_reason` text DEFAULT NULL,
  `evaluation_score` decimal(5,2) DEFAULT NULL,
  `evaluation_details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`evaluation_details`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `applications`
--

INSERT INTO `applications` (`id`, `job_id`, `full_name`, `email`, `phone`, `address`, `city`, `experience`, `license`, `skills`, `start_date`, `days_available`, `cover_letter`, `resume_file`, `status`, `applied_at`, `quiz_score`, `quiz_answers`, `status_reason`, `evaluation_score`, `evaluation_details`) VALUES
(4, 6, 'Lukmanul Hakim Jayadi', '<EMAIL>', '+6283853900297', 'Kp.Kaum Kidul, Desa.Bangbayang, Kecamatan.Cicurug, Kabupaten.Sukabumi', 'Sukabumi', '1-3', NULL, 'management, multitasking, email handling, attention to detail, customer service, teamwork, problem solving, clerical support, database management, office coordination, confidentiality, organization', NULL, '[]', '', NULL, 'interview', '2025-06-22 19:58:50', 0.00, '{\"q1\":\"A\",\"q2\":\"B\",\"q3\":\"A\",\"q4\":\"A\"}', 'Otomatis ditolak: Skor evaluasi rendah (0/100). Belum memenuhi kriteria minimum.', 0.00, '{\"cv\":{\"score\":0,\"max\":20,\"status\":\"Tidak ada CV\\/Resume\"},\"certificate\":{\"score\":0,\"max\":25,\"status\":\"Tidak ada sertifikat\\/lisensi\"},\"experience\":{\"score\":0,\"max\":25,\"status\":\"Pengalaman: 0 tahun, relevansi tinggi\"},\"skills\":{\"score\":0,\"max\":15,\"status\":\"Memiliki 0 skill relevan\"},\"quiz\":{\"score\":0,\"max\":15,\"status\":\"Skor quiz: 0%\"}}'),
(5, 6, 'SITI SALMA ZAKIYAH', '<EMAIL>', '+62 83896982288', 'KPKAUM KIDUL RT/RW 001/002 KEL/DESA KARANGTENGAH KECAMATAN CIBADAK', 'Kabupaten Sukabumi', '0-1', NULL, '', NULL, '[]', '', NULL, 'rejected', '2025-07-08 14:49:41', 25.00, '{\"q1\":\"A\",\"q2\":\"A\",\"q3\":\"A\",\"q4\":\"A\"}', 'Otomatis ditolak: Skor evaluasi rendah (3.75/100). Belum memenuhi kriteria minimum.', 3.75, '{\"cv\":{\"score\":0,\"max\":20,\"status\":\"Tidak ada CV\\/Resume\"},\"certificate\":{\"score\":0,\"max\":25,\"status\":\"Tidak ada sertifikat\\/lisensi\"},\"experience\":{\"score\":0,\"max\":25,\"status\":\"Pengalaman: 0 tahun, relevansi tinggi\"},\"skills\":{\"score\":0,\"max\":15,\"status\":\"Tidak ada skill yang disebutkan\"},\"quiz\":{\"score\":3.8,\"max\":15,\"status\":\"Skor quiz: 25%\"}}'),
(6, 6, 'Lukman Lukmanul hakim jayadi', '<EMAIL>', '************', 'Kp.Kaum Kidul, Desa.Bangbayang, Kecamatan.Cicurug, Kabupaten.Sukabumi', 'Sukabumi', '0-1', NULL, '', NULL, '[]', '', NULL, 'rejected', '2025-07-08 15:32:24', 0.00, '{\"q1\":\"\",\"q2\":\"7\",\"q3\":\"11\",\"q4\":\"15\"}', 'Otomatis ditolak: Skor evaluasi rendah (0/100). Belum memenuhi kriteria minimum.', 0.00, '{\"cv\":{\"score\":0,\"max\":20,\"status\":\"Tidak ada CV\\/Resume\"},\"certificate\":{\"score\":0,\"max\":25,\"status\":\"Tidak ada sertifikat\\/lisensi\"},\"experience\":{\"score\":0,\"max\":25,\"status\":\"Pengalaman: 0 tahun, relevansi tinggi\"},\"skills\":{\"score\":0,\"max\":15,\"status\":\"Tidak ada skill yang disebutkan\"},\"quiz\":{\"score\":0,\"max\":15,\"status\":\"Skor quiz: 0%\"}}'),
(7, 6, 'Lukman Lukmanul hakim jayadi', '<EMAIL>', '************', 'Kp.Kaum Kidul, Desa.Bangbayang, Kecamatan.Cicurug, Kabupaten.Sukabumi', 'Sukabumi', '0-1', NULL, '', NULL, '[]', '', NULL, 'rejected', '2025-07-10 11:16:15', 0.00, '{\"q1\":\"\",\"q2\":\"5\",\"q3\":\"10\",\"q4\":\"14\"}', 'Otomatis ditolak: Skor evaluasi rendah (0/100). Belum memenuhi kriteria minimum.', 0.00, '{\"cv\":{\"score\":0,\"max\":20,\"status\":\"Tidak ada CV\\/Resume\"},\"certificate\":{\"score\":0,\"max\":25,\"status\":\"Tidak ada sertifikat\\/lisensi\"},\"experience\":{\"score\":0,\"max\":25,\"status\":\"Pengalaman: 0 tahun, relevansi tinggi\"},\"skills\":{\"score\":0,\"max\":15,\"status\":\"Tidak ada skill yang disebutkan\"},\"quiz\":{\"score\":0,\"max\":15,\"status\":\"Skor quiz: 0%\"}}');

-- --------------------------------------------------------

--
-- Table structure for table `barbers`
--

CREATE TABLE `barbers` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `image_filename` varchar(255) DEFAULT NULL,
  `services_offered` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `location_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `barbers`
--

INSERT INTO `barbers` (`id`, `name`, `image_filename`, `services_offered`, `created_at`, `location_id`) VALUES
(9, 'Ahmad', '685865d020ce7.jpeg', 'Potong Rambut Pria|30|25000,Cukur Jenggot|20|15000,Hair Wash & Styling|45|35000', '2025-06-22 20:21:36', 3);

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE `bookings` (
  `id` int(11) NOT NULL,
  `location_id` int(11) NOT NULL,
  `barber_id` int(11) NOT NULL,
  `user_name` varchar(255) NOT NULL,
  `user_phone` varchar(20) NOT NULL,
  `service_name` varchar(255) NOT NULL,
  `service_price` decimal(10,2) NOT NULL,
  `booking_time` datetime NOT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'Confirmed',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `bookings`
--

INSERT INTO `bookings` (`id`, `location_id`, `barber_id`, `user_name`, `user_phone`, `service_name`, `service_price`, `booking_time`, `status`, `created_at`) VALUES
(1, 2, 7, 'Lukman', '88', 'Potong Rambut', 300000.00, '2025-06-24 12:00:00', 'Confirmed', '2025-06-15 12:41:12'),
(2, 2, 7, 'Ahmad', '21312', 'Potong Rambut', 300000.00, '2025-06-18 12:00:00', 'Confirmed', '2025-06-15 13:17:07'),
(3, 2, 8, 'Lukman', '21312', 'Potong Rambut', 300000.00, '2025-06-18 12:00:00', 'Confirmed', '2025-06-15 20:31:12'),
(4, 3, 9, 'Lukman', '************', 'Hair Wash & Styling', 35000.00, '2025-06-25 12:00:00', 'Completed', '2025-06-22 20:24:48'),
(5, 3, 9, 'Lukman', '************', 'Potong Rambut Pria', 25000.00, '2025-07-10 12:00:00', 'Confirmed', '2025-07-10 11:25:32');

-- --------------------------------------------------------

--
-- Table structure for table `cart`
--

CREATE TABLE `cart` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `variant_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cart`
--

INSERT INTO `cart` (`id`, `user_id`, `product_id`, `variant_id`, `quantity`, `created_at`, `updated_at`) VALUES
(1, 9, 1, 1, 1, '2025-06-17 10:15:17', '2025-06-17 10:15:17'),
(2, 9, 2, 3, 1, '2025-06-17 10:26:27', '2025-06-17 10:30:57');

-- --------------------------------------------------------

--
-- Stand-in structure for view `daftar_produk`
-- (See below for the actual view)
--
CREATE TABLE `daftar_produk` (
`id` int(11)
,`gambar_utama` varchar(255)
,`nama_produk` varchar(255)
,`nama_kategori` varchar(100)
,`harga` decimal(10,2)
,`aksi` varchar(205)
);

-- --------------------------------------------------------

--
-- Table structure for table `face_analysis`
--

CREATE TABLE `face_analysis` (
  `id` int(11) NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `face_shape` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `recommendation` text DEFAULT NULL,
  `hairline_analysis` text DEFAULT NULL,
  `hair_type_analysis` text DEFAULT NULL,
  `tips` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `confidence` int(11) DEFAULT 0,
  `alt_shape` varchar(50) DEFAULT NULL,
  `alt_confidence` int(11) DEFAULT 0,
  `shape_description` text DEFAULT NULL,
  `ratios` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`ratios`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `face_analysis`
--

INSERT INTO `face_analysis` (`id`, `image_path`, `face_shape`, `description`, `recommendation`, `hairline_analysis`, `hair_type_analysis`, `tips`, `created_at`, `confidence`, `alt_shape`, `alt_confidence`, `shape_description`, `ratios`) VALUES
(1, 'uploads/face_detection/684ea67aa9788.jpg', 'square', 'Bentuk wajah persegi, dengan rahang tegas dan kuat', 'Wajah Persegi (Square)', NULL, NULL, NULL, '2025-06-15 10:54:56', 0, NULL, 0, NULL, NULL),
(2, 'uploads/face_detection/685169f3c2da0.jpg', 'square', 'Bentuk wajah persegi, dengan rahang tegas dan kuat', 'Wajah Persegi (Square)', NULL, NULL, NULL, '2025-06-17 13:13:30', 0, NULL, 0, NULL, NULL),
(3, 'uploads/face_detection/68517742164f4.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:10:15', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(4, 'uploads/face_detection/685177603a67d.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:10:45', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(5, 'uploads/face_detection/68517a35d9f91.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:22:54', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(6, 'uploads/face_detection/68517a513ddf8.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:23:18', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(7, 'uploads/face_detection/68517a64367ca.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:23:36', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(8, 'uploads/face_detection/68517b4cbc60c.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:27:47', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(9, 'uploads/face_detection/68517b6ab7994.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:28:03', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(10, 'uploads/face_detection/68517c0da6b87.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:30:43', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(11, 'uploads/face_detection/68517e09aa381.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:39:26', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(12, 'uploads/face_detection/68517ea559db1.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:41:47', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(13, 'uploads/face_detection/68517f135ec40.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:43:45', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(14, 'uploads/face_detection/68517f84b6e08.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:45:36', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(15, 'uploads/face_detection/68517f98e8522.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:45:58', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(16, 'uploads/face_detection/68517fd081b5a.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:47:03', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(17, 'uploads/face_detection/68517ffcbcda1.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:47:31', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(18, 'uploads/face_detection/6851802d201f3.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:48:18', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(19, 'uploads/face_detection/6851803873677.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:48:32', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(20, 'uploads/face_detection/685181ae345ca.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:54:53', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(21, 'uploads/face_detection/685181c091efc.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:55:05', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(22, 'uploads/face_detection/685181dd8e1bd.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 14:55:30', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(23, 'uploads/face_detection/68518337acacf.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 15:01:24', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(24, 'uploads/face_detection/6851835d0e653.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 15:01:57', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(25, 'uploads/face_detection/685184feceab3.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 15:08:59', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(26, 'uploads/face_detection/6851873cd743e.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 15:18:25', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(27, 'uploads/face_detection/68518a4307947.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-17 15:31:22', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(28, 'uploads/face_detection/68525aa0cbf9a.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-18 06:20:20', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(29, 'uploads/face_detection/6855600ee6798.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 13:20:21', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(30, 'uploads/face_detection/685563c5c823c.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 13:36:12', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(31, 'uploads/face_detection/6855657a5fd31.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 13:43:28', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(32, 'uploads/face_detection/68556617215cd.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 13:46:06', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(33, 'uploads/face_detection/6855663cc1ae5.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 13:46:43', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(34, 'uploads/face_detection/685566d1abe87.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 13:49:23', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(35, 'uploads/face_detection/685566e409ba2.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 13:49:31', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(36, 'uploads/face_detection/685567c228549.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 13:53:12', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(37, 'uploads/face_detection/685568cdd941b.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 13:57:41', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(38, 'uploads/face_detection/68556c668d4f5.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 14:12:59', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(39, 'uploads/face_detection/685571121dbff.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 14:32:56', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(40, 'uploads/face_detection/6855720576ce3.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 14:36:59', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(41, 'uploads/face_detection/685572aa9f5c2.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 14:39:51', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(42, 'uploads/face_detection/6855738aa1827.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 14:43:29', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(43, 'uploads/face_detection/685576259eb9e.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 14:54:36', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(44, 'uploads/face_detection/685578800cef4.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 15:04:40', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(45, 'uploads/face_detection/68557bf54a4ca.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 15:19:25', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(46, 'uploads/face_detection/68557cc31528a.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 15:22:49', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(47, 'uploads/face_detection/685584fead18d.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 15:57:55', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(48, 'uploads/face_detection/685587f685e5e.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-20 16:10:34', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(49, 'uploads/face_detection/685a4ffd85d56.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-06-24 07:13:04', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(50, 'uploads/face_detection/686536ce2a0f8.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-02 13:40:33', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(51, 'uploads/face_detection/6865551766b55.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-02 15:49:48', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(52, 'uploads/face_detection/686bb9ff915a2.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-07 12:13:57', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(53, 'uploads/face_detection/686cfc8ce997a.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 11:10:10', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(54, 'uploads/face_detection/686d044ee4c2b.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 11:43:14', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(55, 'uploads/face_detection/686d06bd8d346.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 11:53:36', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(56, 'uploads/face_detection/686d07bcf0e85.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 11:57:51', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(57, 'uploads/face_detection/686d094e0ff52.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 12:04:32', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(58, 'uploads/face_detection/686d0d22d693d.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 12:20:53', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(59, 'uploads/face_detection/686d0e9ce3f9c.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 12:27:11', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(60, 'uploads/face_detection/686d0f90d3b62.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 12:31:17', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(61, 'uploads/face_detection/686d1acb83870.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 13:19:10', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(62, 'uploads/face_detection/686d1ae068364.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 13:19:31', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(63, 'uploads/face_detection/686d1b2b7cdb2.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 13:20:46', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(64, 'uploads/face_detection/686d1bc380dc3.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 13:23:18', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(65, 'uploads/face_detection/686d1bd9037ae.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 13:23:39', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(66, 'uploads/face_detection/686d1bf554d91.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 13:24:08', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(67, 'uploads/face_detection/686d1ca2f1047.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 13:27:01', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(68, 'uploads/face_detection/686d1dc841cd5.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 13:31:58', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(69, 'uploads/face_detection/686d1de3719bb.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 13:32:24', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(70, 'uploads/face_detection/686d1df65246c.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 13:32:43', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(71, 'uploads/face_detection/686d1f079a3ba.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 13:37:17', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(72, 'uploads/face_detection/686d2797da9d0.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-08 14:13:49', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(73, 'uploads/face_detection/686f371674a4e.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 03:44:27', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(74, 'uploads/face_detection/686f3a072fb4f.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 03:57:00', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(75, 'uploads/face_detection/686f45cc96d63.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 04:47:14', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(76, 'uploads/face_detection/686f469944299.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 04:50:37', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(77, 'uploads/face_detection/686f49ff38cd0.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:05:08', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(78, 'uploads/face_detection/686f4a1b19087.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:05:35', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}');
INSERT INTO `face_analysis` (`id`, `image_path`, `face_shape`, `description`, `recommendation`, `hairline_analysis`, `hair_type_analysis`, `tips`, `created_at`, `confidence`, `alt_shape`, `alt_confidence`, `shape_description`, `ratios`) VALUES
(79, 'uploads/face_detection/686f4a53c0210.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:06:31', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(80, 'uploads/face_detection/686f4ab138a89.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:08:05', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(81, 'uploads/face_detection/686f4accc7a1a.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:08:32', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(82, 'uploads/face_detection/686f4af2e70a3.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:09:10', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(83, 'uploads/face_detection/686f4b9761e81.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:11:55', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(84, 'uploads/face_detection/686f4ccf2d6eb.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:17:06', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(85, 'uploads/face_detection/686f4f4fabcfb.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:27:47', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(86, 'uploads/face_detection/686f4f6831730.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:28:11', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(87, 'uploads/face_detection/686f4f7bc524e.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:28:31', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(88, 'uploads/face_detection/686f546ee4520.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:49:38', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(89, 'uploads/face_detection/686f5484d97b4.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:50:00', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(90, 'uploads/face_detection/686f55a69fe66.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:54:50', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(91, 'uploads/face_detection/686f55c7d17ef.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:55:23', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(92, 'uploads/face_detection/686f55ddd3327.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 05:55:45', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(93, 'uploads/face_detection/686f57d5b95bc.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 06:04:10', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(94, 'uploads/face_detection/686f6821f3bbb.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 07:13:41', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(95, 'uploads/face_detection/686fc56225612.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 13:51:36', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(96, 'uploads/face_detection/686fc59647189.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 13:52:24', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(97, 'uploads/face_detection/686fc85eacaf8.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 14:04:17', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(98, 'uploads/face_detection/686fd5914b4b6.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 15:00:36', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(99, 'uploads/face_detection/686fd5a71a14d.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 15:00:58', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(100, 'uploads/face_detection/686fd6e523e31.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 15:06:16', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(101, 'uploads/face_detection/686fd987da348.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 15:17:32', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(102, 'uploads/face_detection/686fdcd9ca43b.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 15:31:41', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(103, 'uploads/face_detection/686fdeaf0afd5.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 15:39:33', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(104, 'uploads/face_detection/686fdec349a2e.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 15:39:50', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}'),
(105, 'uploads/face_detection/686fdeda66a6e.jpg', 'oval', 'Mock analysis result for testing', '[\"Textured Crop - Gaya modern dengan tekstur alami\",\"Pompadour - Klasik dengan volume di atas\",\"Man Bun - Trendy untuk rambut panjang\",\"Classic Undercut - Bersih dan profesional\"]', 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi.', 'Semua jenis rambut cocok (lurus, gelombang, keriting).', 'Bentuk wajah oval sangat fleksibel untuk berbagai gaya rambut.', '2025-07-10 15:40:13', 85, 'round', 15, 'Bentuk wajah oval dengan proporsi seimbang', '{\"width_height_ratio\":0.8,\"jaw_forehead_ratio\":0.95,\"chin_ratio\":0.3}');

-- --------------------------------------------------------

--
-- Table structure for table `gambar_produk`
--

CREATE TABLE `gambar_produk` (
  `id` int(11) NOT NULL,
  `produk_id` int(11) DEFAULT NULL,
  `gambar` varchar(255) DEFAULT NULL,
  `label` varchar(100) DEFAULT NULL,
  `deskripsi` text DEFAULT NULL,
  `urutan` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `gambar_produk`
--

INSERT INTO `gambar_produk` (`id`, `produk_id`, `gambar`, `label`, `deskripsi`, `urutan`, `created_at`) VALUES
(1, 1, 'asd.jpg', 'sad', NULL, 0, '2025-06-14 18:40:29'),
(2, 1, 'Logo_Universitas_Nusa_Putra.png', '', NULL, 0, '2025-06-14 18:40:30'),
(3, 3, 'SHAMPOOBARBERSHOP.webp', 'Keren abiss', NULL, 0, '2025-06-22 20:05:41'),
(4, 3, 'SHAMPOOBARBERSHOP.webp', 'pelembab', NULL, 0, '2025-06-22 20:05:41');

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` int(11) NOT NULL,
  `position` varchar(255) NOT NULL,
  `location` varchar(255) NOT NULL,
  `type` varchar(50) NOT NULL,
  `salary` varchar(100) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `requirements` text DEFAULT NULL,
  `benefits` text DEFAULT NULL,
  `experience_level` varchar(50) DEFAULT NULL,
  `education` varchar(50) DEFAULT NULL,
  `posted` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `jobs`
--

INSERT INTO `jobs` (`id`, `position`, `location`, `type`, `salary`, `image`, `description`, `requirements`, `benefits`, `experience_level`, `education`, `posted`, `is_active`) VALUES
(5, 'Barberman', 'Pixel Cicurug', 'Full Time', '30000', '68585e38ce2d5_vb.jpg', 'Menyediakan layanan potong rambut sesuai keinginan atau bentuk wajah pelanggan\r\nMemberikan saran gaya rambut yang sesuai dengan tren dan karakteristik fisik pelanggan', '[\"Minimal Pengalaman 2 Tahun\",\"Berpenampilan Menarik\"]', '[\"Gaji + Komisi\",\"Bonus performance bulanan\",\"Training dan pengembangan skill\"]', 'Junior', 'SMA/SMK', '2025-06-22 19:49:12', 1),
(6, 'Admin', 'Pixel Ciaul', 'Full Time', '30000', '68585ea313ba7_vb.jpg', 'Menyusun, mencatat, dan mengarsipkan dokumen penting (fisik maupun digital)\r\n\r\nMemastikan keakuratan dan keterkinian data pelanggan, transaksi, atau operasional harian\r\n\r\n', '[\"Minimal SMA\\/SMK sederajat\",\"Pengalaman minimal 1 tahun di bidang barbershop\",\"Memiliki sertifikat barbering\",\"Komunikasi yang baik dengan pelanggan\"]', '[\"Gaji pokok + komisi\",\"BPJS Kesehatan & Ketenagakerjaan\",\"Bonus performance bulanan\",\"Training dan pengembangan skill\",\"Seragam kerja disediakan\"]', 'Senior', 'S1', '2025-06-22 19:50:59', 1);

-- --------------------------------------------------------

--
-- Table structure for table `kategori_produk`
--

CREATE TABLE `kategori_produk` (
  `id` int(11) NOT NULL,
  `nama_kategori` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `kategori_produk`
--

INSERT INTO `kategori_produk` (`id`, `nama_kategori`, `created_at`) VALUES
(1, 'Perawatan Rambut', '2025-06-14 18:37:44'),
(2, 'Perawatan Wajah', '2025-06-14 18:37:44'),
(3, 'Perawatan Badan', '2025-06-14 18:37:44'),
(4, 'Aksesoris', '2025-06-14 18:37:44'),
(5, 'Perawatan Rambut', '2025-06-15 19:11:54'),
(6, 'Perawatan Wajah', '2025-06-15 19:11:54'),
(7, 'Perawatan Badan', '2025-06-15 19:11:54'),
(8, 'Aksesoris', '2025-06-15 19:11:54');

-- --------------------------------------------------------

--
-- Table structure for table `locations`
--

CREATE TABLE `locations` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `address` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `image_filename` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `locations`
--

INSERT INTO `locations` (`id`, `name`, `address`, `created_at`, `image_filename`) VALUES
(2, 'Pixel ciaul', 'Jl. R.A. Kosasih No.278, Subangjaya, Kec. Cikole, Kota Sukabumi, Jawa Barat 43116', '2025-06-14 22:58:46', '684dfea67e72c.jpg'),
(3, 'Pixel Sudirman', 'Jl. Sudirman No.112, Benteng, Kec. Warudoyong, Kota Sukabumi, Jawa Barat 43132', '2025-06-14 23:35:48', '684e07548b668.jpg');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `order_id` int(11) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `order_id`, `message`, `is_read`, `created_at`) VALUES
(3, 10, 9, 'Pembayaran untuk Order #9 telah dikonfirmasi', 0, '2025-06-22 20:15:35'),
(4, 10, 10, 'Pembayaran untuk Order #10 telah dikonfirmasi', 0, '2025-07-10 11:20:18'),
(5, 12, NULL, 'Status keseluruhan pesanan Anda telah diubah menjadi: Sedang Dikirim', 0, '2025-07-10 12:39:28');

-- --------------------------------------------------------

--
-- Table structure for table `notification_logs`
--

CREATE TABLE `notification_logs` (
  `id` int(11) NOT NULL,
  `application_id` int(11) NOT NULL,
  `type` enum('whatsapp','email','sms') NOT NULL,
  `recipient` varchar(255) NOT NULL,
  `message_sent` text NOT NULL,
  `success` tinyint(1) NOT NULL DEFAULT 0,
  `response` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`response`)),
  `sent_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notification_logs`
--

INSERT INTO `notification_logs` (`id`, `application_id`, `type`, `recipient`, `message_sent`, `success`, `response`, `sent_at`) VALUES
(11, 4, 'whatsapp', '+6283853900297', '📝 *Halo Lukmanul Hakim Jayadi*\n\nTerima kasih telah melamar untuk posisi *Admin* di Pixel Barbershop.\n\nSetelah melalui proses seleksi, saat ini kami belum dapat melanjutkan aplikasi Anda. 📋\n\n📊 Skor Evaluasi: 0.00/100\n🧠 Skor Quiz: 0.00%\n\nJangan berkecil hati! Kami mendorong Anda untuk terus mengembangkan skill dan melamar kembali di kesempatan yang akan datang. 🚀\n\nTerima kasih atas minat Anda bergabung dengan kami! 🙏\n\n_Tim HR Pixel Barbershop_', 1, '{\"reason\":\"invalid token\",\"status\":false}', '2025-06-22 19:58:51'),
(12, 4, 'whatsapp', '+6283853900297', '📝 *Halo Lukmanul Hakim Jayadi*\n\nTerima kasih telah melamar untuk posisi *Admin* di Pixel Barbershop.\n\nSetelah melalui proses seleksi, saat ini kami belum dapat melanjutkan aplikasi Anda. 📋\n\n📊 Skor Evaluasi: 0.00/100\n🧠 Skor Quiz: 0.00%\n\nJangan berkecil hati! Kami mendorong Anda untuk terus mengembangkan skill dan melamar kembali di kesempatan yang akan datang. 🚀\n\nTerima kasih atas minat Anda bergabung dengan kami! 🙏\n\n_Tim HR Pixel Barbershop_', 1, '{\"reason\":\"invalid token\",\"status\":false}', '2025-07-07 12:11:25'),
(13, 4, 'whatsapp', '+6283853900297', '📞 *Selamat Lukmanul Hakim Jayadi!*\n\nAnda telah lolos ke tahap *INTERVIEW* untuk posisi *Admin*! 🎯\n\n📊 Skor Evaluasi: 0.00/100\n🧠 Skor Quiz: 0.00%\n\nTim kami akan menghubungi Anda untuk mengatur jadwal interview.\n\nPersiapkan diri Anda dengan baik! 💪\n\n_Tim HR Pixel Barbershop_', 1, '{\"reason\":\"invalid token\",\"status\":false}', '2025-07-08 14:32:09'),
(14, 4, 'whatsapp', '+6283853900297', '📞 *Selamat Lukmanul Hakim Jayadi!*\n\nAnda telah lolos ke tahap *INTERVIEW* untuk posisi *Admin*! 🎯\n\n📊 Skor Evaluasi: 0.00/100\n🧠 Skor Quiz: 0.00%\n\nTim kami akan menghubungi Anda untuk mengatur jadwal interview.\n\nPersiapkan diri Anda dengan baik! 💪\n\n_Tim HR Pixel Barbershop_', 1, '{\"reason\":\"invalid token\",\"status\":false}', '2025-07-08 14:32:22'),
(15, 4, 'whatsapp', '+6283853900297', '📞 *Selamat Lukmanul Hakim Jayadi!*\n\nAnda telah lolos ke tahap *INTERVIEW* untuk posisi *Admin*! 🎯\n\n📊 Skor Evaluasi: 0.00/100\n🧠 Skor Quiz: 0.00%\n\nTim kami akan menghubungi Anda untuk mengatur jadwal interview.\n\nPersiapkan diri Anda dengan baik! 💪\n\n_Tim HR Pixel Barbershop_', 1, '{\"detail\":\"success! message in queue\",\"id\":[\"107308827\"],\"process\":\"pending\",\"quota\":{\"6283853900297\":{\"details\":\"deduced from total quota\",\"quota\":999,\"remaining\":998,\"used\":1}},\"requestid\":30968619,\"status\":true,\"target\":[\"6283853900297\"]}', '2025-07-08 14:43:39'),
(16, 5, 'whatsapp', '+62 83896982288', '📝 *Halo SITI SALMA ZAKIYAH*\n\nTerima kasih telah melamar untuk posisi *Admin* di Pixel Barbershop.\n\nSetelah melalui proses seleksi, saat ini kami belum dapat melanjutkan aplikasi Anda. 📋\n\n📊 Skor Evaluasi: 3.75/100\n🧠 Skor Quiz: 25.00%\n\nJangan berkecil hati! Kami mendorong Anda untuk terus mengembangkan skill dan melamar kembali di kesempatan yang akan datang. 🚀\n\nTerima kasih atas minat Anda bergabung dengan kami! 🙏\n\n_Tim HR Pixel Barbershop_', 1, '{\"detail\":\"success! message in queue\",\"id\":[\"107309427\"],\"process\":\"pending\",\"quota\":{\"6283853900297\":{\"details\":\"deduced from total quota\",\"quota\":998,\"remaining\":997,\"used\":1}},\"requestid\":30970446,\"status\":true,\"target\":[\"6283896982288\"]}', '2025-07-08 14:49:42'),
(17, 5, 'whatsapp', '+62 83896982288', '📝 *Halo SITI SALMA ZAKIYAH*\n\nTerima kasih telah melamar untuk posisi *Admin* di Pixel Barbershop.\n\nSetelah melalui proses seleksi, saat ini kami belum dapat melanjutkan aplikasi Anda. 📋\n\n📊 Skor Evaluasi: 3.75/100\n🧠 Skor Quiz: 25.00%\n\nJangan berkecil hati! Kami mendorong Anda untuk terus mengembangkan skill dan melamar kembali di kesempatan yang akan datang. 🚀\n\nTerima kasih atas minat Anda bergabung dengan kami! 🙏\n\n_Tim HR Pixel Barbershop_', 1, '{\"detail\":\"success! message in queue\",\"id\":[\"107309449\"],\"process\":\"pending\",\"quota\":{\"6283853900297\":{\"details\":\"deduced from total quota\",\"quota\":997,\"remaining\":996,\"used\":1}},\"requestid\":30970492,\"status\":true,\"target\":[\"6283896982288\"]}', '2025-07-08 14:49:59'),
(18, 6, 'whatsapp', '************', '📝 *Halo Lukman Lukmanul hakim jayadi*\n\nTerima kasih telah melamar untuk posisi *Admin* di Pixel Barbershop.\n\nSetelah melalui proses seleksi, saat ini kami belum dapat melanjutkan aplikasi Anda. 📋\n\n📊 Skor Evaluasi: 0.00/100\n🧠 Skor Quiz: 0.00%\n\nJangan berkecil hati! Kami mendorong Anda untuk terus mengembangkan skill dan melamar kembali di kesempatan yang akan datang. 🚀\n\nTerima kasih atas minat Anda bergabung dengan kami! 🙏\n\n_Tim HR Pixel Barbershop_', 1, '{\"detail\":\"success! message in queue\",\"id\":[\"107312597\"],\"process\":\"pending\",\"quota\":{\"6283853900297\":{\"details\":\"deduced from total quota\",\"quota\":996,\"remaining\":995,\"used\":1}},\"requestid\":30984179,\"status\":true,\"target\":[\"6283853900297\"]}', '2025-07-08 15:32:24'),
(19, 4, 'whatsapp', '+6283853900297', '📞 *Selamat Lukmanul Hakim Jayadi!*\n\nAnda telah lolos ke tahap *INTERVIEW* untuk posisi *Admin*! 🎯\n\n📊 Skor Evaluasi: 0.00/100\n🧠 Skor Quiz: 0.00%\n\nTim kami akan menghubungi Anda untuk mengatur jadwal interview.\n\nPersiapkan diri Anda dengan baik! 💪\n\n_Tim HR Pixel Barbershop_', 1, '{\"detail\":\"success! message in queue\",\"id\":[\"107312673\"],\"process\":\"pending\",\"quota\":{\"6283853900297\":{\"details\":\"deduced from total quota\",\"quota\":995,\"remaining\":994,\"used\":1}},\"requestid\":30984297,\"status\":true,\"target\":[\"6283853900297\"]}', '2025-07-08 15:32:58'),
(20, 7, 'whatsapp', '************', '📝 *Halo Lukman Lukmanul hakim jayadi*\n\nTerima kasih telah melamar untuk posisi *Admin* di Pixel Barbershop.\n\nSetelah melalui proses seleksi, saat ini kami belum dapat melanjutkan aplikasi Anda. 📋\n\n📊 Skor Evaluasi: 0.00/100\n🧠 Skor Quiz: 0.00%\n\nJangan berkecil hati! Kami mendorong Anda untuk terus mengembangkan skill dan melamar kembali di kesempatan yang akan datang. 🚀\n\nTerima kasih atas minat Anda bergabung dengan kami! 🙏\n\n_Tim HR Pixel Barbershop_', 1, '{\"detail\":\"success! message in queue\",\"id\":[\"107648903\"],\"process\":\"pending\",\"quota\":{\"6283853900297\":{\"details\":\"deduced from total quota\",\"quota\":993,\"remaining\":992,\"used\":1}},\"requestid\":32350964,\"status\":true,\"target\":[\"6283853900297\"]}', '2025-07-10 11:16:15');

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `pembeli_id` int(11) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `delivery_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `service_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `order_status` enum('pending','confirmed','processing','shipped','delivered','cancelled') DEFAULT 'pending',
  `payment_status` enum('pending','paid','failed','refunded') DEFAULT 'pending',
  `address_id` int(11) NOT NULL,
  `payment_method_id` int(11) NOT NULL,
  `midtrans_transaction_id` varchar(100) DEFAULT NULL,
  `user_notified` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `tracking_number` varchar(100) DEFAULT NULL COMMENT 'Shipping tracking number'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `pembeli_id`, `total_amount`, `delivery_cost`, `service_fee`, `order_status`, `payment_status`, `address_id`, `payment_method_id`, `midtrans_transaction_id`, `user_notified`, `created_at`, `updated_at`, `tracking_number`) VALUES
(9, 10, 17135.00, 15000.00, 2000.00, 'pending', 'pending', 11, 1, NULL, 0, '2025-06-22 15:15:33', '2025-06-22 20:15:33', NULL),
(10, 10, 2042.00, 0.00, 2000.00, 'pending', 'pending', 11, 1, NULL, 0, '2025-07-10 06:20:17', '2025-07-10 11:20:17', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `order_history`
--

CREATE TABLE `order_history` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `status` enum('pending','confirmed','processing','shipped','delivered','cancelled') NOT NULL,
  `notes` text DEFAULT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `order_items`
--

CREATE TABLE `order_items` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `variant_id` int(11) DEFAULT NULL,
  `quantity` int(11) NOT NULL,
  `price_per_unit` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `product_name_at_purchase` varchar(255) NOT NULL,
  `variant_name_at_purchase` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `status_pengiriman` varchar(32) DEFAULT 'Belum Dikirim'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `order_items`
--

INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `variant_id`, `quantity`, `price_per_unit`, `total_price`, `product_name_at_purchase`, `variant_name_at_purchase`, `created_at`, `status_pengiriman`) VALUES
(9, 9, 3, 4, 3, 45.00, 135.00, 'Shampo', '23 ml', '2025-06-22 20:15:33', 'Belum Dikirim'),
(10, 10, 1, 2, 2, 21.00, 42.00, 'pomade', '23 ml', '2025-07-10 11:20:17', 'Belum Dikirim');

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL,
  `type` varchar(50) DEFAULT 'bank_transfer',
  `qr_code_image` varchar(255) DEFAULT NULL,
  `payment_url` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `name`, `code`, `type`, `qr_code_image`, `payment_url`, `is_active`, `created_at`) VALUES
(1, 'Cash on Delivery', 'cod', 'cod', NULL, NULL, 1, '2025-06-14 18:37:44'),
(2, 'Mandiri', 'bank_transfer', 'M-banking', '*************', NULL, 1, '2025-06-14 18:37:44'),
(4, 'Dana', 'e_wallet', 'E-wallet', '************', NULL, 1, '2025-06-14 18:37:44');

-- --------------------------------------------------------

--
-- Table structure for table `pembeli`
--

CREATE TABLE `pembeli` (
  `id` int(11) NOT NULL,
  `nama_pembeli` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `password` varchar(255) NOT NULL,
  `alamat` text DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `profile_picture` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `pembeli`
--

INSERT INTO `pembeli` (`id`, `nama_pembeli`, `email`, `phone`, `created_at`, `password`, `alamat`, `updated_at`, `profile_picture`) VALUES
(9, 'Lukmanul Hakim Jayadi', '<EMAIL>', '************', '2025-06-17 09:03:01', '$2y$10$FKRMOYZzqlGjjK.rnn40weC75ImXOb7UGS6dgz7Nph3xlvSV4FHKi', NULL, '2025-06-17 09:51:12', 'profile_9_1750153872.jpeg'),
(10, 'Lukman', NULL, '08657', '2025-06-17 10:36:11', '', NULL, '2025-06-17 10:36:11', NULL),
(11, 'Lukman Lukmanul hakim jayadi', '<EMAIL>', '************', '2025-07-02 13:39:31', '$2y$10$wjFaCSJZK6PU4jngca7XK.uCIWWKYTwckQCYOvwlHrl2NPd8k6y0u', NULL, '2025-07-02 13:39:31', NULL),
(12, 'Lukman', '<EMAIL>', '************', '2025-07-10 03:43:23', '$2y$10$k29a.CeNucLYY3kOJmi74.Aj2utnMOMGDNXoYukAYDb/ZgSZ6Eeqm', NULL, '2025-07-10 03:43:23', NULL),
(13, 'Lukman Lukmanul hakim jayadi', '<EMAIL>', '************', '2025-07-10 10:23:06', '$2y$10$p.V0k5oyMgRL3YCLVqAm4uuv95nqlfcXJ4K5hRGePRi07LFRQeSa.', NULL, '2025-07-10 15:44:30', 'profile_13_1752162270.jpeg');

-- --------------------------------------------------------

--
-- Table structure for table `produk`
--

CREATE TABLE `produk` (
  `id` int(11) NOT NULL,
  `nama_produk` varchar(255) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `harga` decimal(10,2) NOT NULL DEFAULT 0.00,
  `gambar_utama` varchar(255) DEFAULT NULL,
  `kategori_id` int(11) DEFAULT NULL,
  `variasi` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`variasi`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `produk`
--

INSERT INTO `produk` (`id`, `nama_produk`, `deskripsi`, `harga`, `gambar_utama`, `kategori_id`, `variasi`, `created_at`) VALUES
(1, 'pomade', 'jasndjaskj', 3213.00, 'pomade.jpg', 1, NULL, '2025-06-14 18:40:29'),
(3, 'Shampo', 'Untuk PerawatanRambut Lebih Baik', 40.00, 'SHAMPOOBARBERSHOP.webp', 1, NULL, '2025-06-22 20:05:41');

-- --------------------------------------------------------

--
-- Table structure for table `quiz_answers`
--

CREATE TABLE `quiz_answers` (
  `id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `answer` text NOT NULL,
  `is_correct` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `quiz_answers`
--

INSERT INTO `quiz_answers` (`id`, `question_id`, `answer`, `is_correct`) VALUES
(5, 2, 'Buzz cut', 0),
(6, 2, 'Quizz', 0),
(7, 2, 'Bowl cut', 0),
(8, 2, 'Fringe', 1),
(9, 3, 'Gel', 0),
(10, 3, 'Serum', 0),
(11, 3, 'Pomade', 1),
(12, 3, 'Kondisioner', 0),
(13, 4, 'Dagu runcing', 0),
(14, 4, 'Pipi bulat', 0),
(15, 4, 'Rahang tegas dan lebar', 1),
(16, 4, 'Dahi sempit', 0);

-- --------------------------------------------------------

--
-- Table structure for table `quiz_questions`
--

CREATE TABLE `quiz_questions` (
  `id` int(11) NOT NULL,
  `question` text NOT NULL,
  `job_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `quiz_questions`
--

INSERT INTO `quiz_questions` (`id`, `question`, `job_id`) VALUES
(2, 'Apa gaya rambut yang paling cocok untuk bentuk wajah round agar terlihat lebih panjang?', 5),
(3, 'Produk apa yang digunakan untuk memberikan efek kilau dan daya tahan tinggi pada gaya rambut?', 5),
(4, 'Teknik fade dalam potongan rambut berarti?', 5);

-- --------------------------------------------------------

--
-- Table structure for table `user_addresses`
--

CREATE TABLE `user_addresses` (
  `id` int(11) NOT NULL,
  `pembeli_id` int(11) NOT NULL,
  `address_name` varchar(255) NOT NULL,
  `phone_number` varchar(20) NOT NULL,
  `full_address` text NOT NULL,
  `address_line1` text NOT NULL,
  `address_line2` text DEFAULT NULL,
  `city` varchar(100) NOT NULL,
  `state` varchar(100) NOT NULL,
  `postal_code` varchar(20) NOT NULL,
  `country` varchar(100) NOT NULL DEFAULT 'Indonesia',
  `is_default` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_addresses`
--

INSERT INTO `user_addresses` (`id`, `pembeli_id`, `address_name`, `phone_number`, `full_address`, `address_line1`, `address_line2`, `city`, `state`, `postal_code`, `country`, `is_default`, `created_at`) VALUES
(11, 10, 'Lukman', '08657', 'Kp.Kaum Kidul, Desa.Bangbayang, Kecamatan.Cicurug, Kabupaten.Sukabumi', '', NULL, '', '', '', 'Indonesia', 0, '2025-06-17 10:36:11');

-- --------------------------------------------------------

--
-- Table structure for table `variasi_produk`
--

CREATE TABLE `variasi_produk` (
  `id` int(11) NOT NULL,
  `produk_id` int(11) NOT NULL,
  `ukuran_ml` varchar(50) NOT NULL,
  `harga` decimal(10,2) NOT NULL,
  `stok` int(11) NOT NULL DEFAULT 0,
  `gambar_varian` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `variasi_produk`
--

INSERT INTO `variasi_produk` (`id`, `produk_id`, `ukuran_ml`, `harga`, `stok`, `gambar_varian`, `created_at`) VALUES
(1, 1, '23', 122.00, 12, '684dc21df150e_pomade.jpg', '2025-06-14 18:40:29'),
(2, 1, '23', 21.00, 23, '684dc21df189a_pomade.jpg', '2025-06-14 18:40:29'),
(4, 3, '23', 45.00, 10, '68586215a2332_SHAMPOOBARBERSHOP.webp', '2025-06-22 20:05:41'),
(5, 3, '30', 50.00, 15, NULL, '2025-06-22 20:05:41');

-- --------------------------------------------------------

--
-- Structure for view `daftar_produk`
--
DROP TABLE IF EXISTS `daftar_produk`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `daftar_produk`  AS SELECT `p`.`id` AS `id`, `p`.`gambar_utama` AS `gambar_utama`, `p`.`nama_produk` AS `nama_produk`, `k`.`nama_kategori` AS `nama_kategori`, `p`.`harga` AS `harga`, concat('<a href="edit_produk.php?id=',`p`.`id`,'" class="btn btn-sm btn-primary">Edit</a> ','<a href="hapus_produk.php?id=',`p`.`id`,'" class="btn btn-sm btn-danger" onclick="return confirm(\'Are you sure?\')">Delete</a>') AS `aksi` FROM (`produk` `p` left join `kategori_produk` `k` on(`p`.`kategori_id` = `k`.`id`)) ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin`
--
ALTER TABLE `admin`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Indexes for table `applications`
--
ALTER TABLE `applications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `job_id` (`job_id`);

--
-- Indexes for table `barbers`
--
ALTER TABLE `barbers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_location` (`location_id`);

--
-- Indexes for table `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `location_id` (`location_id`),
  ADD KEY `barber_id` (`barber_id`);

--
-- Indexes for table `cart`
--
ALTER TABLE `cart`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_cart_item` (`user_id`,`product_id`,`variant_id`);

--
-- Indexes for table `face_analysis`
--
ALTER TABLE `face_analysis`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `gambar_produk`
--
ALTER TABLE `gambar_produk`
  ADD PRIMARY KEY (`id`),
  ADD KEY `produk_id` (`produk_id`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kategori_produk`
--
ALTER TABLE `kategori_produk`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `locations`
--
ALTER TABLE `locations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `order_id` (`order_id`);

--
-- Indexes for table `notification_logs`
--
ALTER TABLE `notification_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_application_id` (`application_id`),
  ADD KEY `idx_sent_at` (`sent_at`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_orders_address` (`address_id`),
  ADD KEY `fk_orders_payment` (`payment_method_id`),
  ADD KEY `idx_orders_status` (`order_status`),
  ADD KEY `idx_orders_pembeli` (`pembeli_id`),
  ADD KEY `idx_orders_created` (`created_at`);

--
-- Indexes for table `order_history`
--
ALTER TABLE `order_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_order_id` (`order_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `admin_id` (`admin_id`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `variant_id` (`variant_id`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `pembeli`
--
ALTER TABLE `pembeli`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `produk`
--
ALTER TABLE `produk`
  ADD PRIMARY KEY (`id`),
  ADD KEY `kategori_id` (`kategori_id`);

--
-- Indexes for table `quiz_answers`
--
ALTER TABLE `quiz_answers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `question_id` (`question_id`);

--
-- Indexes for table `quiz_questions`
--
ALTER TABLE `quiz_questions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user_addresses`
--
ALTER TABLE `user_addresses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pembeli_id` (`pembeli_id`);

--
-- Indexes for table `variasi_produk`
--
ALTER TABLE `variasi_produk`
  ADD PRIMARY KEY (`id`),
  ADD KEY `produk_id` (`produk_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin`
--
ALTER TABLE `admin`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `applications`
--
ALTER TABLE `applications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `barbers`
--
ALTER TABLE `barbers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `bookings`
--
ALTER TABLE `bookings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `cart`
--
ALTER TABLE `cart`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `face_analysis`
--
ALTER TABLE `face_analysis`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=106;

--
-- AUTO_INCREMENT for table `gambar_produk`
--
ALTER TABLE `gambar_produk`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `kategori_produk`
--
ALTER TABLE `kategori_produk`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `locations`
--
ALTER TABLE `locations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `notification_logs`
--
ALTER TABLE `notification_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `order_history`
--
ALTER TABLE `order_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `pembeli`
--
ALTER TABLE `pembeli`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `produk`
--
ALTER TABLE `produk`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `quiz_answers`
--
ALTER TABLE `quiz_answers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `quiz_questions`
--
ALTER TABLE `quiz_questions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `user_addresses`
--
ALTER TABLE `user_addresses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `variasi_produk`
--
ALTER TABLE `variasi_produk`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `applications`
--
ALTER TABLE `applications`
  ADD CONSTRAINT `applications_ibfk_1` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `barbers`
--
ALTER TABLE `barbers`
  ADD CONSTRAINT `fk_location` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `gambar_produk`
--
ALTER TABLE `gambar_produk`
  ADD CONSTRAINT `gambar_produk_ibfk_1` FOREIGN KEY (`produk_id`) REFERENCES `produk` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `pembeli` (`id`),
  ADD CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`);

--
-- Constraints for table `notification_logs`
--
ALTER TABLE `notification_logs`
  ADD CONSTRAINT `notification_logs_ibfk_1` FOREIGN KEY (`application_id`) REFERENCES `applications` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `fk_orders_address` FOREIGN KEY (`address_id`) REFERENCES `user_addresses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_orders_payment` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`),
  ADD CONSTRAINT `fk_orders_pembeli` FOREIGN KEY (`pembeli_id`) REFERENCES `pembeli` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `order_history`
--
ALTER TABLE `order_history`
  ADD CONSTRAINT `order_history_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admin` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `order_items`
--
ALTER TABLE `order_items`
  ADD CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `produk` (`id`),
  ADD CONSTRAINT `order_items_ibfk_3` FOREIGN KEY (`variant_id`) REFERENCES `variasi_produk` (`id`);

--
-- Constraints for table `produk`
--
ALTER TABLE `produk`
  ADD CONSTRAINT `produk_ibfk_1` FOREIGN KEY (`kategori_id`) REFERENCES `kategori_produk` (`id`);

--
-- Constraints for table `quiz_answers`
--
ALTER TABLE `quiz_answers`
  ADD CONSTRAINT `quiz_answers_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `quiz_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_addresses`
--
ALTER TABLE `user_addresses`
  ADD CONSTRAINT `user_addresses_ibfk_1` FOREIGN KEY (`pembeli_id`) REFERENCES `pembeli` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `variasi_produk`
--
ALTER TABLE `variasi_produk`
  ADD CONSTRAINT `variasi_produk_ibfk_1` FOREIGN KEY (`produk_id`) REFERENCES `produk` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
