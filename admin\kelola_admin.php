<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Temporarily disabled login check for initial admin creation
// if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
//     header("Location: login.php");
//     exit();
// }

require_once 'db_config.php';

// Handle admin management actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'create') {
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        $full_name = $_POST['full_name'] ?? '';
        $email = $_POST['email'] ?? '';
        $role = $_POST['role'] ?? 'admin';

        if (!empty($username) && !empty($password) && !empty($full_name) && !empty($email)) {
            try {
                // Check if username already exists
                $stmt = $conn->prepare("SELECT id FROM admin WHERE username = ?");
                $stmt->execute([$username]);

                if ($stmt->fetch()) {
                    $message = "Username sudah ada!";
                    $message_type = "error";
                } else {
                    // Hash password
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

                    // Insert new admin
                    $stmt = $conn->prepare("INSERT INTO admin (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([$username, $hashed_password, $full_name, $email, $role]);

                    $message = "Admin baru berhasil dibuat!";
                    $message_type = "success";
                }
            } catch (PDOException $e) {
                $message = "Error: " . $e->getMessage();
                $message_type = "error";
            }
        } else {
            $message = "Semua field harus diisi!";
            $message_type = "error";
        }
    }

    if ($action === 'reset') {
        $admin_id = $_POST['admin_id'] ?? '';
        $new_password = $_POST['new_password'] ?? '';

        if (!empty($admin_id) && !empty($new_password)) {
            try {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $conn->prepare("UPDATE admin SET password = ? WHERE id = ?");
                $stmt->execute([$hashed_password, $admin_id]);

                $message = "Password berhasil direset!";
                $message_type = "success";
            } catch (PDOException $e) {
                $message = "Error: " . $e->getMessage();
                $message_type = "error";
            }
        } else {
            $message = "ID Admin dan password baru harus diisi!";
            $message_type = "error";
        }
    }

    if ($action === 'toggle_status') {
        $admin_id = $_POST['admin_id'] ?? '';
        $new_status = $_POST['new_status'] ?? '';

        if (!empty($admin_id) && $new_status !== '') {
            try {
                $stmt = $conn->prepare("UPDATE admin SET is_active = ? WHERE id = ?");
                $stmt->execute([$new_status, $admin_id]);

                $message = "Status admin berhasil diperbarui!";
                $message_type = "success";
            } catch (PDOException $e) {
                $message = "Error: " . $e->getMessage();
                $message_type = "error";
            }
        }
    }
}

// Get all admins
try {
    $stmt = $conn->query("SELECT id, username, full_name, email, role, is_active, last_login, created_at FROM admin ORDER BY created_at DESC");
    $admins = $stmt->fetchAll();
} catch (PDOException $e) {
    $admins = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Admin - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            background: #2d3748;
            color: #fff;
        }
        .sidebar .logo {
            background: #1a202c;
            padding: 1rem;
        }
        .sidebar .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .sidebar nav ul li {
            transition: all 0.3s ease;
        }
        .sidebar nav ul li a {
            color: #fff;
        }
        .sidebar nav ul li.active {
            background: rgb(35, 71, 250);
        }
        .sidebar nav ul li.active a,
        .sidebar nav ul li.active i {
            color: #fff;
        }
        .sidebar nav ul li i {
            color: #4299e1;
        }
        .sidebar nav ul li.active i {
            color: #fff;
        }
        .stat-card {
            transition: transform 0.2s ease-in-out;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <aside class="w-64 sidebar">
            <div class="logo">
                <h1 class="text-2xl font-bold">Admin Panel</h1>
            </div>
            <nav class="mt-4">
                <ul>
                    <li class="px-4 py-3">
                        <a href="kelola_lowongan.php" class="flex items-center space-x-3">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <!-- Manajemen Booking -->
                    <li class="mb-2">
                        <button onclick="toggleBookingSubmenu()" class="w-full flex items-center justify-between px-4 py-3 rounded-lg text-white focus:outline-none">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-calendar-check"></i>
                                <span>Manajemen Booking</span>
                            </span>
                            <i id="arrowBookingIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="bookingSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="data_booking.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Data Booking</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_lokasi.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Kelola Lokasi</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_barber.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cut"></i>
                                    <span>Kelola Barber</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- Manajemen Produk -->
                    <li class="mb-2">
                        <button onclick="toggleProdukSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-box"></i>
                                <span>Manajemen Produk</span>
                            </span>
                            <i id="arrowProdukIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="produkSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_produk.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cube"></i>
                                    <span>Produk</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_pembeli.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pembeli</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_metode_pembayaran.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-money-check-alt"></i>
                                    <span>Metode Pembayaran</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- Manajemen Lowongan -->
                    <li class="mb-2">
                        <button onclick="toggleLowonganSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-briefcase"></i>
                                <span>Manajemen Lowongan</span>
                            </span>
                            <i id="arrowLowonganIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="lowonganSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_lowongan.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-tasks"></i>
                                    <span>Kelola Lowongan</span>
                                </a>
                            </li>
                            <li>
                                <a href="data_pelamar.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pelamar</span>
                                </a>
                            </li>
                            <li>
                                <a href="whatsapp_manager.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>WhatsApp Manager</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="px-4 py-3 active">
                        <a href="kelola_admin.php" class="flex items-center space-x-3">
                            <i class="fas fa-user-shield"></i>
                            <span>Kelola Admin</span>
                        </a>
                    </li>

                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col">
            <header class="flex items-center justify-between px-6 py-4 bg-white border-b-4 border-indigo-600">
                <div class="flex items-center">
                    <button class="text-gray-500 focus:outline-none">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-xl font-semibold ml-4">Kelola Admin</h1>
                </div>

                <div class="flex items-center">
                    <span class="mr-4 text-gray-700 hidden sm:block">
                        <?php echo $_SESSION['admin_full_name'] ?? 'Setup Admin'; ?>
                        <span class="text-sm text-gray-500">(Admin Management)</span>
                    </span>
                    <div class="relative">
                        <button id="profileDropdown" class="flex items-center focus:outline-none">
                            <?php
                            // Get admin profile photo
                            if (isset($_SESSION['admin_id'])) {
                                $stmt = $conn->prepare("SELECT profile_photo FROM admin WHERE id = ?");
                                $stmt->execute([$_SESSION['admin_id']]);
                                $profile_photo = $stmt->fetchColumn();
                            } else {
                                $profile_photo = null;
                            }

                            if (!empty($profile_photo) && file_exists('uploads/profiles/' . $profile_photo)):
                            ?>
                                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-300 hover:border-blue-500 transition-colors"
                                     src="uploads/profiles/<?= htmlspecialchars($profile_photo) ?>"
                                     alt="Admin avatar">
                            <?php else: ?>
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-300 hover:border-blue-500 transition-colors">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                            <?php endif; ?>
                            <i class="fas fa-chevron-down ml-2 text-gray-500 text-sm"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="profileDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="profile_settings.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-3 text-gray-500"></i>
                                Profile Settings
                            </a>
                            <hr class="my-1">
                            <a href="logout.php" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-3 text-red-500"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 p-6 overflow-y-auto">
                <!-- Success/Error Messages -->
                <?php if (isset($message)): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <!-- Create Admin Button -->
              <div class="mb-6">
    <button onclick="openCreateAdminModal()" 
        class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg shadow text-base flex items-center">
        <i class="fas fa-user-plus mr-2"></i>
        Tambah Admin
    </button>
</div>


                <!-- Admin List (only show if admins exist) -->
                <?php if (!empty($admins)): ?>
                <div class="bg-white rounded-xl shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold">Daftar Admin</h2>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full border border-gray-300">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th class="px-4 py-2 border border-gray-300 text-left">Username</th>
                                    <th class="px-4 py-2 border border-gray-300 text-left">Nama Lengkap</th>
                                    <th class="px-4 py-2 border border-gray-300 text-left">Email</th>
                                    <th class="px-4 py-2 border border-gray-300 text-left">Role</th>
                                    <th class="px-4 py-2 border border-gray-300 text-left">Status</th>
                                    <th class="px-4 py-2 border border-gray-300 text-left">Last Login</th>
                                    <th class="px-4 py-2 border border-gray-300 text-left">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($admins as $admin): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-2 border border-gray-300"><?= htmlspecialchars($admin['username']) ?></td>
                                    <td class="px-4 py-2 border border-gray-300"><?= htmlspecialchars($admin['full_name']) ?></td>
                                    <td class="px-4 py-2 border border-gray-300"><?= htmlspecialchars($admin['email']) ?></td>
                                    <td class="px-4 py-2 border border-gray-300">
                                        <span class="px-2 py-1 rounded text-xs font-semibold
                                            <?php
                                            switch($admin['role']) {
                                                case 'super_admin': echo 'bg-red-100 text-red-800'; break;
                                                case 'admin': echo 'bg-blue-100 text-blue-800'; break;
                                                case 'moderator': echo 'bg-green-100 text-green-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800';
                                            }
                                            ?>">
                                            <?= ucfirst(str_replace('_', ' ', $admin['role'])) ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 border border-gray-300">
                                        <span class="px-2 py-1 rounded text-xs font-semibold <?= $admin['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                            <?= $admin['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 border border-gray-300">
                                        <?= $admin['last_login'] ? date('d/m/Y H:i', strtotime($admin['last_login'])) : 'Never' ?>
                                    </td>
                                    <td class="px-4 py-2 border border-gray-300">
                                        <div class="flex space-x-1">
                                            <button onclick="resetPassword(<?= $admin['id'] ?>, '<?= htmlspecialchars($admin['username']) ?>')"
                                                    class="px-2 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600">
                                                Reset Password
                                            </button>
                                            <button onclick="toggleStatus(<?= $admin['id'] ?>, <?= $admin['is_active'] ? 0 : 1 ?>)"
                                                    class="px-2 py-1 <?= $admin['is_active'] ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600' ?> text-white rounded text-xs">
                                                <?= $admin['is_active'] ? 'Nonaktifkan' : 'Aktifkan' ?>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- Create Admin Modal -->
    <div id="createAdminModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900">Buat Admin Baru</h3>
                    <button onclick="closeCreateAdminModal()" class="text-gray-500 hover:text-gray-700 text-2xl font-bold focus:outline-none">&times;</button>
                </div>

                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="create">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                            <input type="text" name="username" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="Masukkan username">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <input type="password" name="password" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="Masukkan password">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Nama Lengkap</label>
                            <input type="text" name="full_name" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="Masukkan nama lengkap">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" name="email" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="Masukkan email">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                        <select name="role" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="admin">Admin</option>
                            <option value="super_admin">Super Admin</option>
                            <option value="moderator">Moderator</option>
                        </select>
                    </div>

                    <div class="flex justify-end space-x-3 pt-6">
                        <button type="button" onclick="closeCreateAdminModal()"
                                class="px-6 py-3 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 transition duration-200">
                            Batal
                        </button>
                        <button type="submit"
                                class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200">
                            <i class="fas fa-plus mr-2"></i>Buat Admin
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reset Password Modal -->
    <div id="resetModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-lg w-full max-w-md p-6">
            <h3 class="text-xl font-bold mb-4">Reset Password</h3>
            <form method="POST">
                <input type="hidden" name="action" value="reset">
                <input type="hidden" id="resetAdminId" name="admin_id">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Username:</label>
                    <p id="resetUsername" class="font-semibold"></p>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Password Baru:</label>
                    <input type="password" name="new_password" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div class="flex justify-end space-x-2">
                    <button type="button" onclick="closeResetModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">Batal</button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Reset Password</button>
                </div>
            </form>
        </div>
    </div>

    <script>
    function toggleBookingSubmenu() {
        const submenu = document.getElementById('bookingSubmenu');
        const icon = document.getElementById('arrowBookingIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleProdukSubmenu() {
        const submenu = document.getElementById('produkSubmenu');
        const icon = document.getElementById('arrowProdukIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleLowonganSubmenu() {
        const submenu = document.getElementById('lowonganSubmenu');
        const icon = document.getElementById('arrowLowonganIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function openCreateAdminModal() {
        document.getElementById('createAdminModal').classList.remove('hidden');
    }

    function closeCreateAdminModal() {
        document.getElementById('createAdminModal').classList.add('hidden');
    }

    function resetPassword(adminId, username) {
        document.getElementById('resetAdminId').value = adminId;
        document.getElementById('resetUsername').textContent = username;
        document.getElementById('resetModal').classList.remove('hidden');
    }

    function closeResetModal() {
        document.getElementById('resetModal').classList.add('hidden');
    }

    function toggleStatus(adminId, newStatus) {
        if (confirm('Apakah Anda yakin ingin mengubah status admin ini?')) {
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="toggle_status">
                <input type="hidden" name="admin_id" value="${adminId}">
                <input type="hidden" name="new_status" value="${newStatus}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    }

    // Close modal when clicking outside
    document.addEventListener('DOMContentLoaded', function() {
        const createModal = document.getElementById('createAdminModal');
        const resetModal = document.getElementById('resetModal');

        createModal.addEventListener('click', function(e) {
            if (e.target === createModal) {
                closeCreateAdminModal();
            }
        });

        resetModal.addEventListener('click', function(e) {
            if (e.target === resetModal) {
                closeResetModal();
            }
        });

        // Profile dropdown functionality
        const profileDropdown = document.getElementById('profileDropdown');
        const profileDropdownMenu = document.getElementById('profileDropdownMenu');

        if (profileDropdown && profileDropdownMenu) {
            profileDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdownMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdownMenu.classList.add('hidden');
                }
            });
        }
    });
    </script>
</body>
</html>
