<?php
session_start();
header('Content-Type: application/json');
require_once '../Admin/db_config.php';

$response = [
    'success' => false,
    'message' => '',
    'cart_count' => 0
];

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $response['message'] = 'User not logged in';
    echo json_encode($response);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Method not allowed';
    echo json_encode($response);
    exit();
}

$user_id = $_SESSION['user_id'];
$product_id = $_POST['product_id'] ?? null;
$variant_id = $_POST['variant_id'] ?? null;
$quantity = (int)($_POST['quantity'] ?? 1);

// Validation
if (!$product_id || !$variant_id || $quantity <= 0) {
    $response['message'] = 'Invalid product data';
    echo json_encode($response);
    exit();
}

try {
    // Create cart table if it doesn't exist
    $conn->exec("
        CREATE TABLE IF NOT EXISTS cart (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            product_id INT NOT NULL,
            variant_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_cart_item (user_id, product_id, variant_id)
        )
    ");

    // Check if product and variant exist
    $stmt = $conn->prepare("
        SELECT p.nama_produk, p.gambar_utama, v.ukuran_ml, v.harga, v.stok 
        FROM produk p 
        JOIN variasi_produk v ON p.id = v.produk_id 
        WHERE p.id = ? AND v.id = ?
    ");
    $stmt->execute([$product_id, $variant_id]);
    $product_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product_data) {
        $response['message'] = 'Product or variant not found';
        echo json_encode($response);
        exit();
    }
    
    // Check stock availability
    if ($quantity > $product_data['stok']) {
        $response['message'] = 'Insufficient stock. Available: ' . $product_data['stok'];
        echo json_encode($response);
        exit();
    }
    
    // Check if item already exists in cart
    $stmt = $conn->prepare("
        SELECT id, quantity FROM cart 
        WHERE user_id = ? AND product_id = ? AND variant_id = ?
    ");
    $stmt->execute([$user_id, $product_id, $variant_id]);
    $existing_item = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing_item) {
        // Update existing item
        $new_quantity = $existing_item['quantity'] + $quantity;
        
        // Check total stock
        if ($new_quantity > $product_data['stok']) {
            $response['message'] = 'Cannot add more items. Total would exceed stock limit.';
            echo json_encode($response);
            exit();
        }
        
        $stmt = $conn->prepare("
            UPDATE cart SET quantity = ?, updated_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$new_quantity, $existing_item['id']]);
        
    } else {
        // Insert new item
        $stmt = $conn->prepare("
            INSERT INTO cart (user_id, product_id, variant_id, quantity) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$user_id, $product_id, $variant_id, $quantity]);
    }
    
    // Get updated cart count
    $stmt = $conn->prepare("SELECT SUM(quantity) as total FROM cart WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $cart_count = $stmt->fetchColumn() ?: 0;
    
    // Update session cart count
    $_SESSION['cart_count'] = $cart_count;
    
    $response['success'] = true;
    $response['message'] = 'Item added to cart successfully';
    $response['cart_count'] = $cart_count;
    $response['product_name'] = $product_data['nama_produk'];
    $response['variant_size'] = $product_data['ukuran_ml'];
    
} catch (PDOException $e) {
    $response['message'] = 'Database error: ' . $e->getMessage();
    error_log("Add to cart error: " . $e->getMessage());
}

echo json_encode($response);
?>
