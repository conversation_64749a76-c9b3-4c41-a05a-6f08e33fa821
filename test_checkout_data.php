<?php
session_start();
require_once 'Admin/db_config.php';

echo "<h2>Checkout Data Test</h2>";

// Check session
echo "<h3>1. Session Check:</h3>";
if (isset($_SESSION['user_id'])) {
    echo "✅ User logged in with ID: " . $_SESSION['user_id'] . "<br>";
} else {
    echo "❌ User not logged in<br>";
}

// Check GET parameters
echo "<h3>2. GET Parameters:</h3>";
echo "<pre>";
print_r($_GET);
echo "</pre>";

// Test cart items parameter
if (isset($_GET['cart_items'])) {
    echo "<h3>3. Cart Items Processing:</h3>";
    $cart_item_ids = explode(',', $_GET['cart_items']);
    $cart_item_ids = array_map('intval', $cart_item_ids);
    echo "Cart Item IDs: ";
    print_r($cart_item_ids);
    
    if (!empty($cart_item_ids)) {
        try {
            $placeholders = str_repeat('?,', count($cart_item_ids) - 1) . '?';
            
            $stmt = $conn->prepare("
                SELECT c.id as cart_id, c.quantity, c.product_id, c.variant_id,
                       p.nama_produk, p.gambar_utama, p.deskripsi,
                       v.ukuran_ml, v.harga, v.gambar_varian,
                       (c.quantity * v.harga) as total_price
                FROM cart c
                JOIN produk p ON c.product_id = p.id
                JOIN variasi_produk v ON c.variant_id = v.id
                WHERE c.id IN ($placeholders) AND c.user_id = ?
                ORDER BY c.created_at DESC
            ");
            
            $params = array_merge($cart_item_ids, [$_SESSION['user_id']]);
            $stmt->execute($params);
            $cart_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>Found Cart Items:</h4>";
            echo "<pre>";
            print_r($cart_items);
            echo "</pre>";
            
            $total = 0;
            foreach ($cart_items as $item) {
                $total += $item['total_price'];
            }
            echo "<h4>Total Amount: Rp " . number_format($total, 0, ',', '.') . "</h4>";
            
        } catch (PDOException $e) {
            echo "❌ Database Error: " . $e->getMessage();
        }
    }
} else {
    echo "<h3>3. Single Item Checkout:</h3>";
    $product_id = $_GET['product_id'] ?? null;
    $variant_id = $_GET['variant_id'] ?? null;
    $quantity = $_GET['quantity'] ?? 1;
    
    echo "Product ID: $product_id<br>";
    echo "Variant ID: $variant_id<br>";
    echo "Quantity: $quantity<br>";
}

// Test links
echo "<h3>4. Test Links:</h3>";
echo "<a href='keranjang.php'>← Back to Cart</a><br>";
echo "<a href='cekout.php?cart_items=1,2'>Test Checkout with Cart Items 1,2</a><br>";
echo "<a href='cekout.php?product_id=1&variant_id=1&quantity=2'>Test Single Item Checkout</a><br>";
?>
