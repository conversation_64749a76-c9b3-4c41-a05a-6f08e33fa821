<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Images - Face Detection</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">🔍 Debug Images - Real Path Testing</h1>
                <p class="text-gray-600">Debug gambar model dengan path yang sebenarnya ada di folder</p>
            </div>

            <!-- Quick Test -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">⚡ Quick Path Test</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <button onclick="testDirectPaths()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-search mr-2"></i>Test Direct Paths
                    </button>
                    <button onclick="testAllShapes()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-images mr-2"></i>Test All Shapes
                    </button>
                </div>
                <div id="quickResults" class="space-y-4">
                    <p class="text-gray-500">Klik tombol untuk test path gambar...</p>
                </div>
            </div>

            <!-- Real File Test -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">📁 Test File yang Benar-benar Ada</h2>
                <div id="realFileTest" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <!-- Real files will be tested here -->
                </div>
            </div>

            <!-- Manual Path Test -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🔧 Manual Path Test</h2>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Test Path Manual:</label>
                    <input type="text" id="manualPath" placeholder="rekomendasi/oval/Textured Crop.jpg" 
                           class="border border-gray-300 rounded-md px-3 py-2 w-full">
                </div>
                <button onclick="testManualPath()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded mb-4">
                    <i class="fas fa-test mr-2"></i>Test Path
                </button>
                <div id="manualResults" class="space-y-4">
                    <!-- Manual test results -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // File yang benar-benar ada berdasarkan scan folder
        const realFiles = {
            'oval': [
                'Buzz Haircut.jpg',
                'Caesar Cut.jpg', 
                'Classic Undercut.jpg',
                'Haircut.jpg',
                'Long Layered.jpg',
                'Long Shaggy.jpg',
                'Man Bun.jpg',
                'Pompadour.jpg',
                'Textured Crop.jpg'
            ],
            'round': [
                'Buzz Cut.jpg',
                'Comma Hair.jpg',
                'Crop Cut.jpg',
                'Fluffy with Low Fade.jpg',
                'Fringe Haircut.jpg',
                'Taper Fade.jpg',
                'Two Block Hair.jpg'
            ],
            'square': [
                'Comb Over.jpg',
                'Crew Cut.jpg',
                'Faux Hawk.jpg',
                'Gaya Rambut Spike.jpg',
                'Quiff.jpg',
                'Side Swept.jpg',
                'Slick Back.jpg',
                'Wajah Persegi (Square).jpg'
            ],
            'heart': [
                'Classic Quiff.jpg',
                'Classic Side Part.jpg',
                'Long Fringe.jpg',
                'Long hair side part.jpg',
                'Short Faux Hawk.jpg',
                'Side Part Fringe.jpg',
                'Slick Back.jpg',
                'Taper Fade.jpg'
            ],
            'rectangular': [
                'Crew Cut.jpg',
                'Messy Fringe Haircuts for Men.jpg',
                'Short Spiky.jpg',
                'Side Part.jpg',
                'Slick Back Hairstyles for Men.jpg',
                'Textured Crop.jpg'
            ]
        };

        function testDirectPaths() {
            const resultsDiv = document.getElementById('quickResults');
            resultsDiv.innerHTML = '<p class="text-blue-600">🔍 Testing direct paths...</p>';
            
            // Test beberapa path langsung
            const testPaths = [
                'rekomendasi/oval/Textured Crop.jpg',
                'rekomendasi/oval/Pompadour.jpg',
                'rekomendasi/round/Two Block Hair.jpg',
                'rekomendasi/square/Slick Back.jpg',
                'rekomendasi/heart/Classic Quiff.jpg'
            ];
            
            let results = [];
            let completed = 0;
            
            testPaths.forEach(path => {
                const img = new Image();
                img.onload = function() {
                    results.push({ path, status: 'success', size: `${this.naturalWidth}x${this.naturalHeight}` });
                    completed++;
                    if (completed === testPaths.length) {
                        displayQuickResults(results);
                    }
                };
                img.onerror = function() {
                    results.push({ path, status: 'error', error: 'File not found or cannot load' });
                    completed++;
                    if (completed === testPaths.length) {
                        displayQuickResults(results);
                    }
                };
                img.src = path;
            });
        }

        function displayQuickResults(results) {
            const resultsDiv = document.getElementById('quickResults');
            let html = '<div class="space-y-2">';
            
            results.forEach(result => {
                const statusClass = result.status === 'success' ? 'text-green-600' : 'text-red-600';
                const statusIcon = result.status === 'success' ? '✅' : '❌';
                const extraInfo = result.status === 'success' ? ` (${result.size})` : ` (${result.error})`;
                
                html += `
                    <div class="p-3 border rounded-lg ${result.status === 'success' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}">
                        <span class="${statusClass}">${statusIcon} ${result.path}${extraInfo}</span>
                    </div>
                `;
            });
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        function testAllShapes() {
            const resultsDiv = document.getElementById('quickResults');
            resultsDiv.innerHTML = '<p class="text-green-600">🎨 Testing all shapes...</p>';
            
            let allResults = [];
            let totalTests = 0;
            let completedTests = 0;
            
            // Hitung total tests
            Object.keys(realFiles).forEach(shape => {
                totalTests += realFiles[shape].length;
            });
            
            // Test semua file
            Object.keys(realFiles).forEach(shape => {
                realFiles[shape].forEach(filename => {
                    const path = `rekomendasi/${shape}/${filename}`;
                    
                    const img = new Image();
                    img.onload = function() {
                        allResults.push({ 
                            shape, 
                            filename, 
                            path, 
                            status: 'success', 
                            size: `${this.naturalWidth}x${this.naturalHeight}` 
                        });
                        completedTests++;
                        updateAllShapesProgress(completedTests, totalTests, allResults);
                    };
                    img.onerror = function() {
                        allResults.push({ 
                            shape, 
                            filename, 
                            path, 
                            status: 'error', 
                            error: 'Cannot load' 
                        });
                        completedTests++;
                        updateAllShapesProgress(completedTests, totalTests, allResults);
                    };
                    img.src = path;
                });
            });
        }

        function updateAllShapesProgress(completed, total, results) {
            const resultsDiv = document.getElementById('quickResults');
            const progress = Math.round((completed / total) * 100);
            
            if (completed === total) {
                const successCount = results.filter(r => r.status === 'success').length;
                const errorCount = results.filter(r => r.status === 'error').length;
                const successRate = Math.round((successCount / total) * 100);
                
                let html = `
                    <div class="space-y-4">
                        <div class="p-4 ${successRate >= 80 ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'} border rounded-lg">
                            <h3 class="font-bold ${successRate >= 80 ? 'text-green-800' : 'text-yellow-800'}">
                                📊 Test Complete
                            </h3>
                            <p class="${successRate >= 80 ? 'text-green-700' : 'text-yellow-700'}">
                                Total: ${total} | Success: ${successCount} (${successRate}%) | Failed: ${errorCount}
                            </p>
                        </div>
                `;
                
                // Group by shape
                Object.keys(realFiles).forEach(shape => {
                    const shapeResults = results.filter(r => r.shape === shape);
                    const shapeSuccess = shapeResults.filter(r => r.status === 'success').length;
                    const shapeTotal = shapeResults.length;
                    const shapeRate = Math.round((shapeSuccess / shapeTotal) * 100);
                    
                    html += `
                        <div class="p-3 border rounded-lg ${shapeRate >= 80 ? 'bg-green-50' : 'bg-red-50'}">
                            <h4 class="font-bold capitalize">${shape}: ${shapeSuccess}/${shapeTotal} (${shapeRate}%)</h4>
                            <div class="text-sm mt-1">
                                ${shapeResults.filter(r => r.status === 'error').slice(0, 3).map(r => 
                                    `<span class="text-red-600">❌ ${r.filename}</span>`
                                ).join('<br>')}
                            </div>
                        </div>
                    `;
                });
                
                html += '</div>';
                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `
                    <div class="p-4 bg-blue-50 border-blue-200 border rounded-lg">
                        <p class="text-blue-700">Testing progress: ${completed}/${total} (${progress}%)</p>
                        <div class="w-full bg-blue-200 rounded-full h-2 mt-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: ${progress}%"></div>
                        </div>
                    </div>
                `;
            }
        }

        function testManualPath() {
            const pathInput = document.getElementById('manualPath');
            const resultsDiv = document.getElementById('manualResults');
            const path = pathInput.value.trim();
            
            if (!path) {
                resultsDiv.innerHTML = '<p class="text-red-600">❌ Please enter a path to test</p>';
                return;
            }
            
            resultsDiv.innerHTML = '<p class="text-blue-600">🔍 Testing manual path...</p>';
            
            const img = new Image();
            img.onload = function() {
                resultsDiv.innerHTML = `
                    <div class="p-4 bg-green-50 border-green-200 border rounded-lg">
                        <h3 class="font-bold text-green-800">✅ Success!</h3>
                        <p class="text-green-700">Path: ${path}</p>
                        <p class="text-green-700">Size: ${this.naturalWidth}x${this.naturalHeight}</p>
                        <div class="mt-3">
                            <img src="${path}" alt="Test Image" class="w-32 h-32 object-cover rounded border">
                        </div>
                    </div>
                `;
            };
            img.onerror = function() {
                resultsDiv.innerHTML = `
                    <div class="p-4 bg-red-50 border-red-200 border rounded-lg">
                        <h3 class="font-bold text-red-800">❌ Failed!</h3>
                        <p class="text-red-700">Path: ${path}</p>
                        <p class="text-red-700">Error: Cannot load image</p>
                        <div class="mt-3 text-sm text-gray-600">
                            <p>Possible issues:</p>
                            <ul class="list-disc list-inside">
                                <li>File does not exist</li>
                                <li>Incorrect path</li>
                                <li>Permission issues</li>
                                <li>File format not supported</li>
                            </ul>
                        </div>
                    </div>
                `;
            };
            img.src = path;
        }

        // Initialize real file test
        document.addEventListener('DOMContentLoaded', function() {
            const realFileDiv = document.getElementById('realFileTest');
            
            // Test beberapa file yang pasti ada
            const sampleFiles = [
                { shape: 'oval', file: 'Textured Crop.jpg' },
                { shape: 'oval', file: 'Pompadour.jpg' },
                { shape: 'round', file: 'Two Block Hair.jpg' },
                { shape: 'square', file: 'Slick Back.jpg' },
                { shape: 'heart', file: 'Classic Quiff.jpg' },
                { shape: 'rectangular', file: 'Textured Crop.jpg' }
            ];
            
            sampleFiles.forEach(sample => {
                const path = `rekomendasi/${sample.shape}/${sample.file}`;
                const testCard = document.createElement('div');
                testCard.className = 'border rounded-lg p-3 text-center';
                testCard.innerHTML = `
                    <div class="w-16 h-16 mx-auto mb-2 rounded overflow-hidden border">
                        <img src="${path}" alt="${sample.file}" class="w-full h-full object-cover"
                             onload="this.parentElement.parentElement.classList.add('border-green-500', 'bg-green-50')"
                             onerror="this.parentElement.innerHTML='❌'; this.parentElement.parentElement.classList.add('border-red-500', 'bg-red-50')">
                    </div>
                    <p class="text-xs font-medium">${sample.shape}</p>
                    <p class="text-xs text-gray-600">${sample.file.substring(0, 15)}...</p>
                `;
                realFileDiv.appendChild(testCard);
            });
        });
    </script>
</body>
</html>
