import cv2
import numpy as np
import json
import sys
import os
import base64

class DirectImageOverlay:
    def __init__(self):
        pass
    
    def overlay_images_directly(self, user_image_path, overlay_image_path):
        """Directly overlay 3D result image onto user image"""
        try:
            # Load user image
            user_img = cv2.imread(user_image_path)
            if user_img is None:
                return {'success': False, 'error': 'Failed to load user image'}
            
            # Load overlay image from 3d folder
            overlay_img = cv2.imread(overlay_image_path, cv2.IMREAD_UNCHANGED)
            if overlay_img is None:
                return {'success': False, 'error': 'Failed to load overlay image'}
            
            # Get dimensions
            user_h, user_w = user_img.shape[:2]
            
            # Resize overlay to match user image
            overlay_resized = cv2.resize(overlay_img, (user_w, user_h))
            
            # Direct replacement - use overlay image as result
            result = overlay_resized.copy()
            
            # If overlay has alpha channel, blend with user image
            if len(overlay_resized.shape) == 3 and overlay_resized.shape[2] == 4:
                # Extract RGB and alpha
                overlay_rgb = overlay_resized[:, :, :3]
                alpha = overlay_resized[:, :, 3] / 255.0
                
                # Alpha blending
                result = user_img.copy().astype(np.float32)
                for c in range(3):
                    result[:, :, c] = (alpha * overlay_rgb[:, :, c] + 
                                     (1 - alpha) * result[:, :, c])
                
                result = np.clip(result, 0, 255).astype(np.uint8)
            elif len(overlay_resized.shape) == 3 and overlay_resized.shape[2] == 3:
                # No alpha channel, use direct overlay
                result = overlay_resized
            
            # Create output directory
            output_dir = 'uploads/3d_results/'
            os.makedirs(output_dir, exist_ok=True)
            
            # Save result
            output_path = os.path.join(output_dir, f'direct_overlay_{hash(user_image_path + overlay_image_path)}.png')
            cv2.imwrite(output_path, result)
            
            # Convert to base64
            _, buffer = cv2.imencode('.png', result)
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return {
                'success': True,
                'result_url': output_path,
                'result_base64': f"data:image/png;base64,{img_base64}",
                'method': 'direct_overlay'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

def main():
    if len(sys.argv) < 3:
        print(json.dumps({'success': False, 'error': 'Missing parameters'}))
        return
    
    user_image = sys.argv[1]
    overlay_image = sys.argv[2]
    
    processor = DirectImageOverlay()
    result = processor.overlay_images_directly(user_image, overlay_image)
    
    print(json.dumps(result))

if __name__ == "__main__":
    main()