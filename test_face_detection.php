<?php
session_start();
require_once 'Admin/db_config.php';

echo "<h1>🔍 Test Sistem Deteksi Wajah Viola-Jones</h1>";

// Fungsi untuk logging
function logTest($message) {
    $logFile = 'test_face_detection.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND);
    echo "<p><strong>[$timestamp]</strong> $message</p>";
}

// Test 1: Cek Python Installation
echo "<h2>1. 🐍 Test Python Installation</h2>";
define('PYTHON_PATH', '"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python.exe"');

if (!file_exists(trim(PYTHON_PATH, '"'))) {
    logTest("❌ Python tidak ditemukan di: " . PYTHON_PATH);
} else {
    logTest("✅ Python ditemukan di: " . PYTHON_PATH);
    
    // Test Python version
    $pythonVersion = '';
    exec(PYTHON_PATH . ' --version 2>&1', $pythonVersion, $returnVar);
    $pythonVersion = implode("\n", $pythonVersion);
    logTest("Python Version: " . $pythonVersion);
}

// Test 2: Cek Library Dependencies
echo "<h2>2. 📚 Test Library Dependencies</h2>";

$libraries = ['cv2', 'dlib', 'numpy'];
foreach ($libraries as $lib) {
    $output = '';
    exec(PYTHON_PATH . ' -c "import ' . $lib . '; print(\'' . $lib . ' OK\')" 2>&1', $output, $returnVar);
    $output = implode("\n", $output);
    
    if ($returnVar === 0) {
        logTest("✅ Library $lib: OK");
    } else {
        logTest("❌ Library $lib: ERROR - " . $output);
    }
}

// Test 3: Cek Model File
echo "<h2>3. 🤖 Test Model File</h2>";
$modelPath = 'models/shape_predictor_68_face_landmarks.dat';
if (file_exists($modelPath)) {
    $fileSize = filesize($modelPath);
    logTest("✅ Model file ditemukan: " . $modelPath . " (Size: " . number_format($fileSize) . " bytes)");
} else {
    logTest("❌ Model file tidak ditemukan: " . $modelPath);
    echo "<p><strong>Download model dari:</strong> <a href='http://dlib.net/files/shape_predictor_68_face_landmarks.dat.bz2'>dlib.net</a></p>";
}

// Test 4: Test Face Analysis Script
echo "<h2>4. 🔬 Test Face Analysis Script</h2>";
$scriptPath = __DIR__ . '/face_analysis.py';
if (file_exists($scriptPath)) {
    logTest("✅ Face analysis script ditemukan: " . $scriptPath);
    
    // Test syntax
    $output = '';
    exec(PYTHON_PATH . ' -m py_compile ' . escapeshellarg($scriptPath) . ' 2>&1', $output, $returnVar);
    if ($returnVar === 0) {
        logTest("✅ Script syntax: OK");
    } else {
        logTest("❌ Script syntax error: " . implode("\n", $output));
    }
} else {
    logTest("❌ Face analysis script tidak ditemukan: " . $scriptPath);
}

// Test 5: Test Face Overlay Script
echo "<h2>5. 🎨 Test Face Overlay Script</h2>";
$overlayPath = __DIR__ . '/face_overlay.py';
if (file_exists($overlayPath)) {
    logTest("✅ Face overlay script ditemukan: " . $overlayPath);
    
    // Test syntax
    $output = '';
    exec(PYTHON_PATH . ' -m py_compile ' . escapeshellarg($overlayPath) . ' 2>&1', $output, $returnVar);
    if ($returnVar === 0) {
        logTest("✅ Overlay script syntax: OK");
    } else {
        logTest("❌ Overlay script syntax error: " . implode("\n", $output));
    }
} else {
    logTest("❌ Face overlay script tidak ditemukan: " . $overlayPath);
}

// Test 6: Test Upload Directory
echo "<h2>6. 📁 Test Upload Directory</h2>";
$uploadDir = 'uploads/face_detection/';
if (!file_exists($uploadDir)) {
    if (mkdir($uploadDir, 0777, true)) {
        logTest("✅ Upload directory dibuat: " . $uploadDir);
    } else {
        logTest("❌ Gagal membuat upload directory: " . $uploadDir);
    }
} else {
    logTest("✅ Upload directory sudah ada: " . $uploadDir);
}

// Test 7: Test Database Connection
echo "<h2>7. 🗄️ Test Database Connection</h2>";
try {
    if (isset($conn) && $conn instanceof PDO) {
        logTest("✅ Database connection: OK");
        
        // Test face_analysis table
        $stmt = $conn->query("SHOW TABLES LIKE 'face_analysis'");
        if ($stmt->rowCount() > 0) {
            logTest("✅ Table face_analysis: EXISTS");
            
            // Show table structure
            $stmt = $conn->query("DESCRIBE face_analysis");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            logTest("Table structure: " . count($columns) . " columns");
            foreach ($columns as $column) {
                logTest("  - " . $column['Field'] . " (" . $column['Type'] . ")");
            }
        } else {
            logTest("❌ Table face_analysis: NOT EXISTS");
            
            // Create table
            $createTable = "
                CREATE TABLE face_analysis (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    image_path VARCHAR(255) NOT NULL,
                    face_shape VARCHAR(50) NOT NULL,
                    description TEXT,
                    recommendation TEXT,
                    confidence INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ";
            
            if ($conn->exec($createTable)) {
                logTest("✅ Table face_analysis created successfully");
            } else {
                logTest("❌ Failed to create table face_analysis");
            }
        }
    } else {
        logTest("❌ Database connection: FAILED");
    }
} catch (Exception $e) {
    logTest("❌ Database error: " . $e->getMessage());
}

// Test 8: Sample Face Detection Test
echo "<h2>8. 🧪 Sample Face Detection Test</h2>";

if (isset($_POST['test_detection'])) {
    if (isset($_FILES['test_image']) && $_FILES['test_image']['error'] === UPLOAD_ERR_OK) {
        $testImagePath = $uploadDir . 'test_' . uniqid() . '.jpg';
        
        if (move_uploaded_file($_FILES['test_image']['tmp_name'], $testImagePath)) {
            logTest("✅ Test image uploaded: " . $testImagePath);
            
            // Run face analysis
            $command = PYTHON_PATH . ' ' . escapeshellarg($scriptPath) . ' ' . escapeshellarg($testImagePath) . ' 2>&1';
            logTest("Running command: " . $command);
            
            $output = '';
            exec($command, $output, $returnVar);
            $output = implode("\n", $output);
            
            logTest("Python output: " . $output);
            
            $result = json_decode($output, true);
            if ($result && !isset($result['error'])) {
                logTest("✅ Face detection SUCCESS!");
                logTest("Detected shape: " . $result['shape']);
                logTest("Confidence: " . $result['confidence'] . "%");
                
                if (isset($result['alt_shape'])) {
                    logTest("Alternative shape: " . $result['alt_shape'] . " (" . $result['alt_confidence'] . "%)");
                }
                
                // Test overlay generation
                if (file_exists($overlayPath)) {
                    $overlayCommand = PYTHON_PATH . ' ' . escapeshellarg($overlayPath) . ' ' . 
                                    escapeshellarg($testImagePath) . ' ' . 
                                    escapeshellarg($result['shape']) . ' ' . 
                                    escapeshellarg($result['confidence']) . ' 2>&1';
                    
                    $overlayOutput = '';
                    exec($overlayCommand, $overlayOutput, $overlayReturnVar);
                    $overlayOutput = implode("\n", $overlayOutput);
                    
                    $overlayResult = json_decode($overlayOutput, true);
                    if ($overlayResult && isset($overlayResult['success'])) {
                        logTest("✅ Overlay generation SUCCESS!");
                        logTest("Overlay image: " . $overlayResult['overlay_path']);
                        
                        // Display images
                        echo "<div style='display: flex; gap: 20px; margin: 20px 0;'>";
                        echo "<div>";
                        echo "<h4>Original Image:</h4>";
                        echo "<img src='$testImagePath' style='max-width: 300px; border: 2px solid #ccc;'>";
                        echo "</div>";
                        echo "<div>";
                        echo "<h4>With Face Shape Overlay:</h4>";
                        echo "<img src='" . $overlayResult['overlay_path'] . "' style='max-width: 300px; border: 2px solid #0f0;'>";
                        echo "</div>";
                        echo "</div>";
                    } else {
                        logTest("❌ Overlay generation FAILED: " . $overlayOutput);
                    }
                }
                
            } else {
                logTest("❌ Face detection FAILED: " . ($result['error'] ?? 'Unknown error'));
            }
        } else {
            logTest("❌ Failed to upload test image");
        }
    } else {
        logTest("❌ No test image uploaded or upload error");
    }
}

// Upload form for testing
echo "<h3>Upload Test Image:</h3>";
echo "<form method='POST' enctype='multipart/form-data'>";
echo "<input type='file' name='test_image' accept='image/*' required>";
echo "<button type='submit' name='test_detection'>Test Face Detection</button>";
echo "</form>";

// Summary
echo "<h2>📊 Test Summary</h2>";
echo "<p><strong>Sistem deteksi wajah menggunakan:</strong></p>";
echo "<ul>";
echo "<li>🔍 <strong>Algoritma Viola-Jones</strong> untuk deteksi wajah</li>";
echo "<li>📍 <strong>68-point landmark detection</strong> untuk analisis fitur</li>";
echo "<li>📐 <strong>Antropometric ratios</strong> untuk klasifikasi bentuk wajah</li>";
echo "<li>🎯 <strong>5 bentuk wajah</strong>: Oval, Round, Square, Heart, Rectangular</li>";
echo "<li>🖼️ <strong>Green bounding box</strong> sesuai bentuk wajah yang terdeteksi</li>";
echo "</ul>";

echo "<p><a href='face.php'>← Kembali ke Face Detection</a></p>";
?>
