<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium Treatments - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
     <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
        }
        .treatment-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .treatment-card:hover {
            transform: translateY(-5px);
        }
        .active-category {
            background-color: #0A5144;
            color: white;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
        }
        .duration-badge {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(5px);
        }
            .navbar {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      background: #fff;
      box-shadow: 0 -2px 12px rgba(0,0,0,0.08);
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 10px 0 6px 0;
      z-index: 50;
      border-top-left-radius: 18px;
      border-top-right-radius: 18px;
    }
    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 13px;
      color: #222;
      min-width: 56px;
      transition: color 0.2s;
      cursor: pointer;
      padding: 2px 0;
    }
.nav-item .material-icons {
  font-size: 25px;
  margin-bottom: 2px;
  transition: color 0.2s;
  color: #222;
}
    .nav-item.active,
    .nav-item:active,
    .nav-item:focus {
      color: #222;
    }
    .nav-item.active .material-icons,
    .nav-item:active .material-icons,
    .nav-item:focus .material-icons {
      color: #222;
    }
    .nav-item:hover,
    .nav-item:focus-visible {
      color: #009688;
    }
    .nav-item:hover .material-icons,
    .nav-item:focus-visible .material-icons {
      color: #009688;
    }
    .nav-item span:last-child {
      font-weight: 500;
      letter-spacing: 0.01em;
    }
    </style>
</head>
<body class="pb-24">
    <!-- Header -->
    <header class="sticky top-0 z-10 gradient-bg text-white p-4 shadow-md">
<div class="flex items-center justify-center">
    <h1 class="text-xl font-bold text-center">Premium Treatments</h1>
</div>
    </header>

    <!-- Hero Section -->
    <div class="relative h-48 overflow-hidden">
        <img src="./assets/pixel.jpg" 
             alt="Barber performing treatment" 
             class="w-full h-full object-cover">
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end p-4">
            <div>
                <h2 class="text-2xl font-bold text-white">Revitalize Your Look</h2>
                <p class="text-white/90 text-sm">Professional treatments for hair and skin</p>
            </div>
        </div>
    </div>


    <!-- Main Content -->
    <main class="px-4 py-4">
        <!-- Popular Treatments -->
        <section class="mb-8">
            <h3 class="text-lg font-bold text-gray-900 mb-3 flex items-center">
                <i class="fas fa-fire text-orange-500 mr-2"></i>
                Most Popular
            </h3>
            <div class="grid grid-cols-1 gap-4">
                <!-- Treatment 1 -->
                <div class="treatment-card bg-white rounded-xl overflow-hidden shadow-md relative">
                    <div class="flex">
                        <div class="w-1/3">
                            <img src="./assets/scalp detox.jpg" 
                                 alt="Hot Towel Treatment" 
                                 class="w-full h-full object-cover">
                        </div>
                        <div class="w-2/3 p-4">
                            <div class="flex justify-between items-start">
                                <h4 class="font-bold text-gray-900">Hairspa</h4>
                            </div>
                            <p class="text-sm text-gray-600 mt-1 line-clamp-2">
                                perawatan kulit kepala dan rambut dengan masker
                            </p>
                            <div class="flex items-center justify-between mt-3">
                                <span class="text-[#0A5144] font-bold">Rp 120.000</span>
                            </div>
                        </div>
                    </div>
                    <div class="absolute top-3 right-3 flex">
                        <span class="bg-white/90 rounded-full w-7 h-7 flex items-center justify-center shadow mr-1">
                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                        </span>
                        <span class="bg-white/90 rounded-full w-7 h-7 flex items-center justify-center shadow">
                            <i class="far fa-heart text-gray-600"></i>
                        </span>
                    </div>
                </div>

                <!-- Treatment 2 -->
                                <div class="treatment-card bg-white rounded-xl overflow-hidden shadow-md relative">
                    <div class="flex">
                        <div class="w-1/3">
                            <img src="./assets/coloring.jpg" 
                                 alt="Beard Spa" 
                                 class="w-full h-full object-cover">
                        </div>
                        <div class="w-2/3 p-4">
                            <div class="flex justify-between items-start">
                                <h4 class="font-bold text-gray-900">Coloring</h4>
                            </div>
                            <p class="text-sm text-gray-600 mt-1 line-clamp-2">
                                Memberi efek gradasi terang (highlight) atau gelap (lowlight) pada bagian tertentu rambut
                            </p>
                            <div class="flex items-center justify-between mt-3">
                                <span class="text-[#0A5144] font-bold">Rp 45.000</span>
                            </div>
                        </div>
                    </div>
                    <div class="absolute top-3 right-3 flex">
                        <span class="bg-white/90 rounded-full w-7 h-7 flex items-center justify-center shadow mr-1">
                            <i class="fas fa-star text-yellow-400 text-xs"></i>
                        </span>
                        <span class="bg-white/90 rounded-full w-7 h-7 flex items-center justify-center shadow">
                            <i class="far fa-heart text-gray-600"></i>
                        </span>
                    </div>
                </div>
                                <div class="treatment-card bg-white rounded-xl overflow-hidden shadow-md relative">
                    <div class="flex">
                        <div class="w-1/3">
                            <img src="./assets/Side Part Classic.jpg" 
                                 alt="Beard Spa" 
                                 class="w-full h-full object-cover">
                        </div>
                        <div class="w-2/3 p-4">
                            <div class="flex justify-between items-start">
                                <h4 class="font-bold text-gray-900">sir cut fres tige</h4>
                            </div>
                            <p class="text-sm text-gray-600 mt-1 line-clamp-2">
                                Tampil elegan dan berkelas dengan gaya potongan klasik 'Sir Cut' — pilihan sempurna untuk Anda
                            </p>
                            <div class="flex items-center justify-between mt-3">
                                <span class="text-[#0A5144] font-bold">Rp 50.000</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- All Treatments -->
        <section>
            <h3 class="text-lg font-bold text-gray-900 mb-3">All Treatments</h3>
            <div class="grid grid-cols-1 gap-4">
                <!-- Treatment 3 -->
                <div class="treatment-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="./assets/scalp detox.jpg" 
                         alt="Scalp Treatment" 
                         class="w-full h-32 object-cover">
                    <div class="p-4">
                        <div class="flex justify-between items-start">
                            <h4 class="font-bold text-gray-900">Scalp Detox Treatment</h4>
                            <span class="duration-badge text-xs text-white px-2 py-1 rounded-full bg-[#0A5144]">40 min</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1 line-clamp-2">
                            Deep cleansing treatment to remove product buildup and promote healthy scalp.
                        </p>
                        <div class="flex items-center justify-between mt-3">
                            <span class="text-[#0A5144] font-bold">Rp 85.000</span>
                            <div class="flex items-center text-xs text-gray-500">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                4.9 (128)
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Treatment 4 -->
                <div class="treatment-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="./assets/Men's Facial Refresh.jpg" 
                         alt="Facial Treatment" 
                         class="w-full h-32 object-cover">
                    <div class="p-4">
                        <div class="flex justify-between items-start">
                            <h4 class="font-bold text-gray-900">Men's Facial Refresh</h4>
                            <span class="duration-badge text-xs text-white px-2 py-1 rounded-full bg-[#0A5144]">50 min</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1 line-clamp-2">
                            Complete facial including cleansing, exfoliation, and hydration for tired skin.
                        </p>
                        <div class="flex items-center justify-between mt-3">
                            <span class="text-[#0A5144] font-bold">Rp 150.000</span>
                            <div class="flex items-center text-xs text-gray-500">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                4.8 (95)
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Treatment 5 -->
                <div class="treatment-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="./assets/Keratin Smoothing.jpg" 
                         alt="Hair Treatment" 
                         class="w-full h-32 object-cover">
                    <div class="p-4">
                        <div class="flex justify-between items-start">
                            <h4 class="font-bold text-gray-900">Keratin Smoothing</h4>
                            <span class="duration-badge text-xs text-white px-2 py-1 rounded-full bg-[#0A5144]">60 min</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1 line-clamp-2">
                            Professional keratin treatment to reduce frizz and add shine to your hair.
                        </p>
                        <div class="flex items-center justify-between mt-3">
                            <span class="text-[#0A5144] font-bold">Rp 250.000</span>
                            <div class="flex items-center text-xs text-gray-500">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                4.7 (76)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Bottom Navigation -->
<nav class="fixed bottom-0 left-0 right-0 bg-white shadow-lg flex justify-around py-3 z-50 border-t border-gray-100">
  <a href="index.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-home text-lg mb-1"></i>
    <span>Home</span>
  </a>
  <a href="treatment.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'treatment.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-cut text-lg mb-1"></i>
    <span>Services</span>
  </a>
  <a href="produk.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'produk.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-box-open text-lg mb-1"></i>
    <span>Produk</span>
  </a>
  <a href="location.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'location.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-calendar-alt text-lg mb-1"></i>
    <span>Book</span>
  </a>
</nav>

    <script>
        // Category filter functionality
        const categoryButtons = document.querySelectorAll('.category-btn');
        categoryButtons.forEach(button => {
            button.addEventListener('click', () => {
                categoryButtons.forEach(btn => btn.classList.remove('active-category'));
                categoryButtons.forEach(btn => btn.classList.add('bg-gray-100'));
                button.classList.add('active-category');
                button.classList.remove('bg-gray-100');
            });
        });

        // Like button functionality
        const likeButtons = document.querySelectorAll('.fa-heart');
        likeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                if (button.classList.contains('far')) {
                    button.classList.remove('far');
                    button.classList.add('fas', 'text-red-500');
                } else {
                    button.classList.add('far');
                    button.classList.remove('fas', 'text-red-500');
                }
            });
        });

        // Treatment card click (for viewing details)
        const treatmentCards = document.querySelectorAll('.treatment-card');
        treatmentCards.forEach(card => {
            card.addEventListener('click', () => {
                // In a real app, this would navigate to a detail view
                alert('Showing details for this treatment');
            });
        });
    </script>
</body>
</html>