<?php
/**
 * WhatsApp Notification System
 * Sends automated WhatsApp messages to applicants based on their application status
 */

/**
 * Get database connection
 */
function getDbConnection() {
    global $conn;

    if (!isset($conn) || $conn === null) {
        require_once __DIR__ . '/db_config.php';
    }

    return $conn;
}

class WhatsAppNotification {
    private $config;
    private $api_url;
    private $api_key;
    private $headers;

    public function __construct() {
        // Load configuration
        $this->config = include __DIR__ . '/whatsapp_config.php';

        // Set API settings based on provider
        $provider = $this->config['api_provider'];
        $provider_config = $this->config[$provider];

        $this->api_url = $provider_config['api_url'];
        $this->api_key = $provider_config['api_key'];

        // Set headers based on provider
        switch ($provider) {
            case 'fonnte':
                $this->headers = [
                    'Authorization: ' . $this->api_key,
                    'Content-Type: application/json'
                ];
                break;
            case 'wablas':
                $this->headers = [
                    'Authorization: ' . $this->api_key,
                    'Content-Type: application/json'
                ];
                break;
            case 'ultramsg':
                $this->headers = [
                    'Content-Type: application/x-www-form-urlencoded'
                ];
                break;
            default:
                $this->headers = $provider_config['headers'] ?? [
                    'Content-Type: application/json'
                ];
        }
    }
    
    /**
     * Send WhatsApp message
     */
    public function sendMessage($phone, $message) {
        // Clean phone number (remove non-numeric characters except +)
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        // Ensure phone number starts with country code
        $country_code = $this->config['default_country_code'];
        if (substr($phone, 0, 1) !== '+') {
            // Assume default country if no country code
            if (substr($phone, 0, 1) === '0') {
                $phone = '+' . $country_code . substr($phone, 1);
            } else {
                $phone = '+' . $country_code . $phone;
            }
        }

        // Prepare data based on provider
        $provider = $this->config['api_provider'];
        switch ($provider) {
            case 'fonnte':
                $data = [
                    'target' => $phone,
                    'message' => $message,
                    'countryCode' => $country_code
                ];
                break;
            case 'wablas':
                $data = [
                    'phone' => $phone,
                    'message' => $message
                ];
                break;
            case 'ultramsg':
                $data = [
                    'to' => $phone,
                    'body' => $message,
                    'token' => $this->api_key
                ];
                break;
            default:
                $data = [
                    'phone' => $phone,
                    'message' => $message
                ];
        }
        
        $curl = curl_init();

        // Prepare post fields based on provider
        $provider = $this->config['api_provider'];
        $postfields = ($provider === 'ultramsg') ? http_build_query($data) : json_encode($data);

        curl_setopt_array($curl, [
            CURLOPT_URL => $this->api_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => $this->config['timeout'] ?? 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $postfields,
            CURLOPT_HTTPHEADER => $this->headers,
        ]);
        
        $response = curl_exec($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        
        return [
            'success' => $http_code === 200,
            'response' => json_decode($response, true),
            'http_code' => $http_code
        ];
    }
    
    /**
     * Generate message template based on application status
     */
    public function generateMessage($applicant_name, $position, $status, $evaluation_score = null, $quiz_score = null) {
        $messages = [
            'accepted' => "🎉 *Selamat {$applicant_name}!*\n\n" .
                         "Lamaran Anda untuk posisi *{$position}* telah *DITERIMA*! ✅\n\n" .
                         ($evaluation_score ? "📊 Skor Evaluasi: {$evaluation_score}/100\n" : "") .
                         ($quiz_score ? "🧠 Skor Quiz: {$quiz_score}%\n\n" : "") .
                         "Kami akan segera menghubungi Anda untuk tahap selanjutnya.\n\n" .
                         "Terima kasih telah melamar di Pixel Barbershop! 💈\n\n" .
                         "_Tim HR Pixel Barbershop_",
            
            'reviewed' => "📋 *Halo {$applicant_name}*\n\n" .
                         "Terima kasih telah melamar untuk posisi *{$position}*.\n\n" .
                         "Lamaran Anda sedang dalam tahap *REVIEW* oleh tim kami. ⏳\n\n" .
                         ($evaluation_score ? "📊 Skor Evaluasi: {$evaluation_score}/100\n" : "") .
                         ($quiz_score ? "🧠 Skor Quiz: {$quiz_score}%\n\n" : "") .
                         "Kami akan menginformasikan hasil seleksi dalam 3-5 hari kerja.\n\n" .
                         "Terima kasih atas kesabaran Anda! 🙏\n\n" .
                         "_Tim HR Pixel Barbershop_",
            
            'interview' => "📞 *Selamat {$applicant_name}!*\n\n" .
                          "Anda telah lolos ke tahap *INTERVIEW* untuk posisi *{$position}*! 🎯\n\n" .
                          ($evaluation_score ? "📊 Skor Evaluasi: {$evaluation_score}/100\n" : "") .
                          ($quiz_score ? "🧠 Skor Quiz: {$quiz_score}%\n\n" : "") .
                          "Tim kami akan menghubungi Anda untuk mengatur jadwal interview.\n\n" .
                          "Persiapkan diri Anda dengan baik! 💪\n\n" .
                          "_Tim HR Pixel Barbershop_",
            
            'rejected' => "📝 *Halo {$applicant_name}*\n\n" .
                         "Terima kasih telah melamar untuk posisi *{$position}* di Pixel Barbershop.\n\n" .
                         "Setelah melalui proses seleksi, saat ini kami belum dapat melanjutkan aplikasi Anda. 📋\n\n" .
                         ($evaluation_score ? "📊 Skor Evaluasi: {$evaluation_score}/100\n" : "") .
                         ($quiz_score ? "🧠 Skor Quiz: {$quiz_score}%\n\n" : "") .
                         "Jangan berkecil hati! Kami mendorong Anda untuk terus mengembangkan skill dan melamar kembali di kesempatan yang akan datang. 🚀\n\n" .
                         "Terima kasih atas minat Anda bergabung dengan kami! 🙏\n\n" .
                         "_Tim HR Pixel Barbershop_",
            
            'pending' => "📨 *Halo {$applicant_name}*\n\n" .
                        "Terima kasih telah melamar untuk posisi *{$position}* di Pixel Barbershop! 🎉\n\n" .
                        "Lamaran Anda telah kami terima dan sedang dalam proses evaluasi. ⏳\n\n" .
                        ($evaluation_score ? "📊 Skor Evaluasi: {$evaluation_score}/100\n" : "") .
                        ($quiz_score ? "🧠 Skor Quiz: {$quiz_score}%\n\n" : "") .
                        "Kami akan menginformasikan perkembangan selanjutnya melalui WhatsApp ini.\n\n" .
                        "Terima kasih atas kesabaran Anda! 🙏\n\n" .
                        "_Tim HR Pixel Barbershop_"
        ];
        
        return $messages[$status] ?? $messages['pending'];
    }
    
    /**
     * Send notification to applicant
     */
    public function notifyApplicant($applicant_data) {
        $message = $this->generateMessage(
            $applicant_data['full_name'],
            $applicant_data['position'],
            $applicant_data['status'],
            $applicant_data['evaluation_score'] ?? null,
            $applicant_data['quiz_score'] ?? null
        );
        
        return $this->sendMessage($applicant_data['phone'], $message);
    }
    
    /**
     * Send bulk notifications
     */
    public function sendBulkNotifications($applicants) {
        $results = [];
        
        foreach ($applicants as $applicant) {
            $result = $this->notifyApplicant($applicant);
            $results[] = [
                'applicant_id' => $applicant['id'],
                'name' => $applicant['full_name'],
                'phone' => $applicant['phone'],
                'status' => $applicant['status'],
                'sent' => $result['success'],
                'response' => $result['response']
            ];
            
            // Add delay between messages to avoid rate limiting
            sleep($this->config['message_delay'] ?? 1);
        }
        
        return $results;
    }
    
    /**
     * Test WhatsApp connection
     */
    public function testConnection($test_phone = null) {
        $test_phone = $test_phone ?: '+6281234567890'; // Default test number
        $test_message = "🧪 *Test Message*\n\nIni adalah pesan test dari sistem notifikasi WhatsApp Pixel Barbershop.\n\nJika Anda menerima pesan ini, berarti sistem berfungsi dengan baik! ✅\n\n_Tim IT Pixel Barbershop_";
        
        return $this->sendMessage($test_phone, $test_message);
    }
}

/**
 * Function to send notification when application status changes
 */
function sendWhatsAppNotification($application_id, $new_status = null) {
    $conn = getDbConnection();

    try {
        // Get application details
        $stmt = $conn->prepare("
            SELECT a.*, j.position
            FROM applications a
            JOIN jobs j ON a.job_id = j.id
            WHERE a.id = ?
        ");
        $stmt->execute([$application_id]);
        $applicant = $stmt->fetch();
        
        if (!$applicant) {
            return ['success' => false, 'message' => 'Application not found'];
        }
        
        // Use provided status or current status
        $applicant['status'] = $new_status ?: $applicant['status'];
        
        // Initialize WhatsApp notification
        $whatsapp = new WhatsAppNotification();
        
        // Send notification
        $result = $whatsapp->notifyApplicant($applicant);
        
        // Log notification attempt
        $log_stmt = $conn->prepare("
            INSERT INTO notification_logs (application_id, type, recipient, message_sent, success, response, sent_at) 
            VALUES (?, 'whatsapp', ?, ?, ?, ?, NOW())
        ");
        
        $message = $whatsapp->generateMessage(
            $applicant['full_name'],
            $applicant['position'],
            $applicant['status'],
            $applicant['evaluation_score'] ?? null,
            $applicant['quiz_score'] ?? null
        );
        
        $log_stmt->execute([
            $application_id,
            $applicant['phone'],
            $message,
            $result['success'] ? 1 : 0,
            json_encode($result['response'])
        ]);
        
        return $result;
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Create notification logs table if it doesn't exist
 */
function createNotificationLogsTable() {
    $conn = getDbConnection();

    try {
        $sql = "CREATE TABLE IF NOT EXISTS notification_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            application_id INT NOT NULL,
            type ENUM('whatsapp', 'email', 'sms') NOT NULL,
            recipient VARCHAR(255) NOT NULL,
            message_sent TEXT NOT NULL,
            success BOOLEAN NOT NULL DEFAULT FALSE,
            response JSON NULL,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_application_id (application_id),
            INDEX idx_sent_at (sent_at),
            FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        $conn->exec($sql);
        return true;
    } catch (PDOException $e) {
        error_log("Failed to create notification_logs table: " . $e->getMessage());
        return false;
    } catch (Exception $e) {
        error_log("Error in createNotificationLogsTable: " . $e->getMessage());
        return false;
    }
}

// Note: Call createNotificationLogsTable() manually from admin panel if needed
?>
