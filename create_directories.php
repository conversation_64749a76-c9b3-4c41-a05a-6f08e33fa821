<?php
// <PERSON>ript to create necessary directories for file uploads

$directories = [
    'uploads/resumes',
    'assets'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "Directory '$dir' created successfully.<br>";
        } else {
            echo "Failed to create directory '$dir'.<br>";
        }
    } else {
        echo "Directory '$dir' already exists.<br>";
    }
}

echo "<br>Directory setup completed!";
?>
