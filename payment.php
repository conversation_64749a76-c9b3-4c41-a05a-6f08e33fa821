<?php
require_once 'Admin/db_config.php';

// Retrieve order data from GET parameters
$product_id = isset($_GET['product_id']) ? $_GET['product_id'] : null;
$variant_id = isset($_GET['variant_id']) ? $_GET['variant_id'] : null;
$quantity = isset($_GET['quantity']) ? intval($_GET['quantity']) : 1;
$delivery_cost = isset($_GET['delivery_cost']) ? intval($_GET['delivery_cost']) : 0;
$service_fee = isset($_GET['service_fee']) ? intval($_GET['service_fee']) : 0;
$total_amount = isset($_GET['total_amount']) ? intval($_GET['total_amount']) : 0;
$address_id = isset($_GET['address_id']) ? intval($_GET['address_id']) : null;

$product_data = null;
$variant_data = null;
$address_data = null;

if ($product_id && $variant_id) {
    try {
        // Fetch product details
        $stmt_product = $conn->prepare("SELECT nama_produk, harga, gambar_utama FROM produk WHERE id = :product_id");
        $stmt_product->execute(['product_id' => $product_id]);
        $product_data = $stmt_product->fetch(PDO::FETCH_ASSOC);

        // Fetch variant details
        $stmt_variant = $conn->prepare("SELECT ukuran_ml, harga, gambar_varian FROM variasi_produk WHERE id = :variant_id AND produk_id = :product_id");
        $stmt_variant->execute(['variant_id' => $variant_id, 'product_id' => $product_id]);
        $variant_data = $stmt_variant->fetch(PDO::FETCH_ASSOC);

    } catch (PDOException $e) {
        error_log("Database Error in payment.php (product/variant fetch): " . $e->getMessage());
    }
}

if ($address_id) {
    try {
        // Fetch address details
        $stmt_address = $conn->prepare("SELECT address_name, full_address, phone_number FROM user_addresses WHERE id = :address_id");
        $stmt_address->execute(['address_id' => $address_id]);
        $address_data = $stmt_address->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Database Error in payment.php (address fetch): " . $e->getMessage());
    }
}

// Fetch payment methods
$payment_methods = [];
try {
    $stmt_payment_methods = $conn->prepare("SELECT id, name, type, qr_code_image, payment_url FROM payment_methods ORDER BY name ASC");
    $stmt_payment_methods->execute();
    $payment_methods = $stmt_payment_methods->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Database Error in payment.php (payment methods fetch): " . $e->getMessage());
}

$upload_dir_display = '/aplikasi-pixel/uploads/'; // Path absolut dari root ke folder uploads
$variant_upload_dir_display = '/aplikasi-pixel/uploads/variants/'; // Path untuk gambar varian

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Midtrans Snap JS Library -->
    <script type="text/javascript"
      src="https://app.sandbox.midtrans.com/snap/snap.js"
      data-client-key="SB-Mid-client-YOUR_CLIENT_KEY"></script> <!-- GANTI DENGAN CLIENT KEY ANDA -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
        }
        .payment-method.selected {
            border-color: #0A5144;
            background-color: #F0F9F8;
        }

        /* Styling for active checkout step (Payment) */
        .checkout-step.active .step-number {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
            border-color: #0A5144;
            color: #fff;
        }
        .checkout-step.active span {
            color: #0A5144;
        }

        /* Styling for completed checkout step (Delivery) */
        .checkout-step.completed .step-number {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
            border-color: #0A5144;
            color: #fff;
        }
        .checkout-step.completed span {
            color: #0A5144;
        }
    </style>
</head>
<body class="pb-32">
    <!-- Header -->
    <header class="sticky top-0 z-10 bg-white p-4 shadow-sm">
        <div class="flex items-center justify-between">
            <a href="javascript:history.back()" class="text-gray-600">
                <i class="fas fa-arrow-left text-lg"></i>
            </a>
            <h1 class="text-xl font-bold text-gray-900">Payment</h1>
            <div class="w-6"></div> <!-- Spacer for balance -->
        </div>
    </header>

    <!-- Payment Progress (similar to checkout, but simpler) -->
    <div class="px-4 py-3 bg-white flex justify-between items-center relative">
        <div class="absolute top-1/2 left-4 right-4 h-0.5 bg-gray-200 -translate-y-1/2 z-0"></div>
        <div class="checkout-step completed z-10 flex flex-col items-center">
            <div class="step-number w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center font-bold mb-1">1</div>
            <span class="text-xs font-medium">Delivery</span>
        </div>
        <div class="checkout-step active z-10 flex flex-col items-center">
            <div class="step-number w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center font-bold mb-1">2</div>
            <span class="text-xs font-medium">Payment</span>
        </div>
        <div class="checkout-step inactive z-10 flex flex-col items-center">
            <div class="step-number w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center font-bold mb-1">3</div>
            <span class="text-xs font-medium">Confirm</span>
        </div>
    </div>

    <!-- Delivery Address Summary -->
    <div class="bg-white px-4 py-4 mt-1 shadow-sm">
        <h2 class="font-bold text-gray-900 mb-3">Delivery Address</h2>
        <div class="border border-[#0A5144] rounded-lg p-3 bg-[#F0F9F8]">
            <?php if ($address_data): ?>
                <div>
                    <h3 class="font-medium text-gray-900"><?php echo htmlspecialchars($address_data['address_name']); ?></h3>
                    <p class="text-sm text-gray-600 flex items-start mt-0.5">
                        <i class="fas fa-map-marker-alt mr-2 text-[#0A5144]"></i>
                        <span><?php echo htmlspecialchars($address_data['full_address']); ?></span>
                    </p>
                    <p class="text-sm text-gray-600 mt-1 flex items-center">
                        <i class="fas fa-phone mr-1 text-[#0A5144]"></i>
                        <span><?php echo htmlspecialchars($address_data['phone_number']); ?></span>
                    </p>
                </div>
            <?php else: ?>
                <p class="text-gray-500">No address selected or found.</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- Payment Methods -->
    <div class="bg-white px-4 py-4 mt-4 shadow-sm">
        <h2 class="font-bold text-gray-900 mb-3">Payment Method</h2>
        <div class="space-y-3">
            <select id="paymentMethodSelect" class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0A5144]">
                <option value="">Select Payment Method</option>
                <?php foreach ($payment_methods as $method): ?>
                    <option value="<?php echo htmlspecialchars($method['id']); ?>" data-type="<?php echo htmlspecialchars($method['type']); ?>" data-code="<?php echo htmlspecialchars($method['qr_code_image']); ?>" data-url="<?php echo htmlspecialchars($method['payment_url'] ?? ''); ?>">
                        <?php echo htmlspecialchars($method['name']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <div id="paymentCodeDisplay" class="mt-2 p-3 bg-gray-100 border border-gray-200 rounded-md text-gray-800 text-sm hidden">
                <p class="font-medium">Kode Pembayaran:</p>
                <div class="flex items-center justify-between">
                    <p id="actualPaymentCode" class="font-bold text-lg flex-grow"></p>
                    <button id="copyCodeButton" class="ml-3 bg-blue-500 hover:bg-blue-600 text-white text-sm py-1 px-3 rounded-md">Salin</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Summary (on Payment page) -->
    <div class="bg-white px-4 py-4 mt-4 shadow-sm">
        <h2 class="font-bold text-gray-900 mb-3">Order Summary</h2>
        
        <?php if ($product_data && $variant_data): ?>
        <!-- Product Item -->
        <div class="flex justify-between items-start py-3 border-b border-gray-100">
            <div class="flex items-start">
                <img src="<?php echo $variant_upload_dir_display . htmlspecialchars($variant_data['gambar_varian']); ?>" alt="Product Variant" class="w-12 h-12 rounded-lg object-cover mr-3">
                <div>
                    <h3 class="font-medium text-gray-900"><?php echo htmlspecialchars($product_data['nama_produk']); ?></h3>
                    <p class="text-sm text-gray-500"><?php echo htmlspecialchars($variant_data['ukuran_ml']); ?> ml x <?php echo $quantity; ?></p>
                </div>
            </div>
            <div class="text-right">
                <p class="font-medium text-gray-900">Rp <?php echo number_format($variant_data['harga'] * $quantity, 0, ',', '.'); ?></p>
            </div>
        </div>
        <?php else: ?>
        <p class="text-gray-500">No product selected for payment.</p>
        <?php endif; ?>

        <!-- Order Total -->
        <div class="bg-white px-4 py-4 mt-4 shadow-sm">
            <h2 class="font-bold text-gray-900 mb-3">Order Total</h2>
            
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-gray-600">Subtotal</span>
                    <span class="text-gray-900">Rp <?php echo number_format($product_data && $variant_data ? $variant_data['harga'] * $quantity : 0, 0, ',', '.'); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Service Fee</span>
                    <span class="text-gray-900">Rp <?php echo number_format($service_fee, 0, ',', '.'); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Delivery Fee</span>
                    <span class="text-gray-900">Rp <?php echo number_format($delivery_cost, 0, ',', '.'); ?></span>
                </div>
                <div class="border-t border-gray-200 pt-2 mt-2 flex justify-between">
                    <span class="font-bold text-gray-900">Total</span>
                    <span class="font-bold text-gray-900">Rp <?php echo number_format($total_amount, 0, ',', '.'); ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Fixed Pay Now Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-white p-4 shadow-lg border-t border-gray-100">
        <button class="gradient-bg w-full py-3 rounded-lg text-white font-bold shadow-md hover:opacity-90 transition" id="payNowButton">
            Pay Now - Rp <?php echo number_format($total_amount, 0, ',', '.'); ?>
        </button>
    </div>

</body>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const paymentMethodSelect = document.getElementById('paymentMethodSelect');
        const payNowButton = document.getElementById('payNowButton');
        let selectedPaymentMethod = null;

        // PHP variables injected into JavaScript
        const phpProductId = <?php echo json_encode($product_id); ?>;
        const phpVariantId = <?php echo json_encode($variant_id); ?>;
        const phpQuantity = <?php echo json_encode($quantity); ?>;
        const phpDeliveryCost = <?php echo json_encode($delivery_cost); ?>;
        const phpServiceFee = <?php echo json_encode($service_fee); ?>;
        const phpTotalAmount = <?php echo json_encode($total_amount); ?>;
        const phpProductName = <?php echo json_encode($product_data['nama_produk'] ?? 'Product'); ?>;
        const phpVariantMl = <?php echo json_encode($variant_data['ukuran_ml'] ?? '0'); ?>;
        const phpProductPrice = <?php echo json_encode($variant_data['harga'] ?? 0); ?>;
        const phpCustomerName = <?php echo json_encode($address_data['address_name'] ?? 'Guest User'); ?>; // Using address_name as customer name
        const phpCustomerPhone = <?php echo json_encode($address_data['phone_number'] ?? '08123456789'); ?>; // Using address_phone as customer phone
        const phpCustomerEmail = '<EMAIL>'; // Placeholder, ideally from user session
        const phpAddressId = <?php echo json_encode($address_id); ?>;

        // Inject all payment methods data for client-side access
        const allPaymentMethods = <?php echo json_encode($payment_methods); ?>;

        const paymentCodeDisplay = document.getElementById('paymentCodeDisplay');
        const actualPaymentCode = document.getElementById('actualPaymentCode');
        const copyCodeButton = document.getElementById('copyCodeButton');

        paymentMethodSelect.addEventListener('change', function() {
            selectedPaymentMethod = this.value;
            console.log('Selected payment method ID:', selectedPaymentMethod);

            if (selectedPaymentMethod && selectedPaymentMethod !== '') {
                const selectedMethodData = allPaymentMethods.find(method => method.id == selectedPaymentMethod);
                if (selectedMethodData) {
                    // If the selected method is COD, hide the payment code display
                    if (selectedMethodData.type === 'Lainnya') {
                        paymentCodeDisplay.classList.add('hidden');
                        actualPaymentCode.textContent = '';
                        copyCodeButton.classList.add('hidden');
                        // No further processing needed for COD's payment code display
                        return; 
                    }

                    // Display payment code if available for non-COD methods
                    if (selectedMethodData.qr_code_image) {
                        actualPaymentCode.textContent = selectedMethodData.qr_code_image;
                        copyCodeButton.classList.remove('hidden');
                        paymentCodeDisplay.classList.remove('hidden');
                    } else {
                        actualPaymentCode.textContent = '';
                        copyCodeButton.classList.add('hidden');
                        paymentCodeDisplay.classList.add('hidden');
                    }
                } else {
                    // If method data not found for some reason, hide everything
                    paymentCodeDisplay.classList.add('hidden');
                    actualPaymentCode.textContent = '';
                    copyCodeButton.classList.add('hidden');
                }
            } else {
                // If no payment method is selected
                paymentCodeDisplay.classList.add('hidden');
                actualPaymentCode.textContent = '';
                copyCodeButton.classList.add('hidden');
            }
        });

        copyCodeButton.addEventListener('click', function() {
            const codeToCopy = actualPaymentCode.textContent;
            if (codeToCopy) {
                navigator.clipboard.writeText(codeToCopy).then(function() {
                    alert('Kode pembayaran disalin!');
                }).catch(function(err) {
                    console.error('Gagal menyalin kode: ', err);
                    alert('Gagal menyalin kode pembayaran.');
                });
            }
        });

        payNowButton.addEventListener('click', function() {
            if (!selectedPaymentMethod || selectedPaymentMethod === '') {
                alert('Please select a payment method.');
                return;
            }

            const selectedMethodData = allPaymentMethods.find(method => method.id == selectedPaymentMethod);

            // Check if the selected method is COD
            if (selectedMethodData && selectedMethodData.type === 'cod') {
                // Handle COD order submission
                const orderData = {
                    product_id: phpProductId,
                    variant_id: phpVariantId,
                    quantity: phpQuantity,
                    delivery_cost: phpDeliveryCost,
                    service_fee: phpServiceFee,
                    total_amount: phpTotalAmount,
                    address_id: phpAddressId, // Make sure phpAddressId is available
                    payment_method_id: selectedMethodData.id
                };

                fetch('api/process_cod_order.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(orderData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert('Pesanan COD berhasil dibuat!');
                        // Redirect to a confirmation page or order history
                        window.location.href = 'konfirmasi_pembayaran.php?order_id=' + data.order_id; // Ganti dengan halaman konfirmasi Anda
                    } else {
                        alert('Gagal membuat pesanan COD: ' + data.message);
                        console.error('Error from COD backend:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error processing COD order:', error);
                    alert('Terjadi kesalahan saat memproses pesanan COD.');
                });
                return; // Stop further execution for COD
            }

            if (selectedMethodData && selectedMethodData.payment_url) {
                // If a payment_url is available, redirect directly
                window.location.href = selectedMethodData.payment_url;
                return; // Stop further execution
            }

            // If no payment_url and not COD, proceed with Midtrans Snap flow
            // Prepare item details for Midtrans
            const orderData = {
                product_id: phpProductId,
                variant_id: phpVariantId,
                quantity: phpQuantity,
                delivery_cost: phpDeliveryCost,
                service_fee: phpServiceFee,
                total_amount: phpTotalAmount,
                address_id: phpAddressId,
                payment_method_id: selectedMethodData.id,
                product_name: phpProductName,
                variant_ml: phpVariantMl,
                product_price: phpProductPrice,
                customer_name: phpCustomerName,
                customer_email: phpCustomerEmail,
                customer_phone: phpCustomerPhone
            };

            // Send transaction data to backend to get Snap Token
            fetch('api/process_midtrans_order.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(orderData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.snap_token) {
                    console.log('Snap Token received:', data.snap_token);
                    // Trigger Midtrans Snap popup
                    snap.pay(data.snap_token, {
                        onSuccess: function(result){
                            /* You may add your own implementation here */
                            alert("payment success!"); console.log(result);
                            // Redirect to a success page or update UI
                        },
                        onPending: function(result){
                            /* You may add your own implementation here */
                            alert("wating your payment!"); console.log(result);
                            // Redirect to a pending page or update UI
                        },
                        onError: function(result){
                            /* You may add your own implementation here */
                            alert("payment failed!"); console.log(result);
                            // Redirect to an error page or update UI
                        },
                        onClose: function(){
                            /* You may add your own implementation here */
                            alert('you closed the popup without finishing the payment');
                        }
                    });
                } else {
                    alert('Failed to create Midtrans transaction: ' + data.message);
                    console.error('Error from backend:', data.message);
                }
            })
            .catch(error => {
                console.error('Error fetching Snap Token:', error);
                alert('An error occurred during payment initiation.');
            });
        });
    });
</script>
</html>
