<?php
session_start();
require_once 'Admin/db_config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php?return_url=' . urlencode($_SERVER['REQUEST_URI']));
    exit();
}

$user_id = $_SESSION['user_id'];

// First, ensure the pembeli table has the required columns
try {
    // Add password column if it doesn't exist
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS password VARCHAR(255)");
    // Add alamat column if it doesn't exist
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS alamat TEXT");
    // Add profile_picture column if it doesn't exist
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(255)");
    // Add updated_at column if it doesn't exist
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
} catch (PDOException $e) {
    error_log("Error adding columns to pembeli table: " . $e->getMessage());
}

// First, ensure the pembeli table has the required columns
try {
    // Add password column if it doesn't exist
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS password VARCHAR(255)");
    // Add alamat column if it doesn't exist
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS alamat TEXT");
    // Add updated_at column if it doesn't exist
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
} catch (PDOException $e) {
    error_log("Error adding columns to pembeli table: " . $e->getMessage());
}

// Get user data from database
try {
    $stmt = $conn->prepare("SELECT * FROM pembeli WHERE id = ?");
    $stmt->execute([$user_id]);
    $user_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user_data) {
        // User not found, logout
        session_destroy();
        header('Location: login.php');
        exit();
    }
} catch (PDOException $e) {
    error_log("Error fetching user data: " . $e->getMessage());
    $user_data = [
        'nama_pembeli' => 'Pengguna',
        'email' => '',
        'phone' => '',
        'alamat' => ''
    ];
}

// Ensure all required keys exist
$user_data = array_merge([
    'nama_pembeli' => 'Pengguna',
    'email' => '',
    'phone' => '',
    'alamat' => '',
    'profile_picture' => ''
], $user_data ?: []);
?>

<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Profil Saya - Pixel Tonsorium</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    body {
      font-family: 'Inter', Arial, sans-serif;
      background:rgb(11, 99, 19);
    }
    .profile-card {
      background: #fff;
      border-radius: 2rem 2rem 1.5rem 1.5rem;
      box-shadow: 0 4px 24px 0 rgba(0,0,0,0.06);
      margin-top: 1.5rem;
    }
    .info-box {
      background: #f5f5f7;
      border-radius: 1.5rem;
      box-shadow: 0 2px 8px 0 rgba(0,0,0,0.03);
    }
    .info-row {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem 0.5rem;
      border-bottom: 1px solid #ececec;
    }
    .info-row:last-child {
      border-bottom: none;
    }
    .icon-bg {
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 1rem;
      font-size: 1.5rem;
    }
    .btn-orange {
      background: linear-gradient(90deg, #ffb300 60%, #ff9800 100%);
      color: #fff;
      font-weight: 600;
      border-radius: 1.5rem;
      padding: 0.75rem 0;
      margin-bottom: 0.75rem;
      width: 100%;
      border: none;
      font-size: 1rem;
      transition: background 0.2s;
    }
    .btn-orange:hover {
      background: linear-gradient(90deg, #ffa000 60%, #ff6f00 100%);
    }
    .btn-purple {
      background: #5f3dc4;
      color: #fff;
      font-weight: 600;
      border-radius: 1.5rem;
      padding: 0.75rem 0;
      width: 100%;
      border: none;
      font-size: 1rem;
      transition: background 0.2s;
    }
    .btn-purple:hover {
      background: #3f1fa4;
    }
    .navbar {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      background: #fff;
      box-shadow: 0 -2px 12px rgba(0,0,0,0.08);
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 10px 0 6px 0;
      z-index: 50;
      border-top-left-radius: 18px;
      border-top-right-radius: 18px;
    }
    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 13px;
      color: #222;
      min-width: 56px;
      transition: color 0.2s;
      cursor: pointer;
      padding: 2px 0;
    }
.nav-item .material-icons {
  font-size: 25px;
  margin-bottom: 2px;
  transition: color 0.2s;
  color: #222;
}
    .nav-item.active,
    .nav-item:active,
    .nav-item:focus {
      color: #222;
    }
    .nav-item.active .material-icons,
    .nav-item:active .material-icons,
    .nav-item:focus .material-icons {
      color: #222;
    }
    .nav-item:hover,
    .nav-item:focus-visible {
      color: #009688;
    }
    .nav-item:hover .material-icons,
    .nav-item:focus-visible .material-icons {
      color: #009688;
    }
    .nav-item span:last-child {
      font-weight: 500;
      letter-spacing: 0.01em;
    }
  </style>
</head>
<body class="bg-[#f7f7fb] min-h-screen">
  <div class="w-full min-h-screen flex flex-col items-center justify-start overflow-y-auto pb-[80px]" style="background:rgb(247, 247, 251); max-height:100vh;">
    <div class="w-full bg-white rounded-b-3xl shadow-lg flex flex-col items-center pt-10 pb-6 relative" style="border-top-left-radius:0;border-top-right-radius:0;">
      <!-- Tombol Back pojok kiri atas -->
      <a href="index.php"
        class="absolute top-4 left-4 p-2 rounded-full hover:bg-gray-100 transition"
        title="Kembali ke Beranda"
      >
        <span class="material-icons text-gray-500 text-2xl">arrow_back</span>
      </a>

      <!-- Tombol Setting pojok kanan atas -->
      <button
        class="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 transition"
        title="Edit Profil"
        onclick="openEditProfile()"
        type="button"
      >
        <span class="material-icons text-gray-500 text-2xl">settings</span>
      </button>
      <!-- Foto Profil -->
      <div class="w-24 h-24 rounded-full bg-red-500 flex items-center justify-center mb-2 shadow-lg border-4 border-white overflow-hidden relative group cursor-pointer" id="profile-upload-area">
        <?php if (!empty($user_data['profile_picture'])): ?>
          <img id="profile-img" src="uploads/profile/<?php echo htmlspecialchars($user_data['profile_picture']); ?>"
               alt="Foto Profil" class="object-cover w-full h-full">
          <span id="profile-icon" class="material-icons text-white text-6xl" style="display:none;">person</span>
        <?php else: ?>
          <img id="profile-img" src="assets/img/default-profile.png" alt="Foto Profil" class="object-cover w-full h-full" style="display:none;">
          <span id="profile-icon" class="material-icons text-white text-6xl">person</span>
        <?php endif; ?>
        <form id="form-upload-foto" enctype="multipart/form-data" class="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer">
          <input type="file" id="foto-input" name="foto" accept="image/*" class="absolute inset-0 opacity-0 cursor-pointer" />
          <span class="material-icons text-white text-3xl z-10">photo_camera</span>
        </form>
      </div>
      <div class="text-center mt-2">
        <div id="profile-name" class="text-lg font-semibold text-gray-800">
          <?php echo htmlspecialchars($user_data['nama_pembeli']); ?>
        </div>
        <div id="profile-email" class="text-base text-gray-500 mt-1">
          <?php echo htmlspecialchars($user_data['email']); ?>
        </div>
      </div>
    </div>
    <!-- Info Box -->
    <div class="w-full max-w-md mt-6 px-0">
  <div class="info-box p-4 rounded-2xl shadow-md bg-white flex flex-col gap-0">
    <div class="info-row">
      <div class="icon-bg" style="background:#ffe0e0;">
        <span class="material-icons text-red-400">home</span>
      </div>
      <div>
        <div class="text-xs text-gray-400">Alamat</div>
        <div id="user-alamat" class="text-sm font-medium text-gray-700">
          <?php echo htmlspecialchars(($user_data['alamat'] ?? '') ?: 'Belum diisi'); ?>
        </div>
      </div>
    </div>
    <div class="info-row">
      <div class="icon-bg" style="background:#fff7e0;">
        <span class="material-icons text-orange-400">email</span>
      </div>
      <div>
        <div class="text-xs text-gray-400">Email</div>
        <div id="user-email" class="text-sm font-medium text-gray-700">
          <?php echo htmlspecialchars($user_data['email']); ?>
        </div>
      </div>
    </div>
    <div class="info-row">
      <div class="icon-bg" style="background:#e0f0ff;">
        <span class="material-icons text-blue-400">phone</span>
      </div>
      <div>
        <div class="text-xs text-gray-400">No Telepon</div>
        <div id="user-phone" class="text-sm font-medium text-gray-700">
          <?php echo htmlspecialchars(($user_data['phone'] ?? '') ?: 'Belum diisi'); ?>
        </div>
      </div>
    </div>
    <div class="info-row cursor-pointer" id="riwayat-toggle">
      <div class="icon-bg" style="background:#ede7f6;">
        <span class="material-icons text-purple-500">history</span>
      </div>
      <div>
        <div class="text-xs text-gray-400">Riwayat</div>
        <div class="text-sm font-medium text-gray-700">Lihat aktivitas</div>
      </div>
    </div>
    <div id="riwayat-detail" class="pl-4 pb-2 hidden">
      <div class="text-lg font-semibold text-gray-800 mb-2">Booking History</div>
      <div class="flex flex-col gap-4">
        <!-- Booking 1 -->
        <div class="flex gap-3 items-start">
          <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center border border-gray-300">
            <!-- Placeholder image -->
            <span class="material-icons text-gray-400 text-4xl">image</span>
          </div>
          <div class="flex-1">
            <div class="font-medium text-gray-700">Studio A - 12 Juni 2024</div>
            <div class="text-sm text-gray-500">09:00 - 11:00 WIB</div>
            <div class="text-xs text-gray-400">Status: Selesai</div>
          </div>
        </div>
        <!-- Booking 2 -->
        <div class="flex gap-3 items-start">
          <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center border border-gray-300">
            <span class="material-icons text-gray-400 text-4xl">image</span>
          </div>
          <div class="flex-1">
            <div class="font-medium text-gray-700">Studio B - 5 Juni 2024</div>
            <div class="text-sm text-gray-500">14:00 - 16:00 WIB</div>
            <div class="text-xs text-gray-400">Status: Selesai</div>
          </div>
        </div>
      </div>
    </div>
    <script>
      document.getElementById('riwayat-toggle').addEventListener('click', function() {
        var detail = document.getElementById('riwayat-detail');
        detail.classList.toggle('hidden');
      });
    </script>
    <!-- Tombol di dalam box dan box sampai bawah -->
    <div class="mt-6 flex flex-col gap-3">
      <a href="keranjang.php" class="btn-orange text-center block text-decoration-none">
        <i class="fas fa-shopping-cart mr-2"></i>Pesanan Saya
      </a>
      <a href="logout.php" class="btn-purple text-center block text-decoration-none">
        <i class="fas fa-sign-out-alt mr-2"></i>Logout
      </a>
    </div>
  </div>
</div>

  <!-- Modal Edit Profil -->
  <div id="modal-edit-profile" class="fixed inset-0 bg-black/40 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-2xl shadow-lg p-6 w-[90vw] max-w-md">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-bold text-gray-700">Edit Profil</h2>
        <button onclick="closeEditProfile()" class="text-gray-400 hover:text-gray-700">
          <span class="material-icons">close</span>
        </button>
      </div>
      <form id="edit-profile-form" onsubmit="saveProfile(event)">
        <div class="mb-4">
          <label class="block text-sm text-gray-600 mb-1">Nama Lengkap</label>
          <input type="text" id="input-nama" class="w-full border rounded-lg px-3 py-2"
                 value="<?php echo htmlspecialchars($user_data['nama_pembeli']); ?>" required>
        </div>
        <div class="mb-4">
          <label class="block text-sm text-gray-600 mb-1">Email</label>
          <input type="email" id="input-email" class="w-full border rounded-lg px-3 py-2"
                 value="<?php echo htmlspecialchars($user_data['email']); ?>" required>
        </div>
        <div class="mb-4">
          <label class="block text-sm text-gray-600 mb-1">No Telepon</label>
          <input type="text" id="input-telp" class="w-full border rounded-lg px-3 py-2"
                 value="<?php echo htmlspecialchars($user_data['phone'] ?? ''); ?>" required>
        </div>
        <div class="mb-4">
          <label class="block text-sm text-gray-600 mb-1">Alamat</label>
          <textarea id="input-alamat" class="w-full border rounded-lg px-3 py-2" rows="3"><?php echo htmlspecialchars($user_data['alamat'] ?? ''); ?></textarea>
        </div>
        <div class="flex justify-end gap-2">
          <button type="button" onclick="closeEditProfile()" class="px-4 py-2 rounded-lg bg-gray-200 text-gray-700">Batal</button>
          <button type="submit" class="px-4 py-2 rounded-lg bg-green-600 text-white font-semibold">Simpan</button>
        </div>
      </form>
    </div>
  </div>

<!-- Bottom Navigation Modern Style -->
<nav class="fixed bottom-0 left-0 right-0 bg-white shadow-lg flex justify-around py-3 z-50 border-t border-gray-100">
  <a href="index.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-home text-lg mb-1"></i>
    <span>Home</span>
  </a>
  <a href="treatment.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'treatment.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-cut text-lg mb-1"></i>
    <span>Services</span>
  </a>
  <a href="produk.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'produk.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-box-open text-lg mb-1"></i>
    <span>Produk</span>
  </a>
  <a href="location.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'location.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-calendar-alt text-lg mb-1"></i>
    <span>Book</span>
  </a>
</nav>

  <script>
    // Initialize profile data from database (already loaded via PHP)
    window.addEventListener('DOMContentLoaded', function() {
      // Profile data is already loaded from database via PHP
      // No need for localStorage anymore

      // Debug: Check if user is logged in
      console.log('Profile page loaded');

      // Test API connection
      fetch('api/get_profile.php', {
        credentials: 'same-origin'
      })
      .then(response => response.json())
      .then(data => {
        console.log('Profile API test:', data);
      })
      .catch(error => {
        console.error('Profile API test failed:', error);
      });
    });

    // Klik area profil untuk trigger input file
    document.getElementById('profile-upload-area').addEventListener('click', function() {
      document.getElementById('foto-input').click();
    });

    // Upload foto profil ke server
    document.getElementById('foto-input').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (!file) return;

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        alert('Hanya file gambar (JPG, PNG, GIF) yang diperbolehkan!');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('Ukuran file terlalu besar! Maksimal 5MB.');
        return;
      }

      // Show loading
      const uploadArea = document.getElementById('profile-upload-area');
      const originalContent = uploadArea.innerHTML;
      uploadArea.innerHTML = '<div class="flex items-center justify-center"><i class="fas fa-spinner fa-spin text-white text-2xl"></i></div>';

      // Create FormData
      const formData = new FormData();
      formData.append('profile_picture', file);

      // Upload to server (main API)
      fetch('api/upload_profile_picture.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
      })
      .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.text(); // Get as text first to see raw response
      })
      .then(text => {
        console.log('Raw response:', text);

        try {
          const data = JSON.parse(text);
          console.log('Parsed data:', data);

          if (data.success) {
            // Update profile image
            // Ambil ulang elemen setelah di-replace
            const newProfileImg = document.getElementById('profile-img');
            const newProfileIcon = document.getElementById('profile-icon');
            if (data.success && newProfileImg) {
                newProfileImg.src = data.image_url;
                newProfileImg.style.display = 'block';
                if (newProfileIcon) newProfileIcon.style.display = 'none';
            }
            document.getElementById('foto-input').addEventListener('change', arguments.callee);

            alert('Foto profil berhasil diupload!');
          } else {
            alert('Error: ' + data.message);
          }
        } catch (parseError) {
          console.error('JSON parse error:', parseError);
          console.error('Raw response was:', text);
          alert('Server response error. Check console for details.');
        }
      })
      .catch(error => {
        console.error('Upload error:', error);
        alert('Terjadi kesalahan saat upload: ' + error.message);
      })
      .finally(() => {
        // Restore original content
        uploadArea.innerHTML = originalContent;
        // Re-attach event listener
        document.getElementById('foto-input').addEventListener('change', arguments.callee);
      });
    });

    // Modal logic
    function openEditProfile() {
      document.getElementById('modal-edit-profile').classList.remove('hidden');
      // Values are already set via PHP in the form inputs
    }
    function closeEditProfile() {
      document.getElementById('modal-edit-profile').classList.add('hidden');
    }
    // Simpan perubahan ke database saat form edit disubmit
    async function saveProfile(e) {
      e.preventDefault();

      const nama = document.getElementById('input-nama').value;
      const email = document.getElementById('input-email').value;
      const telp = document.getElementById('input-telp').value;
      const alamat = document.getElementById('input-alamat').value;

      // Show loading
      const submitBtn = e.target.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'Menyimpan...';
      submitBtn.disabled = true;

      try {
        const formData = new FormData();
        formData.append('nama_pembeli', nama);
        formData.append('email', email);
        formData.append('phone', telp);
        formData.append('alamat', alamat);

        const response = await fetch('api/update_profile.php', {
          method: 'POST',
          body: formData,
          credentials: 'same-origin'
        });

        const data = await response.json();

        if (data.success) {
          // Update tampilan profil dan info box
          document.getElementById('profile-name').textContent = nama;
          document.getElementById('profile-email').textContent = email;
          document.getElementById('user-alamat').textContent = alamat || 'Belum diisi';
          document.getElementById('user-email').textContent = email;
          document.getElementById('user-phone').textContent = telp || 'Belum diisi';

          closeEditProfile();

          // Show success message
          alert('Profil berhasil diperbarui!');
        } else {
          alert('Error: ' + data.message);
        }
      } catch (error) {
        console.error('Error updating profile:', error);
        alert('Terjadi kesalahan. Silakan coba lagi.');
      } finally {
        // Reset button
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
      }
    }
  </script>
</body>
</html>
