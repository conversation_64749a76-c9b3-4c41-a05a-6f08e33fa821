<?php
header('Content-Type: application/json');

try {
    // Clear debug.log
    if (file_exists('debug.log')) {
        file_put_contents('debug.log', '');
        $cleared[] = 'debug.log';
    }
    
    // Clear debug_upload.txt
    if (file_exists('debug_upload.txt')) {
        file_put_contents('debug_upload.txt', '');
        $cleared[] = 'debug_upload.txt';
    }
    
    // Log the clearing action
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents('debug.log', "[$timestamp] Logs cleared via clear_logs.php\n", FILE_APPEND);
    
    echo json_encode([
        'success' => true,
        'message' => 'Logs cleared successfully',
        'cleared_files' => $cleared ?? []
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error clearing logs: ' . $e->getMessage()
    ]);
}
?>
