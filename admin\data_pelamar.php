<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header("Location: login.php");
    exit();
}

require_once 'db_config.php';

// Handle status update
if (isset($_POST['update_status'])) {
    $application_id = $_POST['application_id'];
    $new_status = $_POST['status'];
    $send_whatsapp = isset($_POST['send_whatsapp']) ? true : false;

    try {
        // Get current status for comparison
        $stmt = $conn->prepare("SELECT status FROM applications WHERE id = ?");
        $stmt->execute([$application_id]);
        $current_status = $stmt->fetchColumn();

        // Update status
        $stmt = $conn->prepare("UPDATE applications SET status = ? WHERE id = ?");
        $stmt->execute([$new_status, $application_id]);

        $_SESSION['message'] = 'Status pelamar berhasil diperbarui!';
        $_SESSION['message_type'] = 'success';

        // Auto-send WhatsApp for important status changes or if manually requested
        $auto_send_statuses = ['accepted', 'rejected', 'interview'];
        $should_send_whatsapp = $send_whatsapp || (in_array($new_status, $auto_send_statuses) && $current_status !== $new_status);

        if ($should_send_whatsapp) {
            require_once 'whatsapp_notification.php';
            $notification_result = sendWhatsAppNotification($application_id, $new_status);

            if ($notification_result && $notification_result['success']) {
                $_SESSION['message'] .= ' Notifikasi WhatsApp berhasil dikirim.';
            } else {
                $_SESSION['message'] .= ' Namun gagal mengirim notifikasi WhatsApp.';
                if (isset($notification_result['message'])) {
                    error_log("WhatsApp notification failed: " . $notification_result['message']);
                }
            }
        }
    } catch (PDOException $e) {
        $_SESSION['message'] = 'Gagal memperbarui status: ' . $e->getMessage();
        $_SESSION['message_type'] = 'error';
    }
    header("Location: data_pelamar.php");
    exit;
}

// Fetch applications with job details
$applications = [];
try {
    $stmt = $conn->query("
        SELECT a.*, j.position, j.location
        FROM applications a
        JOIN jobs j ON a.job_id = j.id
        ORDER BY a.applied_at DESC
    ");
    $applications = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $applications = [];
    error_log("Error fetching applications: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Pelamar - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            background: #2d3748;
            color: #fff;
            transition: transform 0.3s ease-in-out;
        }
        .sidebar .logo {
            background: #1a202c;
            padding: 1rem;
        }
        .sidebar .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .sidebar nav ul li {
            transition: all 0.3s ease;
        }
        .sidebar nav ul li a {
            color: #fff;
            text-decoration: none;
        }
        .sidebar nav ul li.active {
            background: rgb(35, 71, 250);
        }
        .sidebar nav ul li.active a,
        .sidebar nav ul li.active i {
            color: #fff;
        }
        .sidebar nav ul li i {
            color: #4299e1;
        }
        .sidebar nav ul li.active i {
            color: #fff;
        }
        .sidebar nav ul li:hover {
            background: rgba(66, 153, 225, 0.1);
        }
        .sidebar nav ul li.active:hover {
            background: rgb(35, 71, 250);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 50;
                transform: translateX(-100%);
            }
            .sidebar.open {
                transform: translateX(0);
            }
            .main-content {
                width: 100%;
            }
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 40;
                display: none;
            }
            .overlay.show {
                display: block;
            }
        }

        @media (min-width: 769px) {
            .sidebar {
                position: relative;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile Overlay -->
    <div id="overlay" class="overlay"></div>

    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <aside id="sidebar" class="w-64 sidebar">
            <div class="logo">
                <h1 class="text-2xl font-bold">Admin Panel</h1>
            </div>
            <nav class="mt-4">
                <ul>
                    <li class="px-4 py-3 hover:bg-gray-600 rounded-lg mx-2">
                        <a href="index.php" class="flex items-center space-x-3">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <!-- Manajemen Booking -->
                    <li class="mb-2 mx-2">
                        <button onclick="toggleBookingSubmenu(event)" class="w-full flex items-center justify-between px-4 py-3 rounded-lg text-white focus:outline-none hover:bg-gray-600 transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-calendar-check"></i>
                                <span>Manajemen Booking</span>
                            </span>
                            <i id="arrowBookingIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="bookingSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'data_booking.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="data_booking.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Data Booking</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_lokasi.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_lokasi.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Kelola Lokasi</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_barber.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_barber.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-cut"></i>
                                    <span>Kelola Barber</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Manajemen Produk -->
                    <li class="mb-2 mx-2">
                        <button onclick="toggleProdukSubmenu(event)" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none hover:bg-gray-600 transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-box"></i>
                                <span>Manajemen Produk</span>
                            </span>
                            <i id="arrowProdukIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="produkSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_produk.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_produk.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-cube"></i>
                                    <span>Produk</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_pembeli.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_pembeli.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pembeli</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_metode_pembayaran.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_metode_pembayaran.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-money-check-alt"></i>
                                    <span>Metode Pembayaran</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- Manajemen Lowongan -->
                    <li class="mb-2 mx-2">
                        <button onclick="toggleLowonganSubmenu(event)" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none hover:bg-gray-600 transition-colors duration-200">
                        <span class="flex items-center space-x-3">
                            <i class="fas fa-briefcase"></i>
                            <span>Manajemen Lowongan</span>
                        </span>
                            <i id="arrowLowonganIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="lowonganSubmenu" class="ml-8 mt-2 space-y-2 <?php echo (basename($_SERVER['PHP_SELF']) === 'kelola_lowongan.php' || basename($_SERVER['PHP_SELF']) === 'data_pelamar.php') ? '' : 'hidden'; ?>">
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_lowongan.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_lowongan.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-tasks"></i>
                                    <span>Kelola Lowongan</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'data_pelamar.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="data_pelamar.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pelamar</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'whatsapp_manager.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="whatsapp_manager.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>WhatsApp Manager</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="px-4 py-3 hover:bg-gray-600 rounded-lg mx-2 <?php echo basename($_SERVER['PHP_SELF']) === 'kelola_admin.php' ? 'bg-blue-600' : ''; ?>">
                        <a href="kelola_admin.php" class="flex items-center space-x-3">
                            <i class="fas fa-user-shield"></i>
                            <span>Kelola Admin</span>
                        </a>
                    </li>

                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col main-content">
            <header class="flex items-center justify-between px-6 py-4 bg-white border-b-4 border-indigo-600">
                <div class="flex items-center">
                    <button id="sidebarToggle" class="text-gray-500 focus:outline-none md:hidden mr-4">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <h1 class="text-xl font-semibold">Data Pelamar</h1>
                </div>
                <div class="flex items-center">
                    <span class="mr-4 text-gray-700 hidden sm:block"><?php echo $_SESSION['admin_full_name'] ?? 'Admin'; ?></span>
                    <div class="relative">
                        <button id="profileDropdown" class="flex items-center focus:outline-none">
                            <?php
                            // Get admin profile photo
                            $stmt = $conn->prepare("SELECT profile_photo FROM admin WHERE id = ?");
                            $stmt->execute([$_SESSION['admin_id']]);
                            $profile_photo = $stmt->fetchColumn();

                            if (!empty($profile_photo) && file_exists('uploads/profiles/' . $profile_photo)):
                            ?>
                                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-300 hover:border-blue-500 transition-colors"
                                     src="uploads/profiles/<?= htmlspecialchars($profile_photo) ?>"
                                     alt="Admin avatar">
                            <?php else: ?>
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-300 hover:border-blue-500 transition-colors">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                            <?php endif; ?>
                            <i class="fas fa-chevron-down ml-2 text-gray-500 text-sm"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="profileDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="profile_settings.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-3 text-gray-500"></i>
                                Profile Settings
                            </a>
                            <hr class="my-1">
                            <a href="logout.php" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-3 text-red-500"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 p-6">
                <!-- Success/Error Messages -->
                <?php if (isset($_SESSION['message'])): ?>
                    <div id="notification" class="mb-4 p-4 rounded-lg <?php echo $_SESSION['message_type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?> transition-all duration-500 ease-in-out">
                        <div class="flex items-center justify-between">
                            <span><?php echo htmlspecialchars($_SESSION['message']); ?></span>
                            <button onclick="hideNotification()" class="ml-4 text-gray-500 hover:text-gray-700 font-bold text-lg">&times;</button>
                        </div>
                    </div>
                    <?php unset($_SESSION['message'], $_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Card Data Pelamar -->
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <h2 class="text-2xl font-bold mb-4 border-b-2 border-blue-700 pb-2">Data Pelamar Kerja</h2>

                    <?php if (empty($applications)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-users text-gray-400 text-6xl mb-4"></i>
                            <p class="text-gray-500 text-lg">Belum ada pelamar yang mendaftar</p>
                        </div>
                    <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full border border-gray-400 border-collapse">
                                <thead>
                                    <tr class="bg-blue-700 text-white border border-gray-400">
                                        <th class="py-3 px-4 border border-gray-400 text-center">No</th>
                                        <th class="py-3 px-4 border border-gray-400 text-center">Nama Lengkap</th>
                                        <th class="py-3 px-4 border border-gray-400 text-center">Email</th>
                                        <th class="py-3 px-4 border border-gray-400 text-center">Telepon</th>
                                        <th class="py-3 px-4 border border-gray-400 text-center">Posisi</th>
                                        <th class="py-3 px-4 border border-gray-400 text-center">Lokasi</th>
                                        <th class="py-3 px-4 border border-gray-400 text-center">Evaluation Score</th>
                                        <th class="py-3 px-4 border border-gray-400 text-center">Status</th>
                                        <th class="py-3 px-4 border border-gray-400 text-center">Tanggal Lamar</th>
                                        <th class="py-3 px-4 border border-gray-400 text-center">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody class="text-gray-900">
                                    <?php foreach ($applications as $i => $app): ?>
                                    <tr class="border border-gray-400 hover:bg-gray-50">
                                        <td class="py-3 px-4 border border-gray-400 text-center"><?= $i+1 ?></td>
                                        <td class="py-3 px-4 border border-gray-400"><?= htmlspecialchars($app['full_name']) ?></td>
                                        <td class="py-3 px-4 border border-gray-400"><?= htmlspecialchars($app['email']) ?></td>
                                        <td class="py-3 px-4 border border-gray-400"><?= htmlspecialchars($app['phone']) ?></td>
                                        <td class="py-3 px-4 border border-gray-400"><?= htmlspecialchars($app['position']) ?></td>
                                        <td class="py-3 px-4 border border-gray-400"><?= htmlspecialchars($app['location']) ?></td>
                                        <td class="py-3 px-4 border border-gray-400 text-center">
                                            <?php if (isset($app['evaluation_score']) && $app['evaluation_score'] !== null): ?>
                                                <div class="flex flex-col items-center">
                                                    <span class="px-2 py-1 rounded text-xs font-semibold
                                                        <?php
                                                        $eval_score = floatval($app['evaluation_score']);
                                                        if ($eval_score >= 80) echo 'bg-green-100 text-green-800';
                                                        elseif ($eval_score >= 60) echo 'bg-yellow-100 text-yellow-800';
                                                        else echo 'bg-red-100 text-red-800';
                                                        ?>">
                                                        <?= round($app['evaluation_score']) ?>/100
                                                    </span>
                                                    <?php if (isset($app['quiz_score']) && $app['quiz_score'] !== null): ?>
                                                        <span class="text-xs text-gray-500 mt-1">Quiz: <?= round($app['quiz_score']) ?>%</span>
                                                    <?php endif; ?>
                                                </div>
                                            <?php elseif (isset($app['quiz_score']) && $app['quiz_score'] !== null): ?>
                                                <span class="px-2 py-1 rounded text-xs font-semibold
                                                    <?php
                                                    $score = floatval($app['quiz_score']);
                                                    if ($score >= 85) echo 'bg-green-100 text-green-800';
                                                    elseif ($score >= 70) echo 'bg-yellow-100 text-yellow-800';
                                                    else echo 'bg-red-100 text-red-800';
                                                    ?>">
                                                    Quiz: <?= round($app['quiz_score']) ?>%
                                                </span>
                                            <?php else: ?>
                                                <span class="text-gray-400 text-xs">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="py-3 px-4 border border-gray-400 text-center">
                                            <span class="px-2 py-1 rounded text-xs font-semibold
                                                <?php
                                                switch($app['status']) {
                                                    case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                                    case 'reviewed': echo 'bg-blue-100 text-blue-800'; break;
                                                    case 'interview': echo 'bg-purple-100 text-purple-800'; break;
                                                    case 'accepted': echo 'bg-green-100 text-green-800'; break;
                                                    case 'rejected': echo 'bg-red-100 text-red-800'; break;
                                                    default: echo 'bg-gray-100 text-gray-800';
                                                }
                                                ?>">
                                                <?= ucfirst($app['status']) ?>
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 border border-gray-400 text-center"><?= date('d/m/Y', strtotime($app['applied_at'])) ?></td>
                                        <td class="py-3 px-4 border border-gray-400 text-center">
                                            <button onclick="viewApplication(<?= $app['id'] ?>)" class="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 mr-1">Detail</button>
                                            <button onclick="updateStatus(<?= $app['id'] ?>, '<?= $app['status'] ?>')" class="px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600">Status</button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal for Application Details -->
    <div id="applicationModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">Detail Lamaran</h3>
                    <button onclick="closeApplicationModal()" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
                </div>
                <div id="applicationDetails">
                    <!-- Application details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Status Update -->
    <div id="statusModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-lg w-full max-w-md">
            <div class="p-6">
                <h3 class="text-xl font-bold mb-4">Update Status Pelamar</h3>
                <form method="POST">
                    <input type="hidden" id="statusApplicationId" name="application_id">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status Baru:</label>
                        <select name="status" id="statusSelect" class="w-full border rounded px-3 py-2" required>
                            <option value="pending">Pending</option>
                            <option value="reviewed">Reviewed</option>
                            <option value="interview">Interview</option>
                            <option value="accepted">Accepted</option>
                            <option value="rejected">Rejected</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="send_whatsapp" value="1" class="mr-2" checked>
                            <span class="text-sm text-gray-700">
                                <i class="fab fa-whatsapp text-green-600 mr-1"></i>
                                Kirim notifikasi WhatsApp ke pelamar
                            </span>
                        </label>
                        <div class="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <p class="text-xs text-blue-700">
                                <i class="fas fa-info-circle mr-1"></i>
                                <strong>Auto-Send:</strong> Status <span class="font-semibold">Accepted</span>, <span class="font-semibold">Rejected</span>, dan <span class="font-semibold">Interview</span> akan otomatis mengirim WhatsApp meskipun tidak dicentang.
                            </p>
                            <p class="text-xs text-gray-600 mt-1">Centang untuk mengirim notifikasi pada status lainnya (Pending, Reviewed).</p>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="closeStatusModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">Batal</button>
                        <button type="submit" name="update_status" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Update</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
    // Sidebar Toggle for Mobile
    document.addEventListener('DOMContentLoaded', function() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('open');
                overlay.classList.toggle('show');
            });
        }

        if (overlay) {
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
            });
        }

        // Auto-open current submenu
        const currentPage = '<?php echo basename($_SERVER['PHP_SELF']); ?>';
        if (currentPage === 'kelola_lowongan.php' || currentPage === 'data_pelamar.php') {
            const lowonganSubmenu = document.getElementById('lowonganSubmenu');
            const lowonganIcon = document.getElementById('arrowLowonganIcon');
            if (lowonganSubmenu && lowonganIcon) {
                lowonganSubmenu.classList.remove('hidden');
                lowonganIcon.classList.add('rotate-180');
            }
        }

        // Profile dropdown functionality
        const profileDropdown = document.getElementById('profileDropdown');
        const profileDropdownMenu = document.getElementById('profileDropdownMenu');

        if (profileDropdown && profileDropdownMenu) {
            profileDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdownMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdownMenu.classList.add('hidden');
                }
            });
        }
    });

    function toggleBookingSubmenu(event) {
        event.preventDefault();
        event.stopPropagation();
        const submenu = document.getElementById('bookingSubmenu');
        const icon = document.getElementById('arrowBookingIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleProdukSubmenu(event) {
        event.preventDefault();
        event.stopPropagation();
        const submenu = document.getElementById('produkSubmenu');
        const icon = document.getElementById('arrowProdukIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleLowonganSubmenu(event) {
        event.preventDefault();
        event.stopPropagation();
        const submenu = document.getElementById('lowonganSubmenu');
        const icon = document.getElementById('arrowLowonganIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function viewApplication(applicationId) {
        // Fetch application details via AJAX
        fetch('get_application_details.php?id=' + applicationId)
            .then(response => response.text())
            .then(data => {
                document.getElementById('applicationDetails').innerHTML = data;
                document.getElementById('applicationModal').classList.remove('hidden');
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Gagal memuat detail lamaran');
            });
    }

    function closeApplicationModal() {
        document.getElementById('applicationModal').classList.add('hidden');
    }

    function updateStatus(applicationId, currentStatus) {
        document.getElementById('statusApplicationId').value = applicationId;
        document.getElementById('statusSelect').value = currentStatus;
        document.getElementById('statusModal').classList.remove('hidden');
    }

    function closeStatusModal() {
        document.getElementById('statusModal').classList.add('hidden');
    }

    // Auto-hide notification function
    function hideNotification() {
        const notification = document.getElementById('notification');
        if (notification) {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                notification.style.display = 'none';
            }, 500);
        }
    }

    // Auto-hide notification after 4 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const notification = document.getElementById('notification');
        if (notification) {
            setTimeout(() => {
                hideNotification();
            }, 4000); // Hide after 4 seconds
        }
    });
    </script>
