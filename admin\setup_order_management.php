<?php
/**
 * Setup Order Management System - Create required tables
 */

require_once 'db_config.php';

echo "<h2>Setup Order Management System</h2>";

try {
    // Create order_history table
    $order_history_sql = "
        CREATE TABLE IF NOT EXISTS order_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id INT NOT NULL,
            status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled') NOT NULL,
            notes TEXT,
            admin_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_order_id (order_id),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (admin_id) REFERENCES admin(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $conn->exec($order_history_sql);
    echo "<p style='color: green;'>✅ Table 'order_history' created successfully!</p>";
    
    // Add tracking_number column to orders table if not exists
    $add_tracking_sql = "
        ALTER TABLE orders 
        ADD COLUMN IF NOT EXISTS tracking_number VARCHAR(100) DEFAULT NULL COMMENT 'Shipping tracking number',
        ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ";
    
    try {
        $conn->exec($add_tracking_sql);
        echo "<p style='color: green;'>✅ Added tracking_number and updated_at columns to orders table!</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<p style='color: orange;'>⚠️ Columns already exist in orders table.</p>";
        } else {
            throw $e;
        }
    }
    
    // Update order_status enum if needed
    $update_status_enum_sql = "
        ALTER TABLE orders 
        MODIFY COLUMN order_status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending'
    ";
    
    try {
        $conn->exec($update_status_enum_sql);
        echo "<p style='color: green;'>✅ Updated order_status enum values!</p>";
    } catch (PDOException $e) {
        echo "<p style='color: orange;'>⚠️ Order status enum might already be updated: " . $e->getMessage() . "</p>";
    }
    
    // Create sample order history for existing orders
    $existing_orders_sql = "
        INSERT IGNORE INTO order_history (order_id, status, notes, created_at)
        SELECT id, order_status, 'Initial status from existing order', created_at
        FROM orders
        WHERE id NOT IN (SELECT DISTINCT order_id FROM order_history)
    ";
    
    $stmt = $conn->prepare($existing_orders_sql);
    $stmt->execute();
    $affected_rows = $stmt->rowCount();
    
    if ($affected_rows > 0) {
        echo "<p style='color: green;'>✅ Created initial history records for {$affected_rows} existing orders!</p>";
    } else {
        echo "<p style='color: blue;'>📝 No new history records needed for existing orders.</p>";
    }
    
    // Create indexes for better performance
    $indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(order_status)",
        "CREATE INDEX IF NOT EXISTS idx_orders_pembeli ON orders(pembeli_id)",
        "CREATE INDEX IF NOT EXISTS idx_orders_created ON orders(created_at)"
    ];
    
    foreach ($indexes_sql as $index_sql) {
        try {
            $conn->exec($index_sql);
        } catch (PDOException $e) {
            // Index might already exist, continue
        }
    }
    echo "<p style='color: green;'>✅ Database indexes optimized!</p>";
    
    echo "<h3>✅ Order Management System Setup Complete!</h3>";
    echo "<p>You can now:</p>";
    echo "<ul>";
    echo "<li>Track order status changes with full history</li>";
    echo "<li>Add tracking numbers for shipped orders</li>";
    echo "<li>Add notes for each status change</li>";
    echo "<li>View complete order timeline</li>";
    echo "</ul>";
    
    echo "<p><a href='kelola_pembeli.php' style='background: #3b82f6; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Go to Kelola Pembeli</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Unexpected error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Order Management Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        h2 { color: #333; border-bottom: 2px solid #3b82f6; padding-bottom: 10px; }
        h3 { color: #555; margin-top: 30px; }
        p { line-height: 1.6; }
        ul { margin-left: 20px; }
        li { margin-bottom: 5px; }
    </style>
</head>
<body>
    <!-- Content is generated by PHP above -->
</body>
</html>
