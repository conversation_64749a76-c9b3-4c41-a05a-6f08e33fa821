<?php
header('Content-Type: application/json');

function processComplete3DFace($userImagePath, $hairstylePath = null) {
    try {
        if (!file_exists($userImagePath)) {
            return ['success' => false, 'error' => 'User image not found'];
        }
        
        // Prepare Python command
        $pythonScript = __DIR__ . '/complete_3d_face_processor.py';
        
        if ($hairstylePath && file_exists($hairstylePath)) {
            $command = sprintf('python "%s" "%s" "%s"', $pythonScript, $userImagePath, $hairstylePath);
        } else {
            $command = sprintf('python "%s" "%s"', $pythonScript, $userImagePath);
        }
        
        $output = shell_exec($command . ' 2>&1');
        
        if (!$output) {
            return ['success' => false, 'error' => '3D processing failed'];
        }
        
        $result = json_decode($output, true);
        
        if (!$result) {
            return ['success' => false, 'error' => 'Invalid processing output', 'debug' => $output];
        }
        
        return $result;
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $userImage = $input['user_image'] ?? '';
    $hairstylePath = $input['hairstyle_path'] ?? null;
    
    if (empty($userImage)) {
        echo json_encode(['success' => false, 'error' => 'User image path required']);
        exit;
    }
    
    $result = processComplete3DFace($userImage, $hairstylePath);
    echo json_encode($result);
    exit;
}

echo json_encode(['success' => false, 'error' => 'Invalid request method']);
?>