<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();
require_once 'Admin/db_config.php';

// Check session and restore from cookies if needed
if (!isset($_SESSION['user_id']) && isset($_COOKIE['user_id'])) {
    $_SESSION['user_id'] = $_COOKIE['user_id'];
    $_SESSION['user_name'] = $_COOKIE['user_login'] ?? '';
}

$current_user_id = $_SESSION['user_id'] ?? null;
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Pixel Barbershop App UI</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Inter', Arial, sans-serif;
      background: #f4f4f4;
    }
    .navbar {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      background: #fff;
      box-shadow: 0 -2px 12px rgba(0,0,0,0.08);
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 10px 0 6px 0;
      z-index: 50;
      border-top-left-radius: 18px;
      border-top-right-radius: 18px;
    }
    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 13px;
      color: #222;
      min-width: 56px;
      transition: color 0.2s;
      cursor: pointer;
      padding: 2px 0;
    }
.nav-item .material-icons {
  font-size: 25px;
  margin-bottom: 2px;
  transition: color 0.2s;
  color: #222;
}
    .nav-item.active,
    .nav-item:active,
    .nav-item:focus {
      color: #222;
    }
    .nav-item.active .material-icons,
    .nav-item:active .material-icons,
    .nav-item:focus .material-icons {
      color: #222;
    }
    .nav-item:hover,
    .nav-item:focus-visible {
      color: #009688;
    }
    .nav-item:hover .material-icons,
    .nav-item:focus-visible .material-icons {
      color: #009688;
    }
    .nav-item span:last-child {
      font-weight: 500;
      letter-spacing: 0.01em;
    }
  </style>
</head>
<body class="bg-green-800 pb-28">

  <!-- Header Putih Lebih Modern -->
<div class="bg-white px-3 pt-5 pb-10 rounded-b-3xl shadow-lg">
  <div class="flex items-center justify-between">
    <!-- User Info -->
    <div class="flex items-center gap-6 ml-2">
      <div class="relative">
        <div class="bg-gradient-to-tr from-purple-600 to-indigo-500 rounded-full w-14 h-14 flex items-center justify-center shadow-md cursor-pointer overflow-hidden"
             onclick="goToProfile()">
          <img id="profile-img-index" src="assets/img/default-profile.png" alt="Foto Profil" class="object-cover w-14 h-14" style="display:none;">
          <i id="profile-icon-index" class="fas fa-user text-white text-2xl"></i>
        </div>
      </div>
      <div>
        <h2 class="font-bold text-lg text-black leading-tight">
         Selamat Datang <span id="profile-name-index" class="text-indigo-700"></span>
        </h2>
<div class="flex items-center gap-2 text-sm text-gray-500 mt-1">
  <i class="fas fa-calendar-alt text-gray-400"></i>
  <span id="tanggal-hari-ini"></span>
</div>
<script>
  // Format tanggal: Senin, 3 Juni 2025 (otomatis sesuai hari ini)
  const hari = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];
  const bulan = [
    "January","February","March","April","May","June",
    "July","August","September","October","November","December"
  ];
  const tgl = new Date();
  const hariIni = hari[tgl.getDay()];
  const tanggal = tgl.getDate();
  const bulanIni = bulan[tgl.getMonth()];
  const tahun = tgl.getFullYear();
  document.getElementById('tanggal-hari-ini').innerText = `${hariIni}, ${bulanIni} ${tanggal}, ${tahun}`;
</script>
      </div>
    </div>
    <!-- Notification Bell -->
<div class="relative ml-auto mr-4 flex items-center" id="notificationContainer">
    <div class="relative">
        <a href="#" onclick="goToKeranjang()" class="text-gray-800 hover:text-gray-600 focus:outline-none">
            <i class="fas fa-bell text-2xl"></i>
            <?php
            // Get active orders count (confirmed to shipped)
            $active_orders_count = 0;
            if (isset($_SESSION['user_id'])) {
                try {
                    $stmt = $conn->prepare("
                        SELECT COUNT(*) FROM orders
                        WHERE pembeli_id = :user_id
                        AND order_status IN ('confirmed', 'processing', 'shipped')
                    ");
                    $stmt->execute(['user_id' => $_SESSION['user_id']]);
                    $active_orders_count = $stmt->fetchColumn();
                } catch (PDOException $e) {
                    error_log("Error getting active orders: " . $e->getMessage());
                }
            }
            if ($active_orders_count > 0): ?>
                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center" id="notificationBadge">
                    <?php echo $active_orders_count; ?>
                </span>
            <?php endif; ?>
        </a>
    </div>
</div>
  </div>
</div>

  <!-- Menu Shortcut -->
  <div class="grid grid-cols-4 gap-4 p-4 text-center text-white">
<div>
  <a href="aboutme.php" class="block focus:outline-none">
    <div class="bg-white rounded-full w-14 h-14 mx-auto flex items-center justify-center transition-transform hover:scale-105 overflow-hidden shadow">
      <img src="./assets/logo.png" alt="About Icon" class="w-12 h-12 object-contain" />
    </div>
    <p class="mt-2 text-sm font-semibold text-white">About</p>
  </a>
</div>
  <div>
    <a href="hair.php" class="block focus:outline-none">
      <div class="bg-pink-500 rounded-full w-14 h-14 mx-auto flex items-center justify-center transition-transform hover:scale-105">
        <i class="fas fa-cut text-xl"></i>
      </div>
      <p class="mt-2 text-sm">Hairstyle</p>
    </a>
  </div>
  <div>
    <a href="jobs.php" class="block focus:outline-none">
      <div class="bg-blue-600 rounded-full w-14 h-14 mx-auto flex items-center justify-center transition-transform hover:scale-105">
        <i class="fas fa-bullhorn text-xl"></i>
      </div>
      <p class="mt-2 text-sm">Join Us</p>
    </a>
  </div>
 <div>
  <a href="face.php" class="block focus:outline-none">
    <div class="bg-yellow-400 text-black rounded-full w-14 h-14 mx-auto flex items-center justify-center transition-transform hover:scale-105">
      <!-- Heroicons: Face Detection (User Circle) -->
      <svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2" fill="none"/>
        <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
        <path stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M7 17a5 5 0 0110 0"/>
      </svg>
    </div>
    <p class="mt-2 text-sm">Recognition</p>
  </a>
</div>
</div>

  <!-- Penawaran Khusus -->
  <div class="p-4">
    <div class="flex justify-between items-center mb-2">
      <h3 class="font-bold text-lg text-white">Penawaran Khusus</h3>
      <a href="#" class="text-yellow-300 text-sm">Lihat Semua</a>
    </div>
    <div id="promo-carousel" class="flex overflow-x-auto space-x-6 snap-x snap-mandatory scroll-smooth">
      <!-- Card 1 -->
      <div class="min-w-full bg-purple-700 rounded-2xl p-4 h-44 flex items-center justify-between text-white snap-center shadow-lg relative transition-transform duration-300">
        <div class="flex-1 flex flex-col justify-center">
          <span class="text-xs bg-yellow-400 text-black px-4 py-1 rounded-full font-bold w-max shadow mb-2">Hanya Untukmu</span>
          <p class="font-extrabold text-xl leading-snug mb-1">
            Dapatkan Diskon Spesial
          </p>
          <p class="font-bold text-2xl text-yellow-300">Hingga 40%</p>
          <button class="mt-3 bg-white text-green-600 font-semibold px-4 py-1 rounded-full flex items-center gap-2 shadow w-max text-sm">
            Pesan Sekarang <span class="text-green-600">&rarr;</span>
          </button>
        </div>
      </div>
      <!-- Card 2 -->
      <div class="min-w-full bg-purple-700 rounded-2xl p-4 h-44 flex items-center justify-between text-white snap-center shadow-lg relative transition-transform duration-300">
        <div class="flex-1 flex flex-col justify-center">
          <span class="text-xs bg-yellow-400 text-black px-4 py-1 rounded-full font-bold w-max shadow mb-2">Khusus</span>
          <p class="font-extrabold text-xl leading-snug mb-1">
            Gratis Cukur
          </p>
          <p class="font-bold text-lg">Jenggot</p>
          <button class="mt-3 bg-white text-green-600 font-semibold px-4 py-1 rounded-full flex items-center gap-2 shadow w-max text-sm">
            Pesan Sekarang <span class="text-green-600">&rarr;</span>
          </button>
        </div>
      </div>
      <!-- Card 3 -->
      <div class="min-w-full bg-purple-700 rounded-2xl p-4 h-44 flex items-center justify-between text-white snap-center shadow-lg relative transition-transform duration-300">
        <div class="flex-1 flex flex-col justify-center">
          <span class="text-xs bg-yellow-400 text-black px-4 py-1 rounded-full font-bold w-max shadow mb-2">Spesial Member</span>
          <p class="font-extrabold text-xl leading-snug mb-1">
            Cashback
          </p>
          <p class="font-bold text-lg">10% Setiap Booking</p>
          <button class="mt-3 bg-white text-green-600 font-semibold px-4 py-1 rounded-full flex items-center gap-2 shadow w-max text-sm">
            Pesan Sekarang <span class="text-green-600">&rarr;</span>
          </button>
        </div>
      </div>
    </div>
  </div>
  <script>
    // Auto-slide carousel
    const carousel = document.getElementById('promo-carousel');
    let idx = 0;
    setInterval(() => {
      idx = (idx + 1) % 3;
      carousel.scrollTo({
        left: carousel.offsetWidth * idx,
        behavior: 'smooth'
      });
    }, 3500);
  </script>

  <!-- Cabang Pixel Barbershop -->
  <div class="p-4 text-white">
    <div class="flex justify-between items-center mb-2">
      <h3 class="font-bold text-lg">Cabang Pixel Barbershop</h3>
      <a href="#" class="text-yellow-300 text-sm">Lihat Semua</a>
    </div>
 <div class="flex gap-3 overflow-x-auto snap-x snap-mandatory pb-2">
  <!-- Cabang ciaul -->
<div class="bg-white rounded-2xl shadow p-4 min-w-[240px] max-w-[240px] text-black flex-shrink-0 snap-center flex flex-col">
  <a href="https://www.google.com/maps/place/Pixel+Barbershop+Ciaul/@-6.8938829,104.5458143,8z/data=!4m10!1m2!2m1!1spixel+barbershop!3m6!1s0x2e684926f4295543:0x4cc3edf7fd8f6485!8m2!3d-6.9210447!4d106.9490176!15sChBwaXhlbCBiYXJiZXJzaG9wkgELYmFyYmVyX3Nob3CqAUcKDS9nLzExeTU2cGw4cWgQATIeEAEiGtpNFop9-kxEHYCIOS_l2Ka33aNHfyLOHzQEMhQQAiIQcGl4ZWwgYmFyYmVyc2hvcOABAA!16s%2Fg%2F11lf8y9qmd?entry=ttu&g_ep=EgoyMDI1MDUyOC4wIKXMDSoASAFQAw%3D%3D"
     target="_blank">
    <img src="./assets/pixel.jpg" alt="Pixel Barbershop Ciaul" class="rounded-xl w-full h-32 object-cover mb-2 hover:opacity-80 transition">
  </a>
  <div class="flex-1 flex flex-col justify-start">
    <h4 class="font-bold text-base mb-1 mt-2">Pixel Barbershop Ciaul</h4>
    <div class="flex flex-col mb-1">
      <div class="flex items-start">
        <i class="fas fa-map-marker-alt text-red-500 mr-2 mt-0.5"></i>
        <span class="text-xs text-gray-500 break-words">
          Jl. R.A. Kosasih No.278, Subangjaya, Kec. Cikole, Kota Sukabumi, Jawa Barat 43116
        </span>
      </div>
    </div>
  </div>
</div>
<!-- Cabang Cibadak -->
<div class="bg-white rounded-2xl shadow p-4 min-w-[240px] max-w-[240px] text-black flex-shrink-0 snap-center flex flex-col">
  <a href="https://www.google.com/maps/place/Pixel+Barbershop+Cibadak/@-6.8938829,104.5458143,8z/data=!4m10!1m2!2m1!1spixel+barbershop!3m6!1s0x2e6831e17787084f:0x4f5ad2fc3d35a081!8m2!3d-6.8938829!4d106.7870252!15sChBwaXhlbCBiYXJiZXJzaG9wkgELYmFyYmVyX3Nob3CqAUcKDS9nLzExeTU2cGw4cWgQATIeEAEiGtpNFop9-kxEHYCIOS_l2Ka33aNHfyLOHzQEMhQQAiIQcGl4ZWwgYmFyYmVyc2hvcOABAA!16s%2Fg%2F11y56pl8qh?entry=ttu&g_ep=EgoyMDI1MDUyOC4wIKXMDSoASAFQAw%3D%3D"
     target="_blank">
    <img src="./assets/pixel.jpg" alt="Pixel Barbershop Cibadak" class="rounded-xl w-full h-32 object-cover mb-2 hover:opacity-80 transition">
  </a>
  <div class="flex-1 flex flex-col justify-start">
    <h4 class="font-bold text-base mb-1 mt-2">Pixel Barbershop Cibadak</h4>
    <div class="flex flex-col mb-1">
      <p class="text-xs text-gray-500 break-words flex items-start">
        <span class="flex items-start">
          <i class="fas fa-map-marker-alt text-red-500 mr-1 mt-0.5"></i>
          <span>Jl. Siliwangi, Cibadak, Kec. Cibadak, Kabupaten Sukabumi, Jawa Barat 43351</span>
        </span>
      </p>
    </div>
  </div>
</div>
      <!-- Cabang cisaat -->
<div class="bg-white rounded-2xl shadow p-4 min-w-[240px] max-w-[240px] text-black flex-shrink-0 snap-center flex flex-col">
  <a href="https://www.google.com/maps/place/Pixel+Barbershop+Cisaat/@-6.9094074,106.893612,8z/data=!4m10!1m2!2m1!1spixel+barbershop!3m6!1s0x2e6837556fc84341:0x9a935aff35f3c849!8m2!3d-6.9092998!4d106.8936827!15sChBwaXhlbCBiYXJiZXJzaG9wkgELYmFyYmVyX3Nob3CqAUcKDS9nLzExeTU2cGw4cWgQATIeEAEiGtpNFop9-kxEHYCIOS_l2Ka33aNHfyLOHzQEMhQQAiIQcGl4ZWwgYmFyYmVyc2hvcOABAA!16s%2Fg%2F11w7_bwhh7?entry=ttu&g_ep=EgoyMDI1MDUyOC4wIKXMDSoASAFQAw%3D%3D"
     target="_blank">
    <img src="./assets/pixel.jpg" alt="Pixel Barbershop Cisaat" class="rounded-xl w-full h-32 object-cover mb-2 hover:opacity-80 transition">
  </a>
  <div class="flex-1 flex flex-col justify-start">
    <h4 class="font-bold text-base mb-1 mt-2">Pixel Barbershop Cisaat</h4>
<div class="flex flex-col mb-1">
  <p class="text-xs text-gray-500 break-words flex items-start">
    <span class="flex items-start">
      <span class="mt-0.5 mr-1">
        <i class="fas fa-map-marker-alt text-red-500"></i>
      </span>
      Jl. Raya Cisaat No.118,
    </span>
    <span class="ml-1">Sukamanah, Kec. Cisaat, Kabupaten Sukabumi, Jawa Barat 43152</span>
  </p>
</div>
  </div>
</div>
<!-- Cabang Cicurug -->
<div class="bg-white rounded-2xl shadow p-4 min-w-[240px] max-w-[240px] text-black flex-shrink-0 snap-center flex flex-col">
  <a href="https://www.google.com/maps/place/Pixel+Barbershop+Cicurug/@-6.8938829,104.5458143,8z/data=!4m10!1m2!2m1!1spixel+barbershop!3m6!1s0x2e69cda9d46b335f:0x19c6bd2047cd928a!8m2!3d-6.7840572!4d106.7815006!15sChBwaXhlbCBiYXJiZXJzaG9wkgELYmFyYmVyX3Nob3CqAUcKDS9nLzExeTU2cGw4cWgQATIeEAEiGtpNFop9-kxEHYCIOS_l2Ka33aNHfyLOHzQEMhQQAiIQcGl4ZWwgYmFyYmVyc2hvcOABAA!16s%2Fg%2F11wc1pz_4y?entry=ttu&g_ep=EgoyMDI1MDUyOC4wIKXMDSoASAFQAw%3D%3D"
     target="_blank">
    <img src="./assets/pixel.jpg" alt="Pixel Barbershop Cicurug" class="rounded-xl w-full h-32 object-cover mb-2 hover:opacity-80 transition">
  </a>
  <div class="flex-1 flex flex-col justify-start">
    <h4 class="font-bold text-base mb-1 mt-2">Pixel Barbershop Cicurug</h4>
    <div class="flex flex-col mb-1">
      <div class="flex items-start">
        <i class="fas fa-map-marker-alt text-red-500 mr-2 mt-0.5"></i>
        <span class="text-xs text-gray-500 break-words">
          Jl. Raya Sukabumi No.121, Cicurug, Kec. Cicurug, Kabupaten Sukabumi, Jawa Barat 43359
        </span>
      </div>
    </div>
  </div>
</div>
      <!-- Cabang pelabuhan ratu -->
<!-- Cabang Pelabuhanratu -->
<div class="bg-white rounded-2xl shadow p-4 min-w-[240px] max-w-[240px] text-black flex-shrink-0 snap-center flex flex-col">
  <a href="https://www.google.com/maps/place/Pixel+Barbershop+Palabuhanratu/@-6.8938829,104.5458143,8z/data=!4m10!1m2!2m1!1spixel+barbershop!3m6!1s0x2e68274669b9c835:0xcfd53f0ed7e4b4ff!8m2!3d-6.9880329!4d106.5461003!15sChBwaXhlbCBiYXJiZXJzaG9wkgELYmFyYmVyX3Nob3CqAUcKDS9nLzExeTU2cGw4cWgQATIeEAEiGtpNFop9-kxEHYCIOS_l2Ka33aNHfyLOHzQEMhQQAiIQcGl4ZWwgYmFyYmVyc2hvcOABAA!16s%2Fg%2F11xd7c2zmv?entry=ttu&g_ep=EgoyMDI1MDUyOC4wIKXMDSoASAFQAw%3D%3D"
     target="_blank">
    <img src="./assets/pixel.jpg" alt="Pixel Pelabuhanratu" class="rounded-xl w-full h-32 object-cover mb-2 hover:opacity-80 transition">
  </a>
  <div class="flex-1 flex flex-col justify-start">
    <h4 class="font-bold text-base mb-1 mt-2">Pixel Pelabuhanratu</h4>
    <div class="flex flex-col mb-1">
      <div class="flex items-start">
        <i class="fas fa-map-marker-alt text-red-500 mr-2 mt-0.5"></i>
        <span class="text-xs text-gray-500 break-words">
          Jl. Siliwangi Palabuhanratu, Pelabuhanratu, Kec. Pelabuhanratu, Kabupaten Sukabumi, Jawa Barat 43364
        </span>
      </div>
    </div>
  </div>
</div>
<!-- Cabang Sudirman -->
<div class="bg-white rounded-2xl shadow p-4 min-w-[240px] max-w-[240px] text-black flex-shrink-0 snap-center flex flex-col">
  <a href="https://www.google.com/maps/place/Pixel+Barbershop+Sudirman/@-6.8938829,104.5458143,8z/data=!4m10!1m2!2m1!1spixel+barbershop!3m6!1s0x2e68490065497f0f:0x17c281fa4d8dea0a!8m2!3d-6.9209238!4d106.9224047!15sChBwaXhlbCBiYXJiZXJzaG9wkgELYmFyYmVyX3Nob3CqAUcKDS9nLzExeTU2cGw4cWgQATIeEAEiGtpNFop9-kxEHYCIOS_l2Ka33aNHfyLOHzQEMhQQAiIQcGl4ZWwgYmFyYmVyc2hvcOABAA!16s%2Fg%2F11whfgcdy3?entry=ttu&g_ep=EgoyMDI1MDUyOC4wIKXMDSoASAFQAw%3D%3D"
     target="_blank">
    <img src="./assets/pixel.jpg" alt="Pixel Sudirman" class="rounded-xl w-full h-32 object-cover mb-2 hover:opacity-80 transition">
  </a>
  <div class="flex-1 flex flex-col justify-start">
    <h4 class="font-bold text-base mb-1 mt-2">Pixel Sudirman</h4>
    <div class="flex flex-col mb-1">
      <div class="flex items-start">
        <i class="fas fa-map-marker-alt text-red-500 mr-2 mt-0.5"></i>
        <span class="text-xs text-gray-500 break-words">
          Jl. Sudirman No.112, Benteng, Kec. Warudoyong, Kota Sukabumi, Jawa Barat 43132
        </span>
      </div>
    </div>
  </div>
</div>
    </div>
  </div>

<!-- Popular Barbers Section -->
<div class="p-4">
  <div class="flex justify-between items-center mb-4">
    <h3 class="font-bold text-lg text-white drop-shadow">Popular Barbers</h3>
    <a href="#" class="text-indigo-300 text-sm font-semibold hover:underline transition">See All</a>
  </div>
  <div class="grid grid-cols-2 gap-5">
    <!-- Barber 1 -->
    <div class="group flex flex-col items-center bg-gradient-to-br from-white/90 to-gray-100/80 rounded-2xl shadow-xl  transition-transform duration-200 border-2 border-white/80">
      <div class="relative w-full">
        <img src="./assets/barberman1.jpg" alt="Gentlemen's Barbershop"
          class="rounded-t-2xl w-full h-56 object-cover shadow-sm group-hover:brightness-90 transition" />
        <div class="absolute top-2 right-2 bg-white/80 rounded-full px-2 py-1 text-xs font-semibold text-green-700 shadow group-hover:bg-green-100 transition">★ 4.9</div>
      </div>
      <div class="w-full px-4 py-3 flex flex-col items-center">
        <h4 class="font-bold text-base text-gray-900 text-center mb-1">Gentlemen's Barbershop</h4>
        <span class="text-xs text-green-700 font-medium bg-green-100/80 px-2 py-0.5 rounded-full mb-1">Cicurug</span>
      </div>
    </div>
    <!-- Barber 2 -->
    <div class="group flex flex-col items-center bg-gradient-to-br from-white/90 to-gray-100/80 rounded-2xl shadow-xl  transition-transform duration-200 border-2 border-white/80">
      <div class="relative w-full">
        <img src="./assets/barberman2.jpg" alt="King's Cut Studio"
          class="rounded-t-2xl w-full h-56 object-cover shadow-sm group-hover:brightness-90 transition" />
        <div class="absolute top-2 right-2 bg-white/80 rounded-full px-2 py-1 text-xs font-semibold text-green-700 shadow group-hover:bg-green-100 transition">★ 4.8</div>
      </div>
      <div class="w-full px-4 py-3 flex flex-col items-center">
        <h4 class="font-bold text-base text-gray-900 text-center mb-1">King's Cut Studio</h4>
        <span class="text-xs text-green-700 font-medium bg-green-100/80 px-2 py-0.5 rounded-full mb-1">Cibadak</span>
      </div>
    </div>
    <!-- Barber 3 -->
    <div class="group flex flex-col items-center bg-gradient-to-br from-white/90 to-gray-100/80 rounded-2xl shadow-xl  transition-transform duration-200 border-2 border-white/80">
      <div class="relative w-full">
        <img src="./assets/barberman3.jpg" alt="Barberman 3"
          class="rounded-t-2xl w-full h-56 object-cover shadow-sm group-hover:brightness-90 transition" />
        <div class="absolute top-2 right-2 bg-white/80 rounded-full px-2 py-1 text-xs font-semibold text-green-700 shadow group-hover:bg-green-100 transition">★ 4.7</div>
      </div>
      <div class="w-full px-4 py-3 flex flex-col items-center">
        <h4 class="font-bold text-base text-gray-900 text-center mb-1">Barberman 3</h4>
        <span class="text-xs text-green-700 font-medium bg-green-100/80 px-2 py-0.5 rounded-full mb-1">Cisaat</span>
      </div>
    </div>
    <!-- Barber 4 -->
    <div class="group flex flex-col items-center bg-gradient-to-br from-white/90 to-gray-100/80 rounded-2xl shadow-xl  transition-transform duration-200 border-2 border-white/80">
      <div class="relative w-full">
        <img src="./assets/barberman4.jpg" alt="Barberman 4" class="rounded-t-2xl w-full h-56 object-cover shadow-sm group-hover:brightness-90 transition" />
        <div class="absolute top-2 right-2 bg-white/80 rounded-full px-2 py-1 text-xs font-semibold text-green-700 shadow group-hover:bg-green-100 transition">★ 4.8</div>
      </div>
      <div class="w-full px-4 py-3 flex flex-col items-center">
        <h4 class="font-bold text-base text-gray-900 text-center mb-1">Barberman 4</h4>
        <span class="text-xs text-green-700 font-medium bg-green-100/80 px-2 py-0.5 rounded-full mb-1">Sudirman</span>
      </div>
    </div>
  </div>
</div>
<!-- Kenapa Pilih Pixel Barbershop - Modern Layout (Visual Card Besar) -->
<div class="p-4">
  <h3 class="text-2xl font-bold text-white mb-6">Service Berkualitas</h3>
  <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
    <!-- Handuk Steril -->
    <div class="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 group border-2 border-transparent hover:border-blue-500 flex flex-col overflow-hidden p-0">
      <div class="w-full h-40 sm:h-44 md:h-48 overflow-hidden">
        <img src="./assets/handuk1.jpg" alt="Handuk Steril" class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
      </div>
      <div class="flex-1 flex flex-col items-center justify-center px-4 py-4">
        <h4 class="text-gray-900 font-bold text-base mb-2 text-center">Handuk Selalu Steril</h4>
        <p class="text-sm text-gray-600 text-center leading-snug">
          Dicuci bersih dan tidak dijemur langsung di bawah sinar matahari agar tetap higienis.
        </p>
      </div>
    </div>
    <!-- Silet Sekali Pakai -->
    <div class="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 group border-2 border-transparent hover:border-green-500 flex flex-col overflow-hidden p-0">
      <div class="w-full h-40 sm:h-44 md:h-48 overflow-hidden">
        <img src="./assets/silet.jpg" alt="Silet Sekali Pakai" class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
      </div>
      <div class="flex-1 flex flex-col items-center justify-center px-4 py-4">
        <h4 class="text-gray-900 font-bold text-base mb-2 text-center">Silet Sekali Pakai</h4>
        <p class="text-sm text-gray-600 text-center leading-snug">
          Satu silet untuk satu pelanggan. Langsung dibuang setelah digunakan, demi keamanan dan kenyamanan.
        </p>
      </div>
    </div>
    <!-- Fasilitas Nyaman -->
    <div class="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 group border-2 border-transparent hover:border-blue-500 flex flex-col overflow-hidden p-0">
      <div class="w-full h-40 sm:h-44 md:h-48 overflow-hidden">
        <img src="./assets/tempat .jpeg" alt="Fasilitas Nyaman" class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
      </div>
      <div class="flex-1 flex flex-col items-center justify-center px-4 py-4">
        <h4 class="text-gray-900 font-bold text-base mb-2 text-center">Fasilitas Nyaman</h4>
        <p class="text-sm text-gray-600 text-center leading-snug">
          Ruangan ber-AC, sofa tunggu nyaman, dan suasana yang bersih serta modern.
        </p>
      </div>
    </div>
    <!-- Barberman Profesional -->
    <div class="bg-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 group border-2 border-transparent hover:border-green-500 flex flex-col overflow-hidden p-0">
      <div class="w-full h-40 sm:h-44 md:h-48 overflow-hidden">
        <img src="./assets/cukur.jpeg" alt="Barberman Profesional" class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
      </div>
      <div class="flex-1 flex flex-col items-center justify-center px-4 py-4">
        <h4 class="text-gray-900 font-bold text-base mb-2 text-center">Barberman Profesional</h4>
        <p class="text-sm text-gray-600 text-center leading-snug">
          Ditangani oleh barber berpengalaman, ramah, dan memahami berbagai tren gaya rambut.
        </p>
      </div>
    </div>
  </div>
</div>
<!-- Bottom Navigation Modern Style -->
<nav class="fixed bottom-0 left-0 right-0 bg-white shadow-lg flex justify-around py-3 z-50 border-t border-gray-100">
  <a href="index.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-home text-lg mb-1"></i>
    <span>Home</span>
  </a>
  <a href="service.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'treatment.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-cut text-lg mb-1"></i>
    <span>Services</span>
  </a>
  <a href="produk.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'produk.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-box-open text-lg mb-1"></i>
    <span>Produk</span>
  </a>
  <a href="location.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'location.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-calendar-alt text-lg mb-1"></i>
    <span>Book</span>
  </a>
</nav>
<script>
  // Ambil nama dan foto dari localStorage
  window.addEventListener('DOMContentLoaded', function() {
    const savedNama = localStorage.getItem('profileNama');
    if (savedNama) document.getElementById('profile-name-index').innerText = savedNama;

    const savedImg = localStorage.getItem('profileImg');
    if (savedImg) {
      document.getElementById('profile-img-index').src = savedImg;
      document.getElementById('profile-img-index').style.display = 'block';
      document.getElementById('profile-icon-index').style.display = 'none';
    } else {
      document.getElementById('profile-img-index').style.display = 'none';
      document.getElementById('profile-icon-index').style.display = 'block';
    }
  });
</script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const profileImg = document.getElementById('profile-img-index');
    const profileIcon = document.getElementById('profile-icon-index');
    const profileName = document.getElementById('profile-name-index');

    // Function to fetch profile data
    function fetchProfileData() {
      fetch('api/get_profile.php', {
        credentials: 'same-origin' // Include cookies/session
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          if (data.success && data.data) {
            // Update profile picture if available
            if (data.data.profile_picture_url) {
              profileImg.src = data.data.profile_picture_url;
              profileImg.style.display = 'block';
              profileIcon.style.display = 'none';
            } else {
              profileImg.style.display = 'none';
              profileIcon.style.display = 'block';
            }
            // Update profile name
            profileName.textContent = data.data.nama_pembeli || 'Pengguna';
          } else {
            console.log('Profile fetch info:', data.message);
            profileName.textContent = 'Pengguna';
            profileImg.style.display = 'none';
            profileIcon.style.display = 'block';
          }
        })
        .catch(error => {
          console.error('Error fetching profile data:', error);
          profileName.textContent = 'Pengguna';
          profileImg.style.display = 'none';
          profileIcon.style.display = 'block';
        });
    }

    // Fetch profile data on page load
    fetchProfileData();

    // Auto-refresh profile data every 10 seconds to catch profile picture updates
    setInterval(fetchProfileData, 10000);

    // Notification dropdown functions (define early to avoid reference errors)
    window.toggleNotificationDropdown = function() {
      const dropdown = document.getElementById('notificationDropdown');
      const isHidden = dropdown.classList.contains('hidden');

      if (isHidden) {
        dropdown.classList.remove('hidden');
        loadNotificationDropdown();
        markNotificationsAsRead();
      } else {
        dropdown.classList.add('hidden');
      }
    };

    function loadNotificationDropdown() {
      const notificationList = document.getElementById('notificationList');

      if (window.latestNotifications && window.latestNotifications.length > 0) {
        let html = '';
        window.latestNotifications.forEach(notif => {
          const date = new Date(notif.created_at).toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'short',
            hour: '2-digit',
            minute: '2-digit'
          });

          html += `
            <div class="p-3 border-b border-gray-100 hover:bg-gray-50 ${!notif.is_read ? 'bg-blue-50' : ''}">
              <p class="text-sm text-gray-800 mb-1">${notif.message}</p>
              <p class="text-xs text-gray-500">${date}</p>
              ${notif.order_id ? `<a href="konfirmasi_pembayaran.php?order_id=${notif.order_id}" class="text-xs text-blue-600 hover:text-blue-800">Lihat Pesanan</a>` : ''}
            </div>
          `;
        });
        notificationList.innerHTML = html;
      } else {
        notificationList.innerHTML = `
          <div class="p-4 text-center text-gray-500">
            <i class="fas fa-bell text-2xl mb-2"></i>
            <p>Belum ada notifikasi</p>
          </div>
        `;
      }
    }

    async function markNotificationsAsRead() {
      try {
        const response = await fetch('api/mark_notifications_read.php', {
          method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
          // Update badge to hide it
          const badge = document.getElementById('notificationBadge');
          if (badge) {
            badge.style.display = 'none';
          }
        }
      } catch (error) {
        console.error('Error marking notifications as read:', error);
      }
    }

    // Notification Bell Logic
    async function fetchActiveOrdersCount() {
      try {
        const response = await fetch('api/get_active_orders_count.php', {
          credentials: 'same-origin' // Include cookies/session
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          const count = data.count;
          const notificationBadge = document.querySelector('#notificationBadge');

          if (count > 0) {
            if (notificationBadge) {
              notificationBadge.textContent = count;
              notificationBadge.style.display = 'flex';
            }
          } else {
            if (notificationBadge) {
              notificationBadge.style.display = 'none';
            }
          }

        } else {
          console.log('Active orders fetch info:', data.message);
          // Don't show error for "not logged in" as it's normal for guest users
          if (!data.message.includes('not logged in')) {
            console.error('Error fetching active orders:', data.message);
          }
        }
      } catch (error) {
        console.error('Network error fetching active orders:', error);
      }
    }



    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      const container = document.getElementById('notificationContainer');
      const dropdown = document.getElementById('notificationDropdown');

      if (!container.contains(event.target)) {
        dropdown.classList.add('hidden');
      }
    });

    // Function to handle keranjang navigation
    window.goToKeranjang = function() {
      // Check if user is logged in
      <?php if (isset($_SESSION['user_id'])): ?>
        // User is logged in, go to keranjang
        window.location.href = 'keranjang.php';
      <?php else: ?>
        // User not logged in, go to login with return URL
        window.location.href = 'login.php?return_url=' + encodeURIComponent('keranjang.php');
      <?php endif; ?>
    };

    // Function to handle profile navigation
    window.goToProfile = function() {
      // Check if user is logged in
      <?php if (isset($_SESSION['user_id'])): ?>
        // User is logged in, go to profile
        window.location.href = 'profil.php';
      <?php else: ?>
        // User not logged in, go to login with return URL
        window.location.href = 'login.php?return_url=' + encodeURIComponent('profil.php');
      <?php endif; ?>
    };

    // Fetch active orders count on page load and every 30 seconds
    fetchActiveOrdersCount();
    setInterval(fetchActiveOrdersCount, 30000); // Poll every 30 seconds
  });
</script>
</body>
</html>
