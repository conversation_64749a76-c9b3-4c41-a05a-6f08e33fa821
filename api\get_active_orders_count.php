<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../admin/db_config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$response = [
    'success' => false,
    'count' => 0,
    'orders' => []
];

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $response['message'] = 'User not logged in';
    echo json_encode($response);
    exit();
}

$user_id = $_SESSION['user_id'];

try {
    // Get active orders count (confirmed to shipped)
    $count_stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM orders 
        WHERE pembeli_id = ? 
        AND order_status IN ('confirmed', 'processing', 'shipped')
    ");
    $count_stmt->execute([$user_id]);
    $count_result = $count_stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get recent active orders (last 3)
    $orders_stmt = $conn->prepare("
        SELECT o.id, o.order_status, o.total_amount, o.created_at
        FROM orders o
        WHERE o.pembeli_id = ? 
        AND o.order_status IN ('confirmed', 'processing', 'shipped')
        ORDER BY o.created_at DESC 
        LIMIT 3
    ");
    $orders_stmt->execute([$user_id]);
    $orders = $orders_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $response['success'] = true;
    $response['count'] = $count_result['count'];
    $response['orders'] = $orders;
    
} catch (PDOException $e) {
    $response['message'] = 'Database error: ' . $e->getMessage();
    error_log("Error in get_active_orders_count.php: " . $e->getMessage());
}

echo json_encode($response);
?>
