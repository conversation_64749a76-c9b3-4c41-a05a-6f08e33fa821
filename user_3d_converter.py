import cv2
import numpy as np
import json
import sys
import os
import base64
from PIL import Image, ImageFilter, ImageEnhance

class User3DConverter:
    def __init__(self):
        pass
    
    def create_3d_effect(self, image):
        """Create 3D effect like the reference image"""
        # Convert to PIL for better processing
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # Strong contrast enhancement for 3D pop effect
        enhancer = ImageEnhance.Contrast(pil_image)
        pil_image = enhancer.enhance(1.8)  # Increased from 1.3
        
        # Sharp details like the reference
        enhancer = ImageEnhance.Sharpness(pil_image)
        pil_image = enhancer.enhance(1.6)  # Increased from 1.2
        
        # Enhance saturation for vibrant 3D look
        enhancer = ImageEnhance.Color(pil_image)
        pil_image = enhancer.enhance(1.4)
        
        # Create dramatic depth map
        gray = pil_image.convert('L')
        depth_map = np.array(gray)
        
        # Enhanced depth mapping for 3D effect
        depth_map = (depth_map / 255.0) * 0.6  # Increased from 0.3
        
        # Apply 3D transformation with stronger effect
        result_array = np.array(pil_image)
        
        # Dramatic lighting for 3D appearance
        for i in range(3):  # RGB channels
            result_array[:, :, i] = np.clip(
                result_array[:, :, i] * (1 + depth_map * 1.5), 0, 255
            )
        
        return cv2.cvtColor(result_array, cv2.COLOR_RGB2BGR)
    
    def add_3d_lighting(self, image):
        """Add dramatic 3D lighting like reference image"""
        h, w = image.shape[:2]
        
        # Multiple light sources for 3D effect
        # Main light from top-left (like reference)
        light_x1, light_y1 = w * 0.25, h * 0.15
        # Secondary light from right for rim lighting
        light_x2, light_y2 = w * 0.75, h * 0.3
        
        # Create lighting gradients
        y_coords, x_coords = np.ogrid[:h, :w]
        
        # Main light gradient
        distances1 = np.sqrt((x_coords - light_x1)**2 + (y_coords - light_y1)**2)
        max_distance1 = np.sqrt(w**2 + h**2)
        light_intensity1 = 1 - (distances1 / max_distance1) * 0.4
        light_intensity1 = np.clip(light_intensity1, 0.6, 1.2)
        
        # Secondary light gradient
        distances2 = np.sqrt((x_coords - light_x2)**2 + (y_coords - light_y2)**2)
        max_distance2 = np.sqrt(w**2 + h**2)
        light_intensity2 = 1 - (distances2 / max_distance2) * 0.2
        light_intensity2 = np.clip(light_intensity2, 0.8, 1.0)
        
        # Combine lighting effects
        combined_lighting = (light_intensity1 + light_intensity2 * 0.3) / 1.3
        
        # Apply dramatic lighting
        result = image.copy().astype(np.float32)
        for i in range(3):
            result[:, :, i] *= combined_lighting
        
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def add_3d_shadow(self, image):
        """Add dramatic 3D shadow effect like reference"""
        h, w = image.shape[:2]
        
        # Create multiple shadow layers for depth
        shadow1 = np.zeros_like(image)
        shadow2 = np.zeros_like(image)
        
        # Primary shadow (stronger offset)
        if h > 4 and w > 4:
            shadow1[4:, 4:] = image[:-4, :-4]  # Larger offset
            shadow1 = (shadow1 * 0.2).astype(np.uint8)  # Darker shadow
        
        # Secondary shadow (subtle)
        if h > 2 and w > 2:
            shadow2[2:, 2:] = image[:-2, :-2]  # Smaller offset
            shadow2 = (shadow2 * 0.4).astype(np.uint8)  # Lighter shadow
        
        # Blend shadows with original for 3D depth
        result = cv2.addWeighted(image, 0.7, shadow1, 0.15, 0)
        result = cv2.addWeighted(result, 0.9, shadow2, 0.1, 0)
        
        return result
    
    def add_final_3d_enhancement(self, image):
        """Final enhancement to match reference 3D appearance"""
        # Convert to PIL for final touches
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # Add slight glow effect for 3D pop
        glow = pil_image.filter(ImageFilter.GaussianBlur(2))
        
        # Enhance brightness slightly
        enhancer = ImageEnhance.Brightness(pil_image)
        pil_image = enhancer.enhance(1.1)
        
        # Blend with glow for 3D effect
        result_array = np.array(pil_image).astype(np.float32)
        glow_array = np.array(glow).astype(np.float32)
        
        # Subtle glow blend
        final_result = result_array * 0.85 + glow_array * 0.15
        final_result = np.clip(final_result, 0, 255).astype(np.uint8)
        
        return cv2.cvtColor(final_result, cv2.COLOR_RGB2BGR)
    
    def convert_user_to_3d(self, image_path):
        """Convert user image to 3D appearance"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                return {'success': False, 'error': 'Failed to load image'}
            
            # Apply 3D effects in sequence for maximum impact
            image_3d = self.create_3d_effect(image)
            image_3d = self.add_3d_lighting(image_3d)
            image_3d = self.add_3d_shadow(image_3d)
            
            # Final enhancement for 3D pop effect
            image_3d = self.add_final_3d_enhancement(image_3d)
            
            # Create output directory
            output_dir = 'uploads/3d_results/'
            os.makedirs(output_dir, exist_ok=True)
            
            # Save result
            output_path = os.path.join(output_dir, f'user_3d_{hash(image_path)}.png')
            cv2.imwrite(output_path, image_3d)
            
            # Convert to base64
            _, buffer = cv2.imencode('.png', image_3d)
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return {
                'success': True,
                'result_url': output_path,
                'result_base64': f"data:image/png;base64,{img_base64}"
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

def main():
    if len(sys.argv) < 2:
        print(json.dumps({'success': False, 'error': 'No image path provided'}))
        return
    
    image_path = sys.argv[1]
    
    converter = User3DConverter()
    result = converter.convert_user_to_3d(image_path)
    
    print(json.dumps(result))

if __name__ == "__main__":
    main()