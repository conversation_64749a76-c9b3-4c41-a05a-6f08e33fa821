<?php
session_start();
require_once 'db_config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

try {
    // Get all products
    $stmt = $conn->query("SELECT p.*, k.nama_kategori 
                        FROM produk p 
                        LEFT JOIN kategori_produk k ON p.kategori_id = k.id");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Generate HTML for product list
    $html = '<table class="min-w-full">
                <thead class="bg-gray-100">
                    <tr class="border-b border-gray-300">
                        <th class="border border-gray-300 px-4 py-2 text-left font-bold text-gray-700">ID</th>
                        <th class="border border-gray-300 px-4 py-2 text-left font-bold text-gray-700">Gambar</th>
                        <th class="border border-gray-300 px-4 py-2 text-left font-bold text-gray-700">Nama Produk</th>
                        <th class="border border-gray-300 px-4 py-2 text-left font-bold text-gray-700">Kategori</th>
                        <th class="border border-gray-300 px-4 py-2 text-left font-bold text-gray-700">Harga</th>
                        <th class="border border-gray-300 px-4 py-2 text-left font-bold text-gray-700">Aksi</th>
                    </tr>
                </thead>
                <tbody class="bg-white">
    ';

    foreach ($products as $product) {
        $html .= '<tr class="hover:bg-gray-100 border-b border-gray-300">
                    <td class="border border-gray-300 px-4 py-2 text-sm text-gray-700 whitespace-nowrap">' . htmlspecialchars($product['id']) . '</td>
                    <td class="border border-gray-300 px-4 py-2 whitespace-nowrap">
                        <img src="uploads/' . htmlspecialchars($product['gambar_utama']) . '" alt="Product Image" class="w-16 h-16 object-cover rounded">
                    </td>
                    <td class="border border-gray-300 px-4 py-2 text-sm text-gray-700 whitespace-nowrap">' . htmlspecialchars($product['nama_produk']) . '</td>
                    <td class="border border-gray-300 px-4 py-2 text-sm text-gray-700 whitespace-nowrap">' . htmlspecialchars($product['nama_kategori']) . '</td>
                    <td class="border border-gray-300 px-4 py-2 text-sm text-gray-700 whitespace-nowrap">Rp ' . number_format($product['harga'], 0, ',', '.') . '</td>
                    <td class="border border-gray-300 px-4 py-2 whitespace-nowrap">
                        <a href="edit_produk.php?id=' . $product['id'] . '" class="text-indigo-600 hover:text-indigo-900" title="Edit Product">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="hapus_produk.php?id=' . $product['id'] . '" class="text-red-600 hover:text-red-900 ml-2" title="Delete Product" onclick="return confirm(\"Apakah Anda yakin ingin menghapus produk ini?\")">
                            <i class="fas fa-trash"></i>
                        </a>
                    </td>
                  </tr>';
    }

    $html .= '</tbody>
            </table>';

    echo json_encode(['success' => true, 'html' => $html]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
