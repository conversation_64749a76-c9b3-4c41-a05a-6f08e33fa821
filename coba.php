<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Hair Transfer System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        .hair-transfer-btn {
            transition: all 0.3s ease;
        }
        
        .glow-effect {
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.7);
        }
        
        .pulse-animation {
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.03); }
            100% { transform: scale(1); }
        }
        
        .progress-bar {
            transition: width 0.5s ease;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header Section -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">3D Hair Transfer System</h1>
            <p class="text-gray-600">Terapkan gaya rambut dari model ke avatar Anda</p>
        </div>

        <!-- Control Panel -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Panel Kontrol</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button id="uploadBtn" class="hair-transfer-btn bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium">
                    📷 Upload Foto Anda
                </button>
                <button id="analyzeBtn" class="hair-transfer-btn bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium" disabled>
                    🔍 Analisis Wajah
                </button>
                <button id="resetBtn" class="hair-transfer-btn bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium">
                    🔄 Reset
                </button>
            </div>
            
            <!-- Progress Bar -->
            <div id="progressContainer" class="mt-4 hidden">
                <div class="bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="progress-bar bg-blue-500 h-2 rounded-full" style="width: 0%"></div>
                </div>
                <p id="progressText" class="text-sm text-gray-600 mt-1">Memulai proses...</p>
            </div>
        </div>

        <!-- User Photo Upload Section -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Foto Anda</h2>
            <div id="userPhotoContainer" class="flex justify-center">
                <div class="w-48 h-48 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                    <div class="text-center">
                        <div class="text-4xl mb-2">📷</div>
                        <p class="text-gray-500">Upload foto Anda</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 3D Face Modeling Preview -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">3D Hair Transfer Preview</h2>
                    <div class="flex gap-2">
                        <button id="autoTransferBtn" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm">
                            ✨ Auto Transfer
                        </button>
                        <button id="customizeBtn" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm">
                            🎨 Customize
                        </button>
                    </div>
                </div>
                
                <div id="3dPreviewContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
                    <!-- Hair Style 1: Buzz Cut -->
                    <div class="model-card bg-gray-50 rounded-lg shadow-md p-4 border-2 border-transparent hover:border-blue-300">
                        <div class="flex justify-around w-full mb-4">
                            <div class="flex flex-col items-center">
                                <div class="w-28 h-28 rounded-lg overflow-hidden border-2 border-green-500 relative">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face" alt="Buzz Cut Model" class="w-full h-full object-cover">
                                    <div class="absolute inset-0 bg-green-500 bg-opacity-20"></div>
                                </div>
                                <p class="text-gray-600 text-sm mt-1">Model Referensi</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-28 h-28 rounded-lg overflow-hidden border-2 border-blue-500 relative" id="preview1">
                                    <div class="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                        <span class="text-gray-500 text-xs text-center">Preview<br/>3D Model</span>
                                    </div>
                                </div>
                                <p class="text-gray-600 text-sm mt-1">Hasil Preview</p>
                            </div>
                        </div>
                        <div class="text-center">
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">Buzz Cut</h3>
                            <div class="flex justify-center gap-2 mb-3">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Pendek</span>
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Modern</span>
                            </div>
                            <button class="hair-transfer-btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm w-full" onclick="transferHair('buzzcut')">
                                🔄 Terapkan Gaya Ini
                            </button>
                        </div>
                    </div>

                    <!-- Hair Style 2: Slick Back -->
                    <div class="model-card bg-gray-50 rounded-lg shadow-md p-4 border-2 border-transparent hover:border-blue-300">
                        <div class="flex justify-around w-full mb-4">
                            <div class="flex flex-col items-center">
                                <div class="w-28 h-28 rounded-lg overflow-hidden border-2 border-green-500 relative">
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face" alt="Slick Back Model" class="w-full h-full object-cover">
                                    <div class="absolute inset-0 bg-green-500 bg-opacity-20"></div>
                                </div>
                                <p class="text-gray-600 text-sm mt-1">Model Referensi</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-28 h-28 rounded-lg overflow-hidden border-2 border-blue-500 relative" id="preview2">
                                    <div class="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                        <span class="text-gray-500 text-xs text-center">Preview<br/>3D Model</span>
                                    </div>
                                </div>
                                <p class="text-gray-600 text-sm mt-1">Hasil Preview</p>
                            </div>
                        </div>
                        <div class="text-center">
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">Slick Back</h3>
                            <div class="flex justify-center gap-2 mb-3">
                                <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">Klasik</span>
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Formal</span>
                            </div>
                            <button class="hair-transfer-btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm w-full" onclick="transferHair('slickback')">
                                🔄 Terapkan Gaya Ini
                            </button>
                        </div>
                    </div>

                    <!-- Hair Style 3: Pompadour -->
                    <div class="model-card bg-gray-50 rounded-lg shadow-md p-4 border-2 border-transparent hover:border-blue-300">
                        <div class="flex justify-around w-full mb-4">
                            <div class="flex flex-col items-center">
                                <div class="w-28 h-28 rounded-lg overflow-hidden border-2 border-green-500 relative">
                                    <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face" alt="Pompadour Model" class="w-full h-full object-cover">
                                    <div class="absolute inset-0 bg-green-500 bg-opacity-20"></div>
                                </div>
                                <p class="text-gray-600 text-sm mt-1">Model Referensi</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-28 h-28 rounded-lg overflow-hidden border-2 border-blue-500 relative" id="preview3">
                                    <div class="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                        <span class="text-gray-500 text-xs text-center">Preview<br/>3D Model</span>
                                    </div>
                                </div>
                                <p class="text-gray-600 text-sm mt-1">Hasil Preview</p>
                            </div>
                        </div>
                        <div class="text-center">
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">Pompadour</h3>
                            <div class="flex justify-center gap-2 mb-3">
                                <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">Volume</span>
                                <span class="px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">Trendy</span>
                            </div>
                            <button class="hair-transfer-btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm w-full" onclick="transferHair('pompadour')">
                                🔄 Terapkan Gaya Ini
                            </button>
                        </div>
                    </div>

                    <!-- Hair Style 4: Undercut -->
                    <div class="model-card bg-gray-50 rounded-lg shadow-md p-4 border-2 border-transparent hover:border-blue-300">
                        <div class="flex justify-around w-full mb-4">
                            <div class="flex flex-col items-center">
                                <div class="w-28 h-28 rounded-lg overflow-hidden border-2 border-green-500 relative">
                                    <img src="https://images.unsplash.com/photo-1463453091185-61582044d556?w=150&h=150&fit=crop&crop=face" alt="Undercut Model" class="w-full h-full object-cover">
                                    <div class="absolute inset-0 bg-green-500 bg-opacity-20"></div>
                                </div>
                                <p class="text-gray-600 text-sm mt-1">Model Referensi</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-28 h-28 rounded-lg overflow-hidden border-2 border-blue-500 relative" id="preview4">
                                    <div class="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                        <span class="text-gray-500 text-xs text-center">Preview<br/>3D Model</span>
                                    </div>
                                </div>
                                <p class="text-gray-600 text-sm mt-1">Hasil Preview</p>
                            </div>
                        </div>
                        <div class="text-center">
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">Undercut</h3>
                            <div class="flex justify-center gap-2 mb-3">
                                <span class="px-2 py-1 bg-teal-100 text-teal-800 text-xs rounded-full">Edgy</span>
                                <span class="px-2 py-1 bg-pink-100 text-pink-800 text-xs rounded-full">Kontras</span>
                            </div>
                            <button class="hair-transfer-btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm w-full" onclick="transferHair('undercut')">
                                🔄 Terapkan Gaya Ini
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Customization Panel -->
                <div id="customizationPanel" class="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 hidden">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🎨 Panel Kustomisasi</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Panjang Rambut</label>
                            <input type="range" id="hairLength" min="1" max="10" value="5" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>Pendek</span>
                                <span>Panjang</span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Volume Rambut</label>
                            <input type="range" id="hairVolume" min="1" max="10" value="5" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>Tipis</span>
                                <span>Tebal</span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Warna Rambut</label>
                            <input type="color" id="hairColor" value="#8B4513" class="w-full h-10 rounded-lg border border-gray-300">
                        </div>
                    </div>
                    <div class="flex justify-center mt-6 gap-4">
                        <button id="applyCustomization" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg">
                            ✅ Terapkan Perubahan
                        </button>
                        <button id="resetCustomization" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg">
                            🔄 Reset Kustomisasi
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="mt-8 bg-white rounded-xl shadow-lg p-6 hidden">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">📊 Hasil Hair Transfer</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="text-center">
                    <h3 class="text-lg font-semibold text-gray-700 mb-2">Foto Asli</h3>
                    <div id="originalImage" class="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span class="text-gray-500">Foto asli akan ditampilkan di sini</span>
                    </div>
                </div>
                <div class="text-center">
                    <h3 class="text-lg font-semibold text-gray-700 mb-2">Hasil 3D Model</h3>
                    <div id="resultImage" class="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span class="text-gray-500">Hasil 3D model akan ditampilkan di sini</span>
                    </div>
                </div>
            </div>
            <div class="flex justify-center mt-6 gap-4">
                <button id="downloadResult" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg">
                    💾 Download Hasil
                </button>
                <button id="shareResult" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg">
                    📤 Share
                </button>
            </div>
        </div>
    </div>

    <!-- Modal untuk Upload -->
    <div id="uploadModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Upload Foto Anda</h3>
            <input type="file" id="fileInput" accept="image/*" class="w-full p-2 border border-gray-300 rounded-lg mb-4">
            <div class="flex justify-end gap-2">
                <button id="cancelUpload" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg">Batal</button>
                <button id="confirmUpload" class="px-4 py-2 bg-blue-500 text-white rounded-lg">Upload</button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let userPhoto = null;
        let currentHairStyle = null;
        let isProcessing = false;
        let faceAnalysisComplete = false;
        let scene, camera, renderer, model, controls;
        let hairModel = null;
        let faceModel = null;

        // Hair styles data with 3D parameters
        const hairStyles = {
            buzzcut: {
                name: "Buzz Cut",
                description: "Potongan rambut pendek dan rapi",
                params: { 
                    length: 2, 
                    volume: 3, 
                    texture: "smooth",
                    color: "#2C1810",
                    segments: 8,
                    height: 0.5
                }
            },
            slickback: {
                name: "Slick Back",
                description: "Rambut disisir ke belakang dengan gel",
                params: { 
                    length: 6, 
                    volume: 5, 
                    texture: "sleek",
                    color: "#1A0F0A",
                    segments: 12,
                    height: 1.2
                }
            },
            pompadour: {
                name: "Pompadour",
                description: "Rambut dengan volume tinggi di bagian depan",
                params: { 
                    length: 7, 
                    volume: 8, 
                    texture: "voluminous",
                    color: "#2C1810",
                    segments: 16,
                    height: 2.0
                }
            },
            undercut: {
                name: "Undercut",
                description: "Rambut pendek di samping, panjang di atas",
                params: { 
                    length: 5, 
                    volume: 6, 
                    texture: "contrasted",
                    color: "#1A0F0A",
                    segments: 10,
                    height: 1.5
                }
            }
        };

        // Notification system
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(n => n.remove());
            
            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                info: 'bg-blue-500'
            };
            
            const notification = document.createElement('div');
            notification.className = `notification fixed top-4 right-4 ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Initialize Three.js scene
        function initThreeJS() {
            try {
                // Create scene
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0xf0f0f0);

                // Create camera
                camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                camera.position.set(0, 1.5, 3);

                // Create renderer
                renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(window.innerWidth * 0.8, window.innerHeight * 0.6);
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;

                // Add renderer to container
                const container = document.getElementById('3dPreviewContainer');
                if (container) {
                    container.appendChild(renderer.domElement);
                }

                // Add lights
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
                scene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(5, 5, 5);
                directionalLight.castShadow = true;
                scene.add(directionalLight);

                // Add orbit controls
                controls = new THREE.OrbitControls(camera, renderer.domElement);
                controls.enableDamping = true;
                controls.dampingFactor = 0.05;
                controls.minDistance = 2;
                controls.maxDistance = 5;
                controls.maxPolarAngle = Math.PI / 2;

                // Create basic head model
                createHeadModel();

                // Start animation loop
                animate();

                // Handle window resize
                window.addEventListener('resize', onWindowResize, false);

            } catch (error) {
                console.error('Error initializing Three.js:', error);
                showNotification('Terjadi kesalahan saat menginisialisasi tampilan 3D', 'error');
            }
        }

        // Create basic head model
        function createHeadModel() {
            try {
                // Create head geometry
                const headGeometry = new THREE.SphereGeometry(1, 32, 32);
                const headMaterial = new THREE.MeshPhongMaterial({
                    color: 0xffdbac,
                    specular: 0x111111,
                    shininess: 30
                });
                faceModel = new THREE.Mesh(headGeometry, headMaterial);
                faceModel.castShadow = true;
                faceModel.receiveShadow = true;
                scene.add(faceModel);

                // Create basic hair placeholder
                createHairModel('buzzcut');
            } catch (error) {
                console.error('Error creating head model:', error);
            }
        }

        // Create hair model based on style
        function createHairModel(styleKey) {
            try {
                if (hairModel) {
                    scene.remove(hairModel);
                }

                const style = hairStyles[styleKey];
                if (!style) return;

                // Create hair geometry based on style
                let hairGeometry;
                switch (styleKey) {
                    case 'buzzcut':
                        hairGeometry = createBuzzcutGeometry(style.params);
                        break;
                    case 'slickback':
                        hairGeometry = createSlickbackGeometry(style.params);
                        break;
                    case 'pompadour':
                        hairGeometry = createPompadourGeometry(style.params);
                        break;
                    case 'undercut':
                        hairGeometry = createUndercutGeometry(style.params);
                        break;
                    default:
                        return;
                }

                // Create hair material
                const hairMaterial = new THREE.MeshPhongMaterial({
                    color: style.params.color,
                    specular: 0x111111,
                    shininess: 30
                });

                // Create hair mesh
                hairModel = new THREE.Mesh(hairGeometry, hairMaterial);
                hairModel.castShadow = true;
                hairModel.receiveShadow = true;
                hairModel.position.y = 0.1;
                scene.add(hairModel);

            } catch (error) {
                console.error('Error creating hair model:', error);
            }
        }

        // Create geometry for different hair styles
        function createBuzzcutGeometry(params) {
            const geometry = new THREE.SphereGeometry(1.05, params.segments, params.segments);
            geometry.scale(1, 0.3, 1);
            return geometry;
        }

        function createSlickbackGeometry(params) {
            const geometry = new THREE.CylinderGeometry(1, 1, params.height, params.segments);
            geometry.scale(1, 1, 0.8);
            return geometry;
        }

        function createPompadourGeometry(params) {
            const geometry = new THREE.SphereGeometry(1.1, params.segments, params.segments);
            geometry.scale(1, 1.2, 0.8);
            return geometry;
        }

        function createUndercutGeometry(params) {
            const geometry = new THREE.CylinderGeometry(1, 1, params.height, params.segments);
            geometry.scale(1, 1, 0.7);
            return geometry;
        }

        // Animation loop
        function animate() {
            try {
                requestAnimationFrame(animate);
                
                if (controls) {
                    controls.update();
                }

                if (hairModel) {
                    hairModel.rotation.y += 0.005;
                }

                if (faceModel) {
                    faceModel.rotation.y += 0.005;
                }

                if (renderer && scene && camera) {
                    renderer.render(scene, camera);
                }
            } catch (error) {
                console.error('Error in animation:', error);
            }
        }

        // Handle window resize
        function onWindowResize() {
            try {
                if (camera && renderer) {
                    camera.aspect = window.innerWidth / window.innerHeight;
                    camera.updateProjectionMatrix();
                    renderer.setSize(window.innerWidth * 0.8, window.innerHeight * 0.6);
                }
            } catch (error) {
                console.error('Error handling window resize:', error);
            }
        }

        // Update hair style
        function updateHairStyle(styleKey) {
            try {
                if (!faceAnalysisComplete) {
                    showNotification('Silakan lakukan analisis wajah terlebih dahulu!', 'error');
                    return;
                }

                createHairModel(styleKey);
                currentHairStyle = styleKey;

                // Update preview
                const previewId = `preview${Object.keys(hairStyles).indexOf(styleKey) + 1}`;
                updatePreview(previewId, styleKey);

                showNotification(`Gaya ${hairStyles[styleKey].name} berhasil diterapkan!`, 'success');
            } catch (error) {
                console.error('Error updating hair style:', error);
                showNotification('Terjadi kesalahan saat mengubah gaya rambut', 'error');
            }
        }

        // Apply customization
        function applyCustomization() {
            try {
                if (!currentHairStyle) {
                    showNotification('Silakan pilih gaya rambut terlebih dahulu!', 'error');
                    return;
                }

                const length = document.getElementById('hairLength')?.value || 5;
                const volume = document.getElementById('hairVolume')?.value || 5;
                const color = document.getElementById('hairColor')?.value || '#8B4513';

                // Update hair style parameters
                hairStyles[currentHairStyle].params.length = parseInt(length);
                hairStyles[currentHairStyle].params.volume = parseInt(volume);
                hairStyles[currentHairStyle].params.color = color;

                // Update 3D model
                createHairModel(currentHairStyle);

                showNotification(`Kustomisasi diterapkan: Panjang ${length}/10, Volume ${volume}/10`, 'success');
            } catch (error) {
                console.error('Error applying customization:', error);
                showNotification('Terjadi kesalahan saat menerapkan kustomisasi', 'error');
            }
        }

        // Reset all
        function resetAll() {
            try {
                userPhoto = null;
                currentHairStyle = null;
                isProcessing = false;
                faceAnalysisComplete = false;

                // Reset 3D scene
                if (scene) {
                    while(scene.children.length > 0) { 
                        scene.remove(scene.children[0]); 
                    }
                }
                createHeadModel();

                // Reset UI elements
                const userPhotoContainer = document.getElementById('userPhotoContainer');
                if (userPhotoContainer) {
                    userPhotoContainer.innerHTML = `
                        <div class="w-48 h-48 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                            <div class="text-center">
                                <div class="text-4xl mb-2">📷</div>
                                <p class="text-gray-500">Upload foto Anda</p>
                            </div>
                        </div>
                    `;
                }

                // Reset all previews
                for (let i = 1; i <= 4; i++) {
                    const preview = document.getElementById(`preview${i}`);
                    if (preview) {
                        preview.classList.remove('glow-effect', 'pulse-animation');
                        preview.innerHTML = `
                            <div class="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                <span class="text-gray-500 text-xs text-center">Preview<br/>3D Model</span>
                            </div>
                        `;
                    }
                }

                // Hide results and customization
                const resultsSection = document.getElementById('resultsSection');
                const customizationPanel = document.getElementById('customizationPanel');
                if (resultsSection) resultsSection.classList.add('hidden');
                if (customizationPanel) customizationPanel.classList.add('hidden');

                // Reset buttons
                const analyzeBtn = document.getElementById('analyzeBtn');
                if (analyzeBtn) analyzeBtn.disabled = true;

                // Hide progress
                hideProgress();

                // Reset file input
                const fileInput = document.getElementById('fileInput');
                if (fileInput) fileInput.value = '';

                showNotification('Semua data telah direset!', 'info');
            } catch (error) {
                console.error('Error resetting all:', error);
                showNotification('Terjadi kesalahan saat mereset aplikasi', 'error');
            }
        }

        // Initialize app when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            initializeApp();
            initThreeJS();
        });
    </script>
</body>
</html>