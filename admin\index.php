<?php
session_start();
require_once 'db_config.php';
require_once 'functions_dashboard.php';

// Ambil data cabang
$cabang = getAllLocations($conn);

// Ambil data statistik per cabang
$statistik_cabang = [];
foreach ($cabang as $cb) {
    $id = $cb['id'];
    $booking_today = getBookingToday($conn, $id);
    $total_transaksi = getTotalTransaksi($conn, $id);
    $pendapatan = getPendapatan($conn, $id);
    $barber_aktif = getBarberAktif($conn, $id);
    $statistik_cabang[] = [
        'nama' => $cb['name'],
        'booking_today' => $booking_today,
        'total_transaksi' => $total_transaksi,
        'pendapatan' => $pendapatan,
        'barber_aktif' => $barber_aktif
    ];
}

// Ambil data untuk grafik (per bulan, per cabang)
$bulan = range(1, 12);
$grafik_pendapatan = [];
$grafik_transaksi = [];
foreach ($cabang as $cb) {
    $id = $cb['id'];
    $pendapatan_per_bulan = [];
    $transaksi_per_bulan = [];
    foreach ($bulan as $b) {
        $pendapatan = $conn->query("SELECT SUM(service_price) FROM bookings WHERE location_id=$id AND MONTH(booking_time)=$b AND status='Confirmed'")->fetchColumn();
        $transaksi = $conn->query("SELECT COUNT(*) FROM bookings WHERE location_id=$id AND MONTH(booking_time)=$b")->fetchColumn();
        $pendapatan_per_bulan[] = (int)$pendapatan;
        $transaksi_per_bulan[] = (int)$transaksi;
    }
    $grafik_pendapatan[$cb['name']] = $pendapatan_per_bulan;
    $grafik_transaksi[$cb['name']] = $transaksi_per_bulan;
}

// Ambil data produk terlaris per cabang
$stmt = $conn->query("SELECT p.nama_produk as nama, SUM(oi.quantity) as total
    FROM order_items oi
    JOIN produk p ON oi.product_id = p.id
    GROUP BY oi.product_id
    ORDER BY total DESC
    LIMIT 3");
$produk_terlaris = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Pie: Distribusi pelanggan berdasarkan layanan
$stmt = $conn->query("SELECT service_name, COUNT(*) as jumlah FROM bookings GROUP BY service_name");
$pie_layanan = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Donat: Kategori user (hanya admin)
$stmt = $conn->query("SELECT role, COUNT(*) as jumlah FROM admin GROUP BY role");
$donat_user = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Ranking layanan terlaris (global)
$stmt = $conn->query("SELECT service_name, COUNT(*) as jumlah FROM bookings GROUP BY service_name ORDER BY jumlah DESC LIMIT 5");
$layanan_terlaris = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Tabel aktivitas user terbaru
$stmt = $conn->query("SELECT b.user_name as nama, l.name as cabang, b.service_name, DATE_FORMAT(b.booking_time, '%Y-%m-%d %H:%i') as waktu, b.status FROM bookings b JOIN locations l ON b.location_id=l.id ORDER BY b.created_at DESC LIMIT 10");
$aktivitas_terbaru = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Riwayat transaksi terakhir
$stmt = $conn->query("SELECT l.name as cabang, b.user_name as nama, b.service_name, DATE_FORMAT(b.booking_time, '%Y-%m-%d %H:%i') as waktu, b.user_phone as no_hp, b.service_price as total_price, b.status FROM bookings b JOIN locations l ON b.location_id=l.id ORDER BY b.created_at DESC LIMIT 10");
$riwayat_transaksi = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Pencapaian Jumlah Pelanggan Booking per Cabang
$pelanggan_per_cabang = [];
foreach ($cabang as $cb) {
    $id = $cb['id'];
    // Hitung jumlah pelanggan unik (berdasarkan user_name dan user_phone) yang booking di cabang ini
    $stmt = $conn->prepare("SELECT COUNT(DISTINCT CONCAT(user_name, '-', user_phone)) as total FROM bookings WHERE location_id = ?");
    $stmt->execute([$id]);
    $total = $stmt->fetchColumn();
    $pelanggan_per_cabang[] = [
        'cabang' => $cb['name'],
        'total' => $total
    ];
}

// Pencapaian Booking Terbanyak per Cabang
$booking_terbanyak_per_cabang = [];
$stmt = $conn->query("
    SELECT l.name as cabang, COUNT(*) as total_booking
    FROM bookings b
    JOIN locations l ON b.location_id = l.id
    GROUP BY b.location_id
    ORDER BY total_booking DESC
");
$booking_terbanyak_per_cabang = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Ambil filter pencarian
$search = $_GET['search'] ?? '';
$date = $_GET['date'] ?? '';

// Query dasar
$query = "SELECT oi.id, p.nama_produk, oi.price_per_unit, oi.quantity, o.created_at, oi.status_pengiriman
          FROM order_items oi
          JOIN produk p ON oi.product_id = p.id
          JOIN orders o ON oi.order_id = o.id
          WHERE 1";

// Filter pencarian
$params = [];
if ($search) {
    $query .= " AND (p.nama_produk LIKE :search)";
    $params[':search'] = "%$search%";
}
if ($date) {
    $query .= " AND DATE(o.created_at) = :date";
    $params[':date'] = $date;
}
$query .= " ORDER BY o.created_at DESC";

// Eksekusi query PDO
$stmt = $conn->prepare($query);
$stmt->execute($params);
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Hitung total qty
$total_qty = 0;
if (count($result) > 0) {
    foreach ($result as $row) {
        $total_qty += (int)$row['quantity'];
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <title>Dashboard Admin Barbershop</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Heroicons CDN (outline) -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Font Awesome for sidebar icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen font-sans">

<div class="flex h-screen bg-gray-100">
    <!-- Sidebar -->
    <aside id="sidebar" class="w-72 bg-[#253046] text-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="px-6 py-6 border-b border-[#1e2533]">
            <span class="text-2xl font-bold tracking-wide">Admin Panel</span>
        </div>
        <nav class="mt-4">
            <ul class="space-y-1 px-2">
                <!-- Dashboard -->
                <li>
                    <a href="dashboard.php"
                       class="flex items-center gap-3 px-4 py-3 rounded-lg font-medium transition
                        <?php echo (basename($_SERVER['PHP_SELF']) === 'dashboard.php') ? 'bg-[#1e2533] text-blue-300' : 'text-white'; ?>
                        hover:bg-blue-600 hover:text-white">
                        <i class="fa-solid fa-gauge-high text-blue-300"></i>
                        Dashboard
                    </a>
                </li>
                <!-- Manajemen Booking -->
                <li>
                    <button type="button" class="flex items-center gap-3 w-full px-4 py-3 rounded-lg font-medium hover:bg-[#1e2533] transition focus:outline-none group
                        <?php echo (in_array(basename($_SERVER['PHP_SELF']), ['data_Booking.php','kelola_lokasi.php','kelola_barber.php'])) ? 'bg-[#1e2533] text-blue-300' : 'text-white'; ?>"
                        onclick="toggleSidebarMenu('bookingMenu')">
                        <i class="fa-solid fa-calendar-days text-blue-300"></i>
                        Manajemen Booking
                        <i class="fa-solid fa-chevron-down ml-auto text-xs group-[.open]:rotate-180 transition-transform"></i>
                    </button>
                    <ul id="bookingMenu" class="ml-8 mt-1 space-y-1 <?php echo (in_array(basename($_SERVER['PHP_SELF']), ['data_Booking.php','kelola_lokasi.php','kelola_barber.php'])) ? '' : 'hidden'; ?>">
                        <li>
                            <a href="data_Booking.php" class="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-[#22304a] text-blue-200 <?php echo (basename($_SERVER['PHP_SELF']) === 'data_Booking.php') ? 'font-semibold' : ''; ?>">
                                <i class="fa-solid fa-calendar-check"></i>
                                Data Booking
                            </a>
                        </li>
                        <li>
                            <a href="kelola_lokasi.php" class="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-[#22304a] text-blue-200 <?php echo (basename($_SERVER['PHP_SELF']) === 'kelola_lokasi.php') ? 'font-semibold' : ''; ?>">
                                <i class="fa-solid fa-location-dot"></i>
                                Kelola Lokasi
                            </a>
                        </li>
                        <li>
                            <a href="kelola_barber.php" class="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-[#22304a] text-blue-200 <?php echo (basename($_SERVER['PHP_SELF']) === 'kelola_barber.php') ? 'font-semibold' : ''; ?>">
                                <i class="fa-solid fa-scissors"></i>
                                Kelola Barber
                            </a>
                        </li>
                    </ul>
                </li>
                <!-- Manajemen Produk -->
                <li>
                    <button type="button" class="flex items-center gap-3 w-full px-4 py-3 rounded-lg font-medium hover:bg-[#1e2533] transition focus:outline-none group
                        <?php echo (in_array(basename($_SERVER['PHP_SELF']), ['kelola_produk.php'])) ? 'bg-[#1e2533] text-blue-300' : 'text-white'; ?>"
                        onclick="toggleSidebarMenu('produkMenu')">
                        <i class="fa-solid fa-box text-blue-300"></i>
                        Manajemen Produk
                        <i class="fa-solid fa-chevron-down ml-auto text-xs group-[.open]:rotate-180 transition-transform"></i>
                    </button>
                    <ul id="produkMenu" class="ml-8 mt-1 space-y-1 <?php echo (in_array(basename($_SERVER['PHP_SELF']), ['kelola_produk.php'])) ? '' : 'hidden'; ?>">
                        <li>
                            <a href="kelola_produk.php" class="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-[#22304a] text-blue-200 <?php echo (basename($_SERVER['PHP_SELF']) === 'kelola_produk.php') ? 'font-semibold' : ''; ?>">
                                <i class="fa-solid fa-boxes-stacked"></i>
                                Kelola Produk
                            </a>
                        </li>
                    </ul>
                </li>
                <!-- Kelola Rekrutmen -->
                <li>
                    <button type="button" class="flex items-center gap-3 w-full px-4 py-3 rounded-lg font-medium hover:bg-[#1e2533] transition focus:outline-none group
                        <?php echo (in_array(basename($_SERVER['PHP_SELF']), ['kelola_lowongan.php','data_pelamar.php','whatsapp_manager.php'])) ? 'bg-[#1e2533] text-blue-300' : 'text-white'; ?>"
                        onclick="toggleSidebarMenu('rekrutmenMenu')">
                        <i class="fa-solid fa-briefcase text-blue-300"></i>
                        Kelola Rekrutmen
                        <i class="fa-solid fa-chevron-down ml-auto text-xs group-[.open]:rotate-180 transition-transform"></i>
                    </button>
                    <ul id="rekrutmenMenu" class="ml-8 mt-1 space-y-1 <?php echo (in_array(basename($_SERVER['PHP_SELF']), ['kelola_lowongan.php','data_pelamar.php','whatsapp_manager.php'])) ? '' : 'hidden'; ?>">
                        <li>
                            <a href="kelola_lowongan.php" class="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-[#22304a] text-blue-200 <?php echo (basename($_SERVER['PHP_SELF']) === 'kelola_lowongan.php') ? 'font-semibold' : ''; ?>">
                                <i class="fa-solid fa-briefcase"></i>
                                Kelola Lowongan
                            </a>
                        </li>
                        <li>
                            <a href="data_pelamar.php" class="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-[#22304a] text-blue-200 <?php echo (basename($_SERVER['PHP_SELF']) === 'data_pelamar.php') ? 'font-semibold' : ''; ?>">
                                <i class="fa-solid fa-users"></i>
                                Data Pelamar
                            </a>
                        </li>
                        <li>
                            <a href="whatsapp_manager.php" class="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-[#22304a] text-blue-200 <?php echo (basename($_SERVER['PHP_SELF']) === 'whatsapp_manager.php') ? 'font-semibold' : ''; ?>">
                                <i class="fab fa-whatsapp"></i>
                                WhatsApp Manager
                            </a>
                        </li>
                    </ul>
                </li>
                <!-- Kelola Admin -->
                <li>
                    <a href="kelola_admin.php" class="flex items-center gap-3 px-4 py-3 rounded-lg font-medium hover:bg-[#1e2533] transition
                        <?php echo (basename($_SERVER['PHP_SELF']) === 'kelola_admin.php') ? 'bg-[#1e2533] text-blue-300' : 'text-white'; ?>">
                        <i class="fa-solid fa-user-shield text-blue-300"></i>
                        Kelola Admin
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col">
        <!-- Header -->
        <header class="flex items-center justify-between px-6 py-4 bg-white border-b-4 border-indigo-600 shadow-sm">
            <div class="flex items-center">
                <button id="sidebarToggle" class="text-gray-500 focus:outline-none md:hidden mr-4">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-xl font-semibold">Dashboard Admin Barbershop</h1>
            </div>
            <div class="flex items-center">
                <span class="mr-4 text-gray-700 hidden sm:block"><?php echo $_SESSION['admin_full_name'] ?? 'Admin'; ?></span>
                <div class="relative">
                    <button id="profileDropdown" class="flex items-center focus:outline-none"></button>
                </div>
            </div>
        </header>

        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 py-8">
                <!-- Kartu Statistik Ringkasan per Cabang -->
                <section class="flex flex-wrap gap-8 mb-8">
                    <?php foreach($statistik_cabang as $i => $c): ?>
                    <div class="w-full sm:w-80 bg-white rounded-2xl shadow-lg p-6 flex flex-col gap-4 border-t-4"
                         style="border-color: <?php
                            $colors = ['#60a5fa','#6ee7b7','#fbbf24','#f472b6','#a78bfa','#fde047'];
                            echo $colors[$i%count($colors)];
                         ?>;">
                        <div class="flex items-center gap-3 mb-2">
                            <i data-lucide="store" class="w-7 h-7 text-gray-700"></i>
                            <span class="font-extrabold text-xl text-gray-900"><?php echo $c['nama']; ?></span>
                        </div>
                        <div class="flex items-center gap-2 text-gray-700">
                            <i data-lucide="calendar-days" class="w-5 h-5"></i>
                            <span class="font-semibold"><?php echo $c['booking_today']; ?></span>
                            <span class="text-sm">Booking Hari Ini</span>
                        </div>
                        <div class="flex items-center gap-2 text-gray-700">
                            <i data-lucide="credit-card" class="w-5 h-5"></i>
                            <span class="font-semibold"><?php echo $c['total_transaksi']; ?></span>
                            <span class="text-sm">Transaksi</span>
                        </div>
                        <div class="flex items-center gap-2 text-gray-700">
                            <i data-lucide="coins" class="w-5 h-5"></i>
                            <span class="font-semibold">Rp <?php echo number_format($c['pendapatan'],0,',','.'); ?></span>
                            <span class="text-sm">Pendapatan</span>
                        </div>
                        <div class="flex items-center gap-2 text-gray-700">
                            <i data-lucide="scissors" class="w-5 h-5"></i>
                            <span class="font-semibold"><?php echo $c['barber_aktif']; ?></span>
                            <span class="text-sm">Barber Aktif</span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </section>

                <!-- Grafik & Diagram -->
                <section class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Grafik Garis: Pendapatan & Transaksi per Bulan -->
                    <div class="bg-white rounded-xl shadow p-6">
                        <h2 class="font-semibold text-lg mb-4">Pendapatan & Transaksi per Bulan</h2>
                        <canvas id="lineChart" height="120"></canvas>
                    </div>
                    <!-- Tabel Manajemen Pembelian Produk -->
                    <div class="bg-white rounded-xl shadow p-6">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4 gap-2">
                            <h2 class="text-xl font-semibold">Manajemen Pembelian Produk</h2>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full text-sm border rounded-lg">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="px-4 py-2 text-left font-bold">Nama Produk</th>
                                        <th class="px-4 py-2 text-left font-bold">Harga Pembelian</th>
                                        <th class="px-4 py-2 text-left font-bold">Jumlah Stok Masuk</th>
                                        <th class="px-4 py-2 text-left font-bold">Tanggal Pembelian</th>
                                        <th class="px-4 py-2 text-left font-bold">Status Pengiriman</th>
                                        <th class="px-4 py-2 text-left font-bold">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (count($result) > 0): ?>
                                        <?php foreach($result as $i => $row): ?>
                                            <tr class="border-b hover:bg-gray-50">
                                                <td class="px-4 py-2"><?= htmlspecialchars($row['nama_produk']) ?></td>
                                                <td class="px-4 py-2">Rp <?= number_format($row['price_per_unit'], 0, ',', '.') ?></td>
                                                <td class="px-4 py-2"><?= $row['quantity'] ?></td>
                                                <td class="px-4 py-2"><?= date('d M Y H:i', strtotime($row['created_at'])) ?></td>
                                                <td class="px-4 py-2">
                                                    <button onclick="openStatusModal(<?= $i ?>)" 
                                                        class="px-2 py-1 rounded text-xs font-semibold
                                                        <?php
                                                            $status = $row['status_pengiriman'] ?? 'Belum Dikirim';
                                                            if ($status === 'Sudah Dikirim') echo 'bg-green-100 text-green-800';
                                                            elseif ($status === 'Dalam Proses') echo 'bg-yellow-100 text-yellow-800';
                                                            else echo 'bg-red-100 text-red-800';
                                                        ?>
                                                        hover:opacity-80 focus:outline-none"
                                                        data-status="<?= htmlspecialchars($status) ?>"
                                                        data-id="<?= $row['id'] ?>"
                                                        id="statusBtn<?= $i ?>"
                                                    >
                                                        <?= htmlspecialchars($status) ?>
                                                    </button>
                                                </td>
                                                <td class="px-4 py-2">
                                                    <button onclick="openDetailModal(<?= $i ?>)" class="px-2 py-1 rounded text-xs font-semibold bg-blue-100 text-blue-800 hover:bg-blue-200 focus:outline-none">
                                                        Detail
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center py-4 text-gray-500">Tidak ada data pembelian produk.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Diagram Pie & Donat -->
                <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- Pie: Persentase Pelanggan per Layanan -->
                    <div class="bg-white rounded-xl shadow p-6 flex flex-col items-center">
                        <h2 class="font-semibold text-lg mb-4 text-center">Pelanggan per Layanan</h2>
                        <canvas id="pieChart" width="180" height="180"></canvas>
                    </div>
                    <!-- Donat: Kategori User -->
                    <div class="bg-white rounded-xl shadow p-6 flex flex-col items-center">
                        <h2 class="font-semibold text-lg mb-4 text-center">Kategori User</h2>
                        <canvas id="donutChart" width="180" height="180"></canvas>
                    </div>
                    <!-- Ranking Layanan Terlaris -->
                    <div class="bg-white rounded-xl shadow p-6 col-span-1 md:col-span-2">
                        <h2 class="font-semibold text-lg mb-4">Ranking Layanan Terlaris</h2>
                        <ol class="space-y-2">
                            <?php foreach($layanan_terlaris as $i => $layanan): ?>
                            <li class="flex justify-between items-center">
                                <span class="font-medium"><?php echo $layanan['service_name']; ?></span>
                                <span class="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-semibold"><?php echo $layanan['jumlah']; ?>x</span>
                            </li>
                            <?php endforeach; ?>
                        </ol>
                    </div>
                </section>

                <!-- Tabel Penjualan Booking Terbesar & Aktivitas User -->
                <section class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Tabel Aktivitas User Terbaru -->
                    <div class="bg-white rounded-xl shadow p-6 overflow-x-auto">
                        <h2 class="font-semibold text-lg mb-4">Aktivitas User Terbaru</h2>
                        <table class="min-w-full text-sm">
                            <thead>
                                <tr class="bg-gray-100 text-gray-700">
                                    <th class="py-2 px-3 text-left">Nama Pengguna</th>
                                    <th class="py-2 px-3 text-left">Cabang</th>
                                    <th class="py-2 px-3 text-left">Layanan</th>
                                    <th class="py-2 px-3 text-left">Waktu</th>
                                    <th class="py-2 px-3 text-left">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($aktivitas_terbaru as $aktivitas): ?>
                                <tr>
                                    <td class="py-2 px-3"><?php echo $aktivitas['nama']; ?></td>
                                    <td class="py-2 px-3"><?php echo $aktivitas['cabang']; ?></td>
                                    <td class="py-2 px-3"><?php echo $aktivitas['service_name']; ?></td>
                                    <td class="py-2 px-3"><?php echo $aktivitas['waktu']; ?></td>
                                    <td class="py-2 px-3">
                                        <span class="bg-green-100 text-green-700 px-2 py-1 rounded text-xs"><?php echo $aktivitas['status']; ?></span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- Tabel Pencapaian Booking Terbanyak per Cabang -->
                    <div class="bg-white rounded-xl shadow p-6 overflow-x-auto">
                        <h2 class="font-semibold text-lg mb-4">Pencapaian Booking Terbanyak per Cabang</h2>
                        <table class="min-w-full text-sm">
                            <thead>
                                <tr class="bg-gray-100 text-gray-700">
                                    <th class="py-2 px-3 text-left">Cabang</th>
                                    <th class="py-2 px-3 text-left">Total Booking</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($booking_terbanyak_per_cabang as $row): ?>
                                <tr>
                                    <td class="py-2 px-3"><?php echo $row['cabang']; ?></td>
                                    <td class="py-2 px-3 font-bold"><?php echo $row['total_booking']; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- Riwayat Transaksi Terakhir -->
                <section class="bg-white rounded-xl shadow p-6 mb-8 overflow-x-auto">
                    <div class="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <h2 class="font-semibold text-lg mb-4 sm:mb-0">Riwayat Transaksi Terakhir</h2>
                        <input type="text" id="searchPelanggan" placeholder="Cari Nama Pelanggan..." class="border rounded-lg px-3 py-2 w-full sm:w-64" onkeyup="filterTableByPelanggan()">
                    </div>
                    <table class="min-w-full text-sm" id="riwayatTable">
                        <thead>
                            <tr class="bg-gray-100 text-gray-700">
                                <th class="py-2 px-3 text-left">Cabang</th>
                                <th class="py-2 px-3 text-left">Nama Pelanggan</th>
                                <th class="py-2 px-3 text-left">Nama Barber</th>
                                <th class="py-2 px-3 text-left">Layanan</th>
                                <th class="py-2 px-3 text-left">Waktu Booking</th>
                                <th class="py-2 px-3 text-left">No. HP</th>
                                <th class="py-2 px-3 text-left">Total Transaksi</th>
                                <th class="py-2 px-3 text-left">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Query join bookings, locations, barbers
                            $stmt = $conn->query("SELECT l.name as cabang, b.user_name as nama, br.name as barber, b.service_name, DATE_FORMAT(b.booking_time, '%Y-%m-%d %H:%i') as waktu, b.user_phone as no_hp, b.service_price as total_price, b.status FROM bookings b JOIN locations l ON b.location_id=l.id LEFT JOIN barbers br ON b.barber_id=br.id ORDER BY b.created_at DESC LIMIT 10");
                            $riwayat_transaksi = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            foreach($riwayat_transaksi as $transaksi): ?>
                            <tr>
                                <td class="py-2 px-3"><?php echo $transaksi['cabang']; ?></td>
                                <td class="py-2 px-3 pelanggan-cell"><?php echo $transaksi['nama']; ?></td>
                                <td class="py-2 px-3"><?php echo $transaksi['barber']; ?></td>
                                <td class="py-2 px-3"><?php echo $transaksi['service_name']; ?></td>
                                <td class="py-2 px-3"><?php echo $transaksi['waktu']; ?></td>
                                <td class="py-2 px-3"><?php echo $transaksi['no_hp']; ?></td>
                                <td class="py-2 px-3 font-semibold">Rp <?php echo number_format($transaksi['total_price'],0,',','.'); ?></td>
                                <td class="py-2 px-3">
                                    <span class="bg-green-100 text-green-700 px-2 py-1 rounded text-xs"><?php echo $transaksi['status']; ?></span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </section>
            </div>
        </main>
    </div>
</div>

<!-- Lucide Icon Init -->
<script>
    lucide.createIcons();
</script>

<!-- Chart.js Config -->
<script>
// Data dari PHP untuk Chart.js
const labels = ['Jan','Feb','Mar','Apr','Mei','Jun','Jul','Agu','Sep','Okt','Nov','Des'];
const pendapatanData = <?php echo json_encode($grafik_pendapatan); ?>;
const transaksiData = <?php echo json_encode($grafik_transaksi); ?>;
const cabangNames = <?php echo json_encode(array_column($cabang, 'name')); ?>;

// Pie chart data
const pieLabels = <?php echo json_encode(array_column($pie_layanan, 'service_name')); ?>;
const pieData = <?php echo json_encode(array_column($pie_layanan, 'jumlah')); ?>;

// Donut chart data
const donutLabels = <?php echo json_encode(array_column($donat_user, 'role')); ?>;
const donutData = <?php echo json_encode(array_column($donat_user, 'jumlah')); ?>;

// Grafik Garis: Pendapatan & Transaksi per Bulan
const lineChart = new Chart(document.getElementById('lineChart').getContext('2d'), {
    type: 'line',
    data: {
        labels: labels,
        datasets: (() => {
            const datasets = [];
            cabangNames.forEach((nama, idx) => {
                const colors = ['#2563eb','#22c55e','#f59e42','#ec4899','#a78bfa','#fde047'];
                datasets.push({
                    label: nama + ' (Pendapatan)',
                    data: pendapatanData[nama] || [],
                    borderColor: colors[idx],
                    backgroundColor: 'transparent',
                    tension: 0.4,
                    pointRadius: 4,
                });
                datasets.push({
                    label: nama + ' (Transaksi)',
                    data: transaksiData[nama] || [],
                    borderColor: colors[idx],
                    backgroundColor: 'transparent',
                    borderDash: [5,5],
                    tension: 0.4,
                    pointRadius: 4,
                });
            });
            return datasets;
        })()
    },
    options: {
        responsive: true,
        plugins: {
            legend: { display: true, position: 'bottom' }
        },
        scales: {
            y: { beginAtZero: true }
        }
    }
});

// Pie Chart: Persentase Pelanggan per Layanan
const pieChart = new Chart(document.getElementById('pieChart').getContext('2d'), {
    type: 'pie',
    data: {
        labels: pieLabels,
        datasets: [{
            data: pieData,
            backgroundColor: [
                '#3b82f6', '#22c55e', '#f59e42', '#a78bfa', '#ef4444'
            ],
            borderWidth: 2,
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: { position: 'bottom' }
        }
    }
});

// Donut Chart: Kategori User
const donutChart = new Chart(document.getElementById('donutChart').getContext('2d'), {
    type: 'doughnut',
    data: {
        labels: donutLabels,
        datasets: [{
            data: donutData,
            backgroundColor: [
                '#3b82f6', '#22c55e', '#f59e42', '#ef4444'
            ],
            borderWidth: 2,
        }]
    },
    options: {
        responsive: true,
        cutout: '70%',
        plugins: {
            legend: { position: 'bottom' }
        }
    }
});
</script>

<script>
function toggleSidebarMenu(menuId) {
    const menu = document.getElementById(menuId);
    menu.classList.toggle('hidden');
}

function filterTableByPelanggan() {
    var input = document.getElementById('searchPelanggan');
    var filter = input.value.toLowerCase();
    var table = document.getElementById('riwayatTable');
    var trs = table.getElementsByTagName('tr');
    for (var i = 1; i < trs.length; i++) { // skip header
        var td = trs[i].getElementsByClassName('pelanggan-cell')[0];
        if (td) {
            var txtValue = td.textContent || td.innerText;
            trs[i].style.display = txtValue.toLowerCase().indexOf(filter) > -1 ? '' : 'none';
        }
    }
}
</script>



<!-- Modal Status Pengiriman -->
<div id="statusModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30 hidden">
  <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-xs">
    <h3 class="text-lg font-semibold mb-4">Ubah Status Pengiriman</h3>
    <form onsubmit="event.preventDefault(); saveStatus();">
      <select class="w-full border rounded px-3 py-2 mb-4" id="statusSelect">
        <option value="Belum Dikirim">Belum Dikirim</option>
        <option value="Dalam Proses">Dalam Proses</option>
        <option value="Sudah Dikirim">Sudah Dikirim</option>
      </select>
      <div class="flex justify-end gap-2">
        <button type="button" onclick="closeStatusModal()" class="px-3 py-1 rounded bg-gray-200 hover:bg-gray-300">Batal</button>
        <button type="submit" class="px-3 py-1 rounded bg-blue-600 text-white hover:bg-blue-700">Simpan</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Detail Produk -->
<div id="detailModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30 hidden">
  <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
    <h3 class="text-lg font-semibold mb-4">Detail Produk Dibeli</h3>
    <div id="detailContent" class="text-sm">
      <!-- Konten detail produk akan diisi via JS -->
    </div>
    <div class="flex justify-end mt-4">
      <button type="button" onclick="closeDetailModal()" class="px-3 py-1 rounded bg-gray-200 hover:bg-gray-300">Tutup</button>
    </div>
  </div>
</div>

<script>
// Data dummy untuk status dan detail (karena belum ada backend)
const pembelianData = <?php echo json_encode($result); ?>;
let currentStatusIndex = null;
let currentDetailIndex = null;
let currentStatusId = null;

function openStatusModal(idx) {
  currentStatusIndex = idx;
  const btn = document.getElementById('statusBtn' + idx);
  currentStatusId = btn.getAttribute('data-id');
  document.getElementById('statusSelect').value = btn.getAttribute('data-status');
  document.getElementById('statusModal').classList.remove('hidden');
}
function closeStatusModal() {
  document.getElementById('statusModal').classList.add('hidden');
  currentStatusIndex = null;
  currentStatusId = null;
}
function saveStatus() {
  const status = document.getElementById('statusSelect').value;
  if (!currentStatusId) return;
  fetch('update_status_pengiriman.php', {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: 'id=' + encodeURIComponent(currentStatusId) + '&status=' + encodeURIComponent(status)
  })
  .then(res => res.json())
  .then(data => {
    if (data.success) {
      // Update tampilan tombol status
      const btn = document.getElementById('statusBtn' + currentStatusIndex);
      btn.textContent = status;
      btn.setAttribute('data-status', status);
      btn.className = 'px-2 py-1 rounded text-xs font-semibold hover:opacity-80 focus:outline-none ';
      if (status === 'Sudah Dikirim') btn.className += 'bg-green-100 text-green-800';
      else if (status === 'Dalam Proses') btn.className += 'bg-yellow-100 text-yellow-800';
      else btn.className += 'bg-red-100 text-red-800';
      closeStatusModal();
    } else {
      alert('Gagal update status!');
    }
  });
}
function openDetailModal(idx) {
  currentDetailIndex = idx;
  const data = pembelianData[idx];
  let html = '';
  html += '<div><b>Nama Produk:</b> ' + data.nama_produk + '</div>';
  html += '<div><b>Harga Pembelian:</b> Rp ' + Number(data.price_per_unit).toLocaleString('id-ID') + '</div>';
  html += '<div><b>Jumlah Stok Masuk:</b> ' + data.quantity + '</div>';
  html += '<div><b>Tanggal Pembelian:</b> ' + (new Date(data.created_at)).toLocaleString('id-ID') + '</div>';
  html += '<div class="mt-2 text-xs text-gray-500">* Data detail hanya simulasi</div>';
  document.getElementById('detailContent').innerHTML = html;
  document.getElementById('detailModal').classList.remove('hidden');
}
function closeDetailModal() {
  document.getElementById('detailModal').classList.add('hidden');
  currentDetailIndex = null;
}
</script>
</body>
</html>