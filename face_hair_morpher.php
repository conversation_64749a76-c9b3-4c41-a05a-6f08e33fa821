<?php
header('Content-Type: application/json');

function morphUserFaceToModel($userImagePath, $modelImagePath, $hairStyle, $faceShape) {
    try {
        $outputDir = 'uploads/3d_results/';
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }
        
        // Load images
        $userImg = @imagecreatefromstring(file_get_contents($userImagePath));
        $modelImg = @imagecreatefromstring(file_get_contents($modelImagePath));
        
        if (!$userImg || !$modelImg) {
            return ['success' => false, 'error' => 'Failed to load images'];
        }
        
        $userWidth = imagesx($userImg);
        $userHeight = imagesy($userImg);
        $modelWidth = imagesx($modelImg);
        $modelHeight = imagesy($modelImg);
        
        // Create canvas with user image as base
        $result = imagecreatetruecolor($userWidth, $userHeight);
        imagecopy($result, $userImg, 0, 0, 0, 0, $userWidth, $userHeight);
        
        // Extract model's hair (top 45% of model image)
        $hairHeight = intval($modelHeight * 0.45);
        $hairRegion = imagecreatetruecolor($modelWidth, $hairHeight);
        imagecopy($hairRegion, $modelImg, 0, 0, 0, 0, $modelWidth, $hairHeight);
        
        // Scale hair to match user's head proportions
        $facePositions = [
            'oval' => ['scale' => 0.85, 'x_offset' => 0.075, 'y_offset' => 0.02],
            'round' => ['scale' => 0.9, 'x_offset' => 0.05, 'y_offset' => 0.01],
            'square' => ['scale' => 0.8, 'x_offset' => 0.1, 'y_offset' => 0.03],
            'heart' => ['scale' => 0.75, 'x_offset' => 0.125, 'y_offset' => 0.04],
            'rectangular' => ['scale' => 0.85, 'x_offset' => 0.075, 'y_offset' => 0.02]
        ];
        
        $pos = $facePositions[strtolower($faceShape)] ?? $facePositions['square'];
        
        $scaledWidth = intval($userWidth * $pos['scale']);
        $scaledHeight = intval($hairHeight * ($scaledWidth / $modelWidth));
        
        $scaledHair = imagecreatetruecolor($scaledWidth, $scaledHeight);
        imagecopyresampled($scaledHair, $hairRegion, 0, 0, 0, 0, 
                          $scaledWidth, $scaledHeight, $modelWidth, $hairHeight);
        
        // Position hair on user's head
        $hairX = intval($userWidth * $pos['x_offset']);
        $hairY = intval($userHeight * $pos['y_offset']);
        
        // Create smooth blend mask
        $mask = imagecreatetruecolor($scaledWidth, $scaledHeight);
        $white = imagecolorallocate($mask, 255, 255, 255);
        imagefill($mask, 0, 0, $white);
        
        // Apply gradient fade at edges
        for ($y = 0; $y < $scaledHeight; $y++) {
            for ($x = 0; $x < $scaledWidth; $x++) {
                $alpha = 255;
                
                // Fade at bottom edge
                if ($y > $scaledHeight * 0.7) {
                    $fadeRatio = ($scaledHeight - $y) / ($scaledHeight * 0.3);
                    $alpha = intval(255 * $fadeRatio);
                }
                
                // Fade at side edges
                if ($x < $scaledWidth * 0.1 || $x > $scaledWidth * 0.9) {
                    $sideRatio = ($x < $scaledWidth * 0.1) ? 
                                ($x / ($scaledWidth * 0.1)) : 
                                (($scaledWidth - $x) / ($scaledWidth * 0.1));
                    $alpha = min($alpha, intval(255 * $sideRatio));
                }
                
                $color = imagecolorallocate($mask, $alpha, $alpha, $alpha);
                imagesetpixel($mask, $x, $y, $color);
            }
        }
        
        // Apply hair with smooth blending
        imagecopymerge($result, $scaledHair, $hairX, $hairY, 0, 0, 
                      $scaledWidth, $scaledHeight, 80);
        
        // Save result
        $outputPath = $outputDir . uniqid('morph_') . '.png';
        imagepng($result, $outputPath);
        
        // Cleanup
        imagedestroy($userImg);
        imagedestroy($modelImg);
        imagedestroy($hairRegion);
        imagedestroy($scaledHair);
        imagedestroy($mask);
        imagedestroy($result);
        
        return [
            'success' => true,
            'result_url' => $outputPath,
            'hair_style' => $hairStyle,
            'face_shape' => $faceShape
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $userImage = $input['user_image'] ?? '';
    $modelImage = $input['model_image'] ?? '';
    $hairStyle = $input['hair_style'] ?? '';
    $faceShape = $input['face_shape'] ?? 'square';
    
    if (empty($userImage) || empty($modelImage)) {
        echo json_encode(['success' => false, 'error' => 'Missing images']);
        exit;
    }
    
    $result = morphUserFaceToModel($userImage, $modelImage, $hairStyle, $faceShape);
    echo json_encode($result);
    exit;
}

echo json_encode(['success' => false, 'error' => 'Invalid request']);
?>