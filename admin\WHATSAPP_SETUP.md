# 📱 WhatsApp Notification Setup Guide

## Overview
Sistem notifikasi WhatsApp otomatis untuk mengirim pesan kepada pelamar berdasarkan status aplikasi mereka.

## 🚀 Quick Setup

### 1. Pilih WhatsApp API Provider

#### Option A: Fonnte (Recommended)
1. Daftar di [Fonnte.com](https://fonnte.com)
2. Dapatkan API Key dari dashboard
3. Update `admin/whatsapp_config.php`:
```php
'api_provider' => 'fonnte',
'fonnte' => [
    'api_url' => 'https://api.fonnte.com/send',
    'api_key' => 'YOUR_FONNTE_API_KEY_HERE',
]
```

#### Option B: Wablas
1. Daftar di [Wablas.com](https://wablas.com)
2. Dapatkan token dari dashboard
3. Update `admin/whatsapp_config.php`:
```php
'api_provider' => 'wablas',
'wablas' => [
    'api_url' => 'https://YOUR_DOMAIN.wablas.com/api/send-message',
    'api_key' => 'YOUR_WABLAS_TOKEN_HERE'
]
```

#### Option C: UltraMsg
1. Daftar di [UltraMsg.com](https://ultramsg.com)
2. Dapatkan instance ID dan token
3. Update `admin/whatsapp_config.php`:
```php
'api_provider' => 'ultramsg',
'ultramsg' => [
    'api_url' => 'https://api.ultramsg.com/YOUR_INSTANCE_ID/messages/chat',
    'api_key' => 'YOUR_ULTRAMSG_TOKEN_HERE'
]
```

### 2. Test Connection
1. Login ke admin panel
2. Buka "WhatsApp Manager"
3. Masukkan nomor test (format: +6281234567890)
4. Klik "Send Test"

### 3. Configure Messages
Edit template pesan di `admin/whatsapp_config.php` sesuai kebutuhan:
```php
'templates' => [
    'test' => "🧪 *Test Message*\n\nIni adalah pesan test...",
    'welcome' => "👋 *Selamat datang*\n\nTerima kasih...",
]
```

## 📋 Features

### Automatic Notifications
- ✅ **Application Submitted**: Saat pelamar selesai quiz
- 📋 **Status Updated**: Saat admin mengubah status
- 🎯 **Interview Scheduled**: Saat status berubah ke interview
- ✅ **Accepted**: Saat lamaran diterima
- ❌ **Rejected**: Saat lamaran ditolak

### Manual Notifications
- 👤 **Individual**: Kirim ke pelamar tertentu
- 📢 **Bulk**: Kirim ke semua pelamar dengan status tertentu
- 🧪 **Test**: Test koneksi API

### Message Templates
- 🎉 **Accepted**: Pesan selamat untuk yang diterima
- 📋 **Reviewed**: Pesan untuk status review
- 📞 **Interview**: Pesan untuk tahap interview
- ❌ **Rejected**: Pesan untuk yang ditolak
- ⏳ **Pending**: Pesan untuk status pending

## 🔧 Configuration Options

### Basic Settings
```php
'default_country_code' => '62', // Indonesia
'message_delay' => 1, // Delay antar pesan (detik)
'max_retries' => 3, // Maksimal retry
'timeout' => 30, // Timeout request (detik)
```

### Rate Limiting
```php
'rate_limit' => [
    'enabled' => true,
    'max_messages_per_minute' => 30,
    'max_messages_per_hour' => 1000
]
```

### Logging
```php
'enable_logging' => true,
'log_file' => __DIR__ . '/logs/whatsapp.log',
'log_level' => 'info'
```

## 📊 Usage Examples

### Send Individual Notification
```php
require_once 'admin/whatsapp_notification.php';
$result = sendWhatsAppNotification($application_id, 'accepted');
```

### Send Bulk Notifications
```php
$whatsapp = new WhatsAppNotification();
$results = $whatsapp->sendBulkNotifications($applications);
```

### Custom Message
```php
$whatsapp = new WhatsAppNotification();
$result = $whatsapp->sendMessage('+6281234567890', 'Custom message');
```

## 🔍 Troubleshooting

### Common Issues

#### 1. API Key Invalid
- ✅ Pastikan API key benar
- ✅ Cek quota/saldo di provider
- ✅ Pastikan nomor WhatsApp terdaftar

#### 2. Message Not Delivered
- ✅ Cek format nomor telepon (+62...)
- ✅ Pastikan nomor WhatsApp aktif
- ✅ Cek rate limiting

#### 3. Connection Timeout
- ✅ Cek koneksi internet
- ✅ Tingkatkan timeout di config
- ✅ Cek status server provider

### Debug Mode
Enable debug logging:
```php
'log_level' => 'debug'
```

Check logs di `admin/logs/whatsapp.log`

## 📱 Phone Number Format

### Supported Formats
- ✅ `+6281234567890` (International)
- ✅ `081234567890` (National)
- ✅ `6281234567890` (Without +)

### Auto-Conversion
Sistem otomatis convert ke format international:
- `081234567890` → `+6281234567890`
- `6281234567890` → `+6281234567890`

## 🔐 Security

### API Key Protection
- 🔒 Jangan commit API key ke repository
- 🔒 Gunakan environment variables untuk production
- 🔒 Rotate API key secara berkala

### Rate Limiting
- ⏱️ Delay antar pesan untuk avoid spam
- 📊 Monitor usage untuk avoid quota exceeded
- 🚫 Implement blacklist untuk nomor bermasalah

## 📈 Monitoring

### Notification Logs
Semua notifikasi tercatat di database:
- 📅 Timestamp
- 👤 Recipient
- 📝 Message content
- ✅/❌ Success status
- 📊 API response

### Admin Dashboard
Monitor di "WhatsApp Manager":
- 📊 Success rate
- 📈 Usage statistics
- 🔍 Recent logs
- 🧪 Test functionality

## 🆘 Support

### Provider Support
- **Fonnte**: [Support Fonnte](https://fonnte.com/support)
- **Wablas**: [Support Wablas](https://wablas.com/support)
- **UltraMsg**: [Support UltraMsg](https://ultramsg.com/support)

### Custom Implementation
Untuk custom provider, implement di:
```php
'api_provider' => 'custom',
'custom' => [
    'api_url' => 'YOUR_API_URL',
    'api_key' => 'YOUR_API_KEY',
    'headers' => [...]
]
```

---

**💡 Tips**: Mulai dengan Fonnte untuk setup yang mudah, atau gunakan Wablas untuk fitur lebih advanced.
