# 🔧 WhatsApp Notification Troubleshooting Guide

## Common Errors & Solutions

### 1. "Undefined variable $conn" Error

**Error Message:**
```
Warning: Undefined variable $conn in whatsapp_notification.php on line XXX
Fatal error: Call to a member function prepare() on null
```

**Cause:** Database connection variable not available in function scope.

**Solutions:**

#### Solution A: Run Test Script
1. Open browser: `http://localhost/aplikasi-pixel/admin/test_whatsapp.php`
2. Check if all tests pass
3. If tests fail, follow specific error messages

#### Solution B: Manual Database Setup
1. Open browser: `http://localhost/aplikasi-pixel/admin/setup_whatsapp_db.php`
2. Run the setup script
3. Verify tables are created

#### Solution C: Check Database Connection
```php
// In admin/db_config.php, verify connection:
try {
    $conn = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    echo "Connection successful";
} catch(PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
}
```

### 2. "Table doesn't exist" Error

**Error Message:**
```
Table 'pixel.notification_logs' doesn't exist
```

**Solutions:**

#### Create Table Manually
```sql
CREATE TABLE IF NOT EXISTS notification_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    type ENUM('whatsapp', 'email', 'sms') NOT NULL,
    recipient VARCHAR(255) NOT NULL,
    message_sent TEXT NOT NULL,
    success BOOLEAN NOT NULL DEFAULT FALSE,
    response JSON NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);
```

#### Via WhatsApp Manager
1. Login to admin panel
2. Go to "WhatsApp Manager"
3. Click "Create Notification Table"

### 3. WhatsApp API Connection Issues

**Error Message:**
```
Failed to send WhatsApp message
API connection timeout
```

**Solutions:**

#### Check API Configuration
1. Open `admin/whatsapp_config.php`
2. Verify API provider settings:
```php
'api_provider' => 'fonnte', // or 'wablas', 'ultramsg'
'fonnte' => [
    'api_url' => 'https://api.fonnte.com/send',
    'api_key' => 'YOUR_ACTUAL_API_KEY_HERE' // Must be real key
]
```

#### Test API Connection
1. Go to WhatsApp Manager
2. Enter test phone number
3. Click "Send Test"
4. Check response

#### Common API Issues
- **Invalid API Key**: Get new key from provider
- **Insufficient Balance**: Top up account
- **Rate Limiting**: Reduce message frequency
- **Invalid Phone Format**: Use +************* format

### 4. Phone Number Format Issues

**Error Message:**
```
Invalid phone number format
Message not delivered
```

**Solutions:**

#### Correct Formats
- ✅ `+*************` (International)
- ✅ `************` (National - auto-converted)
- ❌ `***********` (Missing leading zero)
- ❌ `+62-812-3456-7890` (Contains dashes)

#### Auto-Conversion
System automatically converts:
- `************` → `+*************`
- `*************` → `+*************`

### 5. Permission Issues

**Error Message:**
```
Access denied for user 'root'@'localhost'
Cannot create table
```

**Solutions:**

#### Check Database Permissions
```sql
-- Grant necessary permissions
GRANT CREATE, ALTER, INSERT, UPDATE, DELETE, SELECT ON pixel.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

#### Check File Permissions
```bash
# Make sure PHP can write to logs directory
chmod 755 admin/logs/
chmod 644 admin/logs/whatsapp.log
```

### 6. Session Issues

**Error Message:**
```
Session not started
Admin not logged in
```

**Solutions:**

#### Check Session
```php
// At top of file
session_start();
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}
```

#### Clear Session
1. Logout and login again
2. Clear browser cookies
3. Restart browser

## Diagnostic Steps

### Step 1: Basic Checks
1. ✅ XAMPP running (Apache + MySQL)
2. ✅ Database 'pixel' exists
3. ✅ Admin logged in
4. ✅ Internet connection active

### Step 2: Database Verification
```sql
-- Check if tables exist
SHOW TABLES LIKE '%notification%';
SHOW TABLES LIKE '%application%';

-- Check table structure
DESCRIBE notification_logs;
DESCRIBE applications;

-- Check data
SELECT COUNT(*) FROM applications;
SELECT COUNT(*) FROM notification_logs;
```

### Step 3: File Verification
```bash
# Check if files exist
ls -la admin/whatsapp_notification.php
ls -la admin/whatsapp_config.php
ls -la admin/db_config.php

# Check file permissions
ls -la admin/logs/
```

### Step 4: PHP Error Checking
```php
// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Check PHP version
echo phpversion(); // Should be 7.4+

// Check required extensions
echo extension_loaded('pdo') ? 'PDO: OK' : 'PDO: Missing';
echo extension_loaded('curl') ? 'CURL: OK' : 'CURL: Missing';
echo extension_loaded('json') ? 'JSON: OK' : 'JSON: Missing';
```

## Quick Fixes

### Fix 1: Reset Everything
```bash
# 1. Stop XAMPP
# 2. Backup database
mysqldump -u root pixel > pixel_backup.sql

# 3. Drop and recreate database
mysql -u root -e "DROP DATABASE pixel; CREATE DATABASE pixel;"

# 4. Restore backup
mysql -u root pixel < pixel_backup.sql

# 5. Run setup script
# Open: http://localhost/aplikasi-pixel/admin/setup_whatsapp_db.php
```

### Fix 2: Manual Table Creation
```sql
-- Run this in phpMyAdmin or MySQL command line
USE pixel;

CREATE TABLE IF NOT EXISTS notification_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    type ENUM('whatsapp', 'email', 'sms') NOT NULL DEFAULT 'whatsapp',
    recipient VARCHAR(255) NOT NULL,
    message_sent TEXT NOT NULL,
    success BOOLEAN NOT NULL DEFAULT FALSE,
    response JSON NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_application_id (application_id),
    INDEX idx_sent_at (sent_at),
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add evaluation columns if missing
ALTER TABLE applications 
ADD COLUMN IF NOT EXISTS evaluation_score DECIMAL(5,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS evaluation_details JSON DEFAULT NULL;
```

### Fix 3: Configuration Reset
```php
// Reset whatsapp_config.php to defaults
<?php
return [
    'api_provider' => 'fonnte',
    'fonnte' => [
        'api_url' => 'https://api.fonnte.com/send',
        'api_key' => 'YOUR_API_KEY_HERE'
    ],
    'default_country_code' => '62',
    'message_delay' => 1,
    'timeout' => 30
];
?>
```

## Getting Help

### Log Files
Check these files for detailed error information:
- `admin/logs/whatsapp.log`
- `C:\xampp\apache\logs\error.log`
- `C:\xampp\mysql\data\mysql_error.log`

### Test URLs
- Database Setup: `http://localhost/aplikasi-pixel/admin/setup_whatsapp_db.php`
- System Test: `http://localhost/aplikasi-pixel/admin/test_whatsapp.php`
- WhatsApp Manager: `http://localhost/aplikasi-pixel/admin/whatsapp_manager.php`

### Contact Support
If issues persist:
1. Run test script and note exact error messages
2. Check all log files
3. Provide PHP version and XAMPP version
4. Include database structure (SHOW CREATE TABLE)

---

**💡 Pro Tip**: Always run the test script first - it will identify most common issues automatically!
