<?php
session_start();
require_once 'admin/db_config.php';

// Determine the location_id to use
$location_id = null;

if (isset($_POST['location_id'])) {
    // If a location is posted, use it and update the session
    $location_id = $_POST['location_id'];
    $_SESSION['selected_location_id'] = $location_id;
} elseif (isset($_SESSION['selected_location_id'])) {
    // If not posted, but present in session, use the session value
    $location_id = $_SESSION['selected_location_id'];
}

// If no location_id is determined, redirect
if ($location_id === null) {
    header("Location: location.php");
    exit();
}

// Fetch selected location details from database
$location_name = "Lokasi Tidak Ditemukan";
$location_address = "";

try {
    $stmt_loc = $conn->prepare("SELECT name, address FROM locations WHERE id = :location_id");
    $stmt_loc->execute(['location_id' => $location_id]);
    $fetched_location = $stmt_loc->fetch(PDO::FETCH_ASSOC);

    if ($fetched_location) {
        $location_name = $fetched_location['name'];
        $location_address = $fetched_location['address'];
    }
} catch (PDOException $e) {
    error_log("Error fetching location details: " . $e->getMessage());
}

// Fetch barbers for the selected location
$barbers = [];
try {
    $stmt = $conn->prepare("SELECT id, name, services_offered, image_filename FROM barbers WHERE location_id = :location_id");
    $stmt->execute(['location_id' => $location_id]);
    $barbers = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching barbers: " . $e->getMessage());
}

// Process services from barbers' services_offered
$services = [];
$processed_services = [];

foreach ($barbers as $barber) {
    if (!empty($barber['services_offered'])) {
        $barber_services_raw = explode(',', $barber['services_offered']);
        foreach ($barber_services_raw as $service_raw) {
            $parts = explode('|', trim($service_raw));
            if (count($parts) === 3) {
                $service_name = trim($parts[0]);
                $service_duration = (int)trim($parts[1]);
                $service_price = (int)trim($parts[2]);

                if (!in_array($service_name, $processed_services)) {
                    $processed_services[] = $service_name;
                    $services[] = [
                        'id' => count($services) + 1, // Assign a unique ID for booking form
                        'name' => $service_name,
                        'duration' => $service_duration,
                        'price' => $service_price
                    ];
                }
            }
        }
    }
}

// --- NEW PHP LOGIC FOR TIME SELECTION PART --- 
// Fetch selected service and barber details from session (or post if available)
$selected_service_id = $_POST['service_id'] ?? $_SESSION['service_id'] ?? null;
$selected_barber_id = $_POST['barber_id'] ?? $_SESSION['barber_id'] ?? null;

$display_service_name = "Pilih Layanan";
$display_service_price = "0";
$display_barber_name = "Pilih Barber";

// Fetch selected service details for display
if ($selected_service_id !== null) {
    foreach ($services as $svc) {
        if ($svc['id'] == $selected_service_id) {
            $display_service_name = $svc['name'];
            $display_service_price = $svc['price'];
            break;
        }
    }
}

// Fetch selected barber details for display
if ($selected_barber_id !== null) {
    try {
        $stmt_barber_display = $conn->prepare("SELECT name FROM barbers WHERE id = :barber_id");
        $stmt_barber_display->execute(['barber_id' => $selected_barber_id]);
        $fetched_barber_display = $stmt_barber_display->fetch(PDO::FETCH_ASSOC);
        if ($fetched_barber_display) {
            $display_barber_name = $fetched_barber_display['name'];
        }
    } catch (PDOException $e) {
        error_log("Error fetching display barber details: " . $e->getMessage());
    }
}

// Time slots (can remain static or be fetched dynamically based on barber/location)
$time_slots = [
    ['time' => '09:00 AM', 'available' => true],
    ['time' => '10:00 AM', 'available' => true],
    ['time' => '11:00 AM', 'available' => false],
    ['time' => '12:00 PM', 'available' => true],
    ['time' => '01:00 PM', 'available' => true],
    ['time' => '02:00 PM', 'available' => true],
    ['time' => '03:00 PM', 'available' => false],
    ['time' => '04:00 PM', 'available' => true],
    ['time' => '05:00 PM', 'available' => true]
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Appointment - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
        }
        .service-card.selected {
            border-color: #0A5144;
            background-color: #F0F9F8;
        }
        .barber-card.selected {
            border-color: #0A5144;
            background-color: #F0F9F8;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .hidden-section {
            display: none;
        }
        .service-option {
            transition: all 0.2s ease-in-out;
        }
        .service-option:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .service-radio {
            transition: all 0.2s ease-in-out;
        }
    </style>
</head>
<body class="pb-24 overflow-y-auto">
    <!-- Header -->
    <header class="sticky top-0 z-10 bg-white p-4 shadow-sm">
        <div class="flex items-center justify-between">
            <a href="location.php" class="text-gray-600">
                <i class="fas fa-arrow-left text-lg"></i>
            </a>
            <h1 class="text-xl font-bold text-gray-900">Book Appointment</h1>
            <div class="w-6"></div>
        </div>
    </header>

    <!-- Location Info -->
    <div class="bg-white px-4 py-3 shadow-sm">
        <div class="flex items-center">
            <i class="fas fa-map-marker-alt text-[#0A5144] mr-2"></i>
            <div>
                <h2 class="font-bold text-gray-900"><?php echo $location_name; ?></h2>
                <p class="text-sm text-gray-600"><?php echo $location_address; ?></p>
            </div>
        </div>
    </div>

    <!-- Booking Steps -->
    <div class="px-4 py-3 bg-white mt-1 flex justify-between items-center relative">
        <div class="absolute top-1/2 left-4 right-4 h-0.5 bg-gray-200 -translate-y-1/2 z-0"></div>
        <div class="z-10 flex flex-col items-center">
            <div class="w-8 h-8 rounded-full bg-[#0A5144] border-2 border-[#0A5144] flex items-center justify-center font-bold text-white mb-1">1</div>
            <span class="text-xs font-medium">Service</span>
        </div>
        <div class="z-10 flex flex-col items-center">
            <div class="w-8 h-8 rounded-full bg-[#0A5144] border-2 border-[#0A5144] flex items-center justify-center font-bold text-white mb-1">2</div>
            <span class="text-xs font-medium">Barber</span>
        </div>
        <div class="z-10 flex flex-col items-center" id="step3">
            <div class="w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center font-bold bg-white text-gray-400 mb-1">3</div>
            <span class="text-xs font-medium text-gray-400">Time</span>
        </div>
    </div>

    <form action="confirm_booking.php" method="post" id="bookingForm">
        <input type="hidden" name="location_id" value="<?php echo htmlspecialchars($location_id); ?>">

        <!-- User Info Input -->
        <div class="bg-white px-4 py-4 mt-4 shadow-sm">
            <h2 class="font-bold text-gray-900 mb-3">Your Details</h2>
            <div class="space-y-3">
                <input type="text" name="user_name" placeholder="Nama Lengkap" class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#0A5144]">
                <input type="tel" name="user_phone" placeholder="Nomor Telepon" class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#0A5144]">
            </div>
        </div>

        <!-- Service Selection -->
        <div class="bg-white px-4 py-4 mt-1 shadow-sm">
            <h2 class="font-bold text-gray-900 mb-3">
                <i class="fas fa-cut text-[#0A5144] mr-2"></i>
                Pilih Layanan
            </h2>
            <div class="space-y-3">
                <?php if (empty($services)): ?>
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-info-circle text-3xl mb-2"></i>
                        <p>Belum ada layanan tersedia untuk lokasi ini</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($services as $service): ?>
                        <div class="service-option border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-[#0A5144] hover:bg-gray-50 transition-all duration-200"
                             onclick="selectService(<?php echo $service['id']; ?>, '<?php echo htmlspecialchars($service['name']); ?>', <?php echo $service['duration']; ?>, <?php echo $service['price']; ?>)">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 text-lg">
                                        <?php echo htmlspecialchars($service['name']); ?>
                                    </h3>
                                    <div class="flex items-center mt-2 space-x-4">
                                        <div class="flex items-center text-gray-600">
                                            <i class="fas fa-clock text-sm mr-1"></i>
                                            <span class="text-sm"><?php echo $service['duration']; ?> menit</span>
                                        </div>
                                        <div class="flex items-center text-[#0A5144] font-semibold">
                                            <i class="fas fa-tag text-sm mr-1"></i>
                                            <span>Rp <?php echo number_format($service['price'], 0, ',', '.'); ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center service-radio" data-service-id="<?php echo $service['id']; ?>">
                                        <div class="w-3 h-3 bg-[#0A5144] rounded-full hidden"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>

                <!-- Hidden input for selected service -->
                <input type="hidden" id="selectedServiceId" name="service_id" value="">
                <input type="hidden" id="selectedServiceName" name="service_name" value="">
                <input type="hidden" id="selectedServiceDuration" name="service_duration" value="">
                <input type="hidden" id="selectedServicePrice" name="service_price" value="">
            </div>
        </div>

        <!-- Barber Selection -->
        <div class="bg-white px-4 py-4 mt-4 shadow-sm">
            <h2 class="font-bold text-gray-900 mb-3">Select Barber</h2>
            <div class="space-y-3">
                <select id="barberSelect" name="barber_id" class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#0A5144]">
                    <option value="">Pilih Barber</option>
                    <?php foreach ($barbers as $barber): ?>
                        <option value="<?php echo $barber['id']; ?>" data-image-filename="<?php echo htmlspecialchars($barber['image_filename'] ?? ''); ?>">
                            <?php echo htmlspecialchars($barber['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>

        <!-- Display Selected Barber -->
        <div class="bg-white px-4 py-4 mt-4 shadow-sm hidden-section" id="selectedBarberDisplay">
            <h3 class="font-bold text-gray-900 mb-3">Barber Terpilih</h3>
            <div class="flex items-center space-x-4">
                <img id="displayBarberImage" src="" alt="Gambar Barber" class="w-20 h-20 rounded-full object-cover">
                <div class="flex flex-col">
                    <p id="displayBarberName" class="font-semibold text-gray-800"></p>
                    <p class="text-sm text-gray-500">Spesialis Barber</p>
                </div>
            </div>
        </div>

        <!-- Booking Summary (Moved from time_selection.php) -->
        <div class="bg-white px-4 py-3 shadow-sm mt-4 hidden-section" id="bookingSummary">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-bold text-gray-900" id="summaryServiceName"><?php echo htmlspecialchars($display_service_name); ?></h2>
                    <p class="text-sm text-gray-600" id="summaryBarberName">With <?php echo htmlspecialchars($display_barber_name); ?></p>
                </div>
                <p class="font-bold text-gray-900" id="summaryServicePrice">Rp<?php echo htmlspecialchars($display_service_price); ?></p>
            </div>
        </div>

        <!-- Date Picker (Moved from time_selection.php) -->
        <div class="bg-white px-4 py-4 mt-1 shadow-sm hidden-section" id="datePickerSection">
            <h2 class="font-bold text-gray-900 mb-3">Select Date</h2>
            <div class="relative">
                <input type="text" id="datePicker" name="selected_date" class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#0A5144] pr-10" placeholder="Pilih Tanggal">
                <i class="fas fa-calendar-alt absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"></i>
            </div>
        </div>

        <!-- Time Slots (Moved from time_selection.php) -->
        <div class="bg-white px-4 py-4 mt-4 shadow-sm hidden-section" id="timePickerSection">
            <h2 class="font-bold text-gray-900 mb-3">Select Time</h2>
            <div class="relative">
                <input type="text" id="timePicker" name="selected_time" class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#0A5144] pr-10" placeholder="Pilih Waktu">
                <i class="fas fa-clock absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"></i>
            </div>
        </div>

        <!-- Confirm Button (Adjusted for consolidated form) -->
        <div class="fixed bottom-0 left-0 right-0 bg-white p-4 shadow-lg border-t border-gray-100">
            <input type="hidden" name="service_name" id="serviceName">
            <input type="hidden" name="service_price" id="servicePrice">
            <input type="hidden" name="service_duration" id="serviceDuration">
            <button type="submit" id="confirmButton" class="gradient-bg w-full py-3 rounded-lg text-white font-bold shadow-md opacity-50 cursor-not-allowed" disabled>
                Buat Janji Sekarang
            </button>
        </div>
    </form>

    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Form elements
            const form = document.getElementById('bookingForm');
            const userNameInput = form.querySelector('input[name=\"user_name\"]');
            const userPhoneInput = form.querySelector('input[name=\"user_phone\"]');
            const serviceSelect = form.querySelector('#serviceSelect');
            const selectedServiceId = form.querySelector('#selectedServiceId');
            const barberSelect = form.querySelector('#barberSelect');
            const datePickerInput = form.querySelector('#datePicker');
            const timePickerInput = form.querySelector('#timePicker');
            const confirmButton = form.querySelector('#confirmButton');

            // Display sections
            const bookingSummary = document.getElementById('bookingSummary');
            const datePickerSection = document.getElementById('datePickerSection');
            const timePickerSection = document.getElementById('timePickerSection');
            const step3Indicator = document.getElementById('step3');
            const selectedBarberDisplay = document.getElementById('selectedBarberDisplay');
            const displayBarberImage = document.getElementById('displayBarberImage');
            const displayBarberName = document.getElementById('displayBarberName');

            // Hidden inputs for submission
            const serviceNameInput = form.querySelector('#serviceName');
            const servicePriceInput = form.querySelector('#servicePrice');
            const serviceDurationInput = form.querySelector('#serviceDuration');

            function checkConfirmButton() {
                const allFilled = userNameInput.value.trim() !== '' &&
                                  userPhoneInput.value.trim() !== '' &&
                                  selectedServiceId.value !== '' &&
                                  barberSelect.value !== '' &&
                                  datePickerInput.value !== '' &&
                                  timePickerInput.value !== '';

                if (allFilled) {
                    confirmButton.disabled = false;
                    confirmButton.classList.remove('opacity-50', 'cursor-not-allowed');
                } else {
                    confirmButton.disabled = true;
                    confirmButton.classList.add('opacity-50', 'cursor-not-allowed');
                }
            }

            function checkSectionVisibility() {
                const serviceSelected = selectedServiceId.value !== '';
                const barberSelected = barberSelect.value !== '';

                if (serviceSelected && barberSelected) {
                    [bookingSummary, selectedBarberDisplay, datePickerSection, timePickerSection].forEach(el => el.style.display = 'block');
                    step3Indicator.querySelector('div').className = 'w-8 h-8 rounded-full bg-[#0A5144] border-2 border-[#0A5144] flex items-center justify-center font-bold text-white mb-1';
                    step3Indicator.querySelector('span').className = 'text-xs font-medium';
                } else {
                    [bookingSummary, selectedBarberDisplay, datePickerSection, timePickerSection].forEach(el => el.style.display = 'none');
                    step3Indicator.querySelector('div').className = 'w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center font-bold bg-white text-gray-400 mb-1';
                    step3Indicator.querySelector('span').className = 'text-xs font-medium text-gray-400';
                }
            }

            function updateBookingSummary() {
                const selectedBarberOption = barberSelect.options[barberSelect.selectedIndex];

                if (selectedServiceId.value && barberSelect.value) {
                    const serviceName = document.getElementById('selectedServiceName').value;
                    const servicePrice = document.getElementById('selectedServicePrice').value;
                    const serviceDuration = document.getElementById('selectedServiceDuration').value;

                    document.getElementById('summaryServiceName').textContent = serviceName;
                    document.getElementById('summaryBarberName').textContent = `With ${selectedBarberOption.text}`;
                    document.getElementById('summaryServicePrice').textContent = `Rp${parseInt(servicePrice).toLocaleString('id-ID')}`;

                    serviceNameInput.value = serviceName;
                    servicePriceInput.value = servicePrice;
                    serviceDurationInput.value = serviceDuration;

                    displayBarberName.textContent = selectedBarberOption.text;
                    const imageFilename = selectedBarberOption.dataset.imageFilename;
                    displayBarberImage.src = imageFilename ? `uploads/barbers/${imageFilename}` : 'https://via.placeholder.com/150';
                }

                checkSectionVisibility();
                checkConfirmButton();
            }

            // Global function for service selection
            window.selectService = function(serviceId, serviceName, duration, price) {
                // Remove previous selections
                document.querySelectorAll('.service-option').forEach(option => {
                    option.classList.remove('border-[#0A5144]', 'bg-gray-50');
                    option.classList.add('border-gray-200');
                });

                document.querySelectorAll('.service-radio .w-3').forEach(radio => {
                    radio.classList.add('hidden');
                });

                // Add selection to clicked option
                event.currentTarget.classList.remove('border-gray-200');
                event.currentTarget.classList.add('border-[#0A5144]', 'bg-gray-50');

                // Show radio button
                const radio = event.currentTarget.querySelector('.service-radio .w-3');
                radio.classList.remove('hidden');

                // Update hidden inputs
                document.getElementById('selectedServiceId').value = serviceId;
                document.getElementById('selectedServiceName').value = serviceName;
                document.getElementById('selectedServiceDuration').value = duration;
                document.getElementById('selectedServicePrice').value = price;

                // Update booking summary
                updateBookingSummary();

                console.log('Selected service:', serviceName, duration + ' min', 'Rp ' + parseInt(price).toLocaleString('id-ID'));
            }

            flatpickr(datePickerInput, {
                dateFormat: "Y-m-d",
                minDate: "today",
                onChange: () => checkConfirmButton()
            });

            flatpickr(timePickerInput, {
                enableTime: true,
                noCalendar: true,
                dateFormat: "H:i",
                time_24hr: true,
                minTime: "09:00",
                maxTime: "17:00",
                minuteIncrement: 30,
                onChange: () => checkConfirmButton()
            });

            [userNameInput, userPhoneInput].forEach(el => el.addEventListener('input', checkConfirmButton));
            barberSelect.addEventListener('change', updateBookingSummary);

            updateBookingSummary();
        });
    </script>
</body>
</html>