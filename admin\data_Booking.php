<?php
session_start();
require_once 'db_config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Pixel Barbershop Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f0f2f5;
        }
        .sidebar {
            background: #2d3748;
            color: #fff;
        }
        .sidebar .logo {
            background: #1a202c;
            padding: 1rem;
        }
        .sidebar .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .sidebar nav ul li {
            transition: all 0.3s ease;
        }
        .sidebar nav ul li a {
            color: #fff;
        }
        .sidebar nav ul li.active {
            background:rgb(35, 71, 250);
        }
        .sidebar nav ul li.active a {
            color: #fff;
        }
        .sidebar nav ul li i {
            color: #4299e1;
        }
        .sidebar nav ul li.active i {
            color: #fff;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <aside class="w-64 sidebar">
            <div class="logo">
                <h1 class="text-2xl font-bold">Admin Panel</h1>
            </div>
            <nav class="mt-4">
                <ul>
                    <li class="px-4 py-3">
                        <a href="index.php" class="flex items-center space-x-3">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <!-- Manajemen Booking Menu -->
                    <li>
                        <button onclick="toggleBookingSubmenu()" class="w-full flex items-center justify-between px-4 py-3 rounded-lg text-white focus:outline-none">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-calendar-check"></i>
                                <span>Manajemen Booking</span>
                            </span>
                            <i id="arrowBookingIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <!-- Submenu -->
                        <ul id="bookingSubmenu" class="ml-8 mt-2 space-y-2">
                            <li class="active">
                                <a href="data_booking.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Data Booking</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_lokasi.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Kelola Lokasi</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_barber.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cut"></i>
                                    <span>Kelola Barber</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <button onclick="toggleSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-box"></i>
                                <span>Manajemen Produk</span>
                            </span>
                            <i id="arrowIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="submenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_produk.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cube"></i>
                                    <span>Produk</span>
                                </a>
                            </li>
                            <li>
                                <a href="data_pembeli.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pembeli</span>
                                </a>
                            </li>
                            <li>
                                <a href="metode_pembayaran.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-money-check-alt"></i>
                                    <span>Metode Pembayaran</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="mb-2">
                        <button onclick="toggleLowonganSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
<span class="flex items-center space-x-3 text-left">
    <i class="fas fa-briefcase"></i>
    <span>Kelola Rekrutmen</span>
</span>
                            <i id="arrowLowonganIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="lowonganSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_lowongan.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Kelola Lowongan</span>
                                </a>
                            </li>
                            <li>
                                <a href="data_pelamar.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-user-tie"></i>
                                    <span>Data Pelamar</span>
                                </a>
                            </li>
                            <li>
                                <a href="whatsapp_manager.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fab fa-whatsapp"></i>
                                   <span>Interview Schedule</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="px-4 py-3">
                        <a href="kelola_admin.php" class="flex items-center space-x-3">
                            <i class="fas fa-user-shield"></i>
                            <span>Kelola Admin</span>
                        </a>
                    </li>

                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col">
            <header class="flex items-center justify-between px-6 py-4 bg-white border-b-4 border-indigo-600">
                <div class="flex items-center">
                    <button class="text-gray-500 focus:outline-none">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-xl font-semibold ml-4">Kelola Data Booking</h1>
                </div>
                <div class="flex items-center">
                    <span class="mr-4 text-gray-700 hidden sm:block"><?php echo $_SESSION['admin_full_name'] ?? 'Admin'; ?></span>
                    <div class="relative">
                        <button id="profileDropdown" class="flex items-center focus:outline-none">
                            <?php
                            // Get admin profile photo
                            $stmt = $conn->prepare("SELECT profile_photo FROM admin WHERE id = ?");
                            $stmt->execute([$_SESSION['admin_id']]);
                            $profile_photo = $stmt->fetchColumn();

                            if (!empty($profile_photo) && file_exists('uploads/profiles/' . $profile_photo)):
                            ?>
                                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-300 hover:border-blue-500 transition-colors"
                                     src="uploads/profiles/<?= htmlspecialchars($profile_photo) ?>"
                                     alt="Admin avatar">
                            <?php else: ?>
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-300 hover:border-blue-500 transition-colors">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                            <?php endif; ?>
                            <i class="fas fa-chevron-down ml-2 text-gray-500 text-sm"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="profileDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="profile_settings.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-3 text-gray-500"></i>
                                Profile Settings
                            </a>
                            <hr class="my-1">
                            <a href="logout.php" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-3 text-red-500"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <?php
            require_once 'db_config.php';

            try {
                $stmt = $conn->prepare("
                    SELECT
                        b.id, b.user_name, b.user_phone, b.service_name, b.service_price,
                        b.booking_time, b.status, br.name AS barber_name, l.name AS location_name
                    FROM bookings AS b
                    LEFT JOIN barbers AS br ON b.barber_id = br.id
                    LEFT JOIN locations AS l ON b.location_id = l.id
                    ORDER BY b.booking_time DESC
                ");
                $stmt->execute();
                $bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                die("Database Error: " . $e->getMessage());
            }
            ?>
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200">
                <div class="container mx-auto px-6 py-8">
                    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600">Data Booking</h2>
                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white border-collapse border">
                                <thead>
                                    <tr>
                                        <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">NO</th>
                                        <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Nama Pelanggan</th>
                                        <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">No. Telepon</th>
                                        <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Layanan</th>
                                        <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Waktu Booking</th>
                                        <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Barber</th>
                                        <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Lokasi</th>
                                        <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Status</th>
                                        <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($bookings)): ?>
                                        <tr>
                                            <td colspan="9" class="py-4 text-center text-gray-500 border">Tidak ada data booking.</td>
                                        </tr>
                                    <?php else:
                                        $no = 1;
                                        foreach ($bookings as $booking): ?>
                                            <tr>
                                                <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo $no++; ?></td>
                                                <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo htmlspecialchars($booking['user_name']); ?></td>
                                                <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo htmlspecialchars($booking['user_phone']); ?></td>
                                                <td class="py-2 px-4 border text-center text-sm text-gray-700">
                                                    <div><?php echo htmlspecialchars($booking['service_name']); ?></div>
                                                    <div class="text-xs">Rp <?php echo number_format($booking['service_price'], 0, ',', '.'); ?></div>
                                                </td>
                                                <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo htmlspecialchars(date('d M Y, H:i', strtotime($booking['booking_time']))); ?></td>
                                                <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo htmlspecialchars($booking['barber_name'] ?? 'N/A'); ?></td>
                                                <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo htmlspecialchars($booking['location_name'] ?? 'N/A'); ?></td>
                                                <td class="py-2 px-4 border text-center text-sm text-gray-700">
                                                    <span class="px-2 py-1 font-semibold leading-tight text-green-700 bg-green-100 rounded-sm">
                                                        <?php echo htmlspecialchars($booking['status']); ?>
                                                    </span>
                                                </td>
                                                <td class="py-2 px-4 border text-center text-sm text-gray-700">
    <div class="flex flex-col md:flex-row gap-2 justify-center items-center">
        <a href="javascript:void(0);"
           onclick="openEditModal(<?php echo htmlspecialchars(json_encode($booking), ENT_QUOTES, 'UTF-8'); ?>)"
           class="w-full md:w-auto bg-yellow-500 hover:bg-yellow-700 text-white text-xs font-bold py-1 px-2 rounded transition">
            Edit
        </a>
        <a href="delete_booking.php?id=<?php echo $booking['id']; ?>"
           onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?');"
           class="w-full md:w-auto bg-red-500 hover:bg-red-700 text-white text-xs font-bold py-1 px-2 rounded transition">
            Hapus
        </a>
    </div>
</td>
                                            </tr>
                                        <?php endforeach;
                                    endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal Edit Booking -->
<div id="editBookingModal" class="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative">
        <button onclick="closeEditModal()" class="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-2xl font-bold focus:outline-none">&times;</button>
        <h2 class="text-xl font-bold mb-4 border-b-2 border-indigo-600 pb-2">Edit Booking</h2>
        <form id="editBookingForm" method="POST">
            <input type="hidden" name="edit_id" id="edit_id">
            <div class="mb-3">
                <label class="block mb-1 font-semibold">Nama Pelanggan</label>
                <input type="text" name="edit_user_name" id="edit_user_name" class="w-full border rounded px-3 py-2" required>
            </div>
            <div class="mb-3">
                <label class="block mb-1 font-semibold">No. Telepon</label>
                <input type="text" name="edit_user_phone" id="edit_user_phone" class="w-full border rounded px-3 py-2" required>
            </div>
            <div class="mb-3">
                <label class="block mb-1 font-semibold">Status</label>
                <select name="edit_status" id="edit_status" class="w-full border rounded px-3 py-2">
                    <option value="Pending">Pending</option>
                    <option value="Confirmed">Confirmed</option>
                    <option value="Completed">Completed</option>
                    <option value="Cancelled">Cancelled</option>
                </select>
            </div>
            <div class="flex justify-end">
                <button type="button" onclick="closeEditModal()" class="mr-2 px-4 py-2 rounded bg-gray-300 hover:bg-gray-400 text-gray-700">Batal</button>
                <button type="submit" name="save_edit" class="px-4 py-2 rounded bg-indigo-600 hover:bg-indigo-700 text-white font-bold">Simpan</button>
            </div>
        </form>
    </div>
</div>

    <script>
        function toggleBookingSubmenu() {
            const submenu = document.getElementById('bookingSubmenu');
            const arrowIcon = document.getElementById('arrowBookingIcon');
            submenu.classList.toggle('hidden');
            arrowIcon.classList.toggle('rotate-180');
        }

        function toggleSubmenu() {
            const submenu = document.getElementById('submenu');
            const arrowIcon = document.getElementById('arrowIcon');
            submenu.classList.toggle('hidden');
            arrowIcon.classList.toggle('rotate-180');
        }

        function toggleLowonganSubmenu() {
            const submenu = document.getElementById('lowonganSubmenu');
            const icon = document.getElementById('arrowLowonganIcon');
            submenu.classList.toggle('hidden');
            icon.classList.toggle('rotate-180');
        }

        function openEditModal(booking) {
            // Populate the form fields with the booking data
            document.getElementById('edit_id').value = booking.id;
            document.getElementById('edit_user_name').value = booking.user_name;
            document.getElementById('edit_user_phone').value = booking.user_phone;
            document.getElementById('edit_status').value = booking.status;

            // Show the modal
            document.getElementById('editBookingModal').classList.remove('hidden');
        }

        function closeEditModal() {
            // Hide the modal
            document.getElementById('editBookingModal').classList.add('hidden');
        }

        // Keep the active submenu open on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Set active class for current page in sidebar
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar nav ul li a');
            navLinks.forEach(link => {
                // Remove active class from all direct links initially
                link.closest('li').classList.remove('active');

                if (currentPath.includes(link.getAttribute('href'))) {
                    link.closest('li').classList.add('active');

                    // If this active link is inside a submenu, open it
                    const parentUl = link.closest('ul');
                    if (parentUl && (parentUl.id === 'submenu' || parentUl.id === 'bookingSubmenu' || parentUl.id === 'lowonganSubmenu')) {
                        parentUl.classList.remove('hidden');

                        const parentButton = parentUl.previousElementSibling;
                        if (parentButton) {
                            const arrowIcon = parentButton.querySelector('.fa-chevron-down');
                            if (arrowIcon) {
                                arrowIcon.classList.add('rotate-180');
                            }
                            // Ensure the parent LI (which contains the button) does not get the 'active' background
                            const parentLiWithButton = parentButton.closest('li');
                            if (parentLiWithButton) {
                                parentLiWithButton.classList.remove('active');
                            }
                        }
                    }
                }
            });

            // Profile dropdown functionality
            const profileDropdown = document.getElementById('profileDropdown');
            const profileDropdownMenu = document.getElementById('profileDropdownMenu');

            if (profileDropdown && profileDropdownMenu) {
                profileDropdown.addEventListener('click', function(e) {
                    e.stopPropagation();
                    profileDropdownMenu.classList.toggle('hidden');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!profileDropdown.contains(e.target)) {
                        profileDropdownMenu.classList.add('hidden');
                    }
                });
            }
        });
    </script>
</body>
</html>

<?php
if (isset($_POST['save_edit'])) {
    $id = $_POST['edit_id'];
    $user_name = $_POST['edit_user_name'];
    $user_phone = $_POST['edit_user_phone'];
    $status = $_POST['edit_status'];
    $stmt = $conn->prepare("UPDATE bookings SET user_name=?, user_phone=?, status=? WHERE id=?");
    $stmt->execute([$user_name, $user_phone, $status, $id]);
    // Optional: tampilkan pesan sukses atau reload halaman
    echo "<script>window.location='data_Booking.php';</script>";
    exit;
}
?>
