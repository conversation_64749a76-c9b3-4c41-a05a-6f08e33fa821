<?php
require_once 'Admin/db_config.php';

try {
    // Add harga column if it doesn't exist
    $stmt = $conn->prepare("SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                           WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'produk' 
                           AND COLUMN_NAME = 'harga'");
    $stmt->execute([$database]);
    
    if ($stmt->fetchColumn() == 0) {
        // Add the harga column
        $conn->exec("ALTER TABLE produk ADD COLUMN harga DECIMAL(10,2) NOT NULL DEFAULT 0.00");
        echo "Successfully added harga column to produk table\n";
    } else {
        echo "harga column already exists\n";
    }
    
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?> 