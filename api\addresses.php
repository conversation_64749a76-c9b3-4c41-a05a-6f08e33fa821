<?php
require_once '../Admin/db_config.php';

header('Content-Type: application/json');

$response = ['status' => 'error', 'message' => 'Invalid request.'];

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        // For now, fetch all addresses. In a real app, you'd filter by user_id.
        // If you have a user login system, replace the hardcoded ID with the actual logged-in user's ID.
        // Temporarily fetching all to debug display issue.
        $stmt = $conn->prepare("SELECT id, address_name, full_address, phone_number FROM user_addresses ORDER BY created_at DESC");
        $stmt->execute();
        $addresses = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if ($addresses) {
            $response = ['status' => 'success', 'data' => $addresses];
        } else {
            $response = ['status' => 'success', 'message' => 'No addresses found.', 'data' => []];
        }
    } catch (PDOException $e) {
        $response = ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
        error_log('Database Error in addresses.php (GET): ' . $e->getMessage());
    }
} elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);

    $address_name = $input['address_name'] ?? null;
    $full_address = $input['full_address'] ?? null;
    $phone_number = $input['phone_number'] ?? null;
    // $pembeli_id = 1; // Assuming dummy user ID for now - REMOVED

    if ($address_name && $full_address && $phone_number) {
        try {
            $conn->beginTransaction();

            // 1. Determine pembeli_id (buyer ID) - Find or Create
            $stmt_check_buyer = $conn->prepare("SELECT id FROM pembeli WHERE phone = :phone LIMIT 1");
            $stmt_check_buyer->execute([
                ':phone' => $phone_number
            ]);
            $existing_buyer = $stmt_check_buyer->fetch(PDO::FETCH_ASSOC);

            $pembeli_id = null;
            if ($existing_buyer) {
                $pembeli_id = $existing_buyer['id'];
            } else {
                // Create new buyer if not exists, using address_name as nama_pembeli
                $stmt_insert_buyer = $conn->prepare("INSERT INTO pembeli (nama_pembeli, phone) VALUES (:nama_pembeli, :phone)");
                $stmt_insert_buyer->execute([
                    ':nama_pembeli' => $address_name,
                    ':phone' => $phone_number
                ]);
                $pembeli_id = $conn->lastInsertId();
            }

            if (!$pembeli_id) {
                throw new Exception("Gagal mendapatkan ID pembeli.");
            }

            $stmt = $conn->prepare("INSERT INTO user_addresses (pembeli_id, address_name, full_address, phone_number) VALUES (:pembeli_id, :address_name, :full_address, :phone_number)");
            $stmt->execute([
                ':pembeli_id' => $pembeli_id,
                ':address_name' => $address_name,
                ':full_address' => $full_address,
                ':phone_number' => $phone_number
            ]);
            $conn->commit();
            $response = ['status' => 'success', 'message' => 'Address added successfully.', 'id' => $conn->lastInsertId()];
        } catch (PDOException $e) {
            $conn->rollBack();
            $response = ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
            error_log('Database Error in addresses.php (POST): ' . $e->getMessage());
        } catch (Exception $e) {
            $conn->rollBack();
            $response = ['status' => 'error', 'message' => $e->getMessage()];
            error_log('General Error in addresses.php (POST): ' . $e->getMessage());
        }
    } else {
        $response = ['status' => 'error', 'message' => 'Missing required fields.'];
    }
} else {
    $response = ['status' => 'error', 'message' => 'Unsupported request method.'];
}

echo json_encode($response);
?> 