<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Model Images - Face Detection</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">🖼️ Manage Model Images</h1>
                <p class="text-gray-600">Kelola gambar model untuk rekomendasi rambut berdasarkan bentuk wajah</p>
                <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 class="font-bold text-blue-800">📁 Path Gambar:</h3>
                    <p class="text-sm text-blue-700 mt-1">
                        <code>C:\xampp\htdocs\aplikasi-pixel\rekomendasi\{shape}\{haircut}.jpg</code>
                    </p>
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">📊 Statistik Gambar</h2>
                <div id="statistics" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <!-- Statistics will be loaded here -->
                </div>
            </div>

            <!-- Image Management -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🔧 Manajemen Gambar</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <button onclick="scanAllImages()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-search mr-2"></i>Scan Semua Gambar
                    </button>
                    <button onclick="checkMissingImages()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Cek Gambar Hilang
                    </button>
                    <button onclick="generatePlaceholders()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-image mr-2"></i>Generate Placeholder
                    </button>
                </div>
                <div id="managementResults" class="space-y-4">
                    <p class="text-gray-500">Klik tombol di atas untuk memulai manajemen gambar...</p>
                </div>
            </div>

            <!-- Face Shape Galleries -->
            <div id="galleries" class="space-y-8">
                <!-- Galleries will be generated here -->
            </div>
        </div>
    </div>

    <script>
        // Data rekomendasi dari Python analysis
        const pythonRecommendations = {
            'oval': [
                'Textured Crop',
                'Pompadour', 
                'Man Bun',
                'Long Shaggy',
                'Long Layered',
                'Haircut',
                'Classic Undercut',
                'Caesar Cut',
                'Buzz Haircut'
            ],
            'round': [
                'Two Block Hair',
                'Taper Fade',
                'Fringe Haircut',
                'Fluffy with Low Fade',
                'Crop Cut',
                'Comma Hair',
                'Buzz Cut'
            ],
            'square': [
                'Wajah Persegi (Square)',
                'Slick Back',
                'Side Swept',
                'Quiff',
                'Gaya Rambut Spike',
                'Crew Cut',
                'Faux Hawk',
                'Comb Over'
            ],
            'heart': [
                'Taper Fade',
                'Slick Back',
                'Side Part Fringe',
                'Short Faux Hawk',
                'Long hair side part',
                'Long Fringe',
                'Classic Side Part',
                'Classic Quiff'
            ],
            'rectangular': [
                'Textured Crop',
                'Side Part',
                'Messy Fringe Haircuts for Men',
                'Short Spiky',
                'Crew Cut',
                'Slick Back Hairstyles for Men'
            ]
        };

        // Files yang ada di folder (berdasarkan scan)
        const existingFiles = {
            'oval': [
                'Buzz Haircut.jpg',
                'Caesar Cut.jpg',
                'Classic Undercut.jpg',
                'Haircut.jpg',
                'Long Layered.jpg',
                'Long Shaggy.jpg',
                'Man Bun.jpg',
                'Pompadour.jpg',
                'Textured Crop.jpg'
            ],
            'round': [
                'Buzz Cut.jpg',
                'Comma Hair.jpg',
                'Crop Cut.jpg',
                'Fluffy with Low Fade.jpg',
                'Fringe Haircut.jpg',
                'Taper Fade.jpg',
                'Two Block Hair.jpg'
            ],
            'square': [
                'Comb Over.jpg',
                'Crew Cut.jpg',
                'Faux Hawk.jpg',
                'Gaya Rambut Spike.jpg',
                'Quiff.jpg',
                'Side Swept.jpg',
                'Slick Back.jpg',
                'Wajah Persegi (Square).jpg'
            ],
            'heart': [
                'Classic Quiff.jpg',
                'Classic Side Part.jpg',
                'Long Fringe.jpg',
                'Long hair side part.jpg',
                'Short Faux Hawk.jpg',
                'Side Part Fringe.jpg',
                'Slick Back.jpg',
                'Taper Fade.jpg'
            ],
            'rectangular': [
                'Crew Cut.jpg',
                'Messy Fringe Haircuts for Men.jpg',
                'Short Spiky.jpg',
                'Side Part.jpg',
                'Slick Back Hairstyles for Men.jpg',
                'Textured Crop.jpg'
            ]
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            generateStatistics();
            generateGalleries();
        });

        function generateStatistics() {
            const statisticsDiv = document.getElementById('statistics');
            
            Object.keys(pythonRecommendations).forEach(shape => {
                const recommended = pythonRecommendations[shape].length;
                const existing = existingFiles[shape].length;
                const coverage = Math.round((existing / recommended) * 100);
                
                const colorClass = coverage >= 90 ? 'bg-green-50 border-green-200' : 
                                 coverage >= 70 ? 'bg-yellow-50 border-yellow-200' : 
                                 'bg-red-50 border-red-200';
                
                const textColor = coverage >= 90 ? 'text-green-800' : 
                                coverage >= 70 ? 'text-yellow-800' : 
                                'text-red-800';

                statisticsDiv.innerHTML += `
                    <div class="border rounded-lg p-4 ${colorClass}">
                        <h3 class="font-bold ${textColor} capitalize">${shape}</h3>
                        <p class="text-sm ${textColor}">
                            ${existing}/${recommended} gambar<br>
                            <span class="font-bold">${coverage}% coverage</span>
                        </p>
                    </div>
                `;
            });
        }

        function generateGalleries() {
            const galleriesDiv = document.getElementById('galleries');
            
            Object.keys(pythonRecommendations).forEach(shape => {
                const gallery = document.createElement('div');
                gallery.className = 'bg-white rounded-lg shadow-lg p-6';
                gallery.innerHTML = `
                    <h2 class="text-xl font-bold text-gray-800 mb-4 capitalize">${shape} Face Shape</h2>
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4" id="gallery-${shape}">
                        <!-- Images will be added here -->
                    </div>
                `;
                galleriesDiv.appendChild(gallery);
                
                // Add images to gallery
                pythonRecommendations[shape].forEach(haircut => {
                    const fileName = `${haircut}.jpg`;
                    const exists = existingFiles[shape].includes(fileName);
                    const imagePath = `rekomendasi/${shape}/${fileName}`;
                    
                    addImageToGallery(shape, haircut, imagePath, exists);
                });
            });
        }

        function addImageToGallery(shape, haircut, imagePath, exists) {
            const gallery = document.getElementById(`gallery-${shape}`);
            const imageCard = document.createElement('div');
            imageCard.className = 'flex flex-col items-center p-3 border rounded-lg';
            
            if (exists) {
                imageCard.className += ' border-green-500 bg-green-50';
                imageCard.innerHTML = `
                    <div class="w-20 h-20 rounded-lg overflow-hidden border-2 border-green-500 mb-2">
                        <img src="${imagePath}" alt="${haircut}" class="w-full h-full object-cover"
                             onerror="this.parentElement.innerHTML='<div class=\\'w-full h-full bg-gray-200 flex items-center justify-center\\'>❌</div>'">
                    </div>
                    <p class="text-xs text-center font-medium text-green-700">${haircut}</p>
                    <span class="text-xs text-green-600">✅ Available</span>
                `;
            } else {
                imageCard.className += ' border-red-500 bg-red-50';
                imageCard.innerHTML = `
                    <div class="w-20 h-20 rounded-lg overflow-hidden border-2 border-red-500 mb-2 bg-gray-200 flex items-center justify-center">
                        <i class="fas fa-image text-gray-400 text-2xl"></i>
                    </div>
                    <p class="text-xs text-center font-medium text-red-700">${haircut}</p>
                    <span class="text-xs text-red-600">❌ Missing</span>
                    <button onclick="createPlaceholder('${shape}', '${haircut}')" 
                            class="text-xs bg-blue-500 text-white px-2 py-1 rounded mt-1">
                        Create Placeholder
                    </button>
                `;
            }
            
            gallery.appendChild(imageCard);
        }

        function scanAllImages() {
            const resultsDiv = document.getElementById('managementResults');
            resultsDiv.innerHTML = '<p class="text-blue-600">🔍 Scanning all images...</p>';
            
            let totalRecommended = 0;
            let totalExisting = 0;
            let missingImages = [];
            
            Object.keys(pythonRecommendations).forEach(shape => {
                const recommended = pythonRecommendations[shape];
                const existing = existingFiles[shape];
                
                totalRecommended += recommended.length;
                totalExisting += existing.length;
                
                recommended.forEach(haircut => {
                    const fileName = `${haircut}.jpg`;
                    if (!existing.includes(fileName)) {
                        missingImages.push(`${shape}/${haircut}`);
                    }
                });
            });
            
            const coverage = Math.round((totalExisting / totalRecommended) * 100);
            
            resultsDiv.innerHTML = `
                <div class="space-y-4">
                    <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h3 class="font-bold text-blue-800">📊 Scan Results</h3>
                        <p class="text-blue-700">
                            Total Recommended: ${totalRecommended}<br>
                            Total Existing: ${totalExisting}<br>
                            Coverage: ${coverage}%<br>
                            Missing: ${missingImages.length} images
                        </p>
                    </div>
                    
                    ${missingImages.length > 0 ? `
                        <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                            <h3 class="font-bold text-red-800">❌ Missing Images:</h3>
                            <ul class="text-sm text-red-700 mt-2 space-y-1">
                                ${missingImages.slice(0, 10).map(img => `<li>• ${img}</li>`).join('')}
                                ${missingImages.length > 10 ? `<li>... and ${missingImages.length - 10} more</li>` : ''}
                            </ul>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        function checkMissingImages() {
            const resultsDiv = document.getElementById('managementResults');
            resultsDiv.innerHTML = '<p class="text-yellow-600">⚠️ Checking for missing images...</p>';
            
            let missingByShape = {};
            
            Object.keys(pythonRecommendations).forEach(shape => {
                missingByShape[shape] = [];
                
                pythonRecommendations[shape].forEach(haircut => {
                    const fileName = `${haircut}.jpg`;
                    if (!existingFiles[shape].includes(fileName)) {
                        missingByShape[shape].push(haircut);
                    }
                });
            });
            
            let html = '<div class="space-y-4">';
            
            Object.keys(missingByShape).forEach(shape => {
                if (missingByShape[shape].length > 0) {
                    html += `
                        <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <h3 class="font-bold text-yellow-800 capitalize">⚠️ Missing in ${shape}:</h3>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                                ${missingByShape[shape].map(haircut => `
                                    <div class="text-sm text-yellow-700 bg-white p-2 rounded border">
                                        ${haircut}
                                        <button onclick="createPlaceholder('${shape}', '${haircut}')" 
                                                class="block text-xs bg-blue-500 text-white px-2 py-1 rounded mt-1 w-full">
                                            Create Placeholder
                                        </button>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                }
            });
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        function generatePlaceholders() {
            const resultsDiv = document.getElementById('managementResults');
            resultsDiv.innerHTML = '<p class="text-green-600">🎨 Generating placeholders...</p>';
            
            // Simulate placeholder generation
            setTimeout(() => {
                resultsDiv.innerHTML = `
                    <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <h3 class="font-bold text-green-800">✅ Placeholder Generation Complete</h3>
                        <p class="text-green-700 mt-2">
                            Placeholder images have been generated for missing haircuts.<br>
                            <small>Note: This is a simulation. In production, you would need to implement actual image generation.</small>
                        </p>
                    </div>
                `;
            }, 2000);
        }

        function createPlaceholder(shape, haircut) {
            alert(`Creating placeholder for ${haircut} in ${shape} category...`);
            // In production, this would create an actual placeholder image
        }
    </script>
</body>
</html>
