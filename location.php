<?php
// Start session to store selected location
session_start();
require_once 'admin/db_config.php'; // Adjust path if db_config.php is in a different location

$fetched_locations = [];
try {
    $stmt = $conn->query("SELECT id, name, address, image_filename FROM locations ORDER BY id DESC");
    $fetched_locations = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Fetch locations error on public page: " . $e->getMessage());
    // Fallback to empty array to prevent display errors
}

// Map fetched locations to the format expected by the original frontend
$locations = [];
$js_branches = []; // To populate the JavaScript branches array
$upload_dir_public = "uploads/locations/"; // Path for uploaded images from location.php context

foreach ($fetched_locations as $loc) {
    $locations[] = [
        'id' => $loc['id'],
        'name' => $loc['name'],
        'address' => $loc['address'],
        'image' => htmlspecialchars($loc['image_filename']),
    ];

    // For the JavaScript branches array, we need lat/lng.
    // Since they are not in the DB, we'll use dummy values for now.
    // This part requires user attention if they want real geolocation.
    $js_branches[] = [
        'id' => $loc['id'],
        'name' => $loc['name'],
        'lat' => -6.7320229, // Dummy latitude for example
        'lng' => 108.5523164  // Dummy longitude for example
    ];
}

// Convert $js_branches to JSON for JavaScript
$js_branches_json = json_encode($js_branches);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Select Location - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
        }
        .location-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="sticky top-0 z-10 bg-white p-4 shadow-sm">
        <div class="flex items-center justify-between">
            <a href="javascript:history.back()" class="text-gray-600">
                <i class="fas fa-arrow-left text-lg"></i>
            </a>
            <h1 class="text-xl font-bold text-gray-900">Select Location</h1>
            <div class="w-6"></div>
        </div>
    </header>

    <!-- Search Bar -->
    <div class="px-4 py-3 bg-white">
        <div class="relative">
            <input type="text" placeholder="Search locations..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0A5144] focus:border-transparent">
            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
        </div>
    </div>

    <!-- Location List -->
    <div class="px-4 py-4">
        <h2 class="text-lg font-bold text-gray-900 mb-3">Nearby Locations</h2>
        
        <div class="space-y-4">
            <?php if (empty($locations)): ?>
                <p class="text-gray-600 text-center text-lg">Saat ini belum ada lokasi yang tersedia.</p>
            <?php else: ?>
                <?php foreach ($locations as $location): ?>
                <form action="booking.php" method="post" class="location-card transition-all duration-200">
                    <input type="hidden" name="location_id" value="<?php echo $location['id']; ?>">
                    <button type="submit" class="w-full bg-white rounded-lg shadow-sm p-4 flex items-start border border-gray-200">
                        <?php if (!empty($location['image'])): ?>
                            <img src="<?php echo $upload_dir_public . $location['image']; ?>" alt="<?php echo $location['name']; ?>" class="w-24 h-24 rounded-lg object-cover mr-3">
                        <?php else: ?>
                            <div class="w-24 h-24 rounded-lg object-cover mr-3 bg-gray-200 flex items-center justify-center text-gray-500"><i class="fas fa-image text-2xl"></i></div>
                        <?php endif; ?>
                        <div class="flex-1 text-left">
                            <h3 class="text-xl font-bold text-gray-900"><?php echo htmlspecialchars($location['name']); ?></h3>
                            <p class="text-sm text-gray-600 mt-2">
                                <i class="fas fa-map-marker-alt text-[#0A5144] mr-1"></i> <?php echo htmlspecialchars($location['address']); ?></p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400 mt-2"></i>
                    </button>
                </form>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Current Location Button -->
    <div class="fixed bottom-0 left-0 right-0 bg-white p-4 shadow-lg border-t border-gray-100">
        <button id="currentLocationBtn" class="w-full py-3 rounded-lg border-2 border-[#0A5144] text-[#0A5144] font-bold flex items-center justify-center">
            <i class="fas fa-location-arrow mr-2"></i>
            Use Current Location
        </button>
    </div>

<nav class="fixed bottom-0 left-0 right-0 bg-white shadow-lg flex justify-around py-3 z-50 border-t border-gray-100">
  <a href="index.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-home text-lg mb-1"></i>
    <span>Home</span>
  </a>
  <a href="service.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'treatment.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-cut text-lg mb-1"></i>
    <span>Services</span>
  </a>
  <a href="produk.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'produk.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-box-open text-lg mb-1"></i>
    <span>Produk</span>
  </a>
  <a href="booking.php" class="flex flex-col items-center text-xs 
    <?php echo (basename($_SERVER['PHP_SELF']) == 'booking.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
    <i class="fas fa-calendar-alt text-lg mb-1"></i>
    <span>Book</span>
  </a>
</nav>
    <script>
    const branches = <?php echo $js_branches_json; ?>;

    // Haversine formula to calculate distance (in km)
    function getDistance(lat1, lng1, lat2, lng2) {
        const R = 6371; // Radius of the earth in km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a =
            Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    document.getElementById('currentLocationBtn').addEventListener('click', function() {
        if (!navigator.geolocation) {
            alert('Geolocation tidak didukung di browser Anda.');
            return;
        }
        this.disabled = true;
        this.innerHTML = '<span class="animate-spin mr-2"><i class="fas fa-spinner"></i></span> Mencari lokasi...';
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const userLat = position.coords.latitude;
                const userLng = position.coords.longitude;

                // Cari cabang terdekat
                let minDist = Infinity;
                let nearest = branches[0];
                branches.forEach(branch => {
                    const dist = getDistance(userLat, userLng, branch.lat, branch.lng);
                    if (dist < minDist) {
                        minDist = dist;
                        nearest = branch;
                    }
                });

                // Redirect ke booking.php dengan id cabang terdekat
                window.location.href = 'booking.php?location_id=' + nearest.id;
            },
            function(error) {
                alert('Gagal mendapatkan lokasi: ' + error.message);
                document.getElementById('currentLocationBtn').disabled = false;
                document.getElementById('currentLocationBtn').innerHTML = '<i class="fas fa-location-arrow mr-2"></i>Use Current Location';
            }
        );
    });
    </script>
</body>
</html>