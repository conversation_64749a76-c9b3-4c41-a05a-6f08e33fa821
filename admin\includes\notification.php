<?php
/**
 * Standard Notification Template for Admin Pages
 * Include this file in admin pages to show consistent notifications
 */
?>

<!-- Success/Error Messages -->
<?php if (isset($_SESSION['message'])): ?>
    <div id="notification" class="mb-4 p-4 rounded-lg <?php echo $_SESSION['message_type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?> transition-all duration-500 ease-in-out">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <?php if ($_SESSION['message_type'] === 'success'): ?>
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                <?php else: ?>
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                <?php endif; ?>
                <span><?php echo htmlspecialchars($_SESSION['message']); ?></span>
            </div>
            <button onclick="hideNotification()" class="ml-4 text-gray-500 hover:text-gray-700 font-bold text-lg notification-dismiss" title="Tutup notifikasi">&times;</button>
        </div>
    </div>
    <?php unset($_SESSION['message'], $_SESSION['message_type']); ?>
<?php endif; ?>

<!-- Include the notification JavaScript -->
<script src="js/admin_notifications.js"></script>
