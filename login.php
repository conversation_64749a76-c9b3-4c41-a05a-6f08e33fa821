<?php
session_start();
require_once 'Admin/db_config.php';

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

// Handle login form submission
$login_error = '';
$success_message = '';
$email_input = ''; // To retain email input on error

// Check for registration success message
if (isset($_GET['registration']) && $_GET['registration'] === 'success') {
    $success_message = 'Registrasi berhasil! Silakan login dengan akun Anda.';
}

// Check for logout success message
if (isset($_GET['logout']) && $_GET['logout'] === 'success') {
    $success_message = 'Anda telah berhasil logout.';
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $email_input = $email; // Keep email value for pre-filling form

    if (empty($email) || empty($password)) {
        $login_error = 'Email dan kata sandi harus diisi.';
    } else {
        try {
            // Prepare the statement to fetch user by email
            $stmt = $conn->prepare("SELECT id, nama_pembeli, email, password FROM pembeli WHERE email = :email");
            $stmt->execute(['email' => $email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user && password_verify($password, $user['password'])) {
                // Login successful - regenerate session ID for security
                session_regenerate_id(true);

                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_name'] = $user['nama_pembeli'];
                $_SESSION['user_email'] = $user['email'];

                // Set cookies for session persistence
                setcookie('user_id', $user['id'], time() + (86400 * 30), '/', '', false, true); // 30 days, httponly
                setcookie('user_login', $user['nama_pembeli'], time() + (86400 * 30), '/', '', false, true); // 30 days, httponly

                // Check if there's a return URL
                if (isset($_GET['return_url'])) {
                    $return_url = urldecode($_GET['return_url']);
                    header("Location: $return_url");
                } else {
                    header('Location: index.php');
                }
                exit();
            } else {
                // Login failed (email not found or password incorrect)
                $login_error = 'Email atau kata sandi salah.';
            }
        } catch (PDOException $e) {
            error_log("Login error: " . $e->getMessage());
            $login_error = 'Terjadi kesalahan saat mencoba login. Silakan coba lagi nanti.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Login</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
</head>
<body class="bg-[#006A63] flex items-center justify-center min-h-screen font-sans">
  <div class="bg-white p-8 rounded-2xl shadow-lg w-full max-w-sm">
    <h2 class="text-2xl font-bold text-center text-[#006A63] mb-6">Login</h2>

    <?php if ($success_message): ?>
        <div class="bg-green-500 text-white text-sm p-3 rounded-lg mb-4 text-center">
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>

    <?php if ($login_error): ?>
        <div class="bg-red-500 text-white text-sm p-3 rounded-lg mb-4 text-center">
            <?php echo htmlspecialchars($login_error); ?>
        </div>
    <?php endif; ?>

    <form action="login.php" method="POST">
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1" for="email">Email</label>
        <input type="email" id="email" name="email" placeholder="<EMAIL>"
               class="w-full px-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#006A63] bg-gray-100" 
               value="<?php echo htmlspecialchars($email_input); ?>" required />
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1" for="password">Kata Sandi</label>
        <div class="relative">
          <input type="password" id="password" name="password" placeholder="•••"
                 class="w-full px-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#006A63] bg-gray-100" required />
          <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500" onclick="togglePassword()">
            <i class="fa-solid fa-eye" id="password-icon"></i>
          </button>
        </div>
      </div>

      <div class="flex items-center justify-between mb-6 text-sm">
        <label class="flex items-center space-x-2">
          <input type="checkbox" class="accent-[#006A63]" />
          <span>Ingat Aku</span>
        </label>
        <a href="#" class="text-[#006A63] hover:underline">Lupa Kata Sandi?</a>
      </div>

      <button type="submit" class="w-full bg-yellow-400 hover:bg-yellow-300 text-black font-semibold py-2 rounded-xl mb-4">
        Login
      </button>

      <div class="flex items-center my-4">
        <hr class="flex-grow border-gray-300">
        <span class="mx-2 text-gray-500 text-sm">atau</span>
        <hr class="flex-grow border-gray-300">
      </div>

      <p class="text-center text-sm mt-6 text-gray-700">
        Belum punya akun? <a href="registrasi.php" class="text-[#006A63] font-semibold hover:underline">Daftar</a>
      </p>
    </form>
  </div>

  <script>
    function togglePassword() {
      const field = document.getElementById('password');
      const icon = document.getElementById('password-icon');

      if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      }
    }
  </script>
</body>
</html>
