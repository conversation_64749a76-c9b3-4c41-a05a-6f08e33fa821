<?php
require_once '../Admin/db_config.php';

header('Content-Type: application/json');

$response = ['status' => 'error', 'message' => 'Invalid request'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);

    $product_id = $input['product_id'] ?? null;
    $variant_id = $input['variant_id'] ?? null;
    $quantity = $input['quantity'] ?? 0;
    $delivery_cost = $input['delivery_cost'] ?? 0;
    $service_fee = $input['service_fee'] ?? 0;
    $total_amount = $input['total_amount'] ?? 0;
    $address_id = $input['address_id'] ?? null;
    $payment_method_id = $input['payment_method_id'] ?? null;

    // Basic validation
    if (!$product_id || !$variant_id || $quantity <= 0 || !$address_id || !$payment_method_id) {
        $response['message'] = 'Data pesanan tidak lengkap.';
        echo json_encode($response);
        exit();
    }

    try {
        $conn->beginTransaction();

        // 1. Determine pembeli_id (buyer ID)
        // Fetch buyer name and phone from user_addresses table using address_id
        $stmt_address_info = $conn->prepare("SELECT address_name, phone_number FROM user_addresses WHERE id = :address_id");
        $stmt_address_info->execute(['address_id' => $address_id]);
        $address_info = $stmt_address_info->fetch(PDO::FETCH_ASSOC);
        
        $pembeli_id = null;
        if ($address_info) {
            $buyer_name = $address_info['address_name'];
            $buyer_phone = $address_info['phone_number']; // Assuming phone_number can be an identifier for pembeli
            $buyer_email = str_replace(' ', '.', strtolower($buyer_name)) . '@example.com'; // Generate simple placeholder email

            // Check if buyer already exists by name and (optionally) email/phone
            $stmt_check_buyer = $conn->prepare("SELECT id FROM pembeli WHERE nama_pembeli = :nama_pembeli OR email = :email LIMIT 1");
            $stmt_check_buyer->execute([
                ':nama_pembeli' => $buyer_name,
                ':email' => $buyer_email
            ]);
            $existing_buyer = $stmt_check_buyer->fetch(PDO::FETCH_ASSOC);

            if ($existing_buyer) {
                $pembeli_id = $existing_buyer['id'];
            } else {
                // Create new buyer if not exists
                $stmt_insert_buyer = $conn->prepare("INSERT INTO pembeli (nama_pembeli, email) VALUES (:nama_pembeli, :email)");
                $stmt_insert_buyer->execute([
                    ':nama_pembeli' => $buyer_name,
                    ':email' => $buyer_email
                ]);
                $pembeli_id = $conn->lastInsertId();
            }
        } else {
            throw new Exception("Informasi alamat tidak ditemukan untuk pesanan.");
        }

        if (!$pembeli_id) {
            throw new Exception("Gagal mendapatkan ID pembeli.");
        }

        // 2. Insert into orders table for COD
        $created_at = date('Y-m-d H:i:s');
        $order_status = 'pending'; // Or 'COD Pending'
        $payment_status = 'pending'; // Initial payment status for COD

        $stmt_order = $conn->prepare("
            INSERT INTO orders (
                pembeli_id, total_amount, created_at, order_status, payment_status, 
                delivery_cost, service_fee, address_id, payment_method_id
            ) VALUES (
                :pembeli_id, :total_amount, :created_at, :order_status, :payment_status, 
                :delivery_cost, :service_fee, :address_id, :payment_method_id
            )
        ");

        $stmt_order->execute([
            ':pembeli_id' => $pembeli_id,
            ':total_amount' => $total_amount,
            ':created_at' => $created_at,
            ':order_status' => $order_status,
            ':payment_status' => $payment_status,
            ':delivery_cost' => $delivery_cost,
            ':service_fee' => $service_fee,
            ':address_id' => $address_id,
            ':payment_method_id' => $payment_method_id
        ]);

        $order_id = $conn->lastInsertId();

        // 3. Insert into order_items table
        // Fetch product and variant details to store in order_items
        $stmt_prod_var = $conn->prepare("
            SELECT p.nama_produk, vp.ukuran_ml, vp.harga as variant_price
            FROM produk p
            JOIN variasi_produk vp ON p.id = vp.produk_id
            WHERE p.id = :product_id AND vp.id = :variant_id
        ");
        $stmt_prod_var->execute([':product_id' => $product_id, ':variant_id' => $variant_id]);
        $item_details = $stmt_prod_var->fetch(PDO::FETCH_ASSOC);

        if ($item_details) {
            $item_total = $item_details['variant_price'] * $quantity;
            $stmt_item = $conn->prepare("
                INSERT INTO order_items (
                    order_id, product_id, variant_id, quantity, price_per_unit, total_price, 
                    product_name_at_purchase, variant_name_at_purchase
                ) VALUES (
                    :order_id, :product_id, :variant_id, :quantity, :price_per_unit, :total_price, 
                    :product_name_at_purchase, :variant_name_at_purchase
                )
            ");
            $stmt_item->execute([
                ':order_id' => $order_id,
                ':product_id' => $product_id,
                ':variant_id' => $variant_id,
                ':quantity' => $quantity,
                ':price_per_unit' => $item_details['variant_price'],
                ':total_price' => $item_total,
                ':product_name_at_purchase' => $item_details['nama_produk'],
                ':variant_name_at_purchase' => $item_details['ukuran_ml'] . ' ml' // Storing variant info
            ]);
        } else {
            throw new Exception("Detail produk atau varian tidak ditemukan.");
        }

        $conn->commit();
        $response = ['status' => 'success', 'message' => 'Pesanan COD berhasil dibuat!', 'order_id' => $order_id];

    } catch (Exception $e) {
        $conn->rollBack();
        $response['message'] = 'Terjadi kesalahan database: ' . $e->getMessage();
        error_log("COD Order Processing Error: " . $e->getMessage());
    }
}

echo json_encode($response);
?> 