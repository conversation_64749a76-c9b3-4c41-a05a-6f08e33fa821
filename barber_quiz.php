<?php
session_start();
require_once 'admin/db_config.php';

// Check if application data is in session
if (!isset($_SESSION['job_application']) || !isset($_SESSION['application_data'])) {
    header("Location: jobs.php");
    exit();
}

// Ambil quiz dari database
$quiz_questions = $conn->query("SELECT * FROM quiz_questions ORDER BY id ASC")->fetchAll(PDO::FETCH_ASSOC);
$quiz_answers = [];
foreach ($quiz_questions as $q) {
    $quiz_answers[$q['id']] = $conn->query("SELECT * FROM quiz_answers WHERE question_id = " . intval($q['id']))->fetchAll(PDO::FETCH_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Barber Knowledge Quiz - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Poppins', sans-serif; background-color: #f8fafc; }
        .gradient-bg { background: linear-gradient(135deg, #0A5144 0%, #006A63 100%); }
        .quiz-option { transition: all 0.2s ease; }
        .quiz-option:hover { transform: translateY(-2px); }
        .quiz-option.selected { border-color: #0A5144; background-color: #F0F9F8; }
        .quiz-option.correct { border-color: #10B981; background-color: #ECFDF5; }
        .quiz-option.incorrect { border-color: #EF4444; background-color: #FEF2F2; }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="sticky top-0 z-10 bg-white p-4 shadow-sm">
        <div class="flex items-center justify-between">
            <a href="application_form.php" class="text-gray-600">
                <i class="fas fa-arrow-left text-lg"></i>
            </a>
            <h1 class="text-xl font-bold text-gray-900">Barber Knowledge</h1>
            <div class="w-6"></div>
        </div>
    </header>

    <!-- Progress Bar -->
    <div class="px-4 py-3 bg-white">
        <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div class="bg-[#0A5144] h-2.5 rounded-full" id="quiz-progress" style="width: 0%"></div>
        </div>
    </div>

    <!-- Quiz Content -->
    <div class="px-4 py-6 max-w-4xl mx-auto">
        <form id="barberQuiz" action="process_application.php" method="POST">
            <div class="bg-white rounded-xl shadow-sm p-5 mb-6">
                <h2 class="text-xl font-bold text-gray-900 mb-6">Let's assess your barbering knowledge</h2>
                <?php if (empty($quiz_questions)): ?>
                    <p class="text-gray-500">Belum ada pertanyaan quiz.</p>
                <?php else: ?>
                    <?php foreach ($quiz_questions as $i => $q): ?>
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4"><?= ($i+1) . '. ' . htmlspecialchars($q['question']) ?></h3>
                            <div class="space-y-3">
                                <?php foreach ($quiz_answers[$q['id']] as $j => $a): ?>
                                    <div class="quiz-option p-4 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'q<?= $q['id'] ?>')">
                                        <div class="flex items-center">
                                            <div class="w-6 h-6 rounded-full border-2 border-gray-300 mr-3 flex-shrink-0 option-radio"></div>
                                            <span><?= htmlspecialchars($a['answer']) ?></span>
                                        </div>
                                        <input type="radio" name="q<?= $q['id'] ?>" value="<?= $a['id'] ?>" class="hidden">
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            <button type="submit" class="w-full py-3 bg-[#0A5144] text-white font-bold rounded-lg text-lg hover:bg-[#006A63] transition">Submit Quiz</button>
        </form>
    </div>
    <script>
    // Progress bar update
    document.addEventListener('DOMContentLoaded', function() {
        const total = <?= count($quiz_questions) ?>;
        const progress = document.getElementById('quiz-progress');
        if (progress && total > 0) {
            progress.style.width = '0%';
            document.querySelectorAll('input[type=radio]').forEach(function(radio) {
                radio.addEventListener('change', function() {
                    const answered = new Set();
                    document.querySelectorAll('input[type=radio]:checked').forEach(function(r) {
                        answered.add(r.name);
                    });
                    progress.style.width = (answered.size / total * 100) + '%';
                });
            });
        }
    });
    // Option selection styling
    function selectOption(el, qname) {
        document.querySelectorAll('input[name="'+qname+'"]').forEach(function(radio) {
            radio.parentElement.classList.remove('selected');
        });
        el.classList.add('selected');
        el.querySelector('input[type=radio]').checked = true;
    }
    </script>
</body>
</html>