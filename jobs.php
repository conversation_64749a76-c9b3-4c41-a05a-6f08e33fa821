<?php
// Koneksi database
require_once 'admin/db_config.php';

// Periksa dan tambahkan kolom is_active jika belum ada
try {
    $stmt = $conn->query("SHOW COLUMNS FROM jobs LIKE 'is_active'");
    if ($stmt->rowCount() == 0) {
        // Tambahkan kolom is_active
        $conn->exec("ALTER TABLE jobs ADD COLUMN is_active BOOLEAN DEFAULT TRUE");
        // Update semua data existing menjadi aktif
        $conn->exec("UPDATE jobs SET is_active = TRUE WHERE is_active IS NULL");
    }
} catch (PDOException $e) {
    error_log("Error checking/adding is_active column: " . $e->getMessage());
}

// Ambil data lowongan dari database
$jobs = [];
try {
    $stmt = $conn->query("SELECT * FROM jobs WHERE is_active = 1 ORDER BY posted DESC");
    $jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $jobs = [];
    error_log("Error fetching jobs: " . $e->getMessage());
}

// Hitung jumlah lowongan
$total_jobs = count($jobs);

// Ambil data unik untuk filter
$unique_positions = [];
$unique_locations = [];
$unique_types = [];

foreach ($jobs as $job) {
    // Ambil kata kunci dari posisi (misal: "Barber Senior" -> "Barber")
    $position_keywords = explode(' ', $job['position']);
    foreach ($position_keywords as $keyword) {
        $keyword = trim($keyword);
        if (strlen($keyword) > 2 && !in_array($keyword, $unique_positions)) {
            $unique_positions[] = $keyword;
        }
    }

    // Ambil kota dari lokasi (misal: "Jakarta Selatan" -> "Jakarta")
    $location_parts = explode(' ', $job['location']);
    $city = $location_parts[0]; // Ambil kata pertama sebagai kota
    if (!in_array($city, $unique_locations)) {
        $unique_locations[] = $city;
    }

    // Ambil tipe pekerjaan
    if (!in_array($job['type'], $unique_types)) {
        $unique_types[] = $job['type'];
    }
}

// Batasi jumlah filter yang ditampilkan
$unique_positions = array_slice($unique_positions, 0, 3);
$unique_locations = array_slice($unique_locations, 0, 3);
$unique_types = array_slice($unique_types, 0, 3);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Pencarian Job</title>
  <style>
    body {
      margin: 0;
      font-family: 'Segoe UI', sans-serif;
      background: #f8f8f8;
    }
    .header {
      background: #2d5a27;
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 18px;
      position: relative;
    }

    .header .back-btn {
      position: absolute;
      left: 16px;
      font-size: 20px;
      cursor: pointer;
      padding: 4px;
    }

    .header .menu-btn {
      position: absolute;
      right: 16px;
      font-size: 18px;
      cursor: pointer;
      padding: 4px;
    }
    .search-section {
      background: white;
      margin: 0 16px 16px 16px;
      padding: 16px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .search-bar {
      display: flex;
      align-items: center;
      gap: 8px;
      background: #f8f9fa;
      padding: 12px 16px;
      border-radius: 10px;
      border: 1px solid #e9ecef;
      transition: all 0.2s;
    }

    .search-bar:focus-within {
      border-color: #2e5b30;
      box-shadow: 0 0 0 3px rgba(46, 91, 48, 0.1);
    }

    .search-bar input {
      flex: 1;
      border: none;
      outline: none;
      font-size: 16px;
      background: transparent;
      color: #333;
      min-width: 0;
    }

    .search-bar input::placeholder {
      color: #999;
      font-size: 16px;
    }

    .search-btn {
      background: #2e5b30;
      color: white;
      border: none;
      padding: 10px 12px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 44px;
      height: 44px;
    }

    .search-btn:hover {
      background: #1e3f20;
      transform: translateY(-1px);
    }

    .search-btn:active {
      transform: translateY(0);
    }

    @media (max-width: 768px) {
      .search-section {
        margin: 0 12px 12px 12px;
        padding: 12px;
      }

      .search-bar {
        padding: 10px 14px;
      }

      .search-bar input {
        font-size: 16px;
      }

      .search-bar input::placeholder {
        font-size: 14px;
      }

      .search-btn {
        min-width: 40px;
        height: 40px;
        padding: 8px 10px;
        font-size: 14px;
      }
    }

    @media (max-width: 480px) {
      .search-section {
        margin: 0 8px 8px 8px;
        padding: 10px;
      }

      .search-bar {
        padding: 8px 12px;
        gap: 6px;
      }

      .search-bar input::placeholder {
        font-size: 13px;
      }

      .search-btn {
        min-width: 36px;
        height: 36px;
        font-size: 13px;
      }
    }
    .filter-section {
      display: flex;
      gap: 8px;
      align-items: center;
      margin: 0 16px 16px 16px;
      flex-wrap: nowrap;
      overflow-x: auto;
      padding: 4px 0;
    }

    .filter-dropdown {
      flex: 1;
      min-width: 140px;
      max-width: 200px;
    }

    .filter-dropdown select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background: white;
      font-size: 13px;
      color: #333;
      cursor: pointer;
      transition: all 0.2s;
      appearance: none;
      background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 8px center;
      background-size: 16px;
      padding-right: 32px;
    }

    .filter-dropdown select:focus {
      outline: none;
      border-color: #2e5b30;
      box-shadow: 0 0 0 2px rgba(46, 91, 48, 0.1);
    }

    .filter-dropdown select:hover {
      border-color: #2e5b30;
    }

    .clear-filters-btn {
      background: #f44336;
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 13px;
      display: flex;
      align-items: center;
      gap: 6px;
      transition: all 0.2s;
      white-space: nowrap;
      flex-shrink: 0;
    }

    .clear-filters-btn:hover {
      background: #d32f2f;
      transform: translateY(-1px);
    }

    @media (max-width: 768px) {
      .filter-section {
        gap: 6px;
        margin: 0 16px 12px 16px;
      }

      .filter-dropdown {
        min-width: 120px;
        max-width: 160px;
      }

      .filter-dropdown select {
        font-size: 12px;
        padding: 8px 10px;
        padding-right: 28px;
        background-size: 14px;
        background-position: right 6px center;
      }

      .clear-filters-btn {
        padding: 8px 12px;
        font-size: 12px;
      }
    }
    .results-count {
      margin: 0 16px;
      font-size: 13px;
      color: #777;
    }
    .job-card {
      background: white;
      margin: 16px;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05);
      display: flex;
      align-items: center;
      gap: 16px;
      transition: transform 0.2s, box-shadow 0.2s;
      cursor: pointer;
      position: relative;
    }

    .job-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }

    .job-image {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      object-fit: cover;
      flex-shrink: 0;
      background: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: #666;
    }

    .job-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .job-title {
      font-weight: bold;
      font-size: 16px;
      color: #333;
      margin-bottom: 4px;
    }

    .job-desc {
      font-size: 13px;
      color: #666;
      line-height: 1.4;
      margin-bottom: 6px;
    }

    .job-location {
      display: flex;
      align-items: flex-start;
      gap: 4px;
      font-size: 13px;
      color: #666;
      line-height: 1.4;
    }

    .job-location-icon {
      color: #2e5b30;
      font-size: 14px;
      margin-top: 1px;
      flex-shrink: 0;
    }

    .job-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
    }

    .job-type {
      background: #e8f5e8;
      color: #2e5b30;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }

    .job-salary {
      font-weight: bold;
      color: #2e5b30;
      font-size: 14px;
    }

    .job-arrow {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      color: #ccc;
      font-size: 18px;
    }

    .job-card-link {
      text-decoration: none;
      color: inherit;
      display: block;
    }

    .job-card-link:hover {
      text-decoration: none;
      color: inherit;
    }
  </style>
</head>
<body>

  <div class="header">
    <span class="back-btn" onclick="window.location.href='index.php'">&larr;</span>
    <span class="title">Pencarian</span>
    <span class="menu-btn">&#9776;</span>
  </div>

  <div class="search-section">
    <div class="search-bar">
      <input type="text" placeholder="Cari posisi, lokasi, atau tipe pekerjaan..." id="searchInput" />
      <button class="search-btn" onclick="filterJobs()">🔍</button>
    </div>
  </div>

  <div class="filter-section">
    <div class="filter-dropdown">
      <select id="positionFilter" onchange="applyAllFilters()">
        <option value="">Semua Posisi</option>
        <?php foreach ($unique_positions as $position): ?>
          <option value="<?php echo htmlspecialchars($position); ?>"><?php echo htmlspecialchars($position); ?></option>
        <?php endforeach; ?>
      </select>
    </div>

    <div class="filter-dropdown">
      <select id="locationFilter" onchange="applyAllFilters()">
        <option value="">Semua Lokasi</option>
        <?php foreach ($unique_locations as $location): ?>
          <option value="<?php echo htmlspecialchars($location); ?>"><?php echo htmlspecialchars($location); ?></option>
        <?php endforeach; ?>
      </select>
    </div>

    <div class="filter-dropdown">
      <select id="typeFilter" onchange="applyAllFilters()">
        <option value="">Semua Tipe</option>
        <?php foreach ($unique_types as $type): ?>
          <option value="<?php echo htmlspecialchars($type); ?>"><?php echo htmlspecialchars($type); ?></option>
        <?php endforeach; ?>
      </select>
    </div>

    <button class="clear-filters-btn" onclick="clearAllFilters()">
      <span>🗑️</span> Reset
    </button>
  </div>

  <div class="results-count"><?php echo $total_jobs; ?> Jobs Found</div>



  <div id="jobList">
    <?php if (empty($jobs)): ?>
      <div class="job-card">
        <div class="job-header">
          <div>
            <div class="job-title">Tidak ada lowongan tersedia</div>
            <div class="job-role">-</div>
          </div>
        </div>
        <div class="job-desc">
          Saat ini belum ada lowongan pekerjaan yang tersedia. Silakan cek kembali nanti.
        </div>
      </div>
    <?php else: ?>
      <?php foreach ($jobs as $job): ?>
        <a href="job_details.php?id=<?php echo $job['id']; ?>" class="job-card-link">
          <div class="job-card" data-job-id="<?php echo $job['id']; ?>">
            <!-- Job Image -->
            <div class="job-image">
              <?php if (!empty($job['image'])): ?>
                <img src="assets/<?php echo htmlspecialchars($job['image']); ?>" alt="<?php echo htmlspecialchars($job['position']); ?>" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">
              <?php else: ?>
                🏢
              <?php endif; ?>
            </div>

            <!-- Job Content -->
            <div class="job-content">
              <div class="job-title"><?php echo htmlspecialchars($job['position']); ?></div>
              <div class="job-desc"><?php echo htmlspecialchars(substr($job['description'], 0, 80)) . (strlen($job['description']) > 80 ? '...' : ''); ?></div>
              <div class="job-location">
                <span class="job-location-icon">📍</span>
                <span><?php echo htmlspecialchars($job['location']); ?></span>
              </div>
              <div class="job-meta">
                <div class="job-type"><?php echo htmlspecialchars($job['type']); ?></div>
                <div class="job-salary"><?php echo htmlspecialchars($job['salary']); ?></div>
              </div>
            </div>

            <!-- Arrow -->
            <div class="job-arrow">›</div>
          </div>
        </a>
      <?php endforeach; ?>
    <?php endif; ?>
  </div>

  <script>
    function filterJobs() {
      applyAllFilters();
    }

    function applyAllFilters() {
      const keyword = document.getElementById("searchInput").value.toLowerCase();
      const positionFilter = document.getElementById("positionFilter").value.toLowerCase();
      const locationFilter = document.getElementById("locationFilter").value.toLowerCase();
      const typeFilter = document.getElementById("typeFilter").value.toLowerCase();

      const jobLinks = document.querySelectorAll(".job-card-link");

      jobLinks.forEach(jobLink => {
        const job = jobLink.querySelector(".job-card");
        const title = job.querySelector(".job-title")?.innerText.toLowerCase() || '';
        const desc = job.querySelector(".job-desc")?.innerText.toLowerCase() || '';
        const location = job.querySelector(".job-location span:last-child")?.innerText.toLowerCase() || '';
        const type = job.querySelector(".job-type")?.innerText.toLowerCase() || '';

        // Check keyword search
        const matchesKeyword = keyword === '' ||
          title.includes(keyword) ||
          desc.includes(keyword) ||
          location.includes(keyword) ||
          type.includes(keyword);

        // Check position filter
        const matchesPosition = positionFilter === '' ||
          title.includes(positionFilter);

        // Check location filter
        const matchesLocation = locationFilter === '' ||
          location.includes(locationFilter);

        // Check type filter
        const matchesType = typeFilter === '' ||
          type.includes(typeFilter);

        if (matchesKeyword && matchesPosition && matchesLocation && matchesType) {
          jobLink.style.display = "block";
        } else {
          jobLink.style.display = "none";
        }
      });

      // Update results count
      const visibleJobs = document.querySelectorAll(".job-card-link[style='display: block;'], .job-card-link:not([style*='display: none'])");
      const resultsCount = document.querySelector(".results-count");
      resultsCount.textContent = visibleJobs.length + " Jobs Found";
    }

    function clearAllFilters() {
      // Clear search input
      document.getElementById("searchInput").value = '';

      // Reset all dropdowns
      document.getElementById("positionFilter").value = '';
      document.getElementById("locationFilter").value = '';
      document.getElementById("typeFilter").value = '';

      // Apply filters (which will show all jobs)
      applyAllFilters();
    }

    // Initialize filters on page load
    document.addEventListener('DOMContentLoaded', function() {
      applyAllFilters();
    });
  </script>

</body>
</html>
