<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Debug Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">🔍 Image Debug Dashboard</h1>
                <p class="text-gray-600">Real-time debugging untuk masalah gambar model</p>
                <div class="mt-4 flex gap-4">
                    <button onclick="runAllTests()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-play mr-2"></i>Run All Tests
                    </button>
                    <button onclick="checkServerInfo()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-server mr-2"></i>Check Server Info
                    </button>
                    <button onclick="testSpecificFiles()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-images mr-2"></i>Test Specific Files
                    </button>
                </div>
            </div>

            <!-- Status Overview -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">📊 Status Overview</h2>
                <div id="statusOverview" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="text-center p-4 border rounded-lg">
                        <div class="text-2xl mb-2">⏳</div>
                        <div class="text-sm text-gray-600">Loading...</div>
                    </div>
                </div>
            </div>

            <!-- Server Information -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🖥️ Server Information</h2>
                <div id="serverInfo" class="space-y-2">
                    <p class="text-gray-500">Click "Check Server Info" to load...</p>
                </div>
            </div>

            <!-- File Test Results -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">📁 File Test Results</h2>
                <div id="fileTestResults" class="space-y-4">
                    <p class="text-gray-500">Click "Test Specific Files" to load...</p>
                </div>
            </div>

            <!-- Detailed Scan Results -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🔍 Detailed Scan Results</h2>
                <div id="scanResults" class="space-y-4">
                    <p class="text-gray-500">Click "Run All Tests" to load...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function runAllTests() {
            updateStatus('Running comprehensive tests...', 'blue');
            
            try {
                const response = await fetch('check_image_files.php?action=scan');
                const data = await response.json();
                
                if (data.success) {
                    displayScanResults(data);
                    updateStatusOverview(data);
                } else {
                    updateStatus('Error: ' + data.error, 'red');
                }
            } catch (error) {
                updateStatus('Network error: ' + error.message, 'red');
                console.error('Error:', error);
            }
        }

        async function checkServerInfo() {
            updateStatus('Checking server information...', 'green');
            
            try {
                const response = await fetch('check_image_files.php?action=info');
                const data = await response.json();
                
                if (data.success) {
                    displayServerInfo(data.server_info);
                } else {
                    updateStatus('Error: ' + data.error, 'red');
                }
            } catch (error) {
                updateStatus('Network error: ' + error.message, 'red');
                console.error('Error:', error);
            }
        }

        async function testSpecificFiles() {
            updateStatus('Testing specific files...', 'purple');
            
            try {
                const response = await fetch('check_image_files.php?action=test');
                const data = await response.json();
                
                if (data.success) {
                    displayFileTestResults(data.data);
                } else {
                    updateStatus('Error: ' + data.error, 'red');
                }
            } catch (error) {
                updateStatus('Network error: ' + error.message, 'red');
                console.error('Error:', error);
            }
        }

        function updateStatus(message, color) {
            console.log(`[${color.toUpperCase()}] ${message}`);
        }

        function updateStatusOverview(data) {
            const overviewDiv = document.getElementById('statusOverview');
            
            let totalFiles = 0;
            let totalShapes = 0;
            let workingShapes = 0;
            let issues = 0;
            
            Object.keys(data.data).forEach(shape => {
                totalShapes++;
                if (Array.isArray(data.data[shape])) {
                    totalFiles += data.data[shape].length;
                    if (data.data[shape].length > 0) workingShapes++;
                } else {
                    issues++;
                }
            });
            
            overviewDiv.innerHTML = `
                <div class="text-center p-4 border rounded-lg ${totalFiles > 0 ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}">
                    <div class="text-2xl mb-2">${totalFiles > 0 ? '✅' : '❌'}</div>
                    <div class="font-bold">${totalFiles}</div>
                    <div class="text-sm text-gray-600">Total Files</div>
                </div>
                <div class="text-center p-4 border rounded-lg ${workingShapes === totalShapes ? 'border-green-500 bg-green-50' : 'border-yellow-500 bg-yellow-50'}">
                    <div class="text-2xl mb-2">${workingShapes === totalShapes ? '✅' : '⚠️'}</div>
                    <div class="font-bold">${workingShapes}/${totalShapes}</div>
                    <div class="text-sm text-gray-600">Working Shapes</div>
                </div>
                <div class="text-center p-4 border rounded-lg ${data.base_dir_exists ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}">
                    <div class="text-2xl mb-2">${data.base_dir_exists ? '✅' : '❌'}</div>
                    <div class="font-bold">${data.base_dir_exists ? 'Found' : 'Missing'}</div>
                    <div class="text-sm text-gray-600">Base Directory</div>
                </div>
                <div class="text-center p-4 border rounded-lg ${issues === 0 ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}">
                    <div class="text-2xl mb-2">${issues === 0 ? '✅' : '❌'}</div>
                    <div class="font-bold">${issues}</div>
                    <div class="text-sm text-gray-600">Issues</div>
                </div>
            `;
        }

        function displayServerInfo(serverInfo) {
            const infoDiv = document.getElementById('serverInfo');
            
            let html = '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';
            
            Object.keys(serverInfo).forEach(key => {
                const value = serverInfo[key];
                const displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                
                html += `
                    <div class="p-3 border rounded-lg">
                        <div class="font-medium text-gray-800">${displayKey}</div>
                        <div class="text-sm text-gray-600 mt-1 break-all">${value}</div>
                    </div>
                `;
            });
            
            html += '</div>';
            infoDiv.innerHTML = html;
        }

        function displayFileTestResults(testResults) {
            const resultsDiv = document.getElementById('fileTestResults');
            
            let html = '<div class="space-y-3">';
            
            testResults.forEach(result => {
                const statusClass = result.exists && result.readable ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50';
                const statusIcon = result.exists && result.readable ? '✅' : '❌';
                
                html += `
                    <div class="p-4 border rounded-lg ${statusClass}">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="font-medium">${statusIcon} ${result.webpath}</div>
                                <div class="text-sm text-gray-600 mt-1">
                                    Exists: ${result.exists ? 'Yes' : 'No'} | 
                                    Readable: ${result.readable ? 'Yes' : 'No'} | 
                                    Size: ${result.size} bytes
                                    ${result.mime ? ` | Type: ${result.mime}` : ''}
                                </div>
                            </div>
                            <div class="ml-4">
                                <button onclick="testImageLoad('${result.webpath}')" class="bg-blue-500 text-white text-xs px-2 py-1 rounded">
                                    Test Load
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        function displayScanResults(data) {
            const resultsDiv = document.getElementById('scanResults');
            
            let html = '<div class="space-y-6">';
            
            Object.keys(data.data).forEach(shape => {
                const shapeData = data.data[shape];
                
                html += `<div class="border rounded-lg p-4">`;
                html += `<h3 class="font-bold text-lg capitalize mb-3">${shape} Face Shape</h3>`;
                
                if (Array.isArray(shapeData)) {
                    if (shapeData.length > 0) {
                        html += `<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">`;
                        
                        shapeData.forEach(file => {
                            const statusClass = file.exists && file.readable ? 'border-green-500' : 'border-red-500';
                            const statusIcon = file.exists && file.readable ? '✅' : '❌';
                            
                            html += `
                                <div class="border rounded p-2 text-center ${statusClass}">
                                    <div class="w-16 h-16 mx-auto mb-2 rounded overflow-hidden border">
                                        <img src="${file.webpath}" alt="${file.filename}" class="w-full h-full object-cover"
                                             onload="this.parentElement.classList.add('border-green-500')"
                                             onerror="this.parentElement.innerHTML='❌'; this.parentElement.classList.add('border-red-500')">
                                    </div>
                                    <div class="text-xs">
                                        <div class="font-medium">${statusIcon} ${file.filename.substring(0, 15)}...</div>
                                        <div class="text-gray-500">${file.size} bytes</div>
                                    </div>
                                </div>
                            `;
                        });
                        
                        html += `</div>`;
                    } else {
                        html += `<p class="text-gray-500">No files found in this shape directory.</p>`;
                    }
                } else {
                    html += `<p class="text-red-500">Error: ${shapeData.error}</p>`;
                }
                
                html += `</div>`;
            });
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        function testImageLoad(imagePath) {
            const img = new Image();
            img.onload = function() {
                alert(`✅ Image loaded successfully!\nPath: ${imagePath}\nSize: ${this.naturalWidth}x${this.naturalHeight}`);
            };
            img.onerror = function() {
                alert(`❌ Failed to load image!\nPath: ${imagePath}`);
            };
            img.src = imagePath;
        }

        // Auto-run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 Image Debug Dashboard loaded');
            runAllTests();
        });
    </script>
</body>
</html>
