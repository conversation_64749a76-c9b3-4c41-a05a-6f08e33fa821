<?php
session_start();
require_once 'db_config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$pembeli_id = $_GET['pembeli_id'] ?? null;
if (!$pembeli_id) {
    header("Location: kelola_pembeli.php");
    exit();
}

$message = '';
$message_type = '';

// Handle order status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_order_status'])) {
    $order_id = $_POST['order_id'];
    $new_status = $_POST['new_status'];
    $tracking_number = $_POST['tracking_number'] ?? '';
    $notes = $_POST['notes'] ?? '';

    try {
        // Update order status
        $update_sql = "UPDATE orders SET order_status = ?, updated_at = NOW()";
        $params = [$new_status];

        // Add tracking number if status is shipped
        if ($new_status === 'shipped' && !empty($tracking_number)) {
            $update_sql .= ", tracking_number = ?";
            $params[] = $tracking_number;
        }

        $update_sql .= " WHERE id = ?";
        $params[] = $order_id;

        $stmt = $conn->prepare($update_sql);
        $stmt->execute($params);

        // Check if this status already exists in history
        $check_history_stmt = $conn->prepare("
            SELECT id FROM order_history WHERE order_id = ? AND status = ? ORDER BY created_at DESC LIMIT 1
        ");
        $check_history_stmt->execute([$order_id, $new_status]);
        $existing_history = $check_history_stmt->fetch(PDO::FETCH_ASSOC);

        if ($existing_history) {
            // Update existing history entry
            $history_stmt = $conn->prepare("
                UPDATE order_history
                SET notes = ?, admin_id = ?, created_at = NOW()
                WHERE id = ?
            ");
            $history_stmt->execute([$notes, $_SESSION['admin_id'], $existing_history['id']]);
        } else {
            // Add new order history log only if status doesn't exist
            $history_stmt = $conn->prepare("
                INSERT INTO order_history (order_id, status, notes, admin_id, created_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $history_stmt->execute([$order_id, $new_status, $notes, $_SESSION['admin_id']]);
        }

        // Create notification for user
        $get_order_stmt = $conn->prepare("SELECT pembeli_id FROM orders WHERE id = ?");
        $get_order_stmt->execute([$order_id]);
        $order_info = $get_order_stmt->fetch(PDO::FETCH_ASSOC);

        if ($order_info) {
            $status_labels = [
                'pending' => 'Menunggu Konfirmasi',
                'confirmed' => 'Dikonfirmasi',
                'processing' => 'Diproses',
                'shipped' => 'Dikirim',
                'delivered' => 'Terkirim',
                'cancelled' => 'Dibatalkan'
            ];

            $notification_message = "Status pesanan #$order_id telah diubah menjadi: " . $status_labels[$new_status];

            try {
                $notif_stmt = $conn->prepare("
                    INSERT INTO notifications (user_id, order_id, message, is_read, created_at)
                    VALUES (?, ?, ?, FALSE, NOW())
                ");
                $notif_stmt->execute([$order_info['pembeli_id'], $order_id, $notification_message]);
            } catch (PDOException $e) {
                error_log("Error creating notification: " . $e->getMessage());
            }
        }

        $message = 'Status pesanan berhasil diperbarui!';
        $message_type = 'success';

    } catch (PDOException $e) {
        $message = 'Gagal memperbarui status: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Handle cleanup duplicate history entries
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['cleanup_history'])) {
    try {
        // Delete duplicate entries, keeping only the latest one for each order_id + status combination
        $cleanup_sql = "
            DELETE h1 FROM order_history h1
            INNER JOIN order_history h2
            WHERE h1.order_id = h2.order_id
            AND h1.status = h2.status
            AND h1.created_at < h2.created_at
        ";

        $stmt = $conn->prepare($cleanup_sql);
        $stmt->execute();
        $deleted_count = $stmt->rowCount();

        $message = "Berhasil menghapus {$deleted_count} entry duplikat dari riwayat pesanan!";
        $message_type = 'success';

    } catch (PDOException $e) {
        $message = 'Gagal membersihkan riwayat: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Handle buyer overall status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_buyer_status'])) {
    $buyer_id = $_POST['buyer_id'];
    $new_overall_status = $_POST['new_overall_status'];

    try {
        // Update all orders for this buyer based on overall status
        if ($new_overall_status === 'all_completed') {
            $update_orders_sql = "UPDATE orders SET order_status = 'delivered', updated_at = NOW() WHERE pembeli_id = ? AND order_status != 'delivered'";
        } elseif ($new_overall_status === 'shipping') {
            $update_orders_sql = "UPDATE orders SET order_status = 'shipped', updated_at = NOW() WHERE pembeli_id = ? AND order_status NOT IN ('delivered', 'cancelled')";
        } elseif ($new_overall_status === 'pending_payment') {
            $update_orders_sql = "UPDATE orders SET order_status = 'pending', updated_at = NOW() WHERE pembeli_id = ? AND order_status NOT IN ('delivered', 'cancelled')";
        }

        if (isset($update_orders_sql)) {
            $stmt = $conn->prepare($update_orders_sql);
            $stmt->execute([$buyer_id]);

            // Add history for updated orders (only if status doesn't exist)
            $updated_orders_stmt = $conn->prepare("SELECT id FROM orders WHERE pembeli_id = ?");
            $updated_orders_stmt->execute([$buyer_id]);
            $updated_orders = $updated_orders_stmt->fetchAll(PDO::FETCH_ASSOC);

            $status_map = [
                'all_completed' => 'delivered',
                'shipping' => 'shipped',
                'pending_payment' => 'pending'
            ];

            foreach ($updated_orders as $order) {
                $new_mapped_status = $status_map[$new_overall_status];

                // Check if this status already exists in history
                $check_history_stmt = $conn->prepare("
                    SELECT id FROM order_history WHERE order_id = ? AND status = ? ORDER BY created_at DESC LIMIT 1
                ");
                $check_history_stmt->execute([$order['id'], $new_mapped_status]);
                $existing_history = $check_history_stmt->fetch(PDO::FETCH_ASSOC);

                if ($existing_history) {
                    // Update existing history entry
                    $history_stmt = $conn->prepare("
                        UPDATE order_history
                        SET notes = ?, admin_id = ?, created_at = NOW()
                        WHERE id = ?
                    ");
                    $history_stmt->execute([
                        "Status diubah secara massal oleh admin",
                        $_SESSION['admin_id'],
                        $existing_history['id']
                    ]);
                } else {
                    // Add new history entry only if status doesn't exist
                    $history_stmt = $conn->prepare("
                        INSERT INTO order_history (order_id, status, notes, admin_id, created_at)
                        VALUES (?, ?, ?, ?, NOW())
                    ");
                    $history_stmt->execute([
                        $order['id'],
                        $new_mapped_status,
                        "Status diubah secara massal oleh admin",
                        $_SESSION['admin_id']
                    ]);
                }
            }

            // Create notification for bulk status update
            $overall_status_labels = [
                'all_completed' => 'Semua Pesanan Selesai',
                'shipping' => 'Sedang Dikirim',
                'pending_payment' => 'Menunggu Pembayaran'
            ];

            $bulk_notification_message = "Status keseluruhan pesanan Anda telah diubah menjadi: " . $overall_status_labels[$new_overall_status];

            try {
                $bulk_notif_stmt = $conn->prepare("
                    INSERT INTO notifications (user_id, order_id, message, is_read, created_at)
                    VALUES (?, NULL, ?, FALSE, NOW())
                ");
                $bulk_notif_stmt->execute([$buyer_id, $bulk_notification_message]);
            } catch (PDOException $e) {
                error_log("Error creating bulk notification: " . $e->getMessage());
            }
        }

        $message = 'Status keseluruhan pembeli berhasil diperbarui!';
        $message_type = 'success';

    } catch (PDOException $e) {
        $message = 'Gagal memperbarui status keseluruhan: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get pembeli info
$pembeli_stmt = $conn->prepare("SELECT * FROM pembeli WHERE id = ?");
$pembeli_stmt->execute([$pembeli_id]);
$pembeli = $pembeli_stmt->fetch(PDO::FETCH_ASSOC);

if (!$pembeli) {
    header("Location: kelola_pembeli.php");
    exit();
}

$buyer_name = htmlspecialchars($pembeli['nama_pembeli']);

// Get orders for this pembeli
$orders_stmt = $conn->prepare("
    SELECT o.*, ua.address_name, ua.full_address, pm.name as payment_method_name
    FROM orders o
    LEFT JOIN user_addresses ua ON o.address_id = ua.id
    LEFT JOIN payment_methods pm ON o.payment_method_id = pm.id
    WHERE o.pembeli_id = ?
    ORDER BY o.created_at DESC
");
$orders_stmt->execute([$pembeli_id]);
$orders = $orders_stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate overall buyer status based on orders
$overall_status = 'all_completed'; // Default
if (!empty($orders)) {
    $status_counts = array_count_values(array_column($orders, 'order_status'));

    if (isset($status_counts['pending']) || isset($status_counts['confirmed'])) {
        $overall_status = 'pending_payment';
    } elseif (isset($status_counts['processing']) || isset($status_counts['shipped'])) {
        $overall_status = 'shipping';
    } elseif (isset($status_counts['delivered']) && count($status_counts) === 1) {
        $overall_status = 'all_completed';
    } else {
        // Mixed statuses - determine primary status
        if (isset($status_counts['pending']) || isset($status_counts['confirmed'])) {
            $overall_status = 'pending_payment';
        } elseif (isset($status_counts['processing']) || isset($status_counts['shipped'])) {
            $overall_status = 'shipping';
        }
    }
}

// Get order items for each order
foreach ($orders as &$order) {
    $items_stmt = $conn->prepare("
        SELECT oi.*, p.nama_produk, p.gambar_utama
        FROM order_items oi
        LEFT JOIN produk p ON oi.product_id = p.id
        WHERE oi.order_id = ?
    ");
    $items_stmt->execute([$order['id']]);
    $order['items'] = $items_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get order history
    $history_stmt = $conn->prepare("
        SELECT oh.*, a.full_name as admin_name
        FROM order_history oh
        LEFT JOIN admin a ON oh.admin_id = a.id
        WHERE oh.order_id = ?
        ORDER BY oh.created_at DESC
    ");
    $history_stmt->execute([$order['id']]);
    $order['history'] = $history_stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Status options and colors
$status_options = [
    'pending' => ['label' => 'Menunggu Konfirmasi', 'color' => 'bg-yellow-100 text-yellow-800'],
    'confirmed' => ['label' => 'Dikonfirmasi', 'color' => 'bg-blue-100 text-blue-800'],
    'processing' => ['label' => 'Diproses', 'color' => 'bg-purple-100 text-purple-800'],
    'shipped' => ['label' => 'Dikirim', 'color' => 'bg-indigo-100 text-indigo-800'],
    'delivered' => ['label' => 'Terkirim', 'color' => 'bg-green-100 text-green-800'],
    'cancelled' => ['label' => 'Dibatalkan', 'color' => 'bg-red-100 text-red-800']
];

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pesanan Pembeli - <?php echo $buyer_name; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            background: #2d3748;
            color: #fff;
        }
        .sidebar .logo {
            background: #1a202c;
            padding: 1rem;
        }
        .sidebar .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .sidebar nav ul li {
            transition: all 0.3s ease;
        }
        .sidebar nav ul li a {
            color: #fff;
        }
        .sidebar nav ul li.active {
            background:rgb(35, 71, 250);
        }
        .sidebar nav ul li.active a {
            color: #fff;
        }
        .sidebar nav ul li i {
            color: #4299e1;
        }
        .sidebar nav ul li.active i {
            color: #fff;
        }

        /* Custom styles for professional order management */
        .order-card {
            transition: all 0.3s ease;
        }
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .status-badge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.8; }
            100% { opacity: 1; }
        }

        .product-image {
            transition: transform 0.3s ease;
        }
        .product-image:hover {
            transform: scale(1.1);
        }

        .notification-enter {
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <aside class="w-64 sidebar">
            <div class="logo">
                <h1 class="text-2xl font-bold">Admin Panel</h1>
            </div>
            <nav class="mt-4">
                <ul>
                    <li class="px-4 py-3">
                        <a href="#" class="flex items-center space-x-3">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <!-- Manajemen Booking Menu -->
                    <li>
                        <button onclick="toggleBookingSubmenu()" class="w-full flex items-center justify-between px-4 py-3 rounded-lg text-white focus:outline-none">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-calendar-check"></i>
                                <span>Manajemen Booking</span>
                            </span>
                            <i id="arrowBookingIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>

                        <!-- Submenu -->
                        <ul id="bookingSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="data_booking.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Data Booking</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_lokasi.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Kelola Lokasi</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_barber.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cut"></i>
                                    <span>Kelola Barber</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <script>
                        function toggleBookingSubmenu() {
                            const submenu = document.getElementById('bookingSubmenu');
                            const icon = document.getElementById('arrowBookingIcon');
                            submenu.classList.toggle('hidden');
                            icon.classList.toggle('rotate-180');
                        }
                    </script>

                    <li class="mb-2">
                        <button onclick="toggleSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-box"></i>
                                <span>Manajemen Produk</span>
                            </span>
                            <i id="arrowIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="submenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_produk.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cube"></i>
                                    <span>Produk</span>
                                </a>
                            </li>
                            <li class="active"> <!-- Set Data Pembeli as active -->
                                <a href="kelola_pembeli.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pembeli</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_metode_pembayaran.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-money-check-alt"></i>
                                    <span>Metode Pembayaran</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="px-4 py-3">
                        <a href="kelola_admin.php" class="flex items-center space-x-3">
                            <i class="fas fa-user-shield"></i>
                            <span>Kelola Admin</span>
                        </a>
                    </li>
                    <li class="px-4 py-3">
                        <a href="kelola_lowongan.php" class="flex items-center space-x-3">
                            <i class="fas fa-briefcase"></i>
                            <span>Kelola Lowongan</span>
                        </a>
                    </li>

                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col">
            <header class="flex items-center justify-between px-6 py-4 bg-white border-b-4 border-indigo-600">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold">Detail Pesanan - <?= htmlspecialchars($pembeli['nama_pembeli']) ?></h1>
                </div>
                <div class="flex items-center">
                    <span class="mr-4 text-gray-700 hidden sm:block"><?php echo $_SESSION['admin_full_name'] ?? 'Admin'; ?></span>
                    <div class="relative">
                        <button id="profileDropdown" class="flex items-center focus:outline-none">
                            <?php
                            // Get admin profile photo
                            $stmt = $conn->prepare("SELECT profile_photo FROM admin WHERE id = ?");
                            $stmt->execute([$_SESSION['admin_id']]);
                            $profile_photo = $stmt->fetchColumn();

                            if (!empty($profile_photo) && file_exists('uploads/profiles/' . $profile_photo)):
                            ?>
                                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-300 hover:border-blue-500 transition-colors"
                                     src="uploads/profiles/<?= htmlspecialchars($profile_photo) ?>"
                                     alt="Admin avatar">
                            <?php else: ?>
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-300 hover:border-blue-500 transition-colors">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                            <?php endif; ?>
                            <i class="fas fa-chevron-down ml-2 text-gray-500 text-sm"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="profileDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="profile_settings.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-3 text-gray-500"></i>
                                Profile Settings
                            </a>
                            <hr class="my-1">
                            <a href="logout.php" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-3 text-red-500"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200 p-6">
                <div class="mb-6">
                    <a href="kelola_pembeli.php" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center space-x-2 w-max">
                        <i class="fas fa-arrow-left"></i>
                        <span>Kembali ke Daftar Pembeli</span>
                    </a>
                </div>
                    <!-- Order Summary Table -->
                    <div class="bg-white shadow-lg rounded-lg overflow-hidden mb-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full text-sm text-left border-collapse border border-gray-300">
                                <thead class="bg-blue-800 text-white">
                                    <tr>
                                        <th class="px-4 py-4 font-semibold border border-gray-300">NO</th>
                                        <th class="px-4 py-4 font-semibold border border-gray-300">Nama Pembeli</th>
                                        <th class="px-4 py-4 font-semibold border border-gray-300 hidden sm:table-cell">Tanggal Daftar</th>
                                        <th class="px-4 py-4 font-semibold border border-gray-300">Jumlah Pesanan</th>
                                        <th class="px-4 py-4 font-semibold border border-gray-300">Total Pengeluaran</th>
                                        <th class="px-4 py-4 font-semibold border border-gray-300">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="text-gray-700 bg-white">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-4 py-4 border border-gray-300 text-center font-medium">
                                            1
                                        </td>
                                        <td class="px-4 py-4 border border-gray-300 font-medium">
                                            <div class="flex flex-col">
                                                <span><?php echo htmlspecialchars($buyer_name); ?></span>
                                                <span class="text-xs text-gray-500 sm:hidden">
                                                    <?php echo date('d M Y', strtotime($pembeli['created_at'] ?? 'now')); ?>
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-4 py-4 border border-gray-300 hidden sm:table-cell">
                                            <?php echo date('d M Y', strtotime($pembeli['created_at'] ?? 'now')); ?>
                                        </td>
                                        <td class="px-4 py-4 border border-gray-300 text-center font-medium">
                                            <?php echo count($orders); ?>
                                        </td>
                                        <td class="px-4 py-4 border border-gray-300 font-semibold text-gray-900">
                                            <div class="flex flex-col">
                                                <span class="text-sm sm:text-base">
                                                    Rp <?php echo number_format(array_sum(array_column($orders, 'total_amount')), 0, ',', '.'); ?>
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-4 py-4 border border-gray-300">
                                            <select id="buyerStatusSelect"
                                                    data-buyer-id="<?php echo $pembeli_id; ?>"
                                                    class="w-full bg-gray-100 border border-gray-300 rounded-md px-2 py-2 text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                    onchange="updateBuyerStatus(this)">
                                                <option value="all_completed" <?php echo $overall_status === 'all_completed' ? 'selected' : ''; ?>>
                                                    Semua Pesanan Selesai
                                                </option>
                                                <option value="shipping" <?php echo $overall_status === 'shipping' ? 'selected' : ''; ?>>
                                                    Sedang Dikirim
                                                </option>
                                                <option value="pending_payment" <?php echo $overall_status === 'pending_payment' ? 'selected' : ''; ?>>
                                                    Menunggu Pembayaran
                                                </option>
                                            </select>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <?php if ($pembeli_id && !empty($orders)): ?>
                        <?php foreach ($orders as $index => $order): ?>
                        <div class="bg-white shadow-lg rounded-lg overflow-hidden mb-6 order-card">


                            <!-- Products Section -->
                            <div class="p-6">
                                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                    <div class="grid grid-cols-5 gap-4 text-sm font-semibold text-gray-700 uppercase tracking-wider">
                                        <div class="text-left">
                                            <i class="fas fa-box mr-2"></i>PRODUK
                                        </div>
                                        <div class="text-center">
                                            <i class="fas fa-image mr-2"></i>GAMBAR
                                        </div>
                                        <div class="text-center">
                                            <i class="fas fa-sort-numeric-up mr-2"></i>QTY
                                        </div>
                                        <div class="text-right">
                                            <i class="fas fa-tag mr-2"></i>HARGA SATUAN
                                        </div>
                                        <div class="text-right">
                                            <i class="fas fa-calculator mr-2"></i>TOTAL HARGA
                                        </div>
                                    </div>
                                </div>

                                <?php
                                    $total_qty = 0;
                                    $subtotal = 0;

                                    // Group items by product_id and variant_id
                                    $grouped_items = [];
                                    if (!empty($order['items'])) {
                                        foreach ($order['items'] as $item) {
                                            $key = $item['product_id'] . '-' . ($item['variant_id'] ?? 'null');
                                            if (isset($grouped_items[$key])) {
                                                $grouped_items[$key]['quantity'] += $item['quantity'];
                                                $grouped_items[$key]['total_price'] += $item['total_price'];
                                            } else {
                                                $grouped_items[$key] = $item;
                                            }
                                        }
                                    }

                                    if (!empty($grouped_items)):
                                        foreach ($grouped_items as $item):
                                            $total_qty += $item['quantity'];
                                            $subtotal += $item['total_price'];
                                ?>
                                    <div class="grid grid-cols-5 gap-4 py-4 hover:bg-gray-50 rounded-lg">
                                        <div class="px-2">
                                            <h4 class="text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($item['product_name_at_purchase']); ?>
                                            </h4>
                                            <?php if (!empty($item['variant_name_at_purchase'])): ?>
                                                <p class="text-sm text-gray-500">
                                                    Varian: <?php echo htmlspecialchars($item['variant_name_at_purchase']); ?>
                                                </p>
                                            <?php endif; ?>
                                            <p class="text-xs text-gray-400 mt-1">
                                                ID Produk: #<?php echo htmlspecialchars($item['product_id']); ?>
                                            </p>
                                        </div>
                                        <div class="text-center">
                                            <?php if (!empty($item['gambar_utama'])): ?>
                                                <img src="../uploads/products/<?php echo htmlspecialchars($item['gambar_utama']); ?>"
                                                     alt="<?php echo htmlspecialchars($item['product_name_at_purchase']); ?>"
                                                     class="w-16 h-16 object-cover rounded-lg mx-auto border border-gray-200 product-image cursor-pointer"
                                                     onclick="showImageModal(this.src, '<?php echo htmlspecialchars($item['product_name_at_purchase']); ?>')">
                                            <?php else: ?>
                                                <div class="w-16 h-16 bg-gray-200 rounded-lg mx-auto flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-center">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                                <?php echo htmlspecialchars($item['quantity']); ?>
                                            </span>
                                        </div>
                                        <div class="text-right text-sm font-medium text-gray-900">
                                            Rp <?php echo number_format($item['price_per_unit'], 0, ',', '.'); ?>
                                        </div>
                                        <div class="text-right text-sm font-bold text-green-600">
                                            Rp <?php echo number_format($item['total_price'], 0, ',', '.'); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="py-8 text-center text-gray-500">
                                        <i class="fas fa-box-open text-4xl mb-2"></i>
                                        <p>Tidak ada item untuk pesanan ini.</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Order Summary -->
                            <div class="bg-gray-50 p-6">
                                <div class="max-w-md ml-auto">
                                    <div class="space-y-2">
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600">Total Item:</span>
                                            <span class="font-medium"><?php echo $total_qty; ?> item</span>
                                        </div>
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600">Subtotal:</span>
                                            <span class="font-medium">Rp <?php echo number_format($subtotal, 0, ',', '.'); ?></span>
                                        </div>
                                        <?php if ($order['delivery_cost'] > 0): ?>
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600">Biaya Pengiriman:</span>
                                            <span class="font-medium">Rp <?php echo number_format($order['delivery_cost'], 0, ',', '.'); ?></span>
                                        </div>
                                        <?php endif; ?>
                                        <?php if ($order['service_fee'] > 0): ?>
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600">Biaya Layanan:</span>
                                            <span class="font-medium">Rp <?php echo number_format($order['service_fee'], 0, ',', '.'); ?></span>
                                        </div>
                                        <?php endif; ?>
                                        <div class="border-t pt-2">
                                            <div class="flex justify-between text-lg font-bold">
                                                <span class="text-gray-900">Total Pembayaran:</span>
                                                <span class="text-green-600">Rp <?php echo number_format($order['total_amount'], 0, ',', '.'); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Order History -->
                            <?php if (!empty($order['history'])): ?>
                            <div class="border-t bg-white p-6">
                                <h4 class="text-lg font-semibold text-gray-900 mb-4">
                                    <i class="fas fa-history mr-2"></i>Riwayat Pesanan
                                </h4>
                                <div class="space-y-3">
                                    <?php foreach ($order['history'] as $history): ?>
                                    <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-check text-green-600 text-xs"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm font-medium text-gray-900">
                                                Status diubah menjadi: <span class="text-green-600"><?php echo $status_options[$history['status']]['label']; ?></span>
                                            </p>
                                            <p class="text-xs text-gray-500">
                                                oleh <?php echo htmlspecialchars($history['admin_name'] ?? 'Admin'); ?> •
                                                <?php echo date('d/m/Y H:i', strtotime($history['created_at'])); ?>
                                            </p>
                                            <?php if (!empty($history['notes'])): ?>
                                            <p class="text-sm text-gray-600 mt-1"><?php echo htmlspecialchars($history['notes']); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="bg-white shadow-lg rounded-lg p-8 text-center">
                        <div class="mb-4">
                            <i class="fas fa-shopping-cart text-6xl text-gray-300"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">Tidak Ada Pesanan</h3>
                        <p class="text-gray-500">Pembeli ini belum memiliki pesanan apapun.</p>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

<script>
    function toggleSubmenu() {
        const submenu = document.getElementById('submenu');
        const icon = document.getElementById('arrowIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleBookingSubmenu() {
        const submenu = document.getElementById('bookingSubmenu');
        const icon = document.getElementById('arrowBookingIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Set active class for current page in sidebar
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.sidebar nav ul li a');
        navLinks.forEach(link => {
            // Remove active class from all direct links initially to ensure only one is active
            link.closest('li').classList.remove('active');

            if (currentPath.includes(link.getAttribute('href'))) { // Use includes for partial match
                link.closest('li').classList.add('active'); // Make the specific link active

                // If this active link is inside a submenu, open the submenu and activate its parent button's LI
                const parentUl = link.closest('ul');
                if (parentUl && (parentUl.id === 'submenu' || parentUl.id === 'bookingSubmenu')) {
                    parentUl.classList.remove('hidden'); // Show the submenu

                    // Get the button element that controls this submenu
                    const parentButton = parentUl.previousElementSibling;
                    if (parentButton) {
                        parentButton.querySelector('i').classList.add('rotate-180'); // Rotate arrow for parent button
                        // IMPORTANT: Ensure the parent LI (which contains the button) does NOT get the 'active' background
                        const parentLiWithButton = parentButton.closest('li');
                        if (parentLiWithButton) {
                            parentLiWithButton.classList.remove('active');
                        }
                    }
                }
            }
        });

        // Special handling for kelola_pembeli.php and view_pembeli_orders.php to keep 'Data Pembeli' active
        if (currentPath.includes('kelola_pembeli.php') || currentPath.includes('view_pembeli_orders.php')) {
            const dataPembeliLink = document.querySelector('a[href="kelola_pembeli.php"]');
            if (dataPembeliLink) {
                dataPembeliLink.closest('li').classList.add('active');
                // Also ensure the parent submenu is open
                const submenu = document.getElementById('submenu');
                const arrowIcon = document.getElementById('arrowIcon');
                if (submenu && submenu.classList.contains('hidden')) {
                    submenu.classList.remove('hidden');
                    arrowIcon.classList.add('rotate-180');
                }
            }
        }
    });

    async function updateBuyerStatus(selectElement) {
        const buyerId = selectElement.dataset.buyerId;
        const newOverallStatus = selectElement.value;
        const originalValue = selectElement.getAttribute('data-original-value') || selectElement.value;

        // Show confirmation dialog
        const statusLabels = {
            'all_completed': 'Semua Pesanan Selesai',
            'shipping': 'Sedang Dikirim',
            'pending_payment': 'Menunggu Pembayaran'
        };

        if (!confirm(`Apakah Anda yakin ingin mengubah status keseluruhan menjadi "${statusLabels[newOverallStatus]}"?\n\nIni akan mempengaruhi semua pesanan pembeli ini.`)) {
            selectElement.value = originalValue;
            return;
        }

        // Show loading state
        selectElement.disabled = true;
        const originalText = selectElement.options[selectElement.selectedIndex].text;
        selectElement.options[selectElement.selectedIndex].text = 'Memperbarui...';

        try {
            const formData = new FormData();
            formData.append('update_buyer_status', '1');
            formData.append('buyer_id', buyerId);
            formData.append('new_overall_status', newOverallStatus);

            const response = await fetch(window.location.href, {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                // Show success message
                showNotification('Status keseluruhan pembeli berhasil diperbarui!', 'success');

                // Reload page after short delay to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                throw new Error('Failed to update buyer status');
            }
        } catch (error) {
            console.error('Error updating buyer status:', error);
            showNotification('Gagal memperbarui status keseluruhan. Silakan coba lagi.', 'error');

            // Restore original value
            selectElement.value = originalValue;
            selectElement.options[selectElement.selectedIndex].text = originalText;
        } finally {
            selectElement.disabled = false;
        }
    }

    async function updateOrderStatus(selectElement) {
        const orderId = selectElement.dataset.orderId;
        const newStatus = selectElement.value;
        const originalValue = selectElement.getAttribute('data-original-value') || selectElement.value;

        // Show confirmation dialog
        const statusLabels = {
            'pending': 'Menunggu Konfirmasi',
            'confirmed': 'Dikonfirmasi',
            'processing': 'Diproses',
            'shipped': 'Dikirim',
            'delivered': 'Terkirim',
            'cancelled': 'Dibatalkan'
        };

        if (!confirm(`Apakah Anda yakin ingin mengubah status pesanan menjadi "${statusLabels[newStatus]}"?`)) {
            selectElement.value = originalValue;
            return;
        }

        // Show loading state
        selectElement.disabled = true;
        const originalText = selectElement.options[selectElement.selectedIndex].text;
        selectElement.options[selectElement.selectedIndex].text = 'Memperbarui...';

        try {
            const formData = new FormData();
            formData.append('update_order_status', '1');
            formData.append('order_id', orderId);
            formData.append('new_status', newStatus);
            formData.append('notes', `Status diubah menjadi ${statusLabels[newStatus]} oleh admin`);

            const response = await fetch(window.location.href, {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                // Show success message
                showNotification('Status pesanan berhasil diperbarui!', 'success');

                // Reload page after short delay to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                throw new Error('Failed to update status');
            }
        } catch (error) {
            console.error('Error updating order status:', error);
            showNotification('Gagal memperbarui status pesanan. Silakan coba lagi.', 'error');

            // Restore original value
            selectElement.value = originalValue;
            selectElement.options[selectElement.selectedIndex].text = originalText;
        } finally {
            selectElement.disabled = false;
        }
    }

    function printInvoice(orderId) {
        // Create a new window for printing
        const printWindow = window.open('', '_blank');
        const orderElement = document.querySelector('[data-order-id="' + orderId + '"]').closest('.bg-white.shadow-lg');

        if (orderElement) {
            printWindow.document.write('<!DOCTYPE html>');
            printWindow.document.write('<html><head>');
            printWindow.document.write('<title>Invoice #' + orderId + '</title>');
            printWindow.document.write('<style>');
            printWindow.document.write('body { font-family: Arial, sans-serif; margin: 20px; }');
            printWindow.document.write('.header { text-align: center; margin-bottom: 30px; }');
            printWindow.document.write('table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }');
            printWindow.document.write('th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }');
            printWindow.document.write('th { background-color: #f2f2f2; }');
            printWindow.document.write('.total { font-weight: bold; }');
            printWindow.document.write('@media print { body { margin: 0; } }');
            printWindow.document.write('</style></head><body>');
            printWindow.document.write('<div class="header"><h1>PIXEL TONSORIUM</h1><p>Invoice Pembelian Produk</p></div>');
            printWindow.document.write(orderElement.innerHTML);
            printWindow.document.write('<script>window.print(); window.close();<\/script>');
            printWindow.document.write('</body></html>');
            printWindow.document.close();
        }
    }

    function sendNotification(orderId) {
        if (confirm('Kirim notifikasi WhatsApp kepada pembeli tentang status pesanan ini?')) {
            showNotification('Fitur notifikasi WhatsApp akan segera tersedia!', 'info');
            // TODO: Implement WhatsApp notification functionality
        }
    }

    function showNotification(message, type = 'info') {
        const container = document.getElementById('notification-container') || document.body;

        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full notification-enter';

        // Set colors based on type
        const colors = {
            'success': 'bg-green-500 text-white',
            'error': 'bg-red-500 text-white',
            'info': 'bg-blue-500 text-white',
            'warning': 'bg-yellow-500 text-black'
        };

        notification.className += ' ' + (colors[type] || colors.info);

        // Set content
        const iconClass = type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle';
        notification.innerHTML =
            '<div class="flex items-center space-x-2">' +
                '<i class="fas fa-' + iconClass + '"></i>' +
                '<span>' + message + '</span>' +
                '<button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">' +
                    '<i class="fas fa-times"></i>' +
                '</button>' +
            '</div>';

        // Add to container
        container.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 5000);
    }

    function showImageModal(imageSrc, productName) {
        // Create modal overlay
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
        modal.onclick = function() { document.body.removeChild(modal); };

        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.className = 'bg-white rounded-lg p-4 max-w-2xl max-h-full overflow-auto';
        modalContent.onclick = function(e) { e.stopPropagation(); };

        modalContent.innerHTML =
            '<div class="text-center">' +
                '<h3 class="text-lg font-semibold mb-4">' + productName + '</h3>' +
                '<img src="' + imageSrc + '" alt="' + productName + '" class="max-w-full max-h-96 mx-auto rounded-lg">' +
                '<button onclick="this.closest(\'.fixed\').remove()" class="mt-4 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">Tutup</button>' +
            '</div>';

        modal.appendChild(modalContent);
        document.body.appendChild(modal);
    }

    // Store original values for status selects
    document.addEventListener('DOMContentLoaded', function() {
        const statusSelects = document.querySelectorAll('select[data-order-id]');
        statusSelects.forEach(select => {
            select.setAttribute('data-original-value', select.value);
        });

        // Store original value for buyer status select
        const buyerStatusSelect = document.getElementById('buyerStatusSelect');
        if (buyerStatusSelect) {
            buyerStatusSelect.setAttribute('data-original-value', buyerStatusSelect.value);
        }

        // Add notification container if it doesn't exist
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'fixed top-4 right-4 z-50 space-y-2';
            document.body.appendChild(container);
        }
    });
</script>
</body>
</html> 