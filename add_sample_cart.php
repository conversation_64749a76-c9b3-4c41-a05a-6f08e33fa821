<?php
session_start();
require_once 'Admin/db_config.php';

echo "<h2>Add Sample Cart Items</h2>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "❌ Please login first<br>";
    echo "<a href='login.php'>Login</a>";
    exit();
}

$user_id = $_SESSION['user_id'];

// Sample cart items to add
$sample_items = [
    ['product_id' => 1, 'variant_id' => 1, 'quantity' => 2],
    ['product_id' => 2, 'variant_id' => 2, 'quantity' => 1],
    ['product_id' => 3, 'variant_id' => 3, 'quantity' => 3],
];

echo "<h3>Adding Sample Items to Cart:</h3>";

try {
    // Create cart table if not exists
    $conn->exec("
        CREATE TABLE IF NOT EXISTS cart (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            product_id INT NOT NULL,
            variant_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_cart_item (user_id, product_id, variant_id)
        )
    ");
    
    foreach ($sample_items as $item) {
        // Check if product and variant exist
        $check_stmt = $conn->prepare("
            SELECT p.nama_produk, v.ukuran_ml, v.harga 
            FROM produk p 
            JOIN variasi_produk v ON p.id = v.produk_id 
            WHERE p.id = ? AND v.id = ?
        ");
        $check_stmt->execute([$item['product_id'], $item['variant_id']]);
        $product_info = $check_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($product_info) {
            // Add to cart
            $stmt = $conn->prepare("
                INSERT INTO cart (user_id, product_id, variant_id, quantity) 
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE quantity = quantity + VALUES(quantity)
            ");
            $stmt->execute([$user_id, $item['product_id'], $item['variant_id'], $item['quantity']]);
            
            echo "✅ Added: " . $product_info['nama_produk'] . " (" . $product_info['ukuran_ml'] . "ml) x" . $item['quantity'] . "<br>";
        } else {
            echo "❌ Product ID " . $item['product_id'] . " or Variant ID " . $item['variant_id'] . " not found<br>";
        }
    }
    
    echo "<br><h3>Current Cart Items:</h3>";
    
    // Show current cart
    $cart_stmt = $conn->prepare("
        SELECT c.*, p.nama_produk, p.gambar_utama, 
               v.ukuran_ml, v.harga, v.stok,
               (c.quantity * v.harga) as total_price
        FROM cart c
        JOIN produk p ON c.product_id = p.id
        JOIN variasi_produk v ON c.variant_id = v.id
        WHERE c.user_id = ?
        ORDER BY c.created_at DESC
    ");
    $cart_stmt->execute([$user_id]);
    $cart_items = $cart_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($cart_items)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Product</th><th>Variant</th><th>Quantity</th><th>Price</th><th>Total</th><th>Image</th></tr>";
        
        $grand_total = 0;
        foreach ($cart_items as $item) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($item['nama_produk']) . "</td>";
            echo "<td>" . htmlspecialchars($item['ukuran_ml']) . "ml</td>";
            echo "<td>" . $item['quantity'] . "</td>";
            echo "<td>Rp " . number_format($item['harga'], 0, ',', '.') . "</td>";
            echo "<td>Rp " . number_format($item['total_price'], 0, ',', '.') . "</td>";
            
            // Check image
            $image_paths = [
                'uploads/' . $item['gambar_utama'],
                'uploads/products/' . $item['gambar_utama'],
                'Admin/uploads/' . $item['gambar_utama']
            ];
            
            $image_found = false;
            foreach ($image_paths as $path) {
                if (file_exists($path)) {
                    echo "<td><img src='$path' width='50' height='50' style='object-fit: cover;'></td>";
                    $image_found = true;
                    break;
                }
            }
            
            if (!$image_found) {
                echo "<td>No Image (" . $item['gambar_utama'] . ")</td>";
            }
            
            echo "</tr>";
            $grand_total += $item['total_price'];
        }
        
        echo "<tr><td colspan='4'><strong>Grand Total:</strong></td><td><strong>Rp " . number_format($grand_total, 0, ',', '.') . "</strong></td><td></td></tr>";
        echo "</table>";
    } else {
        echo "No items in cart.";
    }
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage();
}

echo "<br><br>";
echo "<a href='keranjang.php'>View Cart</a> | ";
echo "<a href='debug_cart.php'>Debug Cart</a> | ";
echo "<a href='index.php'>Home</a>";

// Clear cart option
echo "<br><br>";
echo "<form method='POST'>";
echo "<button type='submit' name='clear_cart' onclick='return confirm(\"Clear all cart items?\")'>Clear Cart</button>";
echo "</form>";

if (isset($_POST['clear_cart'])) {
    try {
        $stmt = $conn->prepare("DELETE FROM cart WHERE user_id = ?");
        $stmt->execute([$user_id]);
        echo "<br>✅ Cart cleared! <a href='add_sample_cart.php'>Refresh</a>";
    } catch (PDOException $e) {
        echo "<br>❌ Error clearing cart: " . $e->getMessage();
    }
}
?>
