<?php
session_start();
require_once 'db_config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$message = '';
$message_type = '';

// Handle API key update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_config'])) {
    $provider = $_POST['provider'];
    $api_key = $_POST['api_key'];
    $api_url = $_POST['api_url'] ?? '';
    
    // Read current config
    $config_file = __DIR__ . '/whatsapp_config.php';
    $config = include $config_file;
    
    // Update config
    $config['api_provider'] = $provider;
    
    switch ($provider) {
        case 'fonnte':
            $config['fonnte']['api_key'] = $api_key;
            break;
        case 'wablas':
            $config['wablas']['api_key'] = $api_key;
            if ($api_url) $config['wablas']['api_url'] = $api_url;
            break;
        case 'ultramsg':
            $config['ultramsg']['api_key'] = $api_key;
            if ($api_url) $config['ultramsg']['api_url'] = $api_url;
            break;
    }
    
    // Write updated config
    $config_content = "<?php\n/**\n * WhatsApp API Configuration\n * Configure your WhatsApp API settings here\n */\n\nreturn " . var_export($config, true) . ";\n?>";
    
    if (file_put_contents($config_file, $config_content)) {
        $message = "Konfigurasi API berhasil disimpan!";
        $message_type = 'success';
    } else {
        $message = "Gagal menyimpan konfigurasi!";
        $message_type = 'error';
    }
}

// Load current config
$config = include __DIR__ . '/whatsapp_config.php';
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Setup Guide - Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-4xl mx-auto px-4">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-6">📱 WhatsApp API Setup Guide</h1>
                
                <!-- Success/Error Messages -->
                <?php if ($message): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <!-- Current Status -->
                <div class="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
                    <h2 class="text-xl font-semibold text-blue-800 mb-4">📊 Status Konfigurasi Saat Ini</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-600">Provider:</p>
                            <p class="font-semibold text-gray-800"><?= ucfirst($config['api_provider']) ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">API Key:</p>
                            <p class="font-semibold text-gray-800">
                                <?php 
                                $current_key = $config[$config['api_provider']]['api_key'] ?? 'Not set';
                                if (strpos($current_key, 'YOUR_') === 0) {
                                    echo '<span class="text-red-600">❌ Belum dikonfigurasi</span>';
                                } else {
                                    echo '<span class="text-green-600">✅ ' . substr($current_key, 0, 10) . '...</span>';
                                }
                                ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Setup Instructions -->
                <div class="mb-8">
                    <h2 class="text-2xl font-semibold text-gray-800 mb-6">🚀 Langkah Setup</h2>
                    
                    <!-- Provider Options -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <!-- Fonnte -->
                        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                            <div class="text-center mb-4">
                                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fab fa-whatsapp text-2xl text-green-600"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800">Fonnte</h3>
                                <p class="text-sm text-gray-600">Mudah & Terpercaya</p>
                            </div>
                            <div class="space-y-2 text-sm">
                                <p>✅ Setup mudah</p>
                                <p>✅ Harga terjangkau</p>
                                <p>✅ Support Indonesia</p>
                                <p>✅ Dokumentasi lengkap</p>
                            </div>
                            <a href="https://fonnte.com" target="_blank" class="block mt-4 text-center bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700">
                                Daftar Fonnte
                            </a>
                        </div>

                        <!-- Wablas -->
                        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                            <div class="text-center mb-4">
                                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-rocket text-2xl text-blue-600"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800">Wablas</h3>
                                <p class="text-sm text-gray-600">Fitur Advanced</p>
                            </div>
                            <div class="space-y-2 text-sm">
                                <p>✅ Fitur lengkap</p>
                                <p>✅ API powerful</p>
                                <p>✅ Webhook support</p>
                                <p>✅ Analytics detail</p>
                            </div>
                            <a href="https://wablas.com" target="_blank" class="block mt-4 text-center bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                                Daftar Wablas
                            </a>
                        </div>

                        <!-- UltraMsg -->
                        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                            <div class="text-center mb-4">
                                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-bolt text-2xl text-purple-600"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800">UltraMsg</h3>
                                <p class="text-sm text-gray-600">High Performance</p>
                            </div>
                            <div class="space-y-2 text-sm">
                                <p>✅ Speed tinggi</p>
                                <p>✅ Reliability baik</p>
                                <p>✅ Global support</p>
                                <p>✅ Enterprise ready</p>
                            </div>
                            <a href="https://ultramsg.com" target="_blank" class="block mt-4 text-center bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700">
                                Daftar UltraMsg
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Configuration Form -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">⚙️ Konfigurasi API</h3>
                    
                    <form method="POST" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Pilih Provider:</label>
                            <select name="provider" id="provider" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="updateForm()">
                                <option value="fonnte" <?= $config['api_provider'] === 'fonnte' ? 'selected' : '' ?>>Fonnte</option>
                                <option value="wablas" <?= $config['api_provider'] === 'wablas' ? 'selected' : '' ?>>Wablas</option>
                                <option value="ultramsg" <?= $config['api_provider'] === 'ultramsg' ? 'selected' : '' ?>>UltraMsg</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">API Key/Token:</label>
                            <input type="text" name="api_key" id="api_key" 
                                   value="<?= htmlspecialchars($config[$config['api_provider']]['api_key'] ?? '') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                   placeholder="Masukkan API Key Anda" required>
                            <p class="text-xs text-gray-500 mt-1">Dapatkan API key dari dashboard provider yang Anda pilih</p>
                        </div>

                        <div id="url_field" style="display: none;">
                            <label class="block text-sm font-medium text-gray-700 mb-2">API URL (Optional):</label>
                            <input type="text" name="api_url" id="api_url" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                   placeholder="https://your-domain.wablas.com/api/send-message">
                            <p class="text-xs text-gray-500 mt-1">Untuk Wablas dan UltraMsg, sesuaikan dengan domain/instance Anda</p>
                        </div>

                        <div class="flex gap-4">
                            <button type="submit" name="update_config" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                <i class="fas fa-save mr-2"></i>Simpan Konfigurasi
                            </button>
                            <a href="whatsapp_manager.php" class="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                                <i class="fas fa-arrow-left mr-2"></i>Kembali ke Manager
                            </a>
                        </div>
                    </form>
                </div>

                <!-- Quick Setup Instructions -->
                <div class="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h3 class="text-lg font-semibold text-yellow-800 mb-3">⚡ Quick Setup untuk Fonnte</h3>
                    <ol class="list-decimal list-inside space-y-2 text-sm text-yellow-700">
                        <li>Buka <a href="https://fonnte.com" target="_blank" class="underline">fonnte.com</a> dan daftar akun</li>
                        <li>Login ke dashboard Fonnte</li>
                        <li>Scan QR code dengan WhatsApp Anda</li>
                        <li>Copy API Token dari dashboard</li>
                        <li>Paste API Token ke form di atas</li>
                        <li>Klik "Simpan Konfigurasi"</li>
                        <li>Test kirim pesan di WhatsApp Manager</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateForm() {
            const provider = document.getElementById('provider').value;
            const urlField = document.getElementById('url_field');
            const apiKeyField = document.getElementById('api_key');
            
            // Show/hide URL field for providers that need it
            if (provider === 'wablas' || provider === 'ultramsg') {
                urlField.style.display = 'block';
            } else {
                urlField.style.display = 'none';
            }
            
            // Update API key placeholder
            const placeholders = {
                'fonnte': 'Contoh: abc123def456...',
                'wablas': 'Contoh: your-wablas-token...',
                'ultramsg': 'Contoh: your-ultramsg-token...'
            };
            
            apiKeyField.placeholder = placeholders[provider] || 'Masukkan API Key Anda';
        }
        
        // Initialize form on page load
        updateForm();
    </script>
</body>
</html>
