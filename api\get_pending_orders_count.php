<?php
require_once '../db_config.php';

header('Content-Type: application/json');

$response = [
    'success' => false,
    'pending_orders_count' => 0,
    'message' => ''
];

try {
    $stmt = $conn->prepare("SELECT COUNT(*) AS pending_count FROM orders WHERE status = 'pending'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result) {
        $response['success'] = true;
        $response['pending_orders_count'] = (int)$result['pending_count'];
    } else {
        $response['message'] = 'No pending orders found or query returned no rows.';
    }
} catch (PDOException $e) {
    $response['message'] = 'Database Error: ' . $e->getMessage();
    error_log("Database Error in get_pending_orders_count.php: " . $e->getMessage());
}

echo json_encode($response);
?> 