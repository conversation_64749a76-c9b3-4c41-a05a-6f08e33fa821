#!/usr/bin/env python3
"""
Face Overlay Generator - Membuat bingkai hijau sesuai bentuk wajah yang terdeteksi
Menggunakan algoritma Viola-<PERSON> dan 68-point landmark detection
"""

import cv2
import dlib
import numpy as np
import json
import sys
import os
import logging
from PIL import Image

# Setup logging
logging.basicConfig(
    filename='face_overlay.log',
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 1. Load Haar Cascade untuk deteksi wajah
face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

# 2. Load gambar overlay rambut (PNG transparan)
hair_overlay = Image.open('hair_overlay.png').convert('RGBA')

def create_face_overlay(image_path, face_shape, landmarks, face_rect):
    """
    Membuat overlay bingkai hijau sesuai dengan bentuk wajah yang terdeteksi
    """
    try:
        # Baca gambar
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Cannot read image: {image_path}")
        
        # Konversi landmark ke array numpy
        points = np.array([(landmarks.part(i).x, landmarks.part(i).y) for i in range(68)])
        
        # Buat overlay berdasarkan bentuk wajah
        overlay = image.copy()
        
        if face_shape.lower() == 'oval':
            overlay = draw_oval_overlay(overlay, points)
        elif face_shape.lower() == 'round':
            overlay = draw_round_overlay(overlay, points)
        elif face_shape.lower() == 'square':
            overlay = draw_square_overlay(overlay, points)
        elif face_shape.lower() == 'heart':
            overlay = draw_heart_overlay(overlay, points)
        elif face_shape.lower() == 'rectangular':
            overlay = draw_rectangular_overlay(overlay, points)
        else:
            # Default oval overlay
            overlay = draw_oval_overlay(overlay, points)
        
        return overlay
        
    except Exception as e:
        logging.error(f"Error creating face overlay: {str(e)}")
        return None

def draw_oval_overlay(image, points):
    """Menggambar bingkai oval untuk wajah oval"""
    # Hitung pusat dan dimensi oval
    face_left = points[0][0]
    face_right = points[16][0]
    face_top = min(points[17:27, 1])
    face_bottom = points[8][1]
    
    center_x = int((face_left + face_right) / 2)
    center_y = int((face_top + face_bottom) / 2)
    
    # Dimensi oval dengan margin
    width = int((face_right - face_left) * 0.6)
    height = int((face_bottom - face_top) * 0.7)
    
    # Gambar oval hijau
    cv2.ellipse(image, (center_x, center_y), (width, height), 0, 0, 360, (0, 255, 0), 3)
    
    # Tambahkan label
    cv2.putText(image, 'OVAL FACE', (center_x - 50, face_top - 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    return image

def draw_round_overlay(image, points):
    """Menggambar bingkai bulat untuk wajah bulat"""
    # Hitung pusat dan radius
    face_left = points[0][0]
    face_right = points[16][0]
    face_top = min(points[17:27, 1])
    face_bottom = points[8][1]
    
    center_x = int((face_left + face_right) / 2)
    center_y = int((face_top + face_bottom) / 2)
    
    # Radius berdasarkan dimensi wajah
    radius = int(min(face_right - face_left, face_bottom - face_top) * 0.35)
    
    # Gambar lingkaran hijau
    cv2.circle(image, (center_x, center_y), radius, (0, 255, 0), 3)
    
    # Tambahkan label
    cv2.putText(image, 'ROUND FACE', (center_x - 60, face_top - 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    return image

def draw_square_overlay(image, points):
    """Menggambar bingkai persegi untuk wajah persegi"""
    # Hitung dimensi persegi
    face_left = points[0][0]
    face_right = points[16][0]
    face_top = min(points[17:27, 1])
    face_bottom = points[8][1]
    
    # Margin untuk bingkai
    margin_x = int((face_right - face_left) * 0.1)
    margin_y = int((face_bottom - face_top) * 0.1)
    
    # Koordinat persegi
    top_left = (face_left - margin_x, face_top - margin_y)
    bottom_right = (face_right + margin_x, face_bottom + margin_y)
    
    # Gambar persegi hijau
    cv2.rectangle(image, top_left, bottom_right, (0, 255, 0), 3)
    
    # Tambahkan label
    cv2.putText(image, 'SQUARE FACE', (face_left, face_top - 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    return image

def draw_heart_overlay(image, points):
    """Menggambar bingkai hati untuk wajah heart"""
    # Titik-titik untuk bentuk hati
    face_left = points[0][0]
    face_right = points[16][0]
    face_top = min(points[17:27, 1])
    face_bottom = points[8][1]
    
    center_x = int((face_left + face_right) / 2)
    
    # Buat kontur hati menggunakan polylines
    heart_points = []
    
    # Bagian atas hati (dahi lebar)
    forehead_width = int((face_right - face_left) * 0.4)
    heart_points.extend([
        (center_x - forehead_width, face_top),
        (center_x + forehead_width, face_top),
        (face_right, int(face_top + (face_bottom - face_top) * 0.3)),
        (int(center_x + forehead_width * 0.5), int(face_top + (face_bottom - face_top) * 0.6)),
        (center_x, face_bottom),  # Dagu runcing
        (int(center_x - forehead_width * 0.5), int(face_top + (face_bottom - face_top) * 0.6)),
        (face_left, int(face_top + (face_bottom - face_top) * 0.3))
    ])
    
    heart_points = np.array(heart_points, np.int32)
    cv2.polylines(image, [heart_points], True, (0, 255, 0), 3)
    
    # Tambahkan label
    cv2.putText(image, 'HEART FACE', (center_x - 60, face_top - 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    return image

def draw_rectangular_overlay(image, points):
    """Menggambar bingkai persegi panjang untuk wajah rectangular"""
    # Hitung dimensi persegi panjang (lebih tinggi dari lebar)
    face_left = points[0][0]
    face_right = points[16][0]
    face_top = min(points[17:27, 1])
    face_bottom = points[8][1]
    
    # Margin untuk bingkai
    margin_x = int((face_right - face_left) * 0.05)  # Margin kecil di samping
    margin_y = int((face_bottom - face_top) * 0.1)   # Margin normal atas-bawah
    
    # Koordinat persegi panjang
    top_left = (face_left - margin_x, face_top - margin_y)
    bottom_right = (face_right + margin_x, face_bottom + margin_y)
    
    # Gambar persegi panjang hijau
    cv2.rectangle(image, top_left, bottom_right, (0, 255, 0), 3)
    
    # Tambahkan garis tengah untuk menunjukkan proporsi
    center_x = int((face_left + face_right) / 2)
    cv2.line(image, (center_x, top_left[1]), (center_x, bottom_right[1]), (0, 255, 0), 1)
    
    # Tambahkan label
    cv2.putText(image, 'RECTANGULAR FACE', (face_left - 20, face_top - 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    return image

def add_confidence_indicator(image, confidence, alt_confidence=None):
    """Menambahkan indikator confidence pada gambar"""
    height, width = image.shape[:2]

    # Background untuk confidence
    cv2.rectangle(image, (10, 10), (300, 80), (0, 0, 0), -1)
    cv2.rectangle(image, (10, 10), (300, 80), (0, 255, 0), 2)

    # Text confidence
    cv2.putText(image, f'Confidence: {confidence}%', (20, 35),
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

    if alt_confidence:
        cv2.putText(image, f'Alternative: {alt_confidence}%', (20, 60),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

    return image

def save_overlay_image(image, output_path):
    """Simpan gambar dengan overlay"""
    try:
        success = cv2.imwrite(output_path, image)
        if success:
            logging.info(f"Overlay image saved to: {output_path}")
            return True
        else:
            logging.error(f"Failed to save overlay image to: {output_path}")
            return False
    except Exception as e:
        logging.error(f"Error saving overlay image: {str(e)}")
        return False

def overlay_hair(face_path, hair_path):
    face_img = cv2.imread(face_path, cv2.IMREAD_UNCHANGED)
    hair_img = cv2.imread(hair_path, cv2.IMREAD_UNCHANGED)
    if face_img is None or hair_img is None:
        return None
    h, w = face_img.shape[:2]
    # Resize hair ke lebar wajah (setengah tinggi wajah)
    hair_img = cv2.resize(hair_img, (w, int(h/2)))
    # Overlay di bagian atas wajah
    y_offset = 0
    x_offset = 0
    for c in range(0, 3):
        face_img[y_offset:y_offset+hair_img.shape[0], x_offset:x_offset+hair_img.shape[1], c] = \
            hair_img[:,:,c] * (hair_img[:,:,3]/255.0) +  face_img[y_offset:y_offset+hair_img.shape[0], x_offset:x_offset+hair_img.shape[1], c] * (1.0 - hair_img[:,:,3]/255.0)
    # Simpan hasil
    result_path = face_path.replace('.jpg', '_overlay.jpg')
    cv2.imwrite(result_path, face_img)
    return result_path

def overlay_transparent(background, overlay, x, y, overlay_size=None):
    """Overlay RGBA image (Pillow) onto OpenCV BGR image."""
    bg = Image.fromarray(cv2.cvtColor(background, cv2.COLOR_BGR2RGB)).convert('RGBA')
    if overlay_size:
        overlay = overlay.resize(overlay_size, Image.ANTIALIAS)
    bg.paste(overlay, (x, y), overlay)
    return cv2.cvtColor(np.array(bg), cv2.COLOR_RGBA2BGR)

# 3. Buka webcam
cap = cv2.VideoCapture(0)

while True:
    ret, frame = cap.read()
    if not ret:
        break

    # 4. Deteksi wajah
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.3, 5)

    result_frame = frame.copy()

    for (x, y, w, h) in faces:
        # 5. Crop otomatis area wajah (bounding box)
        face_crop = frame[y:y+h, x:x+w]

        # 6. Resize overlay rambut agar sesuai lebar wajah
        overlay_width = w
        overlay_height = int(hair_overlay.height * (w / hair_overlay.width))
        y_offset = y - overlay_height // 2  # Geser overlay ke atas agar pas di dahi

        # 7. Overlay gambar rambut transparan
        result_frame = overlay_transparent(
            result_frame,
            hair_overlay,
            x,
            max(0, y_offset),
            overlay_size=(overlay_width, overlay_height)
        )

        # (Opsional) Gambar bounding box wajah
        cv2.rectangle(result_frame, (x, y), (x+w, y+h), (0, 255, 0), 2)

    # 8. Tampilkan hasil real-time
    cv2.imshow('Face with Hair Overlay', result_frame)

    key = cv2.waitKey(1) & 0xFF
    if key == ord('s'):
        # 9. Simpan hasil saat tombol 's' ditekan
        cv2.imwrite('hasil_overlay.png', result_frame)
        print('Hasil disimpan sebagai hasil_overlay.png')
    elif key == 27:  # ESC untuk keluar
        break

cap.release()
cv2.destroyAllWindows()

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("ERROR: Usage: python face_overlay.py <face_image_path> <hair_image_path>")
        sys.exit(1)
    face_path = sys.argv[1]
    hair_path = sys.argv[2]
    result = overlay_hair(face_path, hair_path)
    if result:
        print(result)
    else:
        print("ERROR: Overlay failed")
