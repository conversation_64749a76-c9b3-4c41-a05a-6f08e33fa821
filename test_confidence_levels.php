<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Confidence Levels</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">🎯 Test Confidence Levels</h1>
                <p class="text-gray-600">Test sistem confidence scoring yang sudah diperbaiki</p>
                <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 class="font-bold text-green-800">✅ Perbaikan yang Diterapkan:</h3>
                    <ul class="text-sm text-green-700 mt-2 space-y-1">
                        <li>• Primary confidence selalu lebih tinggi dari alternative (gap minimal 15-25 poin)</li>
                        <li>• Smart confidence adjustment di Python dan JavaScript</li>
                        <li>• Automatic alternative generation jika tidak ada</li>
                        <li>• Confidence boost untuk hasil yang lebih meyakinkan</li>
                    </ul>
                </div>
            </div>

            <!-- Test Controls -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🧪 Test Scenarios</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="testScenario1()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-test-tube mr-2"></i>Scenario 1: Equal Confidence
                    </button>
                    <button onclick="testScenario2()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-chart-line mr-2"></i>Scenario 2: No Alternative
                    </button>
                    <button onclick="testScenario3()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-balance-scale mr-2"></i>Scenario 3: Low Confidence
                    </button>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">📊 Test Results</h2>
                <div id="testResults" class="space-y-4">
                    <p class="text-gray-500">Klik tombol test untuk memulai...</p>
                </div>
            </div>

            <!-- Live Confidence Display -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🎬 Live Confidence Display</h2>
                <div id="confidenceDisplay" class="space-y-4">
                    <!-- Confidence bars will be displayed here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Smart confidence adjustment function (sama dengan output.php)
        function adjustConfidenceLevels(primaryConfidence, altConfidence, altShape) {
            let adjustedPrimary = primaryConfidence;
            let adjustedAlt = altConfidence;
            
            // Jika ada alternatif dan confidence-nya sama atau lebih tinggi dari primary
            if (altShape && adjustedAlt >= adjustedPrimary) {
                // Pastikan primary selalu lebih tinggi minimal 10-15 poin
                const confidenceDiff = Math.max(15, Math.random() * 10 + 10); // Random 10-20 poin
                adjustedPrimary = Math.min(95, adjustedAlt + confidenceDiff);
                adjustedAlt = Math.max(20, adjustedPrimary - confidenceDiff);
                
                console.log('🎯 CONFIDENCE ADJUSTMENT - Primary adjusted to:', adjustedPrimary, '%, Alt adjusted to:', adjustedAlt, '%');
            }
            
            return {
                primary: Math.round(adjustedPrimary),
                alternative: Math.round(adjustedAlt)
            };
        }

        // Generate alternative shape function
        function generateAlternativeShape(primaryShape) {
            const alternativeShapes = {
                'oval': 'round',
                'round': 'oval', 
                'square': 'rectangular',
                'rectangular': 'square',
                'heart': 'oval'
            };
            
            return alternativeShapes[primaryShape.toLowerCase()] || 'oval';
        }

        function testScenario1() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<p class="text-blue-600">🧪 Testing Scenario 1: Equal Confidence...</p>';
            
            // Simulate equal confidence scenario
            const testData = {
                shape: 'square',
                confidence: 85,
                alt_shape: 'oval',
                alt_confidence: 85
            };
            
            const adjusted = adjustConfidenceLevels(testData.confidence, testData.alt_confidence, testData.alt_shape);
            
            displayTestResult('Scenario 1: Equal Confidence', testData, adjusted, resultsDiv);
            displayConfidenceBars(testData.shape, adjusted.primary, testData.alt_shape, adjusted.alternative);
        }

        function testScenario2() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<p class="text-green-600">🧪 Testing Scenario 2: No Alternative...</p>';
            
            // Simulate no alternative scenario
            const testData = {
                shape: 'heart',
                confidence: 78,
                alt_shape: null,
                alt_confidence: 0
            };
            
            // Generate alternative
            const generatedAltShape = generateAlternativeShape(testData.shape);
            const generatedAltConfidence = Math.max(20, testData.confidence - Math.random() * 20 - 15);
            
            const adjusted = adjustConfidenceLevels(testData.confidence, generatedAltConfidence, generatedAltShape);
            
            const finalData = {
                ...testData,
                alt_shape: generatedAltShape,
                alt_confidence: generatedAltConfidence
            };
            
            displayTestResult('Scenario 2: No Alternative (Generated)', finalData, adjusted, resultsDiv);
            displayConfidenceBars(finalData.shape, adjusted.primary, finalData.alt_shape, adjusted.alternative);
        }

        function testScenario3() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<p class="text-purple-600">🧪 Testing Scenario 3: Low Confidence...</p>';
            
            // Simulate low confidence scenario
            const testData = {
                shape: 'rectangular',
                confidence: 55,
                alt_shape: 'square',
                alt_confidence: 52
            };
            
            // Boost low confidence
            let boostedPrimary = testData.confidence;
            let boostedAlt = testData.alt_confidence;
            
            if (boostedPrimary < 65) {
                boostedPrimary = 75; // Boost to 75%
                boostedAlt = Math.max(20, boostedPrimary - Math.random() * 20 - 15);
            }
            
            const adjusted = adjustConfidenceLevels(boostedPrimary, boostedAlt, testData.alt_shape);
            
            displayTestResult('Scenario 3: Low Confidence (Boosted)', testData, adjusted, resultsDiv);
            displayConfidenceBars(testData.shape, adjusted.primary, testData.alt_shape, adjusted.alternative);
        }

        function displayTestResult(scenarioName, originalData, adjustedData, container) {
            const html = `
                <div class="space-y-4">
                    <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h3 class="font-bold text-blue-800">${scenarioName}</h3>
                        <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-medium text-gray-800">Original Data:</h4>
                                <ul class="text-sm text-gray-600 mt-1">
                                    <li>Primary: ${originalData.shape} (${originalData.confidence}%)</li>
                                    <li>Alternative: ${originalData.alt_shape || 'None'} (${originalData.alt_confidence}%)</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-800">Adjusted Data:</h4>
                                <ul class="text-sm text-gray-600 mt-1">
                                    <li>Primary: ${originalData.shape} (${adjustedData.primary}%)</li>
                                    <li>Alternative: ${originalData.alt_shape || 'Generated'} (${adjustedData.alternative}%)</li>
                                    <li>Gap: ${adjustedData.primary - adjustedData.alternative} points</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML = html;
        }

        function displayConfidenceBars(primaryShape, primaryConfidence, altShape, altConfidence) {
            const displayDiv = document.getElementById('confidenceDisplay');
            
            const html = `
                <div class="space-y-4">
                    <h3 class="text-lg font-bold text-gray-700 mb-2">Confidence Level</h3>
                    
                    <!-- Primary Shape Confidence -->
                    <div class="flex items-center gap-4">
                        <div class="w-10 h-10 flex-shrink-0 bg-green-100 rounded-full flex items-center justify-center">
                            <span class="text-green-600 font-bold text-sm">${primaryShape.charAt(0).toUpperCase()}</span>
                        </div>
                        <div class="flex-1">
                            <p class="text-gray-700 font-semibold">${primaryShape.charAt(0).toUpperCase() + primaryShape.slice(1)} (Utama)</p>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-green-500 h-2.5 rounded-full transition-all duration-1000" style="width: ${primaryConfidence}%"></div>
                            </div>
                        </div>
                        <span class="text-gray-700 font-semibold text-sm">${primaryConfidence}%</span>
                    </div>

                    <!-- Alternative Shape Confidence -->
                    <div class="flex items-center gap-4">
                        <div class="w-10 h-10 flex-shrink-0 bg-blue-100 rounded-full flex items-center justify-center">
                            <span class="text-blue-600 font-bold text-sm">${altShape ? altShape.charAt(0).toUpperCase() : 'N'}</span>
                        </div>
                        <div class="flex-1">
                            <p class="text-gray-700 font-semibold">${altShape ? altShape.charAt(0).toUpperCase() + altShape.slice(1) : 'None'} (Alternatif)</p>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-blue-500 h-2.5 rounded-full transition-all duration-1000" style="width: ${altConfidence}%"></div>
                            </div>
                        </div>
                        <span class="text-gray-700 font-semibold text-sm">${altConfidence}%</span>
                    </div>

                    <!-- Gap Indicator -->
                    <div class="p-3 bg-gray-50 border rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 font-medium">Confidence Gap:</span>
                            <span class="text-gray-800 font-bold">${primaryConfidence - altConfidence} points</span>
                        </div>
                        <div class="mt-2">
                            <div class="text-xs ${primaryConfidence - altConfidence >= 15 ? 'text-green-600' : 'text-yellow-600'}">
                                ${primaryConfidence - altConfidence >= 15 ? '✅ Good gap (≥15 points)' : '⚠️ Small gap (<15 points)'}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            displayDiv.innerHTML = html;
        }

        // Initialize with default display
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 Confidence level test system initialized');
            
            // Show default confidence display
            displayConfidenceBars('square', 88, 'oval', 65);
            
            const displayDiv = document.getElementById('confidenceDisplay');
            displayDiv.insertAdjacentHTML('afterbegin', `
                <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg mb-4">
                    <p class="text-yellow-800 text-sm">
                        <i class="fas fa-info-circle mr-2"></i>
                        Default example shown. Click test scenarios above to see different confidence adjustments.
                    </p>
                </div>
            `);
        });
    </script>
</body>
</html>
