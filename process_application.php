<?php
session_start();
require_once 'admin/db_config.php';

// Check if application data is in session
if (!isset($_SESSION['job_application']) || !isset($_SESSION['application_data'])) {
    header("Location: jobs.php");
    exit();
}

// Process quiz submission if POST data exists
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Define correct answers
    $correct_answers = [
        'q1' => 'C', // Proper blending between lengths
        'q2' => 'A', // After each client
        'q3' => 'B', // Following the natural jawline with slight fading
        'q4' => 'B'  // To soften the hair and prepare the skin
    ];
    
    // Calculate quiz score
    $score = 0;
    $total_questions = count($correct_answers);
    $quiz_answers = [];
    
    foreach ($correct_answers as $question => $correct_answer) {
        $user_answer = $_POST[$question] ?? '';
        $quiz_answers[$question] = $user_answer;
        if ($user_answer === $correct_answer) {
            $score++;
        }
    }
    
    $score_percentage = ($score / $total_questions) * 100;
    
    // Get job requirements to determine qualification
    try {
        $stmt = $conn->prepare("SELECT requirements, position FROM jobs WHERE id = ?");
        $stmt->execute([$_SESSION['job_application']['job_id']]);
        $job = $stmt->fetch();

        // Comprehensive evaluation system
        $evaluation_scores = [];
        $total_score = 0;
        $max_score = 100;

        // Get application data
        $experience = $_SESSION['application_data']['professional']['experience'] ?? '';
        $license = $_SESSION['application_data']['professional']['license'] ?? '';
        $skills = $_SESSION['application_data']['professional']['skills'] ?? '';
        $has_resume = isset($_FILES['resume']) && $_FILES['resume']['error'] === UPLOAD_ERR_OK;

        // 1. CV/Resume Evaluation (20 points)
        $cv_score = 0;
        if ($has_resume) {
            $cv_score = 20;
            $evaluation_scores['cv'] = ['score' => 20, 'max' => 20, 'status' => 'Ada CV/Resume'];
        } else {
            $evaluation_scores['cv'] = ['score' => 0, 'max' => 20, 'status' => 'Tidak ada CV/Resume'];
        }

        // 2. Certificate/License Evaluation (25 points)
        $cert_score = 0;
        $cert_keywords = ['sertifikat', 'certificate', 'lisensi', 'license', 'ijazah', 'diploma', 'barber', 'salon', 'kursus'];
        if (!empty($license)) {
            $license_lower = strtolower($license);
            $cert_matches = 0;
            foreach ($cert_keywords as $keyword) {
                if (stripos($license_lower, $keyword) !== false) {
                    $cert_matches++;
                }
            }
            $cert_score = min(25, $cert_matches * 8); // Max 25 points
            $evaluation_scores['certificate'] = ['score' => $cert_score, 'max' => 25, 'status' => 'Memiliki sertifikat/lisensi relevan'];
        } else {
            $evaluation_scores['certificate'] = ['score' => 0, 'max' => 25, 'status' => 'Tidak ada sertifikat/lisensi'];
        }

        // 3. Experience Evaluation (25 points)
        $exp_score = 0;
        $exp_keywords = ['barber', 'salon', 'hair', 'rambut', 'cukur', 'potong', 'styling', 'grooming', 'tahun', 'bulan'];
        if (!empty($experience)) {
            $experience_lower = strtolower($experience);
            $exp_matches = 0;

            // Check for relevant keywords
            foreach ($exp_keywords as $keyword) {
                if (stripos($experience_lower, $keyword) !== false) {
                    $exp_matches++;
                }
            }

            // Check for years of experience
            $years_pattern = '/(\d+)\s*(tahun|year)/i';
            $months_pattern = '/(\d+)\s*(bulan|month)/i';

            $years_experience = 0;
            if (preg_match($years_pattern, $experience, $matches)) {
                $years_experience = intval($matches[1]);
            }
            if (preg_match($months_pattern, $experience, $matches)) {
                $years_experience += intval($matches[1]) / 12;
            }

            // Score based on experience
            if ($years_experience >= 3) {
                $exp_score = 25;
            } elseif ($years_experience >= 1) {
                $exp_score = 20;
            } elseif ($exp_matches >= 3) {
                $exp_score = 15;
            } elseif ($exp_matches >= 1) {
                $exp_score = 10;
            }

            $evaluation_scores['experience'] = ['score' => $exp_score, 'max' => 25, 'status' => "Pengalaman: {$years_experience} tahun, relevansi tinggi"];
        } else {
            $evaluation_scores['experience'] = ['score' => 0, 'max' => 25, 'status' => 'Tidak ada pengalaman yang disebutkan'];
        }

        // 4. Skills Evaluation (15 points)
        $skill_score = 0;
        $skill_keywords = ['fade', 'pompadour', 'undercut', 'beard', 'mustache', 'trimming', 'clipper', 'scissor', 'razor', 'styling', 'coloring', 'washing'];
        if (!empty($skills)) {
            $skills_lower = strtolower($skills);
            $skill_matches = 0;
            foreach ($skill_keywords as $keyword) {
                if (stripos($skills_lower, $keyword) !== false) {
                    $skill_matches++;
                }
            }
            $skill_score = min(15, $skill_matches * 2); // Max 15 points
            $evaluation_scores['skills'] = ['score' => $skill_score, 'max' => 15, 'status' => "Memiliki {$skill_matches} skill relevan"];
        } else {
            $evaluation_scores['skills'] = ['score' => 0, 'max' => 15, 'status' => 'Tidak ada skill yang disebutkan'];
        }

        // 5. Quiz Score Evaluation (15 points)
        $quiz_score_points = ($score_percentage / 100) * 15;
        $evaluation_scores['quiz'] = ['score' => round($quiz_score_points, 1), 'max' => 15, 'status' => "Skor quiz: {$score_percentage}%"];

        // Calculate total score
        $total_score = $cv_score + $cert_score + $exp_score + $skill_score + $quiz_score_points;

        // Determine status based on comprehensive evaluation
        $auto_status = 'pending'; // default
        $status_reason = '';

        if ($total_score >= 80) {
            $auto_status = 'accepted';
            $status_reason = "Otomatis diterima: Skor evaluasi tinggi ({$total_score}/100). Memenuhi semua kriteria utama.";
        } elseif ($total_score >= 60) {
            $auto_status = 'reviewed';
            $status_reason = "Perlu review manual: Skor evaluasi cukup baik ({$total_score}/100). Memerlukan verifikasi lebih lanjut.";
        } else {
            $auto_status = 'rejected';
            $status_reason = "Otomatis ditolak: Skor evaluasi rendah ({$total_score}/100). Belum memenuhi kriteria minimum.";
        }
        
        // Handle file upload for resume
        $resume_filename = null;
        if (isset($_FILES['resume']) && $_FILES['resume']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/resumes/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $file_extension = pathinfo($_FILES['resume']['name'], PATHINFO_EXTENSION);
            $resume_filename = uniqid() . '_' . time() . '.' . $file_extension;
            $target_file = $upload_dir . $resume_filename;
            
            if (!move_uploaded_file($_FILES['resume']['tmp_name'], $target_file)) {
                $resume_filename = null;
            }
        }
        
        // First, add the new columns to applications table if they don't exist
        try {
            $conn->exec("ALTER TABLE applications ADD COLUMN IF NOT EXISTS quiz_score DECIMAL(5,2) DEFAULT NULL");
            $conn->exec("ALTER TABLE applications ADD COLUMN IF NOT EXISTS quiz_answers JSON DEFAULT NULL");
            $conn->exec("ALTER TABLE applications ADD COLUMN IF NOT EXISTS status_reason TEXT DEFAULT NULL");
            $conn->exec("ALTER TABLE applications ADD COLUMN IF NOT EXISTS evaluation_score DECIMAL(5,2) DEFAULT NULL");
            $conn->exec("ALTER TABLE applications ADD COLUMN IF NOT EXISTS evaluation_details JSON DEFAULT NULL");
        } catch (PDOException $e) {
            // Columns might already exist, continue
        }

        // Insert application into database
        $stmt = $conn->prepare("
            INSERT INTO applications (
                job_id, full_name, email, phone, address, city,
                experience, license, skills, start_date, days_available,
                cover_letter, resume_file, status, quiz_score, quiz_answers,
                status_reason, evaluation_score, evaluation_details
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $_SESSION['job_application']['job_id'],
            $_SESSION['application_data']['personal']['full_name'],
            $_SESSION['application_data']['personal']['email'],
            $_SESSION['application_data']['personal']['phone'],
            $_SESSION['application_data']['personal']['address'],
            $_SESSION['application_data']['personal']['city'],
            $_SESSION['application_data']['professional']['experience'],
            $_SESSION['application_data']['professional']['license'],
            $_SESSION['application_data']['professional']['skills'],
            $_SESSION['application_data']['availability']['start_date'],
            json_encode($_SESSION['application_data']['availability']['days_available']),
            $_SESSION['application_data']['cover_letter'],
            $resume_filename,
            $auto_status,
            $score_percentage,
            json_encode($quiz_answers),
            $status_reason,
            $total_score,
            json_encode($evaluation_scores)
        ]);
        
        // Get the inserted application ID
        $application_id = $conn->lastInsertId();

        // Send WhatsApp notification
        require_once 'admin/whatsapp_notification.php';
        $notification_result = sendWhatsAppNotification($application_id, $auto_status);

        // Store result in session for display
        $_SESSION['application_result'] = [
            'status' => $auto_status,
            'score' => $score_percentage,
            'total_questions' => $total_questions,
            'correct_answers' => $score,
            'status_reason' => $status_reason,
            'evaluation_score' => $total_score,
            'evaluation_details' => $evaluation_scores,
            'whatsapp_sent' => $notification_result['success'] ?? false
        ];

        // Clear application data from session
        unset($_SESSION['job_application']);
        unset($_SESSION['application_data']);

        // Redirect to success page
        header("Location: selesailamar.php");
        exit();
        
    } catch (PDOException $e) {
        error_log("Application submission error: " . $e->getMessage());
        $_SESSION['error_message'] = 'Terjadi kesalahan saat menyimpan lamaran. Silakan coba lagi.';
        header("Location: barber_quiz.php");
        exit();
    }
}

// If not POST request, redirect back
header("Location: barber_quiz.php");
exit();
?>
