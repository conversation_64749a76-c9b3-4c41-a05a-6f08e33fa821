<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Redirect - Face Detection</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">🚀 Debug Redirect System</h1>
                <p class="text-gray-600">Debugging tools untuk masalah redirect setelah capture</p>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🎮 Quick Actions</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="face.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded text-center">
                        <i class="fas fa-camera mr-2"></i>Test Face Detection
                    </a>
                    <a href="output.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-center">
                        <i class="fas fa-eye mr-2"></i>View Output Page
                    </a>
                    <button onclick="testRedirect()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-rocket mr-2"></i>Test Redirect
                    </button>
                </div>
            </div>

            <!-- Debug Results -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🔍 Debug Results</h2>
                <div id="debugResults" class="space-y-4">
                    <p class="text-gray-500">Klik tombol test untuk memulai debugging...</p>
                </div>
            </div>

            <!-- Test Tools -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🧪 Test Tools</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button onclick="testSessionStorage()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-database mr-2"></i>Test SessionStorage
                    </button>
                    <button onclick="testServerConnection()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-server mr-2"></i>Test Server Connection
                    </button>
                    <button onclick="simulateCapture()" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-camera-retro mr-2"></i>Simulate Capture
                    </button>
                    <button onclick="forceRedirect()" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-external-link-alt mr-2"></i>Force Redirect
                    </button>
                </div>
            </div>

            <!-- Current Status -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">📊 Current Status</h2>
                <div id="currentStatus" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-gray-100 p-4 rounded-lg">
                        <h3 class="font-bold text-gray-700 mb-2">SessionStorage</h3>
                        <div id="sessionStorageStatus">Checking...</div>
                    </div>
                    <div class="bg-gray-100 p-4 rounded-lg">
                        <h3 class="font-bold text-gray-700 mb-2">Server Status</h3>
                        <div id="serverStatus">Checking...</div>
                    </div>
                </div>
                <button onclick="refreshStatus()" class="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                    <i class="fas fa-sync mr-2"></i>Refresh Status
                </button>
            </div>
        </div>
    </div>

    <script>
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            addDebugResult('🚀 Debug redirect system initialized', 'info');
        });

        function testRedirect() {
            addDebugResult('🚀 Testing redirect functionality...', 'info');
            
            // Test 1: Check if we can redirect
            try {
                const testUrl = 'output.php?face_shape=oval&test=true';
                addDebugResult(`📍 Test URL: ${testUrl}`, 'info');
                
                // Test redirect in new tab first
                const testWindow = window.open(testUrl, '_blank');
                
                if (testWindow) {
                    addDebugResult('✅ Redirect test: New tab opened successfully', 'success');
                    
                    // Close test window after 2 seconds
                    setTimeout(() => {
                        testWindow.close();
                        addDebugResult('🔄 Test window closed', 'info');
                    }, 2000);
                } else {
                    addDebugResult('❌ Redirect test: Popup blocked or failed', 'error');
                }
                
            } catch (error) {
                addDebugResult('❌ Redirect test error: ' + error.message, 'error');
            }
        }

        function testSessionStorage() {
            addDebugResult('💾 Testing SessionStorage...', 'info');
            
            try {
                // Test write
                const testData = {
                    shape: 'oval',
                    timestamp: new Date().toISOString(),
                    test: true
                };
                
                sessionStorage.setItem('debugTest', JSON.stringify(testData));
                addDebugResult('✅ SessionStorage write: Success', 'success');
                
                // Test read
                const retrieved = JSON.parse(sessionStorage.getItem('debugTest'));
                if (retrieved && retrieved.shape === 'oval') {
                    addDebugResult('✅ SessionStorage read: Success', 'success');
                } else {
                    addDebugResult('❌ SessionStorage read: Failed', 'error');
                }
                
                // Cleanup
                sessionStorage.removeItem('debugTest');
                addDebugResult('🧹 SessionStorage cleanup: Complete', 'info');
                
            } catch (error) {
                addDebugResult('❌ SessionStorage error: ' + error.message, 'error');
            }
        }

        function testServerConnection() {
            addDebugResult('🌐 Testing server connection...', 'info');
            
            // Test 1: Basic server ping
            fetch('analyze_face_sim.php', {
                method: 'POST',
                body: new FormData() // Empty form data
            })
            .then(response => {
                addDebugResult(`📡 Server response: ${response.status} ${response.statusText}`, 
                              response.ok ? 'success' : 'warning');
                return response.text();
            })
            .then(data => {
                addDebugResult(`📄 Server response length: ${data.length} characters`, 'info');
                if (data.includes('error') || data.includes('Error')) {
                    addDebugResult('⚠️ Server response contains errors', 'warning');
                } else {
                    addDebugResult('✅ Server connection: OK', 'success');
                }
            })
            .catch(error => {
                addDebugResult('❌ Server connection error: ' + error.message, 'error');
            });
        }

        function simulateCapture() {
            addDebugResult('📸 Simulating capture process...', 'info');
            
            // Step 1: Create mock image data
            const canvas = document.createElement('canvas');
            canvas.width = 640;
            canvas.height = 480;
            const ctx = canvas.getContext('2d');
            
            // Draw mock face
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, 640, 480);
            ctx.fillStyle = '#333';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('MOCK FACE', 320, 240);
            
            addDebugResult('✅ Step 1: Mock image created', 'success');
            
            // Step 2: Convert to blob
            canvas.toBlob(blob => {
                addDebugResult(`✅ Step 2: Image blob created (${blob.size} bytes)`, 'success');
                
                // Step 3: Create form data
                const formData = new FormData();
                formData.append('image', blob, 'test.jpg');
                addDebugResult('✅ Step 3: FormData prepared', 'success');
                
                // Step 4: Simulate server request
                fetch('analyze_face_sim.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    addDebugResult(`✅ Step 4: Server request completed (${response.status})`, 'success');
                    return response.text();
                })
                .then(data => {
                    addDebugResult('✅ Step 5: Server response received', 'success');
                    
                    // Step 6: Simulate data storage
                    const mockResult = {
                        shape: 'oval',
                        confidence: 85,
                        timestamp: new Date().toISOString(),
                        source: 'simulation'
                    };
                    
                    sessionStorage.setItem('faceAnalysisResult', JSON.stringify(mockResult));
                    addDebugResult('✅ Step 6: Data stored to sessionStorage', 'success');
                    
                    // Step 7: Test redirect
                    addDebugResult('🚀 Step 7: Testing redirect...', 'info');
                    setTimeout(() => {
                        const testUrl = 'output.php?face_shape=oval&simulated=true';
                        addDebugResult(`🎯 Redirect URL: ${testUrl}`, 'info');
                        addDebugResult('✅ Simulation complete! Check output.php manually', 'success');
                    }, 1000);
                })
                .catch(error => {
                    addDebugResult('❌ Simulation error: ' + error.message, 'error');
                });
                
            }, 'image/jpeg', 0.8);
        }

        function forceRedirect() {
            addDebugResult('🚀 Force redirecting to output.php...', 'info');
            
            // Create minimal data if none exists
            if (!sessionStorage.getItem('faceAnalysisResult')) {
                const minimalData = {
                    shape: 'oval',
                    confidence: 85,
                    description: 'Force redirect test',
                    timestamp: new Date().toISOString()
                };
                sessionStorage.setItem('faceAnalysisResult', JSON.stringify(minimalData));
                addDebugResult('📦 Created minimal data for redirect', 'info');
            }
            
            // Force redirect
            window.location.href = 'output.php?face_shape=oval&forced=true';
        }

        function refreshStatus() {
            // Check SessionStorage
            const sessionStorageDiv = document.getElementById('sessionStorageStatus');
            try {
                const testKey = 'statusTest';
                sessionStorage.setItem(testKey, 'test');
                const retrieved = sessionStorage.getItem(testKey);
                sessionStorage.removeItem(testKey);
                
                if (retrieved === 'test') {
                    sessionStorageDiv.innerHTML = '<span class="text-green-600">✅ Working</span>';
                } else {
                    sessionStorageDiv.innerHTML = '<span class="text-red-600">❌ Failed</span>';
                }
            } catch (error) {
                sessionStorageDiv.innerHTML = '<span class="text-red-600">❌ Error: ' + error.message + '</span>';
            }
            
            // Check Server
            const serverStatusDiv = document.getElementById('serverStatus');
            serverStatusDiv.innerHTML = '<span class="text-yellow-600">⏳ Checking...</span>';
            
            fetch('analyze_face_sim.php', {
                method: 'POST',
                body: new FormData()
            })
            .then(response => {
                if (response.ok) {
                    serverStatusDiv.innerHTML = '<span class="text-green-600">✅ Online</span>';
                } else {
                    serverStatusDiv.innerHTML = '<span class="text-yellow-600">⚠️ Issues (' + response.status + ')</span>';
                }
            })
            .catch(error => {
                serverStatusDiv.innerHTML = '<span class="text-red-600">❌ Offline</span>';
            });
        }

        function addDebugResult(message, type) {
            const resultsDiv = document.getElementById('debugResults');
            const resultItem = document.createElement('div');
            
            let colorClass = 'text-gray-700';
            let bgClass = 'bg-gray-100';
            let icon = '📝';
            
            switch (type) {
                case 'success':
                    colorClass = 'text-green-700';
                    bgClass = 'bg-green-100';
                    icon = '✅';
                    break;
                case 'error':
                    colorClass = 'text-red-700';
                    bgClass = 'bg-red-100';
                    icon = '❌';
                    break;
                case 'warning':
                    colorClass = 'text-yellow-700';
                    bgClass = 'bg-yellow-100';
                    icon = '⚠️';
                    break;
                case 'info':
                    colorClass = 'text-blue-700';
                    bgClass = 'bg-blue-100';
                    icon = 'ℹ️';
                    break;
            }
            
            resultItem.className = `p-3 rounded-lg ${bgClass} ${colorClass} font-mono text-sm`;
            resultItem.innerHTML = `${icon} [${new Date().toLocaleTimeString()}] ${message}`;
            
            // Clear "no results" message if it exists
            if (resultsDiv.children.length === 1 && resultsDiv.children[0].textContent.includes('Klik tombol test')) {
                resultsDiv.innerHTML = '';
            }
            
            resultsDiv.appendChild(resultItem);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
    </script>
</body>
</html>
