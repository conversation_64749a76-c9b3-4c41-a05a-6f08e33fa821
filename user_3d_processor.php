<?php
header('Content-Type: application/json');

function convertUserTo3D($imagePath) {
    try {
        if (!file_exists($imagePath)) {
            return ['success' => false, 'error' => 'Image file not found'];
        }
        
        // Call Python script to convert user image to 3D
        $pythonScript = __DIR__ . '/user_3d_converter.py';
        $command = sprintf('python "%s" "%s"', $pythonScript, $imagePath);
        
        $output = shell_exec($command . ' 2>&1');
        
        if (!$output) {
            return ['success' => false, 'error' => 'Python conversion failed'];
        }
        
        $result = json_decode($output, true);
        
        if (!$result) {
            return ['success' => false, 'error' => 'Invalid conversion output', 'debug' => $output];
        }
        
        return $result;
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $imagePath = $input['image_path'] ?? '';
    
    if (empty($imagePath)) {
        echo json_encode(['success' => false, 'error' => 'Image path required']);
        exit;
    }
    
    $result = convertUserTo3D($imagePath);
    echo json_encode($result);
    exit;
}

echo json_encode(['success' => false, 'error' => 'Invalid request method']);
?>