<?php
require_once 'db_config.php';

header('Content-Type: application/json');

$response = ['status' => 'error', 'message' => 'Invalid request.'];

if (isset($_GET['product_id'])) {
    $product_id = $_GET['product_id'];

    try {
        $stmt = $conn->prepare("SELECT id, ukuran_ml, harga, stok, gambar_varian FROM variasi_produk WHERE produk_id = :product_id");
        $stmt->execute([':product_id' => $product_id]);
        $variations = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $response = ['status' => 'success', 'data' => $variations];
    } catch (PDOException $e) {
        $response = ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
    }
} else {
    $response = ['status' => 'error', 'message' => 'Product ID is missing.'];
}

echo json_encode($response);
?> 