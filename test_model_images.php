<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Model Images - Face Detection</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">🖼️ Test Model Images</h1>
                <p class="text-gray-600">Verifikasi ketersediaan gambar model untuk setiap bentuk wajah</p>
            </div>

            <!-- Test Results -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">📊 Image Availability Test</h2>
                <div id="testResults" class="space-y-4">
                    <p class="text-gray-500">Testing image availability...</p>
                </div>
            </div>

            <!-- Face Shape Galleries -->
            <div id="galleries" class="space-y-8">
                <!-- Galleries will be generated here -->
            </div>
        </div>
    </div>

    <script>
        // Define face shapes and their recommended hairstyles
        const faceShapeRecommendations = {
            'oval': [
                'Textured Crop',
                'Pompadour', 
                'Man Bun',
                'Long Shaggy',
                'Classic Undercut'
            ],
            'round': [
                'Two Block Hair',
                'Taper Fade',
                'Fringe Haircut',
                'Fluffy with Low Fade',
                'Crop Cut'
            ],
            'square': [
                'Slick Back',
                'Long Layers',
                'French Crop',
                'Brush Up',
                'Bro Flow'
            ],
            'heart': [
                'Undercut',
                'Textured Crop',
                'Quiff',
                'Pompadour',
                'Fringe'
            ],
            'rectangular': [
                'Slick Back Hairstyles for Men',
                'Short Sides Long Top',
                'Quiff',
                'Man Bun',
                'French Crop'
            ]
        };

        // Initialize testing
        document.addEventListener('DOMContentLoaded', function() {
            testAllImages();
        });

        function testAllImages() {
            const testResults = document.getElementById('testResults');
            const galleries = document.getElementById('galleries');
            
            testResults.innerHTML = '<p class="text-blue-600">🔍 Testing image availability...</p>';
            
            let totalImages = 0;
            let loadedImages = 0;
            let failedImages = 0;
            
            // Count total images
            Object.keys(faceShapeRecommendations).forEach(shape => {
                totalImages += faceShapeRecommendations[shape].length;
            });
            
            // Test each face shape
            Object.keys(faceShapeRecommendations).forEach(shape => {
                const shapeGallery = createShapeGallery(shape);
                galleries.appendChild(shapeGallery);
                
                faceShapeRecommendations[shape].forEach((haircut, index) => {
                    const imagePath = `rekomendasi/${shape}/${haircut}.jpg`;
                    
                    // Test image availability
                    const img = new Image();
                    img.onload = function() {
                        loadedImages++;
                        updateImageStatus(shape, haircut, 'success', imagePath);
                        updateTestResults(testResults, totalImages, loadedImages, failedImages);
                    };
                    img.onerror = function() {
                        failedImages++;
                        updateImageStatus(shape, haircut, 'error', imagePath);
                        updateTestResults(testResults, totalImages, loadedImages, failedImages);
                    };
                    img.src = imagePath;
                });
            });
        }

        function createShapeGallery(shape) {
            const gallery = document.createElement('div');
            gallery.className = 'bg-white rounded-lg shadow-lg p-6';
            gallery.innerHTML = `
                <h2 class="text-xl font-bold text-gray-800 mb-4 capitalize">${shape} Face Shape</h2>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4" id="gallery-${shape}">
                    <!-- Images will be added here -->
                </div>
            `;
            return gallery;
        }

        function updateImageStatus(shape, haircut, status, imagePath) {
            const gallery = document.getElementById(`gallery-${shape}`);
            const imageCard = document.createElement('div');
            imageCard.className = 'flex flex-col items-center p-3 border rounded-lg';
            
            if (status === 'success') {
                imageCard.className += ' border-green-500 bg-green-50';
                imageCard.innerHTML = `
                    <div class="w-20 h-20 rounded-lg overflow-hidden border-2 border-green-500 mb-2">
                        <img src="${imagePath}" alt="${haircut}" class="w-full h-full object-cover">
                    </div>
                    <p class="text-xs text-center font-medium text-green-700">${haircut}</p>
                    <span class="text-xs text-green-600">✅ Available</span>
                `;
            } else {
                imageCard.className += ' border-red-500 bg-red-50';
                imageCard.innerHTML = `
                    <div class="w-20 h-20 rounded-lg overflow-hidden border-2 border-red-500 mb-2 bg-gray-200 flex items-center justify-center">
                        <i class="fas fa-image text-gray-400 text-2xl"></i>
                    </div>
                    <p class="text-xs text-center font-medium text-red-700">${haircut}</p>
                    <span class="text-xs text-red-600">❌ Missing</span>
                `;
            }
            
            gallery.appendChild(imageCard);
        }

        function updateTestResults(testResults, total, loaded, failed) {
            const percentage = Math.round((loaded / total) * 100);
            const failedPercentage = Math.round((failed / total) * 100);
            
            testResults.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 class="font-bold text-blue-800">Total Images</h3>
                        <p class="text-2xl font-bold text-blue-600">${total}</p>
                    </div>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h3 class="font-bold text-green-800">Available</h3>
                        <p class="text-2xl font-bold text-green-600">${loaded} (${percentage}%)</p>
                    </div>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 class="font-bold text-red-800">Missing</h3>
                        <p class="text-2xl font-bold text-red-600">${failed} (${failedPercentage}%)</p>
                    </div>
                </div>
                
                ${loaded + failed === total ? `
                    <div class="mt-4 p-4 ${percentage >= 80 ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'} border rounded-lg">
                        <h3 class="font-bold ${percentage >= 80 ? 'text-green-800' : 'text-yellow-800'}">
                            ${percentage >= 80 ? '✅ Test Complete - Good Coverage' : '⚠️ Test Complete - Some Images Missing'}
                        </h3>
                        <p class="text-sm ${percentage >= 80 ? 'text-green-700' : 'text-yellow-700'} mt-1">
                            ${percentage >= 80 
                                ? 'Most model images are available. The recommendation system should work well.'
                                : 'Some model images are missing. Consider adding the missing images or using placeholder images.'
                            }
                        </p>
                    </div>
                ` : ''}
            `;
        }

        // Function to create missing images (placeholder)
        function createMissingImages() {
            const canvas = document.createElement('canvas');
            canvas.width = 200;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = '#f3f4f6';
            ctx.fillRect(0, 0, 200, 200);
            
            // Border
            ctx.strokeStyle = '#d1d5db';
            ctx.lineWidth = 2;
            ctx.strokeRect(1, 1, 198, 198);
            
            // Icon
            ctx.fillStyle = '#9ca3af';
            ctx.font = 'bold 48px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('👤', 100, 80);
            
            // Text
            ctx.fillStyle = '#6b7280';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('Model Image', 100, 120);
            ctx.font = '12px Arial';
            ctx.fillText('Coming Soon', 100, 140);
            
            return canvas.toDataURL('image/jpeg', 0.8);
        }
    </script>
</body>
</html>
