<?php
require_once 'Admin/db_config.php';
session_start();

// Session management

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Try to get user info from cookies if session is lost
    if (isset($_COOKIE['user_login']) && isset($_COOKIE['user_id'])) {
        $_SESSION['user_id'] = $_COOKIE['user_id'];
        $_SESSION['user_login'] = $_COOKIE['user_login'];
        error_log("Keranjang.php - Restored session from cookies");
    } else {
        // If not logged in, redirect to login with return URL
        $return_url = urlencode($_SERVER['REQUEST_URI']);
        error_log("Keranjang.php - Redirecting to login, no session or cookies found");
        header("Location: login.php?return_url=$return_url");
        exit();
    }
}

$user_id = $_SESSION['user_id'];

// Get user info
$user_stmt = $conn->prepare("SELECT * FROM pembeli WHERE id = ?");
$user_stmt->execute([$user_id]);
$user_info = $user_stmt->fetch(PDO::FETCH_ASSOC);

// Create cart table if it doesn't exist
try {
    $conn->exec("
        CREATE TABLE IF NOT EXISTS cart (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            product_id INT NOT NULL,
            variant_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_cart_item (user_id, product_id, variant_id)
        )
    ");
} catch (PDOException $e) {
    error_log("Error creating cart table: " . $e->getMessage());
}

// Get cart items
$cart_stmt = $conn->prepare("
    SELECT c.*, p.nama_produk, p.gambar_utama, p.deskripsi,
           v.ukuran_ml, v.harga, v.stok,
           (c.quantity * v.harga) as total_price
    FROM cart c
    JOIN produk p ON c.product_id = p.id
    JOIN variasi_produk v ON c.variant_id = v.id
    WHERE c.user_id = ?
    ORDER BY c.created_at DESC
");
$cart_stmt->execute([$user_id]);
$cart_items = $cart_stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate cart totals
$cart_subtotal = 0;
foreach ($cart_items as $item) {
    $cart_subtotal += $item['total_price'];
}

// Get active orders (confirmed to shipped) for order history section
$orders_stmt = $conn->prepare("
    SELECT o.*, ua.address_name, ua.full_address, pm.name as payment_method_name
    FROM orders o
    LEFT JOIN user_addresses ua ON o.address_id = ua.id
    LEFT JOIN payment_methods pm ON o.payment_method_id = pm.id
    WHERE o.pembeli_id = ?
    AND o.order_status IN ('confirmed', 'processing', 'shipped')
    ORDER BY o.created_at DESC
");
$orders_stmt->execute([$user_id]);
$orders = $orders_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get order items for each order
foreach ($orders as &$order) {
    $items_stmt = $conn->prepare("
        SELECT oi.*, p.gambar_utama
        FROM order_items oi
        LEFT JOIN produk p ON oi.product_id = p.id
        WHERE oi.order_id = ?
    ");
    $items_stmt->execute([$order['id']]);
    $order['items'] = $items_stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Status options and colors
$status_options = [
    'confirmed' => ['label' => 'Dikonfirmasi', 'color' => 'bg-blue-100 text-blue-800', 'icon' => 'fas fa-check-circle'],
    'processing' => ['label' => 'Diproses', 'color' => 'bg-purple-100 text-purple-800', 'icon' => 'fas fa-cog'],
    'shipped' => ['label' => 'Dikirim', 'color' => 'bg-indigo-100 text-indigo-800', 'icon' => 'fas fa-truck']
];
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pesanan Saya - Pixel Tonsorium</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg fixed top-0 left-0 right-0 z-20 p-4 shadow-md">
        <div class="flex items-center justify-between">
            <a href="index.php" class="text-white">
                <i class="fas fa-arrow-left text-lg"></i>
            </a>
            <h1 class="text-xl font-bold text-white">Pesanan Saya</h1>
            <div class="w-6"></div> <!-- Spacer for balance -->
        </div>
    </header>

    <!-- Main Content -->
    <div class="pt-20 pb-48 px-4 max-w-4xl mx-auto">
        <!-- Tab Navigation -->
        <div class="bg-white rounded-lg shadow-sm mb-4 p-1">
            <div class="flex space-x-1">
                <button id="cartTab" class="flex-1 py-2 px-4 text-sm font-medium text-center rounded-md bg-green-600 text-white">
                    Keranjang (<?php echo count($cart_items); ?>)
                </button>
                <button id="ordersTab" class="flex-1 py-2 px-4 text-sm font-medium text-center rounded-md text-gray-600 hover:bg-gray-100">
                    Pesanan (<?php echo count($orders); ?>)
                </button>
            </div>
        </div>

        <!-- Cart Items Section -->
        <div id="cartSection" class="tab-section">
            <?php if (empty($cart_items)): ?>
                <div class="bg-white rounded-lg shadow-sm p-8 text-center">
                    <div class="mb-4">
                        <i class="fas fa-shopping-cart text-6xl text-gray-300"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">Keranjang Kosong</h3>
                    <p class="text-gray-500 mb-4">Belum ada produk di keranjang Anda</p>
                    <a href="index.php" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-shopping-cart mr-2"></i>
                        Mulai Belanja
                    </a>
                </div>
            <?php else: ?>
                <!-- Select All Header -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox"
                                   id="selectAllCheckbox"
                                   class="w-5 h-5 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                                   onchange="toggleSelectAll()"
                                   checked>
                            <label for="selectAllCheckbox" class="font-medium text-gray-900">
                                Pilih Semua (<span id="selectedCount"><?php echo count($cart_items); ?></span>)
                            </label>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-600">Total Terpilih:</p>
                            <p class="font-bold text-green-600" id="selectedTotal">
                                Rp <?php echo number_format($cart_subtotal, 0, ',', '.'); ?>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="space-y-3">
                    <?php foreach ($cart_items as $item): ?>
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-200 cart-item cart-item-selected" data-cart-id="<?php echo $item['id']; ?>" data-price="<?php echo $item['total_price']; ?>">
                            <div class="p-4">
                                <div class="flex items-start space-x-4">
                                    <!-- Checkbox Selection -->
                                    <div class="flex-shrink-0 pt-1">
                                        <input type="checkbox"
                                               class="cart-checkbox w-5 h-5 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                                               data-cart-id="<?php echo $item['id']; ?>"
                                               data-price="<?php echo $item['total_price']; ?>"
                                               onchange="updateSelectedItems()"
                                               checked>
                                    </div>

                                    <!-- Product Image -->
                                    <div class="w-16 h-16 sm:w-20 sm:h-20 flex-shrink-0">
                                        <?php if (!empty($item['gambar_utama'])): ?>
                                            <?php
                                            // Check different possible paths for the image
                                            $image_paths = [
                                                'uploads/' . $item['gambar_utama'],
                                                'uploads/products/' . $item['gambar_utama'],
                                                $item['gambar_utama'] // In case it's already a full path
                                            ];

                                            $image_src = '';
                                            foreach ($image_paths as $path) {
                                                if (file_exists($path)) {
                                                    $image_src = $path;
                                                    break;
                                                }
                                            }

                                            // Fallback to first path if none found
                                            if (empty($image_src)) {
                                                $image_src = 'uploads/' . $item['gambar_utama'];
                                            }
                                            ?>
                                            <img src="<?php echo htmlspecialchars($image_src); ?>"
                                                 alt="<?php echo htmlspecialchars($item['nama_produk']); ?>"
                                                 class="w-full h-full object-cover rounded-lg border border-gray-200 shadow-sm"
                                                 onerror="this.parentElement.innerHTML='<div class=\'w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center border border-gray-200\'><i class=\'fas fa-image text-gray-400 text-lg\'></i></div>'">
                                        <?php else: ?>
                                            <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center border border-gray-200">
                                                <i class="fas fa-image text-gray-400 text-lg"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Product Details -->
                                    <div class="flex-1 min-w-0">
                                        <h4 class="font-semibold text-gray-900 text-sm sm:text-base mb-1 line-clamp-2">
                                            <?php echo htmlspecialchars($item['nama_produk']); ?>
                                        </h4>
                                        <div class="flex items-center space-x-2 mb-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <?php echo htmlspecialchars($item['ukuran_ml']); ?>ml
                                            </span>
                                            <span class="text-xs text-gray-500">
                                                Stok: <?php echo $item['stok']; ?>
                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-600 mb-3">
                                            <span class="font-medium">Rp <?php echo number_format($item['harga'], 0, ',', '.'); ?></span>
                                            <span class="text-gray-400"> × <?php echo $item['quantity']; ?></span>
                                        </p>

                                        <!-- Mobile: Quantity and Price Row -->
                                        <div class="flex items-center justify-between">
                                            <!-- Quantity Controls -->
                                            <div class="flex items-center space-x-1 bg-gray-50 rounded-lg p-1">
                                                <button onclick="updateCartQuantity(<?php echo $item['id']; ?>, <?php echo $item['quantity'] - 1; ?>)"
                                                        class="w-8 h-8 rounded-md bg-white border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                        <?php echo $item['quantity'] <= 1 ? 'disabled' : ''; ?>>
                                                    <i class="fas fa-minus text-xs text-gray-600"></i>
                                                </button>
                                                <span class="w-10 text-center font-semibold text-sm"><?php echo $item['quantity']; ?></span>
                                                <button onclick="updateCartQuantity(<?php echo $item['id']; ?>, <?php echo $item['quantity'] + 1; ?>)"
                                                        class="w-8 h-8 rounded-md bg-white border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                        <?php echo $item['quantity'] >= $item['stok'] ? 'disabled' : ''; ?>>
                                                    <i class="fas fa-plus text-xs text-gray-600"></i>
                                                </button>
                                            </div>

                                            <!-- Price and Actions -->
                                            <div class="text-right">
                                                <p class="font-bold text-green-600 text-lg mb-1">
                                                    Rp <?php echo number_format($item['total_price'], 0, ',', '.'); ?>
                                                </p>
                                                <button onclick="removeFromCart(<?php echo $item['id']; ?>)"
                                                        class="inline-flex items-center px-2 py-1 text-xs text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors">
                                                    <i class="fas fa-trash mr-1"></i>
                                                    <span class="hidden sm:inline">Hapus</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <!-- Spacer for fixed bottom navbar -->
                    <div class="h-32"></div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Orders List Section -->
        <div id="ordersSection" class="tab-section hidden">
            <?php if (empty($orders)): ?>
            <div class="bg-white rounded-lg shadow-sm p-8 text-center">
                <div class="mb-4">
                    <i class="fas fa-shopping-bag text-6xl text-gray-300"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-700 mb-2">Belum Ada Pesanan Aktif</h3>
                <p class="text-gray-500 mb-4">Anda belum memiliki pesanan yang sedang diproses</p>
                <a href="index.php" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    Mulai Belanja
                </a>
            </div>
        <?php else: ?>
            <div class="space-y-4">
                <?php foreach ($orders as $order): ?>
                    <div class="order-card bg-white rounded-lg shadow-sm border border-gray-200 transition-all duration-300" data-status="<?php echo $order['order_status']; ?>">
                        <!-- Order Header -->
                        <div class="p-4 border-b border-gray-200">
                            <div class="flex justify-between items-start">
                                <div>
                                    <div class="flex items-center space-x-2 mb-1">
                                        <i class="fas fa-store text-green-600"></i>
                                        <span class="font-medium text-gray-800">Pixel Tonsorium</span>
                                    </div>
                                    <p class="text-sm text-gray-500">Order #<?php echo $order['id']; ?></p>
                                    <p class="text-xs text-gray-400"><?php echo date('d M Y, H:i', strtotime($order['created_at'])); ?></p>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo $status_options[$order['order_status']]['color']; ?>">
                                        <i class="<?php echo $status_options[$order['order_status']]['icon']; ?> mr-1"></i>
                                        <?php echo $status_options[$order['order_status']]['label']; ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items -->
                        <div class="p-4">
                            <?php foreach ($order['items'] as $item): ?>
                                <div class="flex items-center space-x-3 mb-3 last:mb-0">
                                    <!-- Product Image -->
                                    <div class="w-16 h-16 flex-shrink-0">
                                        <?php if (!empty($item['gambar_utama'])): ?>
                                            <img src="uploads/products/<?php echo htmlspecialchars($item['gambar_utama']); ?>"
                                                 alt="<?php echo htmlspecialchars($item['product_name_at_purchase']); ?>"
                                                 class="w-full h-full object-cover rounded-lg border border-gray-200">
                                        <?php else: ?>
                                            <div class="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-image text-gray-400"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Product Details -->
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-800 text-sm">
                                            <?php echo htmlspecialchars($item['product_name_at_purchase']); ?>
                                        </h4>
                                        <?php if (!empty($item['variant_name_at_purchase'])): ?>
                                            <p class="text-xs text-gray-500">
                                                Varian: <?php echo htmlspecialchars($item['variant_name_at_purchase']); ?>
                                            </p>
                                        <?php endif; ?>
                                        <p class="text-xs text-gray-400">Qty: <?php echo $item['quantity']; ?></p>
                                    </div>

                                    <!-- Price -->
                                    <div class="text-right">
                                        <p class="font-medium text-gray-800 text-sm">
                                            Rp <?php echo number_format($item['total_price'], 0, ',', '.'); ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <!-- Order Footer -->
                        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                            <div class="flex justify-between items-center">
                                <div>
                                    <p class="text-sm text-gray-600">Total Pesanan:</p>
                                    <p class="font-semibold text-lg text-gray-800">
                                        Rp <?php echo number_format($order['total_amount'], 0, ',', '.'); ?>
                                    </p>
                                </div>
                                <div class="flex space-x-2">
                                    <?php if ($order['order_status'] === 'shipped'): ?>
                                        <button class="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors">
                                            <i class="fas fa-truck mr-1"></i>
                                            Lacak Paket
                                        </button>
                                    <?php endif; ?>
                                    <a href="konfirmasi_pembayaran.php?order_id=<?php echo $order['id']; ?>"
                                       class="px-4 py-2 border border-gray-300 text-gray-700 text-sm rounded-lg hover:bg-gray-50 transition-colors">
                                        <i class="fas fa-eye mr-1"></i>
                                        Detail
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="mt-6 bg-white rounded-lg shadow-sm p-4">
            <h3 class="font-medium text-gray-800 mb-3">Aksi Cepat</h3>
            <div class="grid grid-cols-2 gap-3">
                <a href="index.php" class="flex items-center justify-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-shopping-cart mr-2 text-green-600"></i>
                    <span class="text-sm font-medium">Belanja Lagi</span>
                </a>
                <a href="notifications.php" class="flex items-center justify-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <i class="fas fa-bell mr-2 text-blue-600"></i>
                    <span class="text-sm font-medium">Notifikasi</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Fixed Bottom Cart Summary Navbar -->
    <?php if (!empty($cart_items)): ?>
    <div id="cartBottomNavbar" class="fixed bottom-0 left-0 right-0 bg-white shadow-2xl border-t border-gray-200 z-50">
        <div class="max-w-4xl mx-auto">
            <!-- Cart Summary Header -->
            <div class="px-4 py-3 bg-gradient-to-r from-green-50 to-blue-50 border-b border-green-100">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-bold text-gray-900 text-lg">Total Keranjang</h3>
                        <p class="text-sm text-gray-600">
                            <span id="bottomSelectedCount"><?php echo count($cart_items); ?></span> item dipilih
                        </p>
                    </div>
                    <div class="text-right">
                        <p class="text-2xl font-bold text-green-600" id="bottomSelectedTotal">
                            Rp <?php echo number_format($cart_subtotal, 0, ',', '.'); ?>
                        </p>
                        <p class="text-xs text-gray-500">Belum termasuk ongkir</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="px-4 py-4">
                <div class="flex space-x-3">
                    <!-- Checkout Button -->
                    <button onclick="proceedToCheckout()"
                            class="flex-1 bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-4 rounded-xl font-semibold text-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <div class="flex items-center justify-center space-x-2">
                            <i class="fas fa-credit-card"></i>
                            <span id="checkoutButtonText">Checkout (<span id="bottomCheckoutCount"><?php echo count($cart_items); ?></span> item)</span>
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </button>

                    <!-- Clear Cart Button -->
                    <button onclick="clearCart()"
                            class="px-4 py-4 bg-white border-2 border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200">
                        <i class="fas fa-trash text-lg"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

<script>
    // Global variables for cart selection
    let selectedItems = new Set();
    let selectedTotal = 0;

    // Tab functionality and navbar management
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize all items as selected
        initializeSelection();
        const cartTab = document.getElementById('cartTab');
        const ordersTab = document.getElementById('ordersTab');
        const cartSection = document.getElementById('cartSection');
        const ordersSection = document.getElementById('ordersSection');
        const cartBottomNavbar = document.getElementById('cartBottomNavbar');

        // Tab switching
        cartTab.addEventListener('click', function() {
            // Update tab appearance
            cartTab.classList.add('bg-green-600', 'text-white');
            cartTab.classList.remove('text-gray-600', 'hover:bg-gray-100');
            ordersTab.classList.remove('bg-green-600', 'text-white');
            ordersTab.classList.add('text-gray-600', 'hover:bg-gray-100');

            // Show/hide sections
            cartSection.classList.remove('hidden');
            ordersSection.classList.add('hidden');

            // Show bottom navbar for cart
            if (cartBottomNavbar) {
                cartBottomNavbar.classList.remove('hidden');
            }
        });

        ordersTab.addEventListener('click', function() {
            // Update tab appearance
            ordersTab.classList.add('bg-green-600', 'text-white');
            ordersTab.classList.remove('text-gray-600', 'hover:bg-gray-100');
            cartTab.classList.remove('bg-green-600', 'text-white');
            cartTab.classList.add('text-gray-600', 'hover:bg-gray-100');

            // Show/hide sections
            ordersSection.classList.remove('hidden');
            cartSection.classList.add('hidden');

            // Hide bottom navbar for orders
            if (cartBottomNavbar) {
                cartBottomNavbar.classList.add('hidden');
            }
        });

        // Smooth scroll behavior for navbar
        let lastScrollTop = 0;
        let isScrolling = false;

        window.addEventListener('scroll', function() {
            if (!cartBottomNavbar) return;

            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (!isScrolling) {
                window.requestAnimationFrame(function() {
                    if (scrollTop > lastScrollTop && scrollTop > 100) {
                        // Scrolling down - hide navbar
                        cartBottomNavbar.style.transform = 'translateY(100%)';
                    } else {
                        // Scrolling up - show navbar
                        cartBottomNavbar.style.transform = 'translateY(0)';
                    }
                    lastScrollTop = scrollTop;
                    isScrolling = false;
                });
                isScrolling = true;
            }
        });

        // Add transition for smooth animation
        if (cartBottomNavbar) {
            cartBottomNavbar.style.transition = 'transform 0.3s ease-in-out';
        }
    });

    // Cart functionality
    async function updateCartQuantity(cartId, newQuantity) {
        if (newQuantity <= 0) {
            removeFromCart(cartId);
            return;
        }

        try {
            const formData = new FormData();
            formData.append('cart_id', cartId);
            formData.append('action', 'update');
            formData.append('quantity', newQuantity);

            const response = await fetch('api/update_cart.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            });

            const data = await response.json();

            if (data.success) {
                showToast('Keranjang berhasil diupdate!', 'success');
                setTimeout(() => {
                    location.reload(); // Refresh to show updated cart
                }, 1000);
            } else {
                showToast('Error: ' + data.message, 'error');
            }

        } catch (error) {
            console.error('Update cart error:', error);
            alert('Terjadi kesalahan saat mengupdate keranjang.');
        }
    }

    async function removeFromCart(cartId) {
        if (!confirm('Apakah Anda yakin ingin menghapus item ini dari keranjang?')) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('cart_id', cartId);
            formData.append('action', 'remove');

            const response = await fetch('api/update_cart.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            });

            const data = await response.json();

            if (data.success) {
                showToast('Item berhasil dihapus dari keranjang!', 'success');
                setTimeout(() => {
                    location.reload(); // Refresh to show updated cart
                }, 1000);
            } else {
                showToast('Error: ' + data.message, 'error');
            }

        } catch (error) {
            console.error('Remove from cart error:', error);
            alert('Terjadi kesalahan saat menghapus item.');
        }
    }

    async function clearCart() {
        if (!confirm('Apakah Anda yakin ingin mengosongkan seluruh keranjang?')) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('action', 'clear');

            const response = await fetch('api/update_cart.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            });

            const data = await response.json();

            if (data.success) {
                showToast('Keranjang berhasil dikosongkan!', 'success');
                setTimeout(() => {
                    location.reload(); // Refresh to show empty cart
                }, 1000);
            } else {
                showToast('Error: ' + data.message, 'error');
            }

        } catch (error) {
            console.error('Clear cart error:', error);
            alert('Terjadi kesalahan saat mengosongkan keranjang.');
        }
    }

    function proceedToCheckout() {
        // Check if any items are selected
        if (selectedItems.size === 0) {
            showToast('Pilih minimal satu item untuk checkout!', 'error');
            return;
        }

        // Show loading state
        const checkoutBtns = document.querySelectorAll('button[onclick="proceedToCheckout()"]');
        checkoutBtns.forEach(btn => {
            const originalContent = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Memproses...';
            btn.disabled = true;

            // Restore after redirect (in case of error)
            setTimeout(() => {
                btn.innerHTML = originalContent;
                btn.disabled = false;
            }, 3000);
        });

        // Add smooth transition
        document.body.style.opacity = '0.8';

        // Create URL with selected cart items
        const selectedItemsArray = Array.from(selectedItems);
        const checkoutUrl = 'cekout.php?cart_items=' + encodeURIComponent(selectedItemsArray.join(','));

        // Redirect to checkout page with selected items
        setTimeout(() => {
            window.location.href = checkoutUrl;
        }, 500);
    }

    // Show toast notification
    function showToast(message, type = 'info') {
        // Remove existing toast
        const existingToast = document.getElementById('toast');
        if (existingToast) {
            existingToast.remove();
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.id = 'toast';
        toast.className = `fixed top-20 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white text-sm font-medium transform transition-all duration-300 translate-x-full max-w-sm`;

        // Set color based on type
        if (type === 'success') {
            toast.className += ' bg-green-500';
        } else if (type === 'error') {
            toast.className += ' bg-red-500';
        } else {
            toast.className += ' bg-blue-500';
        }

        toast.textContent = message;
        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);

        // Auto hide after 3 seconds
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, 3000);
    }

    // Initialize selection - all items selected by default
    function initializeSelection() {
        const checkboxes = document.querySelectorAll('.cart-checkbox');
        selectedItems.clear();
        selectedTotal = 0;

        checkboxes.forEach(checkbox => {
            const cartId = checkbox.dataset.cartId;
            const price = parseFloat(checkbox.dataset.price);

            if (checkbox.checked) {
                selectedItems.add(cartId);
                selectedTotal += price;
            }
        });

        updateSelectionDisplay();
    }

    // Toggle select all
    function toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const checkboxes = document.querySelectorAll('.cart-checkbox');
        const isChecked = selectAllCheckbox.checked;

        selectedItems.clear();
        selectedTotal = 0;

        checkboxes.forEach(checkbox => {
            checkbox.checked = isChecked;

            if (isChecked) {
                const cartId = checkbox.dataset.cartId;
                const price = parseFloat(checkbox.dataset.price);
                selectedItems.add(cartId);
                selectedTotal += price;
            }
        });

        updateSelectionDisplay();
    }

    // Update selected items when individual checkbox changes
    function updateSelectedItems() {
        const checkboxes = document.querySelectorAll('.cart-checkbox');
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');

        selectedItems.clear();
        selectedTotal = 0;
        let checkedCount = 0;

        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const cartId = checkbox.dataset.cartId;
                const price = parseFloat(checkbox.dataset.price);
                selectedItems.add(cartId);
                selectedTotal += price;
                checkedCount++;
            }
        });

        // Update select all checkbox state
        if (checkedCount === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedCount === checkboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }

        updateSelectionDisplay();
    }

    // Update display elements
    function updateSelectionDisplay() {
        const selectedCount = selectedItems.size;
        const formattedTotal = 'Rp ' + selectedTotal.toLocaleString('id-ID');

        // Update header display
        const selectedCountElement = document.getElementById('selectedCount');
        const selectedTotalElement = document.getElementById('selectedTotal');

        if (selectedCountElement) {
            selectedCountElement.textContent = selectedCount;
        }
        if (selectedTotalElement) {
            selectedTotalElement.textContent = formattedTotal;
        }

        // Update bottom navbar display
        const bottomSelectedCount = document.getElementById('bottomSelectedCount');
        const bottomSelectedTotal = document.getElementById('bottomSelectedTotal');
        const bottomCheckoutCount = document.getElementById('bottomCheckoutCount');

        if (bottomSelectedCount) {
            bottomSelectedCount.textContent = selectedCount;
        }
        if (bottomSelectedTotal) {
            bottomSelectedTotal.textContent = formattedTotal;
        }
        if (bottomCheckoutCount) {
            bottomCheckoutCount.textContent = selectedCount;
        }

        // Enable/disable checkout button
        const checkoutBtns = document.querySelectorAll('button[onclick="proceedToCheckout()"]');
        checkoutBtns.forEach(btn => {
            if (selectedCount === 0) {
                btn.disabled = true;
                btn.classList.add('opacity-50', 'cursor-not-allowed');
                btn.classList.remove('hover:from-green-700', 'hover:to-green-800');
            } else {
                btn.disabled = false;
                btn.classList.remove('opacity-50', 'cursor-not-allowed');
                btn.classList.add('hover:from-green-700', 'hover:to-green-800');
            }
        });
    }
</script>
    </div>
</body>
</html>