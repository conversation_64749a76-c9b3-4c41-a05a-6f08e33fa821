/**
 * Admin Notifications Auto-Hide System
 * Automatically hides success/error notifications after a specified time
 */

// Auto-hide notification function
function hideNotification() {
    const notification = document.getElementById('notification');
    if (notification) {
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            notification.style.display = 'none';
        }, 500);
    }
}

// Initialize auto-hide functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide notification after 4 seconds
    const notification = document.getElementById('notification');
    if (notification) {
        setTimeout(() => {
            hideNotification();
        }, 4000); // Hide after 4 seconds
    }
});

// Optional: Add click-to-dismiss functionality for any notification
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('notification-dismiss')) {
        hideNotification();
    }
});
