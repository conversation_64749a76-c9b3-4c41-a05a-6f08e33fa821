<?php
require_once '../db_config.php';

header('Content-Type: application/json');

$response = [
    'success' => false,
    'notifications' => [],
    'count' => 0,
    'message' => ''
];

// Assuming user ID is passed as a GET parameter or from a session
// For now, let's assume it's passed as a GET parameter for testing/simplicity
// In a real application, you would get this from a secure session variable.
$userId = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

// TODO: Implement actual session-based user ID retrieval here
// For example:
// session_start();
// $userId = $_SESSION['user_id'] ?? 0;

if ($userId === 0) {
    $response['message'] = 'User ID is missing.';
    echo json_encode($response);
    exit();
}

try {
    $stmt = $conn->prepare("
        SELECT id, product_name_at_purchase, created_at, total_amount
        FROM orders
        WHERE pembeli_id = :user_id
        AND status = 'confirmed'
        AND user_notified = 0
    ");
    $stmt->execute(['user_id' => $userId]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $response['success'] = true;
    $response['notifications'] = $notifications;
    $response['count'] = count($notifications);

} catch (PDOException $e) {
    $response['message'] = 'Database Error: ' . $e->getMessage();
    error_log("Database Error in get_user_notifications.php: " . $e->getMessage());
}

echo json_encode($response);
?> 