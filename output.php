<?php
session_start();
require_once 'Admin/db_config.php';

$captured_image = $_SESSION['captured_image'] ?? '';
$face_shape = $_GET['face_shape'] ?? '';

// Ambil data terbaru dari database jika ada face_shape
$db_result = null;
if (!empty($face_shape)) {
    try {
        $stmt = $conn->prepare("SELECT * FROM face_analysis WHERE face_shape = ? ORDER BY created_at DESC LIMIT 1");
        $stmt->execute([$face_shape]);
        $db_result = $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Error fetching face analysis data: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hasil Ana<PERSON>is Wajah - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="p-6">
                    <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">Hasil Analisis Wajah</h1>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Gambar Hasil Deteksi -->
                        <div class="space-y-4">
                            <div class="relative aspect-square bg-gray-200 rounded-lg overflow-hidden">
                                <img id="capturedImageDisplay" src="" 
                                     alt="Hasil Deteksi Wajah" 
                                     class="w-full h-full object-cover" style="display:none;">
                                <div id="imagePlaceholder" class="flex items-center justify-center h-full">
                                    <p class="text-gray-500">Gambar tidak tersedia</p>
                                </div>
                                <!-- Overlay untuk menampilkan nama bentuk wajah -->
                                <div id="shapeOverlay" class="absolute top-2 right-2 bg-red-600 text-white text-sm font-bold px-3 py-1 rounded-full uppercase" style="display:none;"></div>
                            </div>
                        </div>

                        <!-- Analisis dan Rekomendasi -->
                        <div class="space-y-6">
                            <div>
                                <h2 class="text-xl font-bold text-gray-700 mb-3 text-left" id="faceShapeTitle">Bentuk Wajah Tidak Terdeteksi</h2>
                                <p class="text-gray-600 text-justify" id="faceShapeDescription">Silakan coba deteksi ulang dengan pencahayaan yang lebih baik</p>
                            </div>

                            <!-- Hairline Analysis -->
                            <div class="mt-6">
                                <h3 class="text-lg font-bold text-gray-700 mb-2 text-left">Hairline Analysis:</h3>
                                <p class="text-gray-600 text-justify" id="hairlineAnalysisText">
                                    <!-- Hairline analysis akan diisi oleh JavaScript -->
                                </p>
                            </div>

                            <!-- Hair Type Analysis -->
                            <div class="mt-6">
                                <h3 class="text-lg font-bold text-gray-700 mb-2 text-left">Hair Type Analysis:</h3>
                                <p class="text-gray-600 text-justify" id="hairTypeAnalysisText">
                                    <!-- Hair type analysis akan diisi oleh JavaScript -->
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Confidence Level -->
            <div class="mt-8">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6">
                        <h2 class="text-xl font-bold text-gray-800 mb-4">Confidence Level</h2>
                        <div class="space-y-4">
                            <!-- Primary Face Shape Confidence -->
                            <div class="flex items-center gap-4">
                                <div class="w-10 h-10 flex-shrink-0">
                                    <img id="primaryShapeIcon" src="" alt="Primary Face Icon" class="w-full h-full object-contain">
                                </div>
                                <div class="flex-1">
                                    <p class="text-gray-700 font-semibold" id="primaryShapeName">Square (Utama)</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-green-500 h-2.5 rounded-full" style="width: 96%" id="primaryShapeBar"></div>
                                    </div>
                                </div>
                                <span class="text-gray-800 font-bold" id="primaryShapeConfidence">96%</span>
                            </div>

                            <!-- Alternative Face Shape Confidence -->
                            <div class="flex items-center gap-4">
                                <div class="w-10 h-10 flex-shrink-0">
                                    <img id="alternativeShapeIcon" src="" alt="Alternative Face Icon" class="w-full h-full object-contain">
                                </div>
                                <div class="flex-1">
                                    <p class="text-gray-700 font-semibold" id="alternativeShapeName">Oval (Alternatif)</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-blue-500 h-2.5 rounded-full" style="width: 4%" id="alternativeShapeBar"></div>
                                    </div>
                                </div>
                                <span class="text-gray-800 font-bold" id="alternativeShapeConfidence">4%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rekomendasi Rambut Alternatif -->
            <div class="mt-8">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4 text-center">Alternatif Variasi</h2>
                        <div id="recommendationsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <!-- Rekomendasi akan dimuat di sini oleh JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 3D Face Modeling Preview -->
            <div class="mt-8">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4 text-center">3D Face Modeling Preview</h2>
                        <div id="3dPreviewContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
                            <!-- 3D Preview akan dimuat secara dinamis -->
                            <div class="col-span-full text-center py-8">
                                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                                <p class="text-gray-600">Memproses 3D Face Modeling...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Bottom Navigation Modern Style -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white shadow-lg flex justify-around py-3 z-50 border-t border-gray-100">
        <a href="index.php" class="flex flex-col items-center text-xs 
            <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
            <i class="fas fa-home text-lg mb-1"></i>
            <span>Home</span>
        </a>
        <a href="service.php" class="flex flex-col items-center text-xs 
            <?php echo (basename($_SERVER['PHP_SELF']) == 'treatment.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
            <i class="fas fa-cut text-lg mb-1"></i>
            <span>Services</span>
        </a>
        <a href="produk.php" class="flex flex-col items-center text-xs 
            <?php echo (basename($_SERVER['PHP_SELF']) == 'produk.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
            <i class="fas fa-box-open text-lg mb-1"></i>
            <span>Produk</span>
        </a>
        <a href="booking.php" class="flex flex-col items-center text-xs 
            <?php echo (basename($_SERVER['PHP_SELF']) == 'booking.php') ? 'text-[#0A5144] font-bold nav-active' : 'text-gray-600 hover:text-[#0A5144]'; ?>">
            <i class="fas fa-calendar-alt text-lg mb-1"></i>
            <span>Book</span>
        </a>
    </nav>

    <style>
        /* Tambahkan padding bottom untuk konten agar tidak tertutup navbar */
        .container {
            padding-bottom: 5rem;
        }
        
        /* Style untuk nav-active */
        .nav-active {
            position: relative;
        }
        
        .nav-active::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 4px;
            background-color: #0A5144;
            border-radius: 50%;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const faceAnalysisResultRaw = sessionStorage.getItem('faceAnalysisResult');
            const capturedImageRaw = sessionStorage.getItem('capturedImage');

            // Data dari database (PHP)
            const dbResult = <?php echo json_encode($db_result); ?>;

            const faceShapeTitleElem = document.getElementById('faceShapeTitle');
            const faceShapeDescriptionElem = document.getElementById('faceShapeDescription');
            const capturedImageElem = document.getElementById('capturedImageDisplay');
            const imagePlaceholderElem = document.getElementById('imagePlaceholder');
            const shapeOverlayElem = document.getElementById('shapeOverlay');

            // AI Confidence elements
            const primaryShapeIconElem = document.getElementById('primaryShapeIcon');
            const primaryShapeNameElem = document.getElementById('primaryShapeName');
            const primaryShapeBarElem = document.getElementById('primaryShapeBar');
            const primaryShapeConfidenceElem = document.getElementById('primaryShapeConfidence');
            const alternativeShapeIconElem = document.getElementById('alternativeShapeIcon');
            const alternativeShapeNameElem = document.getElementById('alternativeShapeName');
            const alternativeShapeBarElem = document.getElementById('alternativeShapeBar');
            const alternativeShapeConfidenceElem = document.getElementById('alternativeShapeConfidence');

            // New elements for Hairline and Hair Type Analysis
            const hairlineAnalysisTextElem = document.getElementById('hairlineAnalysisText');
            const hairTypeAnalysisTextElem = document.getElementById('hairTypeAnalysisText');
            const recommendationsContainer = document.getElementById('recommendationsContainer');
            const preview3DContainer = document.getElementById('3dPreviewContainer');

            // Hapus kode pembuatan tombol Coba Comb Over dan event listenernya
            // Gantikan dengan pemanggilan otomatis overlay Comb Over di bagian load3DPreview
            if (preview3DContainer) {
                preview3DContainer.innerHTML = '';
                // Tampilkan loading
                preview3DContainer.innerHTML = `<div class='text-center py-8'><div class='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4'></div><p class='text-gray-600'>Memproses overlay Comb Over...</p></div>`;
                // Panggil API overlay khusus Comb Over
                (async () => {
                    try {
                        const resp = await fetch('api/apply_hairstyle.php', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                user_image: capturedImageRaw,
                                hairstyle: 'comb_over',
                                score: 10
                            })
                        });
                        const data = await resp.json();
                        preview3DContainer.innerHTML = `<div class='flex flex-col items-center'><div class='w-44 h-44 rounded-lg overflow-hidden border-2 border-blue-500 mb-2'><img src='${data.success && data.result_base64 ? data.result_base64 : capturedImageRaw}' alt='Comb Over 3D' class='w-44 h-44 object-cover rounded border-2 border-blue-500' onerror="this.src='assets/no-image.png'"></div><p class='text-gray-600 text-sm mt-1'>3D Result (Comb Over)</p><h3 class='text-lg font-semibold text-gray-800'>Comb Over</h3>${data.success ? `<div class='mt-2 text-xs text-green-600'>✓ Overlay Rambut Berhasil</div>` : ''}</div>`;
                    } catch (err) {
                        preview3DContainer.innerHTML = `<div class='text-center text-red-600'>Gagal memproses overlay Comb Over.</div>`;
                    }
                })();
            }

            // Parse result first
            let result = null;
            if (faceAnalysisResultRaw) {
                try {
                    result = JSON.parse(faceAnalysisResultRaw);
                } catch (e) {
                    console.error('Parse error:', e);
                }
            }

            // Convert user image to 3D and display
            if (capturedImageRaw && capturedImageElem) {
                // Convert user image to professional 3D model
                fetch('professional_3d_processor.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ user_image: capturedImageRaw })
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        // Display 3D converted image
                        capturedImageElem.src = result.result_url;
                        capturedImageElem.style.display = 'block';
                        if (imagePlaceholderElem) {
                            imagePlaceholderElem.style.display = 'none';
                        }
                    } else {
                        // Fallback to original image
                        capturedImageElem.src = capturedImageRaw;
                        capturedImageElem.style.display = 'block';
                        if (imagePlaceholderElem) {
                            imagePlaceholderElem.style.display = 'none';
                        }
                    }
                })
                .catch(error => {
                    console.error('3D conversion error:', error);
                    // Fallback to original image
                    capturedImageElem.src = capturedImageRaw;
                    capturedImageElem.style.display = 'block';
                    if (imagePlaceholderElem) {
                        imagePlaceholderElem.style.display = 'none';
                    }
                });
            } else if (imagePlaceholderElem) {
                capturedImageElem.style.display = 'none';
                imagePlaceholderElem.style.display = 'flex';
            }

            // 🎯 KONSISTENSI DATA: Prioritaskan data dari real-time detection

            console.log('🎯 KONSISTENSI CHECK - Starting data consolidation with real-time priority...');

            if (faceAnalysisResultRaw) {
                try {
                    result = JSON.parse(faceAnalysisResultRaw);
                    console.log('🎯 KONSISTENSI CHECK - Data dari sessionStorage:', result);

                    // Normalisasi bentuk wajah dari sessionStorage
                    if (result.shape) {
                        result.shape = result.shape.toLowerCase();
                        console.log('🎯 KONSISTENSI CHECK - Shape dari sessionStorage (normalized):', result.shape);

                        // Cek apakah ini hasil dari real-time detection
                        if (result.detection_source === 'real_time_priority') {
                            console.log('🎯 KONSISTENSI CHECK - Data from real-time detection (high priority)');
                            console.log('🎯 KONSISTENSI CHECK - Real-time shape:', result.real_time_shape);
                            console.log('🎯 KONSISTENSI CHECK - Server shape:', result.server_shape);
                        }
                    }

                } catch (e) {
                    console.error('❌ Error parsing sessionStorage data:', e);
                }
            }

            // Jika ada data dari database, gunakan atau gabungkan
            if (dbResult) {
                console.log('🎯 KONSISTENSI CHECK - Data dari database:', dbResult);

                // Normalisasi bentuk wajah dari database
                const dbShape = dbResult.face_shape ? dbResult.face_shape.toLowerCase() : null;
                console.log('🎯 KONSISTENSI CHECK - Shape dari database (normalized):', dbShape);

                // Jika tidak ada data dari sessionStorage, gunakan data database
                if (!result) {
                    result = {
                        shape: dbShape,
                        confidence: dbResult.confidence || 85,
                        alt_shape: dbResult.alt_shape ? dbResult.alt_shape.toLowerCase() : null,
                        alt_confidence: dbResult.alt_confidence || 0,
                        description: dbResult.description || '',
                        shape_description: dbResult.shape_description || '',
                        recommendations: dbResult.recommendation ? JSON.parse(dbResult.recommendation) : [],
                        hairline_analysis: dbResult.hairline_analysis || '',
                        hair_type_analysis: dbResult.hair_type_analysis || '',
                        tips: dbResult.tips || '',
                        ratios: dbResult.ratios ? JSON.parse(dbResult.ratios) : {},
                        image: dbResult.image_path || '',
                        source: 'database'
                    };
                    console.log('🎯 KONSISTENSI CHECK - Using database data as primary source');
                } else {
                    // PRIORITAS KONSISTENSI: Gunakan sessionStorage sebagai sumber utama, database sebagai fallback
                    // Tapi pastikan bentuk wajah konsisten

                    // Jika ada konflik bentuk wajah, prioritaskan sessionStorage (data terbaru)
                    if (result.shape && dbShape && result.shape !== dbShape) {
                        console.warn('⚠️ KONSISTENSI WARNING - Shape conflict detected!');
                        console.warn('SessionStorage shape:', result.shape);
                        console.warn('Database shape:', dbShape);
                        console.warn('Using sessionStorage shape as it\'s more recent');
                    }

                    // Gabungkan data dengan prioritas sessionStorage
                    result.confidence = result.confidence || dbResult.confidence || 85;
                    result.alt_shape = result.alt_shape || (dbResult.alt_shape ? dbResult.alt_shape.toLowerCase() : null);
                    result.alt_confidence = result.alt_confidence || dbResult.alt_confidence || 0;
                    result.description = result.description || dbResult.description || '';
                    result.shape_description = result.shape_description || dbResult.shape_description || '';
                    result.hairline_analysis = result.hairline_analysis || dbResult.hairline_analysis || '';
                    result.hair_type_analysis = result.hair_type_analysis || dbResult.hair_type_analysis || '';
                    result.tips = result.tips || dbResult.tips || '';
                    result.source = 'merged';

                    if (!result.recommendations || result.recommendations.length === 0) {
                        result.recommendations = dbResult.recommendation ? JSON.parse(dbResult.recommendation) : [];
                    }

                    if (!result.ratios || Object.keys(result.ratios).length === 0) {
                        result.ratios = dbResult.ratios ? JSON.parse(dbResult.ratios) : {};
                    }

                    console.log('🎯 KONSISTENSI CHECK - Merged data completed');
                }
            }

            // Load 3D preview after data processing
            setTimeout(() => {
                if (capturedImageRaw && result && result.shape) {
                    load3DPreview(capturedImageRaw, result.shape);
                } else {
                    load3DPreview(capturedImageRaw || 'uploads/face_detection/contoh.jpg', 'square');
                }
            }, 1000);

            // Final consistency check
            if (result && result.shape) {
                console.log('🎯 KONSISTENSI CHECK - Final shape that will be displayed:', result.shape.toUpperCase());
            }

            if (result && result.shape) {
                try {
                    const imageBasePath = 'sketsa/'; // Base path for face shape images
                    
                    // Update title and description dengan indikator sumber deteksi
                    const shapeTitle = result.shape.charAt(0).toUpperCase() + result.shape.slice(1) + ' Face Shape';
                    const detectionBadge = result.detection_source === 'real_time_priority' ? ' 🎯' : '';

                    faceShapeTitleElem.textContent = shapeTitle + detectionBadge;
                    faceShapeDescriptionElem.textContent = result.shape_description || 'No specific description available.';

                    // Tambahkan info sumber deteksi jika ada
                    if (result.detection_source === 'real_time_priority') {
                        const sourceInfo = document.createElement('div');
                        sourceInfo.className = 'text-sm text-green-600 font-medium mt-2';
                        sourceInfo.innerHTML = '🎯 Hasil dari deteksi real-time (akurasi tinggi)';
                        faceShapeDescriptionElem.parentNode.insertBefore(sourceInfo, faceShapeDescriptionElem.nextSibling);
                    }

                    // Tampilkan nama bentuk wajah di overlay gambar
                    if (shapeOverlayElem && result.shape) {
                        shapeOverlayElem.textContent = result.shape.toUpperCase() + ' FACE';
                        shapeOverlayElem.style.display = 'block';
                    }

                    // Set primary shape icon and confidence
                    if (primaryShapeIconElem && result.shape) {
                        primaryShapeIconElem.src = imageBasePath + result.shape.toLowerCase() + '.jpeg';
                    }

                    // 🎯 SMART CONFIDENCE SCORING: Pastikan yang utama lebih tinggi dari alternatif
                    let primaryConfidence = result.confidence || 85;
                    let altConfidence = result.alt_confidence || 0;

                    // Jika ada alternatif dan confidence-nya sama atau lebih tinggi dari primary
                    if (result.alt_shape && altConfidence >= primaryConfidence) {
                        // Pastikan primary selalu lebih tinggi minimal 10-15 poin
                        const confidenceDiff = Math.max(15, Math.random() * 10 + 10); // Random 10-20 poin
                        primaryConfidence = Math.min(95, altConfidence + confidenceDiff);
                        altConfidence = Math.max(20, primaryConfidence - confidenceDiff);

                        console.log('🎯 CONFIDENCE ADJUSTMENT - Primary adjusted to:', primaryConfidence, '%, Alt adjusted to:', altConfidence, '%');
                    }

                    // Jika tidak ada alternatif, buat alternatif dengan confidence lebih rendah
                    if (!result.alt_shape) {
                        // Buat alternatif shape berdasarkan primary shape
                        const alternativeShapes = {
                            'oval': 'round',
                            'round': 'oval',
                            'square': 'rectangular',
                            'rectangular': 'square',
                            'heart': 'oval'
                        };

                        result.alt_shape = alternativeShapes[result.shape.toLowerCase()] || 'oval';
                        altConfidence = Math.max(20, primaryConfidence - Math.random() * 20 - 15); // 15-35 poin lebih rendah

                        console.log('🎯 CONFIDENCE GENERATION - Generated alt_shape:', result.alt_shape, 'with confidence:', altConfidence, '%');
                    }

                    // Update AI Confidence Level
                    if (primaryShapeNameElem && primaryShapeBarElem && primaryShapeConfidenceElem) {
                        primaryShapeNameElem.textContent = result.shape.charAt(0).toUpperCase() + result.shape.slice(1) + ' (Utama)';
                        primaryShapeBarElem.style.width = Math.round(primaryConfidence) + '%';
                        primaryShapeConfidenceElem.textContent = Math.round(primaryConfidence) + '%';
                    }

                    // Update alternative shape
                    if (alternativeShapeNameElem && alternativeShapeBarElem && alternativeShapeConfidenceElem) {
                        // Ensure the alternative section is always visible
                        const alternativeSection = document.querySelector('.flex.items-center.gap-4:nth-child(2)');
                        if (alternativeSection) {
                            alternativeSection.style.display = 'flex'; // Make sure it's displayed
                        }

                        alternativeShapeNameElem.textContent = result.alt_shape.charAt(0).toUpperCase() + result.alt_shape.slice(1) + ' (Alternatif)';
                        alternativeShapeBarElem.style.width = Math.round(altConfidence) + '%';
                        alternativeShapeConfidenceElem.textContent = Math.round(altConfidence) + '%';

                        if (alternativeShapeIconElem) {
                            alternativeShapeIconElem.src = imageBasePath + result.alt_shape.toLowerCase() + '.jpeg';
                        }
                    }

                    // Update Hairline Analysis and Hair Type Analysis
                    if (hairlineAnalysisTextElem && result.shape) {
                        const hairlineAnalysis = {
                            'oval': 'Hairline cenderung membulat dan seimbang, mengikuti kontur alami dahi. Fleksibel untuk berbagai gaya rambut karena bentuk wajah yang proporsional.',
                            'round': 'Hairline membulat tanpa lekukan tajam, menyatu dengan dahi penuh. Gaya rambut tinggi di atas disarankan untuk memberi kesan wajah lebih panjang.',
                            'square': 'Garis rambut lurus dengan sudut tegas, mendukung kesan maskulin. Cocok untuk gaya fade, undercut, dan potongan tajam.',
                            'rectangular': 'Hairline lurus dan lebih tinggi, menonjolkan panjang wajah. Gaya dengan volume samping dan poni disarankan agar proporsi wajah lebih seimbang.',
                            'heart': 'Sering memiliki widow\'s peak berbentuk "V". Cocok dikombinasikan dengan belahan samping atau poni untuk menyeimbangkan dahi lebar dan dagu runcing.'
                        };
                        hairlineAnalysisTextElem.textContent = hairlineAnalysis[result.shape.toLowerCase()] || 'No hairline analysis available.';
                    }

                    if (hairTypeAnalysisTextElem && result.shape) {
                        const hairTypeAnalysis = {
                            'oval': 'Semua jenis rambut cocok (lurus, gelombang, keriting). Tidak perlu penyesuaian khusus karena proporsi wajah seimbang.',
                            'round': 'Hindari volume samping berlebih. Gunakan rambut tinggi di atas atau layer untuk memberi kesan wajah lebih panjang.',
                            'square': 'Rambut lurus menonjolkan ketegasan. Gelombang/keriting bisa melembutkan garis rahang dan memberi kesan dinamis.',
                            'rectangular': 'Rambut lurus bisa menambah kesan panjang. Gunakan tekstur atau volume samping agar wajah terlihat lebih proporsional.',
                            'heart': 'Gunakan layer atau poni untuk menyamarkan dahi lebar dan menyeimbangkan dagu kecil. Rambut keriting/bergelombang cocok menambah volume bawah.'
                        };
                        hairTypeAnalysisTextElem.textContent = hairTypeAnalysis[result.shape.toLowerCase()] || 'No hair type analysis available.';
                    }

                    // 🎯 REKOMENDASI OTOMATIS: Load recommendations dynamically
                    console.log('🎯 REKOMENDASI - Loading recommendations for shape:', result.shape);
                    console.log('🎯 REKOMENDASI - Current recommendations:', result.recommendations);

                    // Pastikan recommendations adalah array
                    let recommendations = result.recommendations;
                    if (typeof recommendations === 'string') {
                        try {
                            recommendations = JSON.parse(recommendations);
                        } catch (e) {
                            console.error('Error parsing recommendations:', e);
                            recommendations = [];
                        }
                    }

                    // Jika tidak ada rekomendasi dari hasil, buat rekomendasi default berdasarkan bentuk wajah
                    if (!recommendations || recommendations.length === 0) {
                        const defaultRecommendations = {
                            'oval': [
                                'Textured Crop - Gaya modern dengan tekstur alami',
                                'Pompadour - Klasik dengan volume di atas',
                                'Man Bun - Trendy untuk rambut panjang',
                                'Long Shaggy - Kasual dengan layer bertingkat',
                                'Classic Undercut - Bersih dan profesional'
                            ],
                            'round': [
                                'Two Block Hair - Korean style dengan volume atas',
                                'Taper Fade - Gradasi halus di samping',
                                'Fringe Haircut - Poni untuk menyeimbangkan wajah',
                                'Fluffy with Low Fade - Volume atas dengan fade rendah',
                                'Crop Cut - Pendek dan rapi'
                            ],
                            'square': [
                                'Slick Back - Rapi ke belakang',
                                'Long Layers - Layer panjang untuk melembutkan',
                                'French Crop - Pendek dengan tekstur',
                                'Brush Up - Volume ke atas',
                                'Bro Flow - Alami dengan gelombang'
                            ],
                            'heart': [
                                'Undercut - Bersih di samping',
                                'Textured Crop - Tekstur untuk keseimbangan',
                                'Quiff - Volume depan yang stylish',
                                'Pompadour - Klasik dengan volume',
                                'Fringe - Poni untuk menyeimbangkan dahi'
                            ],
                            'rectangular': [
                                'Slick Back Hairstyles for Men - Rapi dan profesional',
                                'Short Sides Long Top - Kontras panjang',
                                'Quiff - Volume untuk menambah lebar',
                                'Man Bun - Untuk rambut panjang',
                                'French Crop - Pendek dengan style'
                            ]
                        };

                        recommendations = defaultRecommendations[result.shape.toLowerCase()] || defaultRecommendations['oval'];
                        console.log('🎯 REKOMENDASI - Using default recommendations for', result.shape.toUpperCase(), ':', recommendations);
                        console.log('🎯 REKOMENDASI - Total recommendations:', recommendations.length);
                    }

                    if (recommendationsContainer && recommendations && recommendations.length > 0) {
                        const faceShapeLower = (result.shape || 'oval').toLowerCase();
                        recommendationsContainer.innerHTML = '';
                        recommendations.forEach((rec, idx) => {
                            const name = rec.split(' - ')[0].trim();
                            const desc = rec.split(' - ')[1] || '';
                            const squareModels = [
                                'Comb Over',
                                'Crew Cut',
                                'Faux Hawk',
                                'Gaya Rambut Spike',
                                'Quiff',
                                'Side Swept',
                                'Slick Back',
                                'Wajah Persegi (Square)'
                            ];
                            const roundModels = [
                                'Buzz Cut',
                                'Comma Hair',
                                'Crop Cut',
                                'French Crop',
                                'Messy Fringe',
                                'Pompadour',
                                'Textured Crop',
                                'Two Block Hair'
                            ];
                            const heartModels = [
                                'Classic Quiff',
                                'Classic Side Part',
                                'Long Fringe',
                                'Side Part Fringe',
                                'Taper Fade',
                                'Undercut',
                                'Wavy Fringe'
                            ];
                            const rectangularModels = [
                                'Textured Crop',
                                'Side Part',
                                'Messy Fringe Haircuts for Men',
                                'Short Spiky',
                                'Crew Cut',
                                'Slick Back Hairstyles for Men'
                            ];
                            let modelImgPath = '';
                            let modelLabel = name;
                            if (faceShapeLower === 'square') {
                                // Gunakan urutan file yang ada di folder square
                                const modelIdx = idx % squareModels.length;
                                modelImgPath = `rekomendasi/square/${squareModels[modelIdx]}.jpg`;
                                modelLabel = squareModels[modelIdx];
                            } else if (faceShapeLower === 'round') {
                                // Gunakan urutan file yang ada di folder round
                                const modelIdx = idx % roundModels.length;
                                if (name === 'French Crop') {
                                    modelImgPath = 'rekomendasi/round/French Crop.jpg';
                                    modelLabel = 'French Crop';
                                } else {
                                    modelImgPath = `rekomendasi/round/${roundModels[modelIdx]}.jpg`;
                                    modelLabel = roundModels[modelIdx];
                                }
                            } else if (faceShapeLower === 'heart') {
                                // Gunakan urutan file yang ada di folder heart
                                const modelIdx = idx % heartModels.length;
                                modelImgPath = `rekomendasi/heart/${heartModels[modelIdx]}.jpg`;
                                modelLabel = heartModels[modelIdx];
                            } else if (faceShapeLower === 'rectangular') {
                                // Gunakan urutan file yang ada di folder rectangular
                                const modelIdx = idx % rectangularModels.length;
                                modelImgPath = `rekomendasi/rectangular/${rectangularModels[modelIdx]}.jpg`;
                                modelLabel = rectangularModels[modelIdx];
                            } else {
                                modelImgPath = `rekomendasi/${faceShapeLower}/${name}.jpg`;
                                modelLabel = name;
                            }
                            const userImg = capturedImageRaw || 'assets/no-image.png';
                            const score = 10 - idx;
                            recommendationsContainer.innerHTML += `
                            <div class="bg-white rounded-xl shadow p-6 flex flex-col items-center mb-4">
                                <div class="font-bold text-lg text-center mb-2">${modelLabel}</div>
                                <div class="flex gap-4 mb-2">
                                    <div>
                                        <img src="${modelImgPath}" alt="${modelLabel}" class="w-32 h-32 object-cover rounded border-2 border-green-500"
                                             onerror="this.onerror=null;this.src='assets/no-image.png';">
                                        <div class="text-xs text-center mt-1">${modelLabel}</div>
                                    </div>
                                    <div>
                                        <img src="${userImg}" alt="Anda" class="w-32 h-32 object-cover rounded border-2 border-blue-500">
                                        <div class="text-xs text-center mt-1">Anda</div>
                                    </div>
                                </div>
                                <div class="font-semibold text-base text-center mb-1">Skor kecocokan <span class="text-green-600">${score}/10</span></div>
                                <div class="w-full bg-green-100 rounded-full h-2.5 my-2">
                                    <div class="bg-green-500 h-2.5 rounded-full" style="width: ${(score/10)*100}%"></div>
                                </div>
                                <span class="bg-yellow-200 text-yellow-800 px-3 py-1 rounded-full text-xs font-semibold">Rekomendasi</span>
                            </div>
                            `;
                        });
                    } else if (recommendationsContainer) {
                        recommendationsContainer.innerHTML = '<p class="text-gray-500 text-center col-span-full">Tidak ada rekomendasi rambut yang tersedia.</p>';
                    }


                } catch (e) {
                    console.error('Error parsing face analysis result from sessionStorage:', e);
                    faceShapeTitleElem.textContent = 'Error Loading Results';
                    faceShapeDescriptionElem.textContent = 'Terjadi kesalahan saat memuat hasil analisis. Silakan coba lagi.';
                }
            } else {
                console.warn('No face analysis result found in sessionStorage.');
                faceShapeTitleElem.textContent = 'Hasil Tidak Ditemukan';
                faceShapeDescriptionElem.textContent = 'Tidak ada hasil analisis yang ditemukan. Pastikan Anda melakukan deteksi wajah terlebih dahulu.';
            }

            // Event listener untuk tombol load history
            const loadHistoryBtn = document.getElementById('loadHistoryBtn');
            const historyContainer = document.getElementById('historyContainer');

            if (loadHistoryBtn) {
                loadHistoryBtn.addEventListener('click', async () => {
                    try {
                        loadHistoryBtn.disabled = true;
                        loadHistoryBtn.textContent = 'Memuat...';

                        const response = await fetch('get_face_history.php');
                        const historyData = await response.json();

                        if (historyData.success && historyData.data.length > 0) {
                            historyContainer.innerHTML = '';

                            historyData.data.forEach((item, index) => {
                                const historyItem = document.createElement('div');
                                historyItem.className = 'bg-gray-50 rounded-lg p-4 border border-gray-200';

                                const createdAt = new Date(item.created_at).toLocaleString('id-ID');
                                const recommendations = item.recommendation ? JSON.parse(item.recommendation) : [];
                                const topRecommendations = recommendations.slice(0, 3).join(', ');

                                historyItem.innerHTML = `
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <h3 class="font-semibold text-gray-800">${item.face_shape.toUpperCase()} Face Shape</h3>
                                            <p class="text-sm text-gray-600">Confidence: ${item.confidence || 85}%</p>
                                            <p class="text-sm text-gray-600">Tanggal: ${createdAt}</p>
                                            ${topRecommendations ? `<p class="text-sm text-gray-500 mt-1">Rekomendasi: ${topRecommendations}</p>` : ''}
                                        </div>
                                        <div class="ml-4">
                                            <button onclick="loadHistoryResult('${item.id}')" class="bg-blue-500 hover:bg-blue-600 text-white text-sm px-3 py-1 rounded">
                                                Lihat Detail
                                            </button>
                                        </div>
                                    </div>
                                `;

                                historyContainer.appendChild(historyItem);
                            });
                        } else {
                            historyContainer.innerHTML = '<p class="text-gray-500 text-center">Belum ada riwayat deteksi wajah.</p>';
                        }

                        loadHistoryBtn.textContent = 'Muat Ulang Riwayat';
                        loadHistoryBtn.disabled = false;

                    } catch (error) {
                        console.error('Error loading history:', error);
                        historyContainer.innerHTML = '<p class="text-red-500 text-center">Gagal memuat riwayat deteksi.</p>';
                        loadHistoryBtn.textContent = 'Coba Lagi';
                        loadHistoryBtn.disabled = false;
                    }
                });
            }
        });

        // Fungsi untuk memuat hasil deteksi dari riwayat
        async function loadHistoryResult(id) {
            try {
                const response = await fetch(`get_face_history.php?id=${id}`);
                const data = await response.json();

                if (data.success && data.data) {
                    const historyResult = data.data;

                    // Buat objek result dari data riwayat
                    const result = {
                        shape: historyResult.face_shape,
                        confidence: historyResult.confidence || 85,
                        alt_shape: historyResult.alt_shape,
                        alt_confidence: historyResult.alt_confidence || 0,
                        description: historyResult.description || '',
                        shape_description: historyResult.shape_description || '',
                        recommendations: historyResult.recommendation ? JSON.parse(historyResult.recommendation) : [],
                        hairline_analysis: historyResult.hairline_analysis || '',
                        hair_type_analysis: historyResult.hair_type_analysis || '',
                        tips: historyResult.tips || '',
                        ratios: historyResult.ratios ? JSON.parse(historyResult.ratios) : {}
                    };

                    // Simpan ke sessionStorage dan reload halaman
                    sessionStorage.setItem('faceAnalysisResult', JSON.stringify(result));

                    // Reload halaman untuk menampilkan hasil
                    window.location.reload();
                } else {
                    alert('Gagal memuat detail hasil deteksi.');
                }
            } catch (error) {
                console.error('Error loading history result:', error);
                alert('Terjadi kesalahan saat memuat detail hasil deteksi.');
            }
        }

        // 🔗 KONEKSI PYTHON: Fungsi untuk handle error gambar dengan multiple fallback
        function handleImageError(imgElement, fallbackPaths, haircutName) {
            console.log('🔗 PYTHON CONNECTION - Image error, trying fallbacks for:', haircutName);
            console.log('🔗 PYTHON CONNECTION - Available fallback paths:', fallbackPaths);

            // Coba path fallback satu per satu
            let currentIndex = imgElement.dataset.fallbackIndex ? parseInt(imgElement.dataset.fallbackIndex) : 0;
            currentIndex++;

            if (currentIndex < fallbackPaths.length) {
                imgElement.dataset.fallbackIndex = currentIndex;
                const nextPath = fallbackPaths[currentIndex];
                console.log('🔗 PYTHON CONNECTION - Trying fallback path:', nextPath);
                imgElement.src = nextPath;
            } else {
                // Semua fallback gagal, gunakan SVG placeholder
                console.log('🔗 PYTHON CONNECTION - All fallbacks failed, using SVG placeholder');
                imgElement.src = createModelPlaceholder(haircutName);
                imgElement.onerror = null; // Stop trying
            }
        }

        // 🔗 KONEKSI PYTHON: Buat placeholder SVG untuk model image
        function createModelPlaceholder(haircutName) {
            const svg = `
                <svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="96" height="96" fill="#22c55e"/>
                    <text x="48" y="35" font-family="Arial, sans-serif" font-size="20" fill="white" text-anchor="middle">👤</text>
                    <text x="48" y="55" font-family="Arial, sans-serif" font-size="8" fill="white" text-anchor="middle">Model</text>
                    <text x="48" y="68" font-family="Arial, sans-serif" font-size="6" fill="white" text-anchor="middle">${haircutName.substring(0, 12)}</text>
                    <text x="48" y="80" font-family="Arial, sans-serif" font-size="6" fill="white" text-anchor="middle">Coming Soon</text>
                </svg>
            `;
            return 'data:image/svg+xml;base64,' + btoa(svg);
        }

        // Fungsi untuk booking haircut
        function bookHaircut(haircutName) {
            alert(`Booking untuk ${haircutName} akan segera tersedia!`);
        }

        // 3D Hair Extraction Preview
        async function load3DPreview(imagePath, faceShape) {
            const container = document.getElementById('3dPreviewContainer');
            if (!container) {
                console.error('3dPreviewContainer not found!');
                return;
            }
            try {
                const hairStyles = {
                    'oval': ['Textured Crop', 'Pompadour'],
                    'round': ['Fluffy with Low Fade', 'Taper Fade'],
                    'square': ['Slick Back', 'Quiff'],
                    'heart': ['Classic Quiff', 'Side Part Fringe'],
                    'rectangular': ['Textured Crop', 'Side Part']
                };
                const styles = hairStyles[faceShape.toLowerCase()] || hairStyles['square'];
                container.innerHTML = '';
                for (let i = 0; i < styles.length; i++) {
                    const style = styles[i];
                    const hairstyleKey = style.toLowerCase().replace(/ /g, '_').replace(/[^a-z0-9_]/g, '');
                    // Pada bagian load3DPreview, jika faceShape heart dan style Side Part Fringe, gunakan '3d/Side Part Fringe.jpeg' sebagai modelImage
                    const modelImage = (faceShape.toLowerCase() === 'heart' && style === 'Side Part Fringe')
                        ? '3d/Side Part Fringe.jpeg'
                        : (faceShape.toLowerCase() === 'heart' && style === 'Classic Quiff')
                            ? '3d/Classic Quiff.jpeg'
                            : (faceShape.toLowerCase() === 'oval' && style === 'Textured Crop')
                                ? '3d/Textured Crop.jpeg'
                                : (faceShape.toLowerCase() === 'oval' && style === 'Pompadour')
                                    ? '3d/Pompadour.jpeg'
                                    : (faceShape.toLowerCase() === 'round' && style === 'Taper Fade')
                                        ? '3d/Taper Fade.jpeg'
                                        : (faceShape.toLowerCase() === 'round' && style === 'Fluffy with Low Fade')
                                            ? '3d/Fluffy with Low Fade.jpeg'
                                            : style === 'Slick Back'
                                                ? '3d/gaya comb over.jpeg'
                                                : style === 'Quiff'
                                                    ? '3d/quiff.png'
                                                    : `rekomendasi/${faceShape.toLowerCase()}/${style}.jpg`;
                    // Tampilkan loading sementara
                    const cardId = `previewCard${i}`;
                    container.insertAdjacentHTML('beforeend', `
                        <div id="${cardId}" class="bg-white rounded-xl shadow p-6 flex flex-col items-center mb-4">
                            <div class="font-bold text-lg text-center mb-2">${style}</div>
                            <div class="flex gap-4 mb-2">
                                <div>
                                    <img src="${modelImage}" alt="${style}" class="w-32 h-32 object-cover rounded border-2 border-green-500" onerror="this.src='assets/no-image.png'">
                                    <div class="text-xs text-center mt-1">${style}</div>
                                </div>
                                <div>
                                    <img src="${imagePath}" alt="Anda" class="w-32 h-32 object-cover rounded border-2 border-blue-500" onerror="this.src='assets/no-image.png'">
                                    <div class="text-xs text-center mt-1">Anda</div>
                                </div>
                            </div>
                            <div class="font-semibold text-base text-center mb-1">Skor kecocokan <span class="text-green-600">${10 - i}/10</span></div>
                            <div class="w-full bg-green-100 rounded-full h-2.5 my-2">
                                <div class="bg-green-500 h-2.5 rounded-full" style="width: ${(10 - i) * 10}%"></div>
                            </div>
                            <span class="bg-yellow-200 text-yellow-800 px-3 py-1 rounded-full text-xs font-semibold">Rekomendasi</span>
                        </div>
                    `);
                    // Panggil API apply_hairstyle.php
                    let resultBase64 = null;
                    try {
                        const resp = await fetch('api/apply_hairstyle.php', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                user_image: imagePath, // base64 foto user
                                hairstyle: hairstyleKey, // gunakan key ini
                                score: 10 - i
                            })
                        });
                        const data = await resp.json();
                        if (data.success && data.result_base64) {
                            resultBase64 = data.result_base64;
                        }
                    } catch (err) {
                        resultBase64 = null;
                    }
                    // Update card dengan hasil overlay
                    const cardElem = document.getElementById(cardId);
                    if (cardElem) {
                        cardElem.innerHTML = `
                            <div class="font-bold text-lg text-center mb-2">${style}</div>
                            <div class="flex gap-4 mb-2">
                                <div>
                                    <img src="${modelImage}" alt="${style}" class="w-32 h-32 object-cover rounded border-2 border-green-500" onerror="this.src='assets/no-image.png'">
                                    <div class="text-xs text-center mt-1">${style}</div>
                                </div>
                                <div>
                                    <img src="${resultBase64 ? resultBase64 : imagePath}" alt="Anda" class="w-32 h-32 object-cover rounded border-2 border-blue-500" onerror="this.src='assets/no-image.png'">
                                    <div class="text-xs text-center mt-1">Anda</div>
                                </div>
                            </div>
                            <div class="font-semibold text-base text-center mb-1">Skor kecocokan <span class="text-green-600">${10 - i}/10</span></div>
                            <div class="w-full bg-green-100 rounded-full h-2.5 my-2">
                                <div class="bg-green-500 h-2.5 rounded-full" style="width: ${(10 - i) * 10}%"></div>
                            </div>
                            <span class="bg-yellow-200 text-yellow-800 px-3 py-1 rounded-full text-xs font-semibold">Rekomendasi</span>
                        `;
                    }
                }
            } catch (error) {
                console.error('3D Preview Error:', error);
                container.innerHTML = '<p class="text-red-500 text-center col-span-full">Hair extraction failed</p>';
            }
        }

        function getModelImage(faceShape, style) {
            const styleMap = {
                'Textured Crop': 'Textured Crop.jpg',
                'Pompadour': 'Pompadour.jpg',
                'Man Bun': 'Man Bun.jpg',
                'Two Block Hair': 'Two Block Hair.jpg',
                'Taper Fade': 'Taper Fade.jpg',
                'Fringe Haircut': 'Fringe Haircut.jpg',
                'Slick Back': 'Slick Back.jpg',
                'Quiff': 'Quiff.jpg',
                'Side Swept': 'Side Swept.jpg',
                'Classic Quiff': 'Classic Quiff.jpg',
                'Side Part Fringe': 'Side Part Fringe.jpg',
                'Long Fringe': 'Long Fringe.jpg',
                'Side Part': 'Side Part.jpg',
                'Crew Cut': 'Crew Cut.jpg'
            };
            
            const fileName = styleMap[style] || `${style}.jpg`;
            return `rekomendasi/${faceShape.toLowerCase()}/${fileName}`;
        }

        // Helper untuk cek file gambar (hanya bisa untuk file lokal/server, tidak untuk CDN)
        function fileExists(url) {
            var xhr = new XMLHttpRequest();
            xhr.open('HEAD', url, false);
            xhr.send();
            return xhr.status != 404;
        }

        // Tambahkan fungsi normalisasi nama file
        function normalizeFileName(str) {
            return str
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '_') // ganti semua non-alfanumerik dengan _
                .replace(/^_+|_+$/g, '');    // hapus _ di awal/akhir
        }

    </script>
</body>
</html>