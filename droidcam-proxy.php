<?php
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/octet-stream');

// Get the URL parameter
$url = $_GET['url'] ?? '';
$override = $_GET['override'] ?? false;

if (empty($url)) {
    http_response_code(400);
    echo 'URL parameter is required';
    exit;
}

// Initialize cURL
$ch = curl_init($url);

// Set cURL options
// Don't verify SSL certificate
if (stripos($url, 'https://') !== false) {
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
}

// Set timeout
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

// First try to get the status
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$initial_response = curl_exec($ch);

// Check if we need to override
if (strpos($initial_response, 'DroidCam is connected to another client') !== false && !$override) {
    // Try to override connection
    $override_url = str_replace('/video', '/override', $url);
    $override_ch = curl_init($override_url);
    curl_setopt($override_ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($override_ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($override_ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($override_ch, CURLOPT_TIMEOUT, 30);
    
    $override_response = curl_exec($override_ch);
    
    if (curl_errno($override_ch)) {
        http_response_code(500);
        echo 'Error overriding connection: ' . curl_error($override_ch);
        curl_close($override_ch);
        exit;
    }
    
    curl_close($override_ch);
    
    // Reinitialize for video connection
    $ch = curl_init($url);
}

// Add headers to force connection takeover
$headers = array(
    'Connection: close',
    'Upgrade: websocket',
    'Sec-WebSocket-Version: 13',
    'Sec-WebSocket-Key: ' . base64_encode(random_bytes(16)),
    'Accept: */*',
    'User-Agent: Mozilla/5.0'
);

curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

// Get the response
$response = curl_exec($ch);

// Check for errors
if (curl_errno($ch)) {
    $error = curl_error($ch);
    curl_close($ch);
    
    // Handle specific error cases
    if (strpos($error, 'Connection refused') !== false) {
        http_response_code(409);
        echo 'Error: DroidCam is already connected to another client. Please disconnect the other client first.';
    } else {
        http_response_code(500);
        echo 'Error: ' . $error;
    }
    exit;
}

// Get content type
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
if ($contentType) {
    header('Content-Type: ' . $contentType);
}

// Close cURL
curl_close($ch);

// Output the response
echo $response;
?>
