-- Create database if not exists
CREATE DATABASE IF NOT EXISTS pixel;
USE pixel;

-- Create kategori_produk table
CREATE TABLE IF NOT EXISTS kategori_produk (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_kategori VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create produk table (basic product information)
CREATE TABLE IF NOT EXISTS produk (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_produk VARCHAR(255) NOT NULL,
    deskripsi TEXT,
    gambar_utama VARCHAR(255),
    kategori_id INT,
    variasi JSON, -- Store variations as JSON array
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (kategori_id) REFERENCES kategori_produk(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Drop existing variasi_produk table if it exists
DROP TABLE IF EXISTS variasi_produk;

-- Add variasi JSON column to produk table if it doesn't exist
ALTER TABLE produk ADD COLUMN IF NOT EXISTS variasi JSON;

-- Create gambar_produk table
CREATE TABLE IF NOT EXISTS gambar_produk (
    id INT AUTO_INCREMENT PRIMARY KEY,
    produk_id INT,
    gambar VARCHAR(255),
    label VARCHAR(100),
    deskripsi TEXT,
    urutan INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (produk_id) REFERENCES produk(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert some sample categories
INSERT INTO kategori_produk (nama_kategori) VALUES
('Perawatan Rambut'),
('Perawatan Wajah'),
('Perawatan Badan'),
('Aksesoris');

-- Create daftar_produk view
CREATE VIEW daftar_produk AS
SELECT 
    p.id,
    p.gambar_utama,
    p.nama_produk,
    k.nama_kategori,
    p.harga,
    CONCAT('<a href="edit_produk.php?id=', p.id, '" class="btn btn-sm btn-primary">Edit</a> ',
           '<a href="hapus_produk.php?id=', p.id, '" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">Delete</a>') as aksi
FROM produk p
LEFT JOIN kategori_produk k ON p.kategori_id = k.id;
