<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <h1>Test Upload Profile Picture</h1>
    
    <form id="uploadForm">
        <input type="file" id="fileInput" accept="image/*" required>
        <button type="submit">Upload</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a file');
                return;
            }
            
            const formData = new FormData();
            formData.append('profile_picture', file);
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Uploading...';
            
            fetch('api/test_upload.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Raw response:', text);
                
                try {
                    const data = JSON.parse(text);
                    console.log('Parsed data:', data);
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="result success">
                                <h3>Success!</h3>
                                <p>${data.message}</p>
                                <p>Image URL: ${data.image_url}</p>
                                <img src="${data.image_url}" style="max-width: 200px; height: auto;">
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="result error">
                                <h3>Error</h3>
                                <p>${data.message}</p>
                            </div>
                        `;
                    }
                } catch (parseError) {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>Parse Error</h3>
                            <p>Could not parse JSON response</p>
                            <pre>${text}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            });
        });
    </script>
</body>
</html>
