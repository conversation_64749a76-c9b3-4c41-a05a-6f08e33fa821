<?php
echo "<h2>Fix Admin Login Issues</h2>";

try {
    require_once 'db_config.php';
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Check if admin table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'admin'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ Admin table doesn't exist. Creating...</p>";
        
        $sql = "CREATE TABLE admin (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
            is_active BOOLEAN DEFAULT TRUE,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✅ Admin table created</p>";
    } else {
        echo "<p style='color: green;'>✅ Admin table exists</p>";
    }
    
    // Clear existing admin data and create fresh ones
    echo "<h3>Resetting Admin Accounts...</h3>";
    $conn->exec("DELETE FROM admin");
    echo "<p style='color: orange;'>🔄 Cleared existing admin accounts</p>";
    
    // Create fresh admin accounts
    $default_password = password_hash('password', PASSWORD_DEFAULT);
    
    $admins = [
        ['admin', $default_password, 'Super Administrator', '<EMAIL>', 'super_admin'],
        ['manager', $default_password, 'Manager', '<EMAIL>', 'admin'],
        ['staff', $default_password, 'Staff Admin', '<EMAIL>', 'moderator'],
        ['lukman', $default_password, 'Lukmanul Hakim Jayadi', '<EMAIL>', 'admin']
    ];
    
    $stmt = $conn->prepare("INSERT INTO admin (username, password, full_name, email, role, is_active) VALUES (?, ?, ?, ?, ?, 1)");
    
    foreach ($admins as $admin) {
        $stmt->execute($admin);
        echo "<p style='color: green;'>✅ Created admin: " . $admin[0] . " (" . $admin[2] . ")</p>";
    }
    
    // Verify the accounts
    echo "<h3>Verifying Admin Accounts:</h3>";
    $stmt = $conn->query("SELECT username, full_name, email, role, is_active FROM admin ORDER BY id");
    $all_admins = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Username</th><th>Full Name</th><th>Email</th><th>Role</th><th>Active</th></tr>";
    
    foreach ($all_admins as $admin) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['full_name']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['email']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['role']) . "</td>";
        echo "<td>" . ($admin['is_active'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test password verification
    echo "<h3>Testing Password Verification:</h3>";
    foreach ($all_admins as $admin) {
        $stmt = $conn->prepare("SELECT password FROM admin WHERE username = ?");
        $stmt->execute([$admin['username']]);
        $stored_password = $stmt->fetchColumn();
        
        if (password_verify('password', $stored_password)) {
            echo "<p style='color: green;'>✅ " . $admin['username'] . ": Password 'password' works</p>";
        } else {
            echo "<p style='color: red;'>❌ " . $admin['username'] . ": Password verification failed</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3>🎉 Admin accounts are now ready!</h3>";
    echo "<p><strong>You can now login with any of these accounts:</strong></p>";
    echo "<ul>";
    foreach ($all_admins as $admin) {
        echo "<li><strong>Username:</strong> " . $admin['username'] . " | <strong>Password:</strong> password</li>";
    }
    echo "</ul>";
    
    echo "<p><a href='login.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>→ Go to Login Page</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and make sure MySQL is running.</p>";
}
?>
