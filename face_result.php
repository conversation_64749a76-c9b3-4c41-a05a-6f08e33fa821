<?php
session_start();
require_once 'Admin/db_config.php';

// Mendapatkan parameter dari URL
$shape = isset($_GET['shape']) ? $_GET['shape'] : '';
$image = isset($_GET['image']) ? $_GET['image'] : '';

// Memeriksa apakah parameter ada
if (empty($shape) || empty($image)) {
    header('Location: face.php');
    exit;
}

// Mendapatkan deskripsi dan rekomendasi dari database
$stmt = $conn->prepare("SELECT description, recommendation FROM face_analysis WHERE face_shape = ?");
$stmt->execute([$shape]);
$row = $stmt->fetch();
if ($row) {
    $description = $row['description'];
    $recommendation = $row['recommendation'];
} else {
    $description = "Bentuk wajah $shape";
    $recommendation = "Rekomendasi untuk bentuk wajah $shape";
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hasil <PERSON> W<PERSON> - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Poppins', sans-serif;
        }
        
        .result-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .face-image {
            max-width: 400px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .analysis-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .shape-badge {
            background: #10B981;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }
        
        .recommendation-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
    </style>
</head>
<body class="bg-[#f8fafc] min-h-screen">
    <div class="result-container">
        <div class="flex justify-between items-center mb-8">
            <a href="face.php" class="text-gray-600">
                <i class="fas fa-arrow-left text-lg mr-2"></i> Kembali
            </a>
            <h1 class="text-2xl font-bold text-gray-900">Hasil Analisis Wajah</h1>
            <div class="w-6"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Gambar Wajah -->
            <div class="md:order-2">
                <div class="text-center mb-4">
                    <span class="shape-badge"><?php echo htmlspecialchars($shape); ?></span>
                </div>
                <img src="uploads/face_detection/<?php echo htmlspecialchars($image); ?>" 
                     alt="Hasil Deteksi Wajah" 
                     class="face-image mx-auto">
            </div>

            <!-- Analisis dan Rekomendasi -->
            <div class="md:order-1">
                <div class="analysis-card">
                    <h2 class="text-xl font-semibold mb-4">Deskripsi Bentuk Wajah</h2>
                    <p class="text-gray-600 leading-relaxed">
                        <?php echo htmlspecialchars($description); ?>
                    </p>
                </div>

                <div class="recommendation-card">
                    <h3 class="text-lg font-semibold mb-4">Rekomendasi Gaya Rambut</h3>
                    <p class="text-gray-600 leading-relaxed">
                        <?php echo htmlspecialchars($recommendation); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
