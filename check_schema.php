<?php
require_once 'Admin/db_config.php';

try {
    $stmt = $conn->prepare("SELECT id, nama_produk, gambar_utama, variasi FROM produk WHERE id = ?");
    $stmt->execute([$_GET['id'] ?? 1]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($product) {
        echo "Product ID: " . htmlspecialchars($product['id']) . "<br>";
        echo "Product Name: " . htmlspecialchars($product['nama_produk']) . "<br>";
        echo "Main Image: " . htmlspecialchars($product['gambar_utama']) . "<br>";
        
        if ($product['variasi']) {
            $variasi = json_decode($product['variasi'], true);
            echo "<br>Variations:<br>";
            foreach ($variasi as $var) {
                echo "- " . htmlspecialchars($var['varian']) . "<br>";
            }
        }
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
