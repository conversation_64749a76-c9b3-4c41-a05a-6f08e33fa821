import cv2
import numpy as np
import json
import sys
import os
from PIL import Image, ImageFilter, ImageEnhance

class Hair3DExtractor:
    def __init__(self):
        self.hair_regions = {
            'top': (0.1, 0.4, 0.9, 0.6),      # x1, y1, x2, y2 ratios
            'sides': (0.0, 0.3, 1.0, 0.7),
            'back': (0.2, 0.2, 0.8, 0.8),
            'fringe': (0.3, 0.1, 0.7, 0.4)
        }
    
    def extract_hair_segment(self, model_image_path, hair_style):
        """Extract 3D hair segment from model image"""
        try:
            image = cv2.imread(model_image_path)
            if image is None:
                return None
            
            # Convert to HSV for better hair detection
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Create hair mask based on color and position
            hair_mask = self.create_hair_mask(hsv, hair_style)
            
            # Extract hair region
            hair_segment = cv2.bitwise_and(image, image, mask=hair_mask)
            
            # Create 3D depth map
            depth_map = self.generate_depth_map(hair_segment, hair_style)
            
            # Apply 3D effects
            hair_3d = self.apply_3d_effects(hair_segment, depth_map)
            
            return {
                'hair_segment': hair_3d,
                'depth_map': depth_map,
                'mask': hair_mask,
                'style': hair_style
            }
            
        except Exception as e:
            print(f"Hair extraction error: {e}")
            return None
    
    def create_hair_mask(self, hsv_image, hair_style):
        """Create mask for hair region based on style"""
        h, w = hsv_image.shape[:2]
        mask = np.zeros((h, w), dtype=np.uint8)
        
        # Hair color ranges (brown, black, blonde)
        hair_ranges = [
            ([8, 50, 20], [25, 255, 200]),    # Brown
            ([0, 0, 0], [180, 255, 30]),      # Black
            ([15, 30, 100], [35, 255, 255])   # Blonde
        ]
        
        # Combine color masks
        for lower, upper in hair_ranges:
            color_mask = cv2.inRange(hsv_image, np.array(lower), np.array(upper))
            mask = cv2.bitwise_or(mask, color_mask)
        
        # Apply style-specific region
        region_mask = self.get_style_region_mask(h, w, hair_style)
        mask = cv2.bitwise_and(mask, region_mask)
        
        # Clean up mask
        kernel = np.ones((3,3), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        return mask
    
    def get_style_region_mask(self, height, width, hair_style):
        """Get region mask based on hair style"""
        mask = np.zeros((height, width), dtype=np.uint8)
        
        style_regions = {
            'buzz_cut': 'top',
            'pompadour': 'top',
            'quiff': 'top',
            'undercut': 'sides',
            'fade': 'sides',
            'fringe': 'fringe',
            'long_hair': 'top'
        }
        
        region = style_regions.get(hair_style.lower(), 'top')
        x1, y1, x2, y2 = self.hair_regions[region]
        
        start_x, start_y = int(x1 * width), int(y1 * height)
        end_x, end_y = int(x2 * width), int(y2 * height)
        
        mask[start_y:end_y, start_x:end_x] = 255
        return mask
    
    def generate_depth_map(self, hair_segment, hair_style):
        """Generate depth map for 3D effect"""
        gray = cv2.cvtColor(hair_segment, cv2.COLOR_BGR2GRAY)
        
        # Create depth based on brightness and style
        depth = gray.astype(np.float32) / 255.0
        
        # Style-specific depth adjustments
        if 'fade' in hair_style.lower():
            # Gradient depth for fade
            h, w = depth.shape
            gradient = np.linspace(1.0, 0.3, h).reshape(-1, 1)
            depth = depth * gradient
        elif 'pompadour' in hair_style.lower():
            # Higher depth at top
            h, w = depth.shape
            top_boost = np.zeros_like(depth)
            top_boost[:h//3, :] = 0.3
            depth = depth + top_boost
        
        # Smooth depth map
        depth = cv2.GaussianBlur(depth, (5, 5), 0)
        return (depth * 255).astype(np.uint8)
    
    def apply_3d_effects(self, hair_segment, depth_map):
        """Apply 3D visual effects to hair segment"""
        # Convert to PIL for advanced effects
        pil_image = Image.fromarray(cv2.cvtColor(hair_segment, cv2.COLOR_BGR2RGB))
        
        # Enhance contrast and sharpness
        enhancer = ImageEnhance.Contrast(pil_image)
        pil_image = enhancer.enhance(1.2)
        
        enhancer = ImageEnhance.Sharpness(pil_image)
        pil_image = enhancer.enhance(1.1)
        
        # Add subtle shadow effect
        shadow = pil_image.filter(ImageFilter.GaussianBlur(2))
        
        # Convert back to OpenCV
        result = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        return result
    
    def create_3d_hair_model(self, extracted_hair, user_face_landmarks):
        """Create 3D hair model positioned on user's face"""
        try:
            if user_face_landmarks is None:
                return extracted_hair['hair_segment']
            
            # Get face dimensions
            face_width = np.max(user_face_landmarks[:, 0]) - np.min(user_face_landmarks[:, 0])
            face_height = np.max(user_face_landmarks[:, 1]) - np.min(user_face_landmarks[:, 1])
            
            # Scale hair to fit face
            hair_img = extracted_hair['hair_segment']
            scale_factor = face_width / hair_img.shape[1] * 1.1
            
            new_width = int(hair_img.shape[1] * scale_factor)
            new_height = int(hair_img.shape[0] * scale_factor)
            
            scaled_hair = cv2.resize(hair_img, (new_width, new_height))
            
            return scaled_hair
            
        except Exception as e:
            print(f"3D model creation error: {e}")
            return extracted_hair['hair_segment']

def main():
    if len(sys.argv) < 3:
        print(json.dumps({'success': False, 'error': 'Missing parameters'}))
        return
    
    model_image_path = sys.argv[1]
    hair_style = sys.argv[2]
    
    extractor = Hair3DExtractor()
    result = extractor.extract_hair_segment(model_image_path, hair_style)
    
    if result:
        # Save extracted hair segment
        output_path = f"hair_segments/{hair_style}_3d.png"
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        cv2.imwrite(output_path, result['hair_segment'])
        
        print(json.dumps({
            'success': True,
            'hair_segment_path': output_path,
            'style': hair_style,
            'has_depth_map': True
        }))
    else:
        print(json.dumps({'success': False, 'error': 'Hair extraction failed'}))

if __name__ == "__main__":
    main()