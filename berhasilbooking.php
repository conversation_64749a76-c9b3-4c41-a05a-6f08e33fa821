<?php
session_start();
require_once 'admin/db_config.php'; // Ensure this path is correct for database connection

$booking = $_SESSION['last_booking'] ?? null;

if (!$booking) {
    // If no booking data found in session, redirect to home or booking page
    header("Location: index.php");
    exit();
}

// Format date and time for display
$display_date = date('d F Y', strtotime($booking['booking_date']));
$display_time = date('H:i WIB', strtotime($booking['booking_time_slot']));

// Assuming location details might be needed for the receipt, fetch if not in session
$location_name = "Pixel Tonsorium"; // Default as per image, can be fetched if dynamic
$location_address = "Jl. R.A. Kosasih No.278, Subangjaya,<br/>Kec. C<PERSON>le, Kota Sukabumi, Jawa Barat";
$location_phone = "08997573730";

// You might want to fetch actual location data if location_id is available and needed dynamically
/*
if (isset($booking['location_id'])) {
    try {
        $stmt_loc = $conn->prepare("SELECT name, address, phone FROM locations WHERE id = :location_id");
        $stmt_loc->execute(['location_id' => $booking['location_id']]);
        $fetched_location = $stmt_loc->fetch(PDO::FETCH_ASSOC);
        if ($fetched_location) {
            $location_name = $fetched_location['name'];
            $location_address = str_replace("\n", "<br/>", htmlspecialchars($fetched_location['address']));
            $location_phone = $fetched_location['phone'];
        }
    } catch (PDOException $e) {
        error_log("Error fetching location details for receipt: " . $e->getMessage());
    }
}
*/

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Confirmed - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
        }
        .receipt-container {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            overflow: hidden;
            position: relative;
        }
        .receipt-header {
            text-align: center;
            padding: 30px 20px;
            color: #fff;
        }
        .receipt-header i {
            font-size: 4rem;
            margin-bottom: 15px;
        }
        .receipt-content {
            padding: 30px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px dashed #eee;
        }
        .info-row:last-of-type {
            border-bottom: none;
        }
        .info-row .label {
            color: #666;
            font-size: 0.9em;
        }
        .info-row .value {
            font-weight: 500;
            color: #333;
            text-align: right;
        }
        .total-section {
            border-top: 2px dashed #ddd;
            padding-top: 15px;
            margin-top: 20px;
        }
        .total-section .label {
            font-weight: 600;
            font-size: 1.1em;
        }
        .total-section .value {
            font-weight: 700;
            font-size: 1.3em;
            color: #0A5144;
        }
        .footer-text {
            text-align: center;
            margin-top: 25px;
            font-size: 0.9em;
            color: #777;
        }
        .invoice-details p {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="receipt-container" id="receiptContainer">
        <div class="receipt-content">
            <div class="text-center mb-6">
            <img src="./assets/struk.png" alt="Pixel Tonsorium Logo" class="w-20 h-20 mx-auto mb-3" />
                <h2 class="text-2xl font-bold text-gray-900">PIXEL TONSORIUM</h2>
                <p class="text-sm text-gray-600 leading-tight"><?php echo $location_address; ?></p>
                <p class="text-sm text-gray-600 mt-1">No. Telp <?php echo htmlspecialchars($location_phone); ?></p>
                <p class="text-lg font-bold text-gray-900 mt-3">Order ID: #<?php echo htmlspecialchars($booking['id']); ?></p>
            </div>

            <div class="invoice-details mb-6 text-center">
                <h3 class="text-xl font-bold text-gray-900 mb-2">Transaksi Berhasil!</h3>
                <p class="text-sm text-gray-600 mb-4"><?php echo $display_date . ' ' . $display_time; ?></p>
                <p class="text-4xl font-bold text-[#0A5144]">Rp<?php echo number_format($booking['total_price'], 0, ',', '.'); ?></p>
            </div>

            <div class="product-details mb-6">
                <h3 class="font-bold text-gray-900 mb-3">Detail Penjadwalan:</h3>
                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                    <span class="text-gray-800 font-medium"><?php echo htmlspecialchars($booking['service_name']); ?></span>
                    <span class="text-gray-800">Rp<?php echo number_format($booking['service_price'], 0, ',', '.'); ?></span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                    <span class="text-gray-600 text-sm">Barber: <?php echo htmlspecialchars($booking['barber_name']); ?></span>
                    <span class="text-gray-600 text-sm"><?php echo htmlspecialchars($booking['service_duration']); ?> min</span>
                </div>
                <div class="flex justify-between items-center py-2">
                    <span class="text-gray-600 text-sm">Tanggal: <?php echo $display_date; ?></span>
                    <span class="text-gray-600 text-sm">Waktu: <?php echo htmlspecialchars($booking['booking_time_slot']); ?></span>
                </div>
            </div>

            <div class="flex justify-between items-center total-section">
                <span class="label">Total Pembayaran</span>
                <span class="value">Rp<?php echo number_format($booking['total_price'], 0, ',', '.'); ?></span>
            </div>

            <p class="footer-text mt-6">Terima Kasih Telah Berbelanja</p>
            <p class="text-center text-sm text-[#006A63] mt-2">
                <a href="#" class="underline">Link Kritik dan Saran:</a><br/>
                com/e-receipt/S-00D39U-07G344G
            </p>
        </div>
    </div>

    <div class="fixed bottom-0 left-0 right-0 bg-white p-4 shadow-lg border-t border-gray-100 flex justify-center space-x-4">
        <a href="index.php" class="border border-[#0A5144] text-[#0A5144] py-3 px-6 rounded-lg font-bold hover:bg-[#F0F9F8]">
            Kembali Beranda
        </a>
        <button id="printReceiptBtn" class="gradient-bg text-white py-3 px-6 rounded-lg font-bold shadow-md hover:opacity-90">
            Cetak Struk
        </button>
    </div>

    <script>
        document.getElementById('printReceiptBtn').addEventListener('click', function() {
            const receiptContainer = document.getElementById('receiptContainer');
            const fixedButtons = document.querySelector('.fixed.bottom-0');

            // Sembunyikan tombol agar tidak ikut tercetak di gambar
            fixedButtons.style.display = 'none';

            // Gunakan html2canvas untuk mengambil gambar dari kontainer struk
            html2canvas(receiptContainer, {
                scale: 2, // Tingkatkan skala untuk kualitas gambar yang lebih baik
                useCORS: true
            }).then(canvas => {
                // Buat link untuk memicu unduhan
                const link = document.createElement('a');
                link.href = canvas.toDataURL('image/png');
                link.download = 'struk-booking-pixel.png'; // Nama file gambar yang akan diunduh

                // Picu unduhan secara otomatis
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Tampilkan kembali tombol setelah selesai
                fixedButtons.style.display = 'flex';
            }).catch(err => {
                // Jika terjadi error, tampilkan di konsol dan munculkan kembali tombolnya
                console.error('Gagal mencetak struk:', err);
                fixedButtons.style.display = 'flex';
            });
        });
    </script>
</body>
</html>