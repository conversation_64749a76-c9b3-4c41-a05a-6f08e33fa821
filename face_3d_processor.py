import cv2
import numpy as np
import dlib
import json
import sys
import os
from PIL import Image, ImageDraw
import base64
from io import BytesIO

class Face3DProcessor:
    def __init__(self):
        # Initialize face detector and landmark predictor
        self.detector = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # Try to load dlib predictor
        try:
            self.predictor = dlib.shape_predictor('models/shape_predictor_68_face_landmarks.dat')
            self.dlib_detector = dlib.get_frontal_face_detector()
        except:
            self.predictor = None
            self.dlib_detector = None
            
        # 3D head template points (simplified)
        self.template_3d_points = np.array([
            [0.0, 0.0, 0.0],      # Nose tip
            [0.0, -330.0, -65.0], # Chin
            [-225.0, 170.0, -135.0], # Left eye left corner
            [225.0, 170.0, -135.0],  # Right eye right corner
            [-150.0, -150.0, -125.0], # Left mouth corner
            [150.0, -150.0, -125.0]   # Right mouth corner
        ], dtype=np.float64)
        
    def detect_face_landmarks(self, image):
        """Detect face and extract 68 landmarks using dlib"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Use dlib if available
        if self.dlib_detector and self.predictor:
            faces = self.dlib_detector(gray)
            if len(faces) > 0:
                landmarks = self.predictor(gray, faces[0])
                points = []
                for i in range(68):
                    points.append([landmarks.part(i).x, landmarks.part(i).y])
                return np.array(points)
        
        # Fallback to OpenCV Haar Cascade
        faces = self.detector.detectMultiScale(gray, 1.3, 5)
        if len(faces) > 0:
            # Generate approximate landmarks for the detected face
            x, y, w, h = faces[0]
            return self.generate_approximate_landmarks(x, y, w, h)
        
        return None
    
    def generate_approximate_landmarks(self, x, y, w, h):
        """Generate approximate 68 landmarks from face rectangle"""
        landmarks = []
        
        # Face outline (17 points)
        for i in range(17):
            px = x + (i / 16.0) * w
            py = y + h * 0.8 + (h * 0.2) * np.sin(i * np.pi / 16)
            landmarks.append([px, py])
        
        # Eyebrows (10 points)
        for i in range(5):
            landmarks.append([x + w * 0.2 + i * w * 0.15, y + h * 0.3])
        for i in range(5):
            landmarks.append([x + w * 0.55 + i * w * 0.15, y + h * 0.3])
        
        # Nose (9 points)
        for i in range(9):
            landmarks.append([x + w * 0.5, y + h * 0.4 + i * h * 0.05])
        
        # Eyes (12 points each)
        for eye_x in [x + w * 0.3, x + w * 0.7]:
            for i in range(6):
                angle = i * np.pi / 3
                landmarks.append([eye_x + 15 * np.cos(angle), y + h * 0.4 + 8 * np.sin(angle)])
        
        # Mouth (20 points)
        for i in range(20):
            angle = i * np.pi / 10
            landmarks.append([x + w * 0.5 + 30 * np.cos(angle), y + h * 0.7 + 15 * np.sin(angle)])
        
        return np.array(landmarks[:68])
    
    def map_to_3d_template(self, landmarks):
        """Map 2D landmarks to 3D template coordinates"""
        if landmarks is None or len(landmarks) < 6:
            return None
            
        # Key landmark indices for 3D mapping
        key_points_2d = np.array([
            landmarks[30],  # Nose tip
            landmarks[8],   # Chin
            landmarks[36],  # Left eye left corner
            landmarks[45],  # Right eye right corner
            landmarks[48],  # Left mouth corner
            landmarks[54]   # Right mouth corner
        ], dtype=np.float64)
        
        return key_points_2d
    
    def apply_texture_mapping(self, image, landmarks, hair_style=None):
        """Apply texture mapping and hair overlay"""
        if landmarks is None:
            return image
            
        result = image.copy()
        
        # Create face mask
        face_points = landmarks[:17]  # Face outline
        mask = np.zeros(image.shape[:2], dtype=np.uint8)
        cv2.fillPoly(mask, [face_points.astype(np.int32)], 255)
        
        # Apply seamless cloning for better texture integration
        try:
            center = (int(np.mean(landmarks[:, 0])), int(np.mean(landmarks[:, 1])))
            result = cv2.seamlessClone(image, result, mask, center, cv2.NORMAL_CLONE)
        except:
            pass
        
        # Apply hair overlay if provided
        if hair_style:
            result = self.apply_hair_overlay(result, landmarks, hair_style)
            
        return result
    
    def apply_hair_overlay(self, image, landmarks, hair_style):
        """Apply hair style overlay"""
        try:
            # Load hair style image
            hair_path = f"hair_styles/{hair_style}.png"
            if os.path.exists(hair_path):
                hair_img = cv2.imread(hair_path, cv2.IMREAD_UNCHANGED)
                
                # Calculate hair position based on face landmarks
                forehead_y = int(np.min(landmarks[:17, 1]) - 50)
                face_width = int(np.max(landmarks[:17, 0]) - np.min(landmarks[:17, 0]))
                face_center_x = int(np.mean(landmarks[:17, 0]))
                
                # Resize hair to fit face
                hair_width = int(face_width * 1.2)
                hair_height = int(hair_img.shape[0] * hair_width / hair_img.shape[1])
                hair_resized = cv2.resize(hair_img, (hair_width, hair_height))
                
                # Position hair on head
                start_x = max(0, face_center_x - hair_width // 2)
                start_y = max(0, forehead_y - hair_height // 2)
                end_x = min(image.shape[1], start_x + hair_width)
                end_y = min(image.shape[0], start_y + hair_height)
                
                # Blend hair with image
                if hair_resized.shape[2] == 4:  # Has alpha channel
                    alpha = hair_resized[:, :, 3] / 255.0
                    for c in range(3):
                        image[start_y:end_y, start_x:end_x, c] = (
                            alpha * hair_resized[:end_y-start_y, :end_x-start_x, c] +
                            (1 - alpha) * image[start_y:end_y, start_x:end_x, c]
                        )
                else:
                    image[start_y:end_y, start_x:end_x] = hair_resized[:end_y-start_y, :end_x-start_x]
                    
        except Exception as e:
            print(f"Hair overlay error: {e}")
            
        return image
    
    def create_3d_preview(self, image_path, hair_style=None):
        """Create 3D face modeling preview"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                return None
                
            # Detect landmarks
            landmarks = self.detect_face_landmarks(image)
            if landmarks is None:
                return None
            
            # Map to 3D template
            key_points = self.map_to_3d_template(landmarks)
            
            # Apply texture mapping and hair overlay
            result = self.apply_texture_mapping(image, landmarks, hair_style)
            
            # Convert to base64 for web display
            _, buffer = cv2.imencode('.png', result)
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return {
                'success': True,
                'image': f"data:image/png;base64,{img_base64}",
                'landmarks_count': len(landmarks),
                'has_hair_overlay': hair_style is not None
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

def main():
    if len(sys.argv) < 2:
        print(json.dumps({'success': False, 'error': 'No image path provided'}))
        return
    
    image_path = sys.argv[1]
    hair_style = sys.argv[2] if len(sys.argv) > 2 else None
    
    processor = Face3DProcessor()
    result = processor.create_3d_preview(image_path, hair_style)
    
    print(json.dumps(result))

if __name__ == "__main__":
    main()