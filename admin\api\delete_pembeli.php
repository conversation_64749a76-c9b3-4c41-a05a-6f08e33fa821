<?php
session_start();
require_once '../db_config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit();
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['pembeli_id']) || empty($input['pembeli_id'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Pembeli ID is required'
    ]);
    exit();
}

$pembeli_id = (int)$input['pembeli_id'];

try {
    // Start transaction
    $conn->beginTransaction();
    
    // Get pembeli info before deletion for logging
    $stmt = $conn->prepare("SELECT nama_pembeli FROM pembeli WHERE id = ?");
    $stmt->execute([$pembeli_id]);
    $pembeli = $stmt->fetch();
    
    if (!$pembeli) {
        $conn->rollback();
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Pembeli tidak ditemukan'
        ]);
        exit();
    }
    
    $pembeli_name = $pembeli['nama_pembeli'];
    
    // Check if pembeli has orders
    $stmt = $conn->prepare("SELECT COUNT(*) as order_count FROM orders WHERE pembeli_id = ?");
    $stmt->execute([$pembeli_id]);
    $order_count = $stmt->fetchColumn();
    
    // Delete related data in correct order to avoid foreign key constraints
    $deleted_notifications = 0;
    $deleted_history = 0;
    $deleted_items = 0;
    $deleted_orders = 0;
    $deleted_addresses = 0;
    $deleted_other = 0;

    // 1. Delete notifications first (references orders)
    try {
        $stmt = $conn->prepare("
            DELETE n FROM notifications n
            INNER JOIN orders o ON n.order_id = o.id
            WHERE o.pembeli_id = ?
        ");
        $stmt->execute([$pembeli_id]);
        $deleted_notifications = $stmt->rowCount();
    } catch (PDOException $e) {
        // Table might not exist, continue
        error_log("Could not delete notifications: " . $e->getMessage());
    }

    // 2. Delete order history
    try {
        $stmt = $conn->prepare("
            DELETE oh FROM order_history oh
            INNER JOIN orders o ON oh.order_id = o.id
            WHERE o.pembeli_id = ?
        ");
        $stmt->execute([$pembeli_id]);
        $deleted_history = $stmt->rowCount();
    } catch (PDOException $e) {
        // Table might not exist, continue
        error_log("Could not delete order history: " . $e->getMessage());
    }

    // 3. Delete any other tables that might reference orders
    $other_tables = ['reviews', 'ratings', 'order_tracking', 'order_logs'];
    foreach ($other_tables as $table) {
        try {
            $stmt = $conn->prepare("
                DELETE t FROM {$table} t
                INNER JOIN orders o ON t.order_id = o.id
                WHERE o.pembeli_id = ?
            ");
            $stmt->execute([$pembeli_id]);
            $deleted_other += $stmt->rowCount();
        } catch (PDOException $e) {
            // Table might not exist or have different structure, continue
            error_log("Could not delete from {$table}: " . $e->getMessage());
        }
    }

    // 4. Delete order items
    $stmt = $conn->prepare("
        DELETE oi FROM order_items oi
        INNER JOIN orders o ON oi.order_id = o.id
        WHERE o.pembeli_id = ?
    ");
    $stmt->execute([$pembeli_id]);
    $deleted_items = $stmt->rowCount();

    // 5. Delete orders
    $stmt = $conn->prepare("DELETE FROM orders WHERE pembeli_id = ?");
    $stmt->execute([$pembeli_id]);
    $deleted_orders = $stmt->rowCount();

    // 6. Delete user addresses
    try {
        $stmt = $conn->prepare("DELETE FROM user_addresses WHERE pembeli_id = ?");
        $stmt->execute([$pembeli_id]);
        $deleted_addresses = $stmt->rowCount();
    } catch (PDOException $e) {
        // Table might not exist, continue
        error_log("Could not delete user addresses: " . $e->getMessage());
    }
    
    // 7. Finally delete the pembeli
    $stmt = $conn->prepare("DELETE FROM pembeli WHERE id = ?");
    $stmt->execute([$pembeli_id]);
    $deleted_pembeli = $stmt->rowCount();

    if ($deleted_pembeli === 0) {
        $conn->rollback();
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Gagal menghapus pembeli'
        ]);
        exit();
    }

    // Commit transaction
    $conn->commit();

    // Log the deletion activity
    $total_deleted = $deleted_orders + $deleted_items + $deleted_addresses + $deleted_history + $deleted_notifications + $deleted_other;
    error_log("Admin {$_SESSION['admin_id']} deleted pembeli: {$pembeli_name} (ID: {$pembeli_id}) with {$order_count} orders and {$total_deleted} related records");

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => "Pembeli '{$pembeli_name}' dan semua data terkait berhasil dihapus",
        'data' => [
            'pembeli_id' => $pembeli_id,
            'pembeli_name' => $pembeli_name,
            'deleted_orders' => $deleted_orders,
            'deleted_items' => $deleted_items,
            'deleted_addresses' => $deleted_addresses,
            'deleted_history' => $deleted_history,
            'deleted_notifications' => $deleted_notifications,
            'deleted_other' => $deleted_other,
            'total_deleted' => $total_deleted
        ]
    ]);
    
} catch (PDOException $e) {
    // Rollback transaction on error
    $conn->rollback();
    
    // Log the error
    error_log("Error deleting pembeli {$pembeli_id}: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan database: ' . $e->getMessage()
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    
    // Log the error
    error_log("Unexpected error deleting pembeli {$pembeli_id}: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan tidak terduga'
    ]);
}
?>
