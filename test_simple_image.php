<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Image Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">🖼️ Simple Image Test</h1>
        
        <!-- Direct Image Test -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Direct Image Test</h2>
            <div class="grid grid-cols-3 md:grid-cols-6 gap-4">
                
                <!-- Test Oval Images -->
                <div class="text-center">
                    <div class="w-20 h-20 border rounded overflow-hidden mb-2">
                        <img src="rekomendasi/oval/Textured Crop.jpg" alt="Textured Crop" class="w-full h-full object-cover"
                             onload="this.parentElement.classList.add('border-green-500')"
                             onerror="this.parentElement.innerHTML='❌'; this.parentElement.classList.add('border-red-500')">
                    </div>
                    <p class="text-xs">Textured Crop</p>
                </div>
                
                <div class="text-center">
                    <div class="w-20 h-20 border rounded overflow-hidden mb-2">
                        <img src="rekomendasi/oval/Pompadour.jpg" alt="Pompadour" class="w-full h-full object-cover"
                             onload="this.parentElement.classList.add('border-green-500')"
                             onerror="this.parentElement.innerHTML='❌'; this.parentElement.classList.add('border-red-500')">
                    </div>
                    <p class="text-xs">Pompadour</p>
                </div>
                
                <!-- Test Round Images -->
                <div class="text-center">
                    <div class="w-20 h-20 border rounded overflow-hidden mb-2">
                        <img src="rekomendasi/round/Two Block Hair.jpg" alt="Two Block Hair" class="w-full h-full object-cover"
                             onload="this.parentElement.classList.add('border-green-500')"
                             onerror="this.parentElement.innerHTML='❌'; this.parentElement.classList.add('border-red-500')">
                    </div>
                    <p class="text-xs">Two Block Hair</p>
                </div>
                
                <div class="text-center">
                    <div class="w-20 h-20 border rounded overflow-hidden mb-2">
                        <img src="rekomendasi/round/Taper Fade.jpg" alt="Taper Fade" class="w-full h-full object-cover"
                             onload="this.parentElement.classList.add('border-green-500')"
                             onerror="this.parentElement.innerHTML='❌'; this.parentElement.classList.add('border-red-500')">
                    </div>
                    <p class="text-xs">Taper Fade</p>
                </div>
                
                <!-- Test Square Images -->
                <div class="text-center">
                    <div class="w-20 h-20 border rounded overflow-hidden mb-2">
                        <img src="rekomendasi/square/Slick Back.jpg" alt="Slick Back" class="w-full h-full object-cover"
                             onload="this.parentElement.classList.add('border-green-500')"
                             onerror="this.parentElement.innerHTML='❌'; this.parentElement.classList.add('border-red-500')">
                    </div>
                    <p class="text-xs">Slick Back</p>
                </div>
                
                <div class="text-center">
                    <div class="w-20 h-20 border rounded overflow-hidden mb-2">
                        <img src="rekomendasi/square/Quiff.jpg" alt="Quiff" class="w-full h-full object-cover"
                             onload="this.parentElement.classList.add('border-green-500')"
                             onerror="this.parentElement.innerHTML='❌'; this.parentElement.classList.add('border-red-500')">
                    </div>
                    <p class="text-xs">Quiff</p>
                </div>
                
            </div>
        </div>
        
        <!-- JavaScript Test -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">JavaScript Image Loading Test</h2>
            <button onclick="testImageLoading()" class="bg-blue-500 text-white px-4 py-2 rounded mb-4">
                Test Image Loading
            </button>
            <div id="jsTestResults" class="space-y-2">
                <!-- Results will appear here -->
            </div>
        </div>
        
        <!-- Simulation of Output.php -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold mb-4">Simulate Output.php Recommendation Cards</h2>
            <button onclick="simulateRecommendationCards()" class="bg-green-500 text-white px-4 py-2 rounded mb-4">
                Simulate Recommendation Cards
            </button>
            <div id="simulationResults" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Simulation results will appear here -->
            </div>
        </div>
    </div>

    <script>
        // Real files yang ada di folder
        const realFiles = {
            'oval': [
                'Buzz Haircut.jpg',
                'Caesar Cut.jpg', 
                'Classic Undercut.jpg',
                'Haircut.jpg',
                'Long Layered.jpg',
                'Long Shaggy.jpg',
                'Man Bun.jpg',
                'Pompadour.jpg',
                'Textured Crop.jpg'
            ],
            'round': [
                'Buzz Cut.jpg',
                'Comma Hair.jpg',
                'Crop Cut.jpg',
                'Fluffy with Low Fade.jpg',
                'Fringe Haircut.jpg',
                'Taper Fade.jpg',
                'Two Block Hair.jpg'
            ],
            'square': [
                'Comb Over.jpg',
                'Crew Cut.jpg',
                'Faux Hawk.jpg',
                'Gaya Rambut Spike.jpg',
                'Quiff.jpg',
                'Side Swept.jpg',
                'Slick Back.jpg',
                'Wajah Persegi (Square).jpg'
            ]
        };

        function testImageLoading() {
            const resultsDiv = document.getElementById('jsTestResults');
            resultsDiv.innerHTML = '<p class="text-blue-600">Testing image loading...</p>';
            
            const testPaths = [
                'rekomendasi/oval/Textured Crop.jpg',
                'rekomendasi/oval/Pompadour.jpg',
                'rekomendasi/round/Two Block Hair.jpg',
                'rekomendasi/square/Slick Back.jpg'
            ];
            
            let results = [];
            let completed = 0;
            
            testPaths.forEach(path => {
                const img = new Image();
                img.onload = function() {
                    results.push({
                        path: path,
                        status: 'success',
                        width: this.naturalWidth,
                        height: this.naturalHeight
                    });
                    completed++;
                    if (completed === testPaths.length) {
                        displayResults(results);
                    }
                };
                img.onerror = function() {
                    results.push({
                        path: path,
                        status: 'error'
                    });
                    completed++;
                    if (completed === testPaths.length) {
                        displayResults(results);
                    }
                };
                img.src = path;
            });
        }
        
        function displayResults(results) {
            const resultsDiv = document.getElementById('jsTestResults');
            let html = '';
            
            results.forEach(result => {
                const statusClass = result.status === 'success' ? 'text-green-600' : 'text-red-600';
                const statusIcon = result.status === 'success' ? '✅' : '❌';
                const sizeInfo = result.status === 'success' ? ` (${result.width}x${result.height})` : '';
                
                html += `
                    <div class="p-2 border rounded ${result.status === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}">
                        <span class="${statusClass}">${statusIcon} ${result.path}${sizeInfo}</span>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }
        
        function simulateRecommendationCards() {
            const resultsDiv = document.getElementById('simulationResults');
            resultsDiv.innerHTML = '<p class="text-green-600 col-span-full">Creating recommendation cards...</p>';
            
            // Simulate recommendations for square face
            const faceShape = 'square';
            const recommendations = [
                'Slick Back - Rapi ke belakang',
                'Quiff - Volume depan yang stylish', 
                'Crew Cut - Pendek dan profesional'
            ];
            
            setTimeout(() => {
                resultsDiv.innerHTML = '';
                
                recommendations.forEach((rec, index) => {
                    const hairCutName = rec.split(' - ')[0].trim();
                    const description = rec.split(' - ')[1] || 'Gaya rambut yang cocok';
                    
                    // Find best match from real files
                    const availableFiles = realFiles[faceShape] || [];
                    const bestMatch = availableFiles.find(file => 
                        file.toLowerCase().includes(hairCutName.toLowerCase())
                    ) || availableFiles[0];
                    
                    const modelImagePath = bestMatch ? `rekomendasi/${faceShape}/${bestMatch}` : '';
                    const score = Math.max(10 - index, 7);
                    
                    const cardHtml = `
                        <div class="bg-white rounded-lg shadow-md p-4 flex flex-col items-center text-center border">
                            <h3 class="text-sm font-semibold text-gray-800 mb-2">${hairCutName}</h3>
                            <p class="text-xs text-gray-600 mb-3">${description}</p>
                            <div class="flex justify-center items-center gap-2 mb-3">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 rounded-lg overflow-hidden border-2 border-green-500 mb-1">
                                        <img src="${modelImagePath}" alt="Model ${hairCutName}" class="w-full h-full object-cover"
                                             onload="console.log('✅ Image loaded:', '${modelImagePath}'); this.parentElement.classList.add('border-green-600')"
                                             onerror="console.log('❌ Image failed:', '${modelImagePath}'); this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjMjJjNTVlIi8+Cjx0ZXh0IHg9IjMyIiB5PSIyOCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE2IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+8J+RpDwvdGV4dD4KPHRleHQgeD0iMzIiIHk9IjQ0IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iOCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPk1vZGVsPC90ZXh0Pgo8L3N2Zz4K'; this.parentElement.classList.add('border-red-500')">
                                    </div>
                                    <p class="text-gray-600 text-xs font-medium">Model</p>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 rounded-lg overflow-hidden border-2 border-blue-500 mb-1 bg-blue-100 flex items-center justify-center">
                                        <span class="text-blue-600 text-xs">You</span>
                                    </div>
                                    <p class="text-gray-600 text-xs font-medium">Anda</p>
                                </div>
                            </div>
                            <div class="w-full">
                                <p class="text-gray-700 font-medium text-xs">Skor <span class="text-green-600">${score}/10</span></p>
                                <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                    <div class="bg-green-500 h-1.5 rounded-full" style="width: ${score * 10}%"></div>
                                </div>
                                <span class="inline-block bg-yellow-400 text-gray-800 text-xs font-semibold px-2 py-0.5 rounded-full mt-2">Rekomendasi</span>
                            </div>
                        </div>
                    `;
                    resultsDiv.insertAdjacentHTML('beforeend', cardHtml);
                });
            }, 1000);
        }
        
        // Auto-run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🖼️ Simple image test page loaded');
            console.log('📁 Testing paths from rekomendasi folder');
        });
    </script>
</body>
</html>
