-- Update database structure for user authentication and profile management
-- Run this SQL to ensure all required columns exist

-- Add missing columns to pembeli table if they don't exist
ALTER TABLE pembeli
ADD COLUMN IF NOT EXISTS password VARCHAR(255),
ADD COLUMN IF NOT EXISTS alamat TEXT,
ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(255),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Update existing records to have default values for new columns
UPDATE pembeli 
SET alamat = COALESCE(alamat, ''), 
    updated_at = COALESCE(updated_at, NOW())
WHERE alamat IS NULL OR updated_at IS NULL;

-- Create index on email for faster login queries
CREATE INDEX IF NOT EXISTS idx_pembeli_email ON pembeli(email);

-- Show current structure
DESCRIBE pembeli;
