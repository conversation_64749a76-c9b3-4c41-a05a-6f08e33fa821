<?php
session_start();

// Check if job application is in session
if (!isset($_SESSION['job_application'])) {
    header("Location: jobs.php");
    exit();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Store form data in session
    $_SESSION['application_data'] = [
        'personal' => [
            'full_name' => $_POST['full_name'],
            'email' => $_POST['email'],
            'phone' => $_POST['phone'],
            'address' => $_POST['address'],
            'city' => $_POST['city']
        ],
        'professional' => [
            'experience' => $_POST['experience'],
            'license' => $_POST['license'],
            'skills' => $_POST['skills']
        ],
        'availability' => [
            'start_date' => $_POST['start_date'],
            'days_available' => isset($_POST['days_available']) ? $_POST['days_available'] : []
        ],
        'cover_letter' => $_POST['cover_letter']
    ];
    
    // Process file uploads (in a real app, you would save these to a server directory)
    if (isset($_FILES['resume']) && $_FILES['resume']['error'] === UPLOAD_ERR_OK) {
        $_SESSION['application_data']['files']['resume'] = $_FILES['resume']['name'];
    }
    
    if (isset($_FILES['portfolio']) && $_FILES['portfolio']['error'] === UPLOAD_ERR_OK) {
        $_SESSION['application_data']['files']['portfolio'] = $_FILES['portfolio']['name'];
    }
    
    // Redirect to quiz page
    header("Location: barber_quiz.php");
    exit();
}

// If not a POST request, redirect back
header("Location: application_form.php");
exit();
?>