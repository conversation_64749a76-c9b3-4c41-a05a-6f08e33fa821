<?php
require_once 'db_config.php';

// Booking data (from data_Booking.php)
function getBookingToday($conn, $location_id = null) {
    $where = $location_id ? "AND location_id=$location_id" : "";
    $today = date('Y-m-d');
    $stmt = $conn->query("SELECT COUNT(*) FROM bookings WHERE DATE(booking_time)='$today' $where");
    return $stmt->fetchColumn();
}

function getTotalTransaksi($conn, $location_id = null) {
    $where = $location_id ? "AND location_id=$location_id" : "";
    $stmt = $conn->query("SELECT COUNT(*) FROM bookings WHERE 1 $where");
    return $stmt->fetchColumn();
}

function getPendapatan($conn, $location_id = null) {
    $where = $location_id ? "AND location_id=$location_id" : "";
    $stmt = $conn->query("SELECT SUM(service_price) FROM bookings WHERE status='Confirmed' $where");
    return $stmt->fetchColumn();
}

// Location data (from Kelola_lokasi.php)
function getAllLocations($conn) {
    $stmt = $conn->query("SELECT id, name FROM locations ORDER BY id ASC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Barber data (from kelola_barber.php)
function getBarberAktif($conn, $location_id = null) {
    $where = $location_id ? "WHERE location_id=$location_id" : "";
    $stmt = $conn->query("SELECT COUNT(*) FROM barbers $where");
    return $stmt->fetchColumn();
} 