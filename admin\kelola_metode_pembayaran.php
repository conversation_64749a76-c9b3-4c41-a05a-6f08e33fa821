<?php
session_start();
require_once 'db_config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Path for QR code uploads
$upload_dir = "../uploads/payment_methods/";

// Handle Add/Update Payment Method
if (isset($_POST['add_payment_method']) || isset($_POST['update_payment_method'])) {
    $id = $_POST['id'] ?? null;
    $name = $_POST['name'] ?? '';
    $type = $_POST['type'] ?? '';
    $old_qr_code = $_POST['old_qr_code'] ?? '';
    $qr_code_image = $_POST['qr_code_image'] ?? ''; // Now it's text input

    try {
        // No file upload handling needed for QR code image as it's now a text input.
        // If adding new and no code uploaded
        if (!isset($id) && empty($qr_code_image)) { 
             throw new Exception("Kode Pembayaran wajib diisi untuk metode pembayaran baru.");
        }

        if (isset($_POST['add_payment_method'])) {
            $stmt = $conn->prepare("INSERT INTO payment_methods (name, type, qr_code_image) VALUES (:name, :type, :qr_code_image)");
            $stmt->execute([':name' => $name, ':type' => $type, ':qr_code_image' => $qr_code_image]);
            $message = "Metode pembayaran berhasil ditambahkan.";
        } else { // Update
            $stmt = $conn->prepare("UPDATE payment_methods SET name = :name, type = :type, qr_code_image = :qr_code_image WHERE id = :id");
            $stmt->execute([':id' => $id, ':name' => $name, ':type' => $type, ':qr_code_image' => $qr_code_image]);
            $message = "Metode pembayaran berhasil diperbarui.";
        }

        header("Location: kelola_metode_pembayaran.php?success=" . urlencode($message));
        exit();

    } catch (Exception $e) {
        header("Location: kelola_metode_pembayaran.php?error=" . urlencode($e->getMessage()));
        exit();
    }
}

// Handle Delete Payment Method
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id = $_GET['id'];
    try {
        // No QR code image file to delete from disk as it's now a text input
        // The database record will still be deleted.

        $stmt = $conn->prepare("DELETE FROM payment_methods WHERE id = :id");
        $stmt->execute([':id' => $id]);
        header("Location: kelola_metode_pembayaran.php?success=" . urlencode("Metode pembayaran berhasil dihapus."));
        exit();
    } catch (Exception $e) {
        header("Location: kelola_metode_pembayaran.php?error=" . urlencode($e->getMessage()));
        exit();
    }
}

// Fetch all payment methods
$stmt = $conn->prepare("SELECT * FROM payment_methods ORDER BY name ASC");
$stmt->execute();
$payment_methods = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Metode Pembayaran - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            background: #2d3748;
            color: #fff;
        }
        .sidebar .logo {
            background: #1a202c;
            padding: 1rem;
        }
        .sidebar .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .sidebar nav ul li {
            transition: all 0.3s ease;
        }
        .sidebar nav ul li a {
            color: #fff;
        }
        .sidebar nav ul li.active {
            background:rgb(35, 71, 250);
        }
        .sidebar nav ul li.active a {
            color: #fff;
        }
        .sidebar nav ul li i {
            color: #4299e1;
        }
        .sidebar nav ul li.active i {
            color: #fff;
        }
    </style>
</head>
<body class="bg-white">
    <div class="flex h-screen bg-white">
        <!-- Sidebar -->
        <aside class="w-64 sidebar">
            <div class="logo">
                <h1 class="text-2xl font-bold">Admin Panel</h1>
            </div>
            <nav class="mt-4">
                <ul>
                    <li class="px-4 py-3">
                        <a href="index.php" class="flex items-center space-x-3">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <!-- Manajemen Booking Menu -->
                    <li>
                        <button onclick="toggleBookingSubmenu()" class="w-full flex items-center justify-between px-4 py-3 rounded-lg text-white focus:outline-none">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-calendar-check"></i>
                                <span>Manajemen Booking</span>
                            </span>
                            <i id="arrowBookingIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>

                        <!-- Submenu -->
                        <ul id="bookingSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="data_booking.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Data Booking</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_lokasi.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Kelola Lokasi</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_barber.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cut"></i>
                                    <span>Kelola Barber</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <script>
                        function toggleBookingSubmenu() {
                            const submenu = document.getElementById('bookingSubmenu');
                            const icon = document.getElementById('arrowBookingIcon');
                            submenu.classList.toggle('hidden');
                            icon.classList.toggle('rotate-180');
                        }
                    </script>

                    <li class="mb-2">
                        <button onclick="toggleSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-box"></i>
                                <span>Manajemen Produk</span>
                            </span>
                            <i id="arrowIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="submenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_produk.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cube"></i>
                                    <span>Produk</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_pembeli.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pembeli</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_metode_pembayaran.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-money-check-alt"></i>
                                    <span>Metode Pembayaran</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="mb-2">
                        <button onclick="toggleLowonganSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-briefcase"></i>
                                <span>Manajemen Lowongan</span>
                            </span>
                            <i id="arrowLowonganIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="lowonganSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_lowongan.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Kelola Lowongan</span>
                                </a>
                            </li>
                            <li>
                                <a href="data_pelamar.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-user-tie"></i>
                                    <span>Data Pelamar</span>
                                </a>
                            </li>
                            <li>
                                <a href="whatsapp_manager.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>WhatsApp Manager</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="px-4 py-3">
                        <a href="kelola_admin.php" class="flex items-center space-x-3">
                            <i class="fas fa-user-shield"></i>
                            <span>Kelola Admin</span>
                        </a>
                    </li>

                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col">
            <header class="flex items-center justify-between px-6 py-4 bg-white border-b-4 border-indigo-600">
                <div class="flex items-center">
                    <button class="text-gray-500 focus:outline-none">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-xl font-semibold ml-4">Kelola Metode Pembayaran</h1>
                </div>
                <div class="flex items-center">
                    <span class="mr-4 text-gray-700 hidden sm:block"><?php echo $_SESSION['admin_full_name'] ?? 'Admin'; ?></span>
                    <div class="relative">
                        <button id="profileDropdown" class="flex items-center focus:outline-none">
                            <?php
                            // Get admin profile photo
                            $stmt = $conn->prepare("SELECT profile_photo FROM admin WHERE id = ?");
                            $stmt->execute([$_SESSION['admin_id']]);
                            $profile_photo = $stmt->fetchColumn();

                            if (!empty($profile_photo) && file_exists('uploads/profiles/' . $profile_photo)):
                            ?>
                                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-300 hover:border-blue-500 transition-colors"
                                     src="uploads/profiles/<?= htmlspecialchars($profile_photo) ?>"
                                     alt="Admin avatar">
                            <?php else: ?>
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-300 hover:border-blue-500 transition-colors">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                            <?php endif; ?>
                            <i class="fas fa-chevron-down ml-2 text-gray-500 text-sm"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="profileDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="profile_settings.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-3 text-gray-500"></i>
                                Profile Settings
                            </a>
                            <hr class="my-1">
                            <a href="logout.php" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-3 text-red-500"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200 p-6">
                <!-- Success/Error Message Display -->
                <?php if (isset($_GET['success'])): ?>
                    <div id="success-message" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                        <span class="block sm:inline"><?php echo htmlspecialchars($_GET['success']); ?></span>
                    </div>
                    <script>
                        // Remove success parameter from URL without refreshing
                        window.history.replaceState({}, document.title, window.location.pathname);
                        
                        // Auto hide success message after 5 seconds
                        setTimeout(function() {
                            const successMessage = document.getElementById('success-message');
                            if (successMessage) {
                                successMessage.style.transition = 'opacity 0.5s ease-in-out';
                                successMessage.style.opacity = '0';
                                setTimeout(() => successMessage.remove(), 500);
                            }
                        }, 5000);
                    </script>
                <?php elseif (isset($_GET['error'])): ?>
                    <div id="error-message" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                        <strong class="font-bold">Error!</strong>
                        <span class="block sm:inline"><?php echo htmlspecialchars($_GET['error']); ?></span>
                        <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                            <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.03a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.03a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                        </span>
                    </div>
                <?php endif; ?>

                <!-- Add Payment Method Button -->
                <div class="mb-4">
                    <button id="openAddPaymentMethodModalBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg flex items-center space-x-2">
                        <i class="fas fa-plus"></i>
                        <span>Tambah Metode Pembayaran</span>
                    </button>
                </div>

                <!-- Add Payment Method Modal -->
                <div id="addPaymentMethodModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
                    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4 relative">
                        <button onclick="closeAddPaymentMethodModal()" class="absolute top-3 right-3 text-gray-600 hover:text-gray-900 text-2xl font-bold">&times;</button>
                        <h2 class="text-2xl font-semibold mb-4">Tambah Metode Pembayaran Baru</h2>
                        <form action="kelola_metode_pembayaran.php" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="add_payment_method" value="1">
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="name">Nama Metode</label>
                                <input type="text" id="name" name="name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="type">Tipe Metode</label>
                                <select id="type" name="type" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                                    <option value="">Pilih Tipe</option>
                                    <option value="M-banking">M-banking</option>
                                    <option value="E-wallet">E-wallet</option>
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="qr_code_image">Kode Pembayaran</label>
                                <input type="text" id="qr_code_image" name="qr_code_image" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                            </div>
                            <div class="flex items-center justify-between">
                                <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                    Tambah Metode
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Edit Payment Method Modal -->
                <div id="editPaymentMethodModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
                    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4 relative">
                        <button onclick="closeEditPaymentMethodModal()" class="absolute top-3 right-3 text-gray-600 hover:text-gray-900 text-2xl font-bold">&times;</button>
                        <h2 class="text-2xl font-semibold mb-4">Edit Metode Pembayaran</h2>
                        <form id="editPaymentMethodForm" action="kelola_metode_pembayaran.php" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="update_payment_method" value="1">
                            <input type="hidden" id="edit_id" name="id">
                            <input type="hidden" id="edit_old_qr_code" name="old_qr_code">

                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="edit_name">Nama Metode</label>
                                <input type="text" id="edit_name" name="name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="edit_type">Tipe Metode</label>
                                <select id="edit_type" name="type" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                                    <option value="">Pilih Tipe</option>
                                    <option value="M-banking">M-banking</option>
                                    <option value="E-wallet">E-wallet</option>
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="edit_qr_code_image">Kode Pembayaran</label>
                                <input type="text" id="edit_qr_code_image" name="qr_code_image" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            </div>
                            <div class="flex justify-end mt-4">
                                <button type="button" onclick="closeEditPaymentMethodModal()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">Batal</button>
                                <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">Simpan Perubahan</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Payment Method List Section -->
                <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
                <h2 class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600">Daftar Metode Pembayaran</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border-collapse border">
                            <thead>
                                <tr>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">NO</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Nama Metode</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Tipe</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Kode Pembayaran</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($payment_methods)): ?>
                                    <?php $no = 1; // Initialize counter ?>
                                    <?php foreach ($payment_methods as $method): ?>
                                        <tr>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo $no++; ?></td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo htmlspecialchars($method['name']); ?></td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo htmlspecialchars($method['type']); ?></td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700">
                                                <?php echo htmlspecialchars($method['qr_code_image']); // Display as text ?>
                                            </td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700">
                                                <button onclick="openEditPaymentMethodModal(<?php echo htmlspecialchars(json_encode($method)); ?>)" class="bg-yellow-500 hover:bg-yellow-700 text-white text-xs font-bold py-1 px-2 rounded mr-1">Edit</button>
                                                <a href="kelola_metode_pembayaran.php?action=delete&id=<?php echo $method['id']; ?>" onclick="return confirm('Apakah Anda yakin ingin menghapus metode pembayaran ini?');" class="bg-red-500 hover:bg-red-700 text-white text-xs font-bold py-1 px-2 rounded">Hapus</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="py-4 text-center text-gray-500 border">
                                            Tidak ada metode pembayaran yang tersedia.
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

<script>
    function toggleSubmenu() {
        const submenu = document.getElementById('submenu');
        const icon = document.getElementById('arrowIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleLowonganSubmenu() {
        const submenu = document.getElementById('lowonganSubmenu');
        const icon = document.getElementById('arrowLowonganIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleBookingSubmenu() {
        const submenu = document.getElementById('bookingSubmenu');
        const icon = document.getElementById('arrowBookingIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Handle success/error message close
        const alertCloseButtons = document.querySelectorAll('.bg-green-100 svg, .bg-red-100 svg');
        alertCloseButtons.forEach(button => {
            button.addEventListener('click', function() {
                this.closest('[role="alert"]').remove();
            });
        });

        // Auto-hide success/error messages after 5 seconds
        const successAlert = document.getElementById('success-message');
        const errorAlert = document.getElementById('error-message');

        if (successAlert) {
            setTimeout(() => {
                successAlert.style.transition = 'opacity 0.5s ease-out';
                successAlert.style.opacity = '0';
                setTimeout(() => successAlert.remove(), 500); // Remove after transition
            }, 5000);
        }

        if (errorAlert) {
            setTimeout(() => {
                errorAlert.style.transition = 'opacity 0.5s ease-out';
                errorAlert.style.opacity = '0';
                setTimeout(() => errorAlert.remove(), 500); // Remove after transition
            }, 5000);
        }

        // Add Payment Method Modal Logic
        const addPaymentMethodModal = document.getElementById('addPaymentMethodModal');
        const openAddPaymentMethodModalBtn = document.getElementById('openAddPaymentMethodModalBtn');
        
        openAddPaymentMethodModalBtn.addEventListener('click', function() {
            addPaymentMethodModal.classList.remove('hidden');
        });

        window.closeAddPaymentMethodModal = function() {
            addPaymentMethodModal.classList.add('hidden');
            // Optionally clear form fields
            document.getElementById('name').value = '';
            document.getElementById('type').value = '';
            document.getElementById('qr_code_image').value = '';
        };

        // Close modal when clicking outside of it
        addPaymentMethodModal.addEventListener('click', function(e) {
            if (e.target === addPaymentMethodModal) {
                closeAddPaymentMethodModal();
            }
        });

        // Edit Payment Method Modal Logic
        const editPaymentMethodModal = document.getElementById('editPaymentMethodModal');

        window.openEditPaymentMethodModal = function(method) {
            document.getElementById('edit_id').value = method.id;
            document.getElementById('edit_name').value = method.name;
            document.getElementById('edit_type').value = method.type;
            document.getElementById('edit_old_qr_code').value = method.qr_code_image;
            editPaymentMethodModal.classList.remove('hidden');
        };

        window.closeEditPaymentMethodModal = function() {
            editPaymentMethodModal.classList.add('hidden');
            // Optionally clear form fields
            document.getElementById('edit_name').value = '';
            document.getElementById('edit_type').value = '';
            document.getElementById('edit_qr_code_image').value = '';
            document.getElementById('edit_old_qr_code').value = '';
        };

        // Close modal when clicking outside of it
        editPaymentMethodModal.addEventListener('click', function(e) {
            if (e.target === editPaymentMethodModal) {
                closeEditPaymentMethodModal();
            }
        });

        // Set active class for current page in sidebar
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.sidebar nav ul li a');
        navLinks.forEach(link => {
            // Remove active class from all direct links initially to ensure only one is active
            link.closest('li').classList.remove('active');

            if (currentPath.includes(link.getAttribute('href'))) { // Use includes for partial match
                link.closest('li').classList.add('active'); // Make the specific link active

                // If this active link is inside a submenu, open the submenu and activate its parent button's LI
                const parentUl = link.closest('ul');
                if (parentUl && (parentUl.id === 'submenu' || parentUl.id === 'bookingSubmenu' || parentUl.id === 'lowonganSubmenu')) {
                    parentUl.classList.remove('hidden'); // Show the submenu

                    // Get the button element that controls this submenu
                    const parentButton = parentUl.previousElementSibling;
                    if (parentButton) {
                        parentButton.querySelector('i').classList.add('rotate-180'); // Rotate arrow for parent button
                        // IMPORTANT: Ensure the parent LI (which contains the button) does NOT get the 'active' background
                        const parentLiWithButton = parentButton.closest('li');
                        if (parentLiWithButton) {
                            parentLiWithButton.classList.remove('active');
                        }
                    }
                }
            }
        });

        // Profile dropdown functionality
        const profileDropdown = document.getElementById('profileDropdown');
        const profileDropdownMenu = document.getElementById('profileDropdownMenu');

        if (profileDropdown && profileDropdownMenu) {
            profileDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdownMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdownMenu.classList.add('hidden');
                }
            });
        }
    });
</script>
</body>

</html>