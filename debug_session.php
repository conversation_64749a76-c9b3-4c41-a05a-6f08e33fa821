<?php
session_start();

echo "<h2>Session Debug Information</h2>";

echo "<h3>Session Status:</h3>";
echo "Session ID: " . session_id() . "<br>";
echo "Session Status: " . session_status() . "<br>";

echo "<h3>Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Cookies:</h3>";
echo "<pre>";
print_r($_COOKIE);
echo "</pre>";

echo "<h3>Server Info:</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";

echo "<h3>Upload Settings:</h3>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "post_max_size: " . ini_get('post_max_size') . "<br>";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "<br>";
echo "file_uploads: " . (ini_get('file_uploads') ? 'Enabled' : 'Disabled') . "<br>";

echo "<h3>Directory Check:</h3>";
$upload_dir = 'uploads/profile/';
echo "Upload directory: " . $upload_dir . "<br>";
echo "Directory exists: " . (is_dir($upload_dir) ? 'Yes' : 'No') . "<br>";
echo "Directory writable: " . (is_writable($upload_dir) ? 'Yes' : 'No') . "<br>";

if (!is_dir($upload_dir)) {
    echo "Creating directory...<br>";
    if (mkdir($upload_dir, 0755, true)) {
        echo "Directory created successfully<br>";
    } else {
        echo "Failed to create directory<br>";
    }
}

echo "<h3>Quick Actions:</h3>";
echo "<a href='login.php'>Login</a> | ";
echo "<a href='profil.php'>Profile</a> | ";
echo "<a href='test_upload.html'>Test Upload</a> | ";
echo "<a href='index.php'>Home</a>";
?>
