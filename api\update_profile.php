<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../Admin/db_config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$response = [
    'success' => false,
    'message' => ''
];

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $response['message'] = 'User not logged in';
    echo json_encode($response);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Method not allowed';
    echo json_encode($response);
    exit();
}

$user_id = $_SESSION['user_id'];

// Get POST data
$nama_pembeli = trim($_POST['nama_pembeli'] ?? '');
$email = trim($_POST['email'] ?? '');
$phone = trim($_POST['phone'] ?? '');
$alamat = trim($_POST['alamat'] ?? '');

// Validation
if (empty($nama_pembeli) || empty($email) || empty($phone)) {
    $response['message'] = 'Nama, email, dan nomor telepon harus diisi';
    echo json_encode($response);
    exit();
}

if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $response['message'] = 'Format email tidak valid';
    echo json_encode($response);
    exit();
}

try {
    // First ensure the table has the required columns
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS alamat TEXT");
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(255)");
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");

    // Check if email is already used by another user
    $check_stmt = $conn->prepare("SELECT id FROM pembeli WHERE email = ? AND id != ?");
    $check_stmt->execute([$email, $user_id]);

    if ($check_stmt->fetch()) {
        $response['message'] = 'Email sudah digunakan oleh pengguna lain';
        echo json_encode($response);
        exit();
    }

    // Update user profile
    $update_stmt = $conn->prepare("
        UPDATE pembeli
        SET nama_pembeli = ?, email = ?, phone = ?, alamat = ?, updated_at = NOW()
        WHERE id = ?
    ");

    $update_stmt->execute([$nama_pembeli, $email, $phone, $alamat, $user_id]);
    
    // Update session data
    $_SESSION['user_name'] = $nama_pembeli;
    $_SESSION['user_email'] = $email;
    
    // Update cookies
    setcookie('user_login', $nama_pembeli, time() + (86400 * 30), '/', '', false, true);
    
    $response['success'] = true;
    $response['message'] = 'Profil berhasil diperbarui';
    
} catch (PDOException $e) {
    $response['message'] = 'Terjadi kesalahan sistem. Silakan coba lagi.';
    error_log("Update profile error: " . $e->getMessage());
}

echo json_encode($response);
?>
