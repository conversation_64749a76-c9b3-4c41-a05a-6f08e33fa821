import cv2
import numpy as np
import dlib
import json
import sys
import os
from PIL import Image, ImageFilter, ImageEnhance
import mediapipe as mp
from scipy.spatial.distance import euclidean
from scipy.spatial import ConvexHull
import base64
from io import BytesIO

class Advanced3DFaceProcessor:
    def __init__(self):
        # Initialize MediaPipe Face Mesh
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5
        )
        
        # Initialize dlib face detector and predictor
        self.detector = dlib.get_frontal_face_detector()
        try:
            self.predictor = dlib.shape_predictor('models/shape_predictor_68_face_landmarks.dat')
        except:
            self.predictor = None
            
        # 3D face model template points
        self.face_3d_model = np.array([
            [0.0, 0.0, 0.0],           # Nose tip
            [0.0, -330.0, -65.0],     # <PERSON>
            [-225.0, 170.0, -135.0],  # Left eye left corner
            [225.0, 170.0, -135.0],   # Right eye right corner
            [-150.0, -150.0, -125.0], # Left mouth corner
            [150.0, -150.0, -125.0],  # Right mouth corner
        ], dtype=np.float64)
        
        # Hair region definitions for different styles
        self.hair_regions = {
            'buzz_cut': {'top': 0.15, 'sides': 0.05, 'back': 0.1},
            'pompadour': {'top': 0.4, 'sides': 0.15, 'back': 0.25},
            'quiff': {'top': 0.35, 'sides': 0.1, 'back': 0.2},
            'undercut': {'top': 0.3, 'sides': 0.02, 'back': 0.05},
            'fade': {'top': 0.25, 'sides': 0.08, 'back': 0.12},
            'long_hair': {'top': 0.5, 'sides': 0.4, 'back': 0.45}
        }
    
    def detect_face_landmarks_mediapipe(self, image):
        """Detect face landmarks using MediaPipe"""
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.face_mesh.process(rgb_image)
        
        if results.multi_face_landmarks:
            landmarks = results.multi_face_landmarks[0]
            h, w = image.shape[:2]
            
            # Convert normalized coordinates to pixel coordinates
            face_landmarks = []
            for landmark in landmarks.landmark:
                x = int(landmark.x * w)
                y = int(landmark.y * h)
                z = landmark.z
                face_landmarks.append([x, y, z])
            
            return np.array(face_landmarks)
        return None
    
    def detect_face_landmarks_dlib(self, image):
        """Detect face landmarks using dlib"""
        if not self.predictor:
            return None
            
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.detector(gray)
        
        if len(faces) > 0:
            landmarks = self.predictor(gray, faces[0])
            points = []
            for i in range(68):
                points.append([landmarks.part(i).x, landmarks.part(i).y, 0])
            return np.array(points)
        return None
    
    def create_3d_depth_map(self, image, landmarks, hair_style):
        """Create 3D depth map from face landmarks"""
        h, w = image.shape[:2]
        depth_map = np.zeros((h, w), dtype=np.float32)
        
        if landmarks is None:
            return depth_map
        
        # Create face mask from landmarks
        if len(landmarks) > 68:  # MediaPipe landmarks
            # Use key facial points for depth mapping
            key_points = landmarks[[10, 152, 234, 454, 172, 397]]  # Key face contour points
        else:  # dlib landmarks
            key_points = landmarks[[30, 8, 36, 45, 48, 54]]  # Nose, chin, eyes, mouth
        
        # Create depth based on facial structure
        for i, point in enumerate(key_points):
            x, y = int(point[0]), int(point[1])
            if 0 <= x < w and 0 <= y < h:
                # Different depth values for different facial features
                depth_values = [0.8, 0.2, 0.6, 0.6, 0.4, 0.4]  # Nose highest, chin lowest
                depth = depth_values[i] if i < len(depth_values) else 0.5
                
                # Create circular depth region around each point
                radius = 30
                y_coords, x_coords = np.ogrid[:h, :w]
                mask = (x_coords - x)**2 + (y_coords - y)**2 <= radius**2
                depth_map[mask] = np.maximum(depth_map[mask], depth * np.exp(-((x_coords[mask] - x)**2 + (y_coords[mask] - y)**2) / (2 * (radius/2)**2)))
        
        # Smooth the depth map
        depth_map = cv2.GaussianBlur(depth_map, (15, 15), 0)
        return depth_map
    
    def extract_hair_with_3d_analysis(self, model_image, hair_style):
        """Extract hair region with 3D analysis"""
        # Detect landmarks in model image
        landmarks = self.detect_face_landmarks_mediapipe(model_image)
        if landmarks is None:
            landmarks = self.detect_face_landmarks_dlib(model_image)
        
        if landmarks is None:
            # Fallback to simple region extraction
            return self.simple_hair_extraction(model_image, hair_style)
        
        h, w = model_image.shape[:2]
        
        # Create hair mask based on landmarks and style
        hair_mask = np.zeros((h, w), dtype=np.uint8)
        
        # Get hair region parameters
        hair_params = self.hair_regions.get(hair_style.lower(), self.hair_regions['pompadour'])
        
        # Define hair region based on face landmarks
        if len(landmarks) > 68:  # MediaPipe
            # Use forehead and temple points
            forehead_points = landmarks[[10, 151, 9, 10, 151, 337, 299, 333, 298]]
            top_y = int(np.min(forehead_points[:, 1]) - h * hair_params['top'])
            
            # Create hair region
            hair_region = np.array([
                [int(w * 0.1), top_y],
                [int(w * 0.9), top_y],
                [int(w * 0.95), int(h * 0.4)],
                [int(w * 0.05), int(h * 0.4)]
            ])
        else:  # dlib
            # Use face outline points
            face_top = int(np.min(landmarks[:17, 1]) - h * hair_params['top'])
            hair_region = np.array([
                [int(w * 0.1), face_top],
                [int(w * 0.9), face_top],
                [int(w * 0.9), int(h * 0.4)],
                [int(w * 0.1), int(h * 0.4)]
            ])
        
        cv2.fillPoly(hair_mask, [hair_region], 255)
        
        # Refine mask using color segmentation
        hsv = cv2.cvtColor(model_image, cv2.COLOR_BGR2HSV)
        
        # Hair color ranges
        hair_ranges = [
            ([0, 0, 0], [180, 255, 50]),      # Black/Dark
            ([8, 50, 20], [25, 255, 200]),   # Brown
            ([15, 30, 100], [35, 255, 255])  # Blonde
        ]
        
        color_mask = np.zeros((h, w), dtype=np.uint8)
        for lower, upper in hair_ranges:
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            color_mask = cv2.bitwise_or(color_mask, mask)
        
        # Combine region and color masks
        final_mask = cv2.bitwise_and(hair_mask, color_mask)
        
        # Clean up mask
        kernel = np.ones((5, 5), np.uint8)
        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_CLOSE, kernel)
        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)
        
        # Extract hair
        hair_extracted = cv2.bitwise_and(model_image, model_image, mask=final_mask)
        
        return hair_extracted, final_mask
    
    def simple_hair_extraction(self, model_image, hair_style):
        """Simple fallback hair extraction"""
        h, w = model_image.shape[:2]
        hair_height = int(h * 0.4)
        hair_region = model_image[:hair_height, :]
        
        mask = np.zeros((h, w), dtype=np.uint8)
        mask[:hair_height, :] = 255
        
        return hair_region, mask
    
    def apply_3d_hair_to_face(self, user_image, hair_extracted, hair_mask, face_shape, hair_style):
        """Apply extracted hair to user face with 3D positioning"""
        # Detect user face landmarks
        user_landmarks = self.detect_face_landmarks_mediapipe(user_image)
        if user_landmarks is None:
            user_landmarks = self.detect_face_landmarks_dlib(user_image)
        
        h, w = user_image.shape[:2]
        result = user_image.copy()
        
        # Calculate hair positioning based on face shape and landmarks
        if user_landmarks is not None:
            # Get face dimensions
            if len(user_landmarks) > 68:  # MediaPipe
                face_width = np.max(user_landmarks[:, 0]) - np.min(user_landmarks[:, 0])
                face_top = int(np.min(user_landmarks[:, 1]))
            else:  # dlib
                face_width = np.max(user_landmarks[:, 0]) - np.min(user_landmarks[:, 0])
                face_top = int(np.min(user_landmarks[:17, 1]))
        else:
            # Fallback positioning
            face_width = w * 0.6
            face_top = int(h * 0.2)
        
        # Face shape specific adjustments
        shape_adjustments = {
            'oval': {'scale': 0.85, 'y_offset': -0.05, 'x_offset': 0},
            'round': {'scale': 0.9, 'y_offset': -0.08, 'x_offset': 0},
            'square': {'scale': 0.8, 'y_offset': -0.03, 'x_offset': 0},
            'heart': {'scale': 0.75, 'y_offset': -0.02, 'x_offset': 0},
            'rectangular': {'scale': 0.85, 'y_offset': -0.06, 'x_offset': 0}
        }
        
        adj = shape_adjustments.get(face_shape.lower(), shape_adjustments['oval'])
        
        # Scale and position hair
        hair_h, hair_w = hair_extracted.shape[:2]
        new_width = int(face_width * adj['scale'])
        new_height = int(hair_h * (new_width / hair_w))
        
        # Resize hair
        hair_resized = cv2.resize(hair_extracted, (new_width, new_height))
        mask_resized = cv2.resize(hair_mask, (new_width, new_height))
        
        # Position hair
        start_x = max(0, int((w - new_width) / 2 + w * adj['x_offset']))
        start_y = max(0, int(face_top + h * adj['y_offset']))
        end_x = min(w, start_x + new_width)
        end_y = min(h, start_y + new_height)
        
        # Adjust dimensions if needed
        actual_width = end_x - start_x
        actual_height = end_y - start_y
        
        if actual_width > 0 and actual_height > 0:
            hair_final = cv2.resize(hair_resized, (actual_width, actual_height))
            mask_final = cv2.resize(mask_resized, (actual_width, actual_height))
            
            # Create smooth blending
            mask_final = mask_final.astype(np.float32) / 255.0
            mask_final = cv2.GaussianBlur(mask_final, (5, 5), 0)
            
            # Apply hair with blending
            for c in range(3):
                result[start_y:end_y, start_x:end_x, c] = (
                    mask_final * hair_final[:, :, c] +
                    (1 - mask_final) * result[start_y:end_y, start_x:end_x, c]
                )
        
        return result
    
    def process_3d_face_modeling(self, user_image_path, model_image_path, hair_style, face_shape):
        """Main processing function"""
        try:
            # Load images
            user_img = cv2.imread(user_image_path)
            model_img = cv2.imread(model_image_path)
            
            if user_img is None or model_img is None:
                return {'success': False, 'error': 'Failed to load images'}
            
            # Extract hair from model with 3D analysis
            hair_extracted, hair_mask = self.extract_hair_with_3d_analysis(model_img, hair_style)
            
            # Apply hair to user face
            result = self.apply_3d_hair_to_face(user_img, hair_extracted, hair_mask, face_shape, hair_style)
            
            # Create output directory
            output_dir = 'uploads/3d_results/'
            os.makedirs(output_dir, exist_ok=True)
            
            # Save result
            output_path = os.path.join(output_dir, f'3d_model_{hash(user_image_path + hair_style)}.png')
            cv2.imwrite(output_path, result)
            
            # Convert to base64 for immediate display
            _, buffer = cv2.imencode('.png', result)
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return {
                'success': True,
                'result_url': output_path,
                'result_base64': f"data:image/png;base64,{img_base64}",
                'hair_style': hair_style,
                'face_shape': face_shape,
                'processing_method': '3D_MediaPipe_Analysis'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

def main():
    if len(sys.argv) < 5:
        print(json.dumps({'success': False, 'error': 'Missing parameters'}))
        return
    
    user_image = sys.argv[1]
    model_image = sys.argv[2]
    hair_style = sys.argv[3]
    face_shape = sys.argv[4]
    
    processor = Advanced3DFaceProcessor()
    result = processor.process_3d_face_modeling(user_image, model_image, hair_style, face_shape)
    
    print(json.dumps(result))

if __name__ == "__main__":
    main()