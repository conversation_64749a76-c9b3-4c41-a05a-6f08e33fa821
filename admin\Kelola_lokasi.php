<?php
session_start();
require_once 'db_config.php';

$message = '';
$message_type = ''; // 'success' or 'error'

// Path for image uploads
$upload_dir = "../uploads/locations/";

// Ensure upload directory exists
if (!is_dir($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

// Handle Add Location
if (isset($_POST['add_location'])) {
    $name = trim($_POST['name'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $image_filename = null;

    if (empty($name) || empty($address)) {
        $message = 'Nama dan alamat lokasi tidak boleh kosong.';
        $message_type = 'error';
    } else {
        // Handle image upload
        if (isset($_FILES['location_image']) && $_FILES['location_image']['error'] == UPLOAD_ERR_OK) {
            $file_extension = pathinfo($_FILES['location_image']['name'], PATHINFO_EXTENSION);
            $unique_filename = uniqid() . '.' . $file_extension;
            $target_file = $upload_dir . $unique_filename;

            if (move_uploaded_file($_FILES['location_image']['tmp_name'], $target_file)) {
                $image_filename = $unique_filename;
            } else {
                $message = 'Gagal mengunggah gambar lokasi.';
                $message_type = 'error';
            }
        }

        if ($message_type !== 'error') { // Only proceed if image upload was successful or no image was uploaded
            try {
                $stmt = $conn->prepare("INSERT INTO locations (name, address, image_filename) VALUES (:name, :address, :image_filename)");
                $stmt->execute([
                    'name' => $name,
                    'address' => $address,
                    'image_filename' => $image_filename
                ]);
            $message = 'Lokasi berhasil ditambahkan!';
            $message_type = 'success';
        } catch (PDOException $e) {
            error_log("Add location error: " . $e->getMessage());
            $message = 'Gagal menambahkan lokasi. Silakan coba lagi.';
            $message_type = 'error';

                // If database insert fails, delete the uploaded image
                if ($image_filename && file_exists($upload_dir . $image_filename)) {
                    unlink($upload_dir . $image_filename);
                }
            }
        }
    }
}

// Handle Update Location
if (isset($_POST['update_location'])) {
    $id = $_POST['id'];
    $name = trim($_POST['name'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $old_image_filename = $_POST['old_image_filename'] ?? null;
    $image_filename = $old_image_filename;

    if (empty($name) || empty($address)) {
        $message = 'Nama dan alamat lokasi tidak boleh kosong.';
        $message_type = 'error';
    } else {
        // Handle image upload jika ada file baru
        if (isset($_FILES['location_image']) && $_FILES['location_image']['error'] == UPLOAD_ERR_OK) {
            $file_extension = pathinfo($_FILES['location_image']['name'], PATHINFO_EXTENSION);
            $unique_filename = uniqid() . '.' . $file_extension;
            $target_file = $upload_dir . $unique_filename;

            if (move_uploaded_file($_FILES['location_image']['tmp_name'], $target_file)) {
                // Hapus gambar lama jika ada dan berbeda
                if ($old_image_filename && file_exists($upload_dir . $old_image_filename)) {
                    unlink($upload_dir . $old_image_filename);
                }
                $image_filename = $unique_filename;
            } else {
                $message = 'Gagal mengunggah gambar lokasi.';
                $message_type = 'error';
            }
        }

        if ($message_type !== 'error') {
            try {
                $stmt = $conn->prepare("UPDATE locations SET name = :name, address = :address, image_filename = :image_filename WHERE id = :id");
                $stmt->execute([
                    'name' => $name,
                    'address' => $address,
                    'image_filename' => $image_filename,
                    'id' => $id
                ]);
                $message = 'Lokasi berhasil diupdate!';
                $message_type = 'success';
            } catch (PDOException $e) {
                error_log("Update location error: " . $e->getMessage());
                $message = 'Gagal mengupdate lokasi. Silakan coba lagi.';
                $message_type = 'error';
            }
        }
    }
}

// Handle Delete Location
if (isset($_GET['delete_id'])) {
    $delete_id = $_GET['delete_id'];

    try {
        // Get image filename before deleting location from DB
        $stmt_get_image = $conn->prepare("SELECT image_filename FROM locations WHERE id = :id");
        $stmt_get_image->execute(['id' => $delete_id]);
        $location_to_delete = $stmt_get_image->fetch();

        $stmt = $conn->prepare("DELETE FROM locations WHERE id = :id");
        $stmt->execute(['id' => $delete_id]);
        $message = 'Lokasi berhasil dihapus!';
        $message_type = 'success';

        // Delete the associated image file if it exists
        if ($location_to_delete && !empty($location_to_delete['image_filename'])) {
            $image_path = $upload_dir . $location_to_delete['image_filename'];
            if (file_exists($image_path)) {
                unlink($image_path);
            }
        }

    } catch (PDOException $e) {
        error_log("Delete location error: " . $e->getMessage());
        $message = 'Gagal menghapus lokasi. Pastikan tidak ada data terkait yang bergantung pada lokasi ini.';
        $message_type = 'error';
    }
}

// Fetch all locations
$locations = [];
try {
    $stmt = $conn->query("SELECT * FROM locations ORDER BY id DESC");
    $locations = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Fetch locations error: " . $e->getMessage());
    $message = 'Gagal mengambil data lokasi.';
    $message_type = 'error';
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Lokasi - Pixel Barbershop Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f0f2f5;
        }
        .sidebar {
            background: #2d3748;
            color: #fff;
        }
        .sidebar .logo {
            background: #1a202c;
            padding: 1rem;
        }
        .sidebar .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .sidebar nav ul li {
            transition: all 0.3s ease;
        }
        .sidebar nav ul li a {
            color: #fff;
        }
        .sidebar nav ul li.active {
            background:rgb(35, 71, 250);
        }
        .sidebar nav ul li.active a {
            color: #fff;
        }
        .sidebar nav ul li i {
            color: #4299e1;
        }
        .sidebar nav ul li.active i {
            color: #fff;
        }
        .sidebar nav ul li:hover {
            background: rgba(66, 153, 225, 0.1);
        }
        .sidebar nav ul li.active:hover {
            background: rgb(35, 71, 250);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 50;
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
            }
            .sidebar.open {
                transform: translateX(0);
            }
            .main-content {
                width: 100%;
            }
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 40;
                display: none;
            }
            .overlay.show {
                display: block;
            }
        }

        @media (min-width: 769px) {
            .sidebar {
                position: relative;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile Overlay -->
    <div id="overlay" class="overlay"></div>

    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <aside id="sidebar" class="w-64 sidebar">
            <div class="logo">
                <h1 class="text-2xl font-bold">Admin Panel</h1>
            </div>
            <nav class="mt-4">
                <ul>
                    <li class="px-4 py-3">
                        <a href="index.php" class="flex items-center space-x-3">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <!-- Manajemen Booking Menu -->
                    <li>
                        <button onclick="toggleBookingSubmenu()" class="w-full flex items-center justify-between px-4 py-3 rounded-lg text-white focus:outline-none">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-calendar-check"></i>
                                <span>Manajemen Booking</span>
                            </span>
                            <i id="arrowBookingIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <!-- Submenu -->
                        <ul id="bookingSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="data_booking.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Data Booking</span>
                                </a>
                            </li>
                            <li class="active">
                                <a href="kelola_lokasi.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Kelola Lokasi</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_barber.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cut"></i>
                                    <span>Kelola Barber</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <script>
                        function toggleBookingSubmenu() {
                            const submenu = document.getElementById('bookingSubmenu');
                            const icon = document.getElementById('arrowBookingIcon');
                            submenu.classList.toggle('hidden');
                            icon.classList.toggle('rotate-180');
                        }
                    </script>
                    <li>
                        <button onclick="toggleSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-box"></i>
                                <span>Manajemen Produk</span>
                            </span>
                            <i id="arrowIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="submenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_produk.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cube"></i>
                                    <span>Produk</span>
                                </a>
                            </li>
                            <li>
                                <a href="data_pembeli.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pembeli</span>
                                </a>
                            </li>
                            <li>
                                <a href="metode_pembayaran.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-money-check-alt"></i>
                                    <span>Metode Pembayaran</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="mb-2">
    <button onclick="toggleLowonganSubmenu()" 
        class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
        <span class="flex items-center space-x-3 text-left whitespace-nowrap">
            <i class="fas fa-briefcase text-lg md:text-base"></i>
            <span class="truncate">Kelola Rekrutmen</span>
        </span>
        <i id="arrowLowonganIcon" class="fas fa-chevron-down transition-transform"></i>
    </button>
    <ul id="lowonganSubmenu" class="ml-8 mt-2 space-y-2 hidden">
        <li>
            <a href="kelola_lowongan.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                <i class="fas fa-briefcase"></i>
                <span>Kelola Lowongan</span>
            </a>
        </li>
        <li>
            <a href="data_pelamar.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                <i class="fas fa-user-tie"></i>
                <span>Data Pelamar</span>
            </a>
        </li>
        <li>
            <a href="whatsapp_manager.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                <i class="fab fa-whatsapp"></i>
                <span>WhatsApp Manager</span>
            </a>
        </li>
    </ul>
</li>
                                        <li class="px-4 py-3">
                        <a href="kelola_admin.php" class="flex items-center space-x-3">
                            <i class="fas fa-user-shield"></i>
                            <span>Kelola Admin</span>
                        </a>
                    </li>

                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col">
            <header class="flex items-center justify-between px-6 py-4 bg-white border-b-4 border-indigo-600">
                <div class="flex items-center">
                    <button class="text-gray-500 focus:outline-none">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-xl font-semibold ml-4">Kelola Lokasi</h1>
                </div>
                <div class="flex items-center">
                    <span class="mr-4 text-gray-700 hidden sm:block"><?php echo $_SESSION['admin_full_name'] ?? 'Admin'; ?></span>
                    <div class="relative">
                        <button id="profileDropdown" class="flex items-center focus:outline-none">
                            <?php
                            // Get admin profile photo
                            $stmt = $conn->prepare("SELECT profile_photo FROM admin WHERE id = ?");
                            $stmt->execute([$_SESSION['admin_id']]);
                            $profile_photo = $stmt->fetchColumn();

                            if (!empty($profile_photo) && file_exists('uploads/profiles/' . $profile_photo)):
                            ?>
                                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-300 hover:border-blue-500 transition-colors"
                                     src="uploads/profiles/<?= htmlspecialchars($profile_photo) ?>"
                                     alt="Admin avatar">
                            <?php else: ?>
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-300 hover:border-blue-500 transition-colors">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                            <?php endif; ?>
                            <i class="fas fa-chevron-down ml-2 text-gray-500 text-sm"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="profileDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="profile_settings.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-3 text-gray-500"></i>
                                Profile Settings
                            </a>
                            <hr class="my-1">
                            <a href="logout.php" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-3 text-red-500"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
    </header>

            <!-- Page Content -->
            <main class="flex-1 p-6">
                <div class="max-w-full lg:max-w-screen-xl mx-auto">
    <?php if ($message): ?>
        <div class="<?php echo $message_type === 'success' ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700'; ?> border px-4 py-3 rounded relative mb-4" role="alert">
            <strong class="font-bold">Pesan!</strong>
            <span class="block sm:inline"><?php echo htmlspecialchars($message); ?></span>
        </div>
    <?php endif; ?>

                    <button onclick="toggleAddLocationForm()" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg focus:outline-none focus:shadow-outline mb-4 flex items-center space-x-2">
                        <i class="fas fa-plus"></i>
                        <span>Tambah Lokasi Baru</span>
            </button>

    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600">Daftar Lokasi</h2>
        <?php if (empty($locations)): ?>
            <p class="text-gray-600 text-center">Belum ada lokasi yang ditambahkan.</p>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full leading-normal">
                    <thead>
                        <tr>
                                            <th class="py-2 px-4 border text-left text-xs font-semibold text-white bg-blue-800">ID</th>
                                            <th class="py-2 px-4 border text-left text-xs font-semibold text-white bg-blue-800">Nama</th>
                                            <th class="py-2 px-4 border text-left text-xs font-semibold text-white bg-blue-800">Alamat</th>
                                            <th class="py-2 px-4 border text-left text-xs font-semibold text-white bg-blue-800">Gambar</th>
                                            <th class="py-2 px-4 border text-center text-xs font-semibold text-white bg-blue-800">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                                        <?php $counter = 1; // Initialize counter ?>
                        <?php foreach ($locations as $loc): ?>
                        <tr>
                                            <td class="py-2 px-4 border border-gray-200 bg-white text-sm text-left"><?php echo $counter++; ?></td>
                                            <td class="py-2 px-4 border border-gray-200 bg-white text-sm text-left"><?php echo htmlspecialchars($loc['name']); ?></td>
                                            <td class="py-2 px-4 border border-gray-200 bg-white text-sm break-words whitespace-normal"><?php echo htmlspecialchars($loc['address']); ?></td>
                                            <td class="py-2 px-4 border border-gray-200 bg-white text-sm text-center">
                                                <?php if (!empty($loc['image_filename'])): ?>
                                                    <img src="../uploads/locations/<?php echo htmlspecialchars($loc['image_filename']); ?>" alt="Gambar Lokasi" class="w-20 h-20 object-cover rounded-md">
                                                <?php else: ?>
                                                    Tidak ada gambar
                                                <?php endif; ?>
                                            </td>
                                            <td class="py-2 px-4 border border-gray-200 bg-white text-sm text-center">
                                                <button onclick="openEditLocationModal(<?php echo htmlspecialchars(json_encode($loc)); ?>)" class="bg-yellow-500 hover:bg-yellow-700 text-white text-xs font-bold py-1 px-2 rounded mr-1">Edit</button>
                                                <a href="kelola_lokasi.php?delete_id=<?php echo $loc['id']; ?>" onclick="return confirm('Anda yakin ingin menghapus lokasi ini?');" class="bg-red-500 hover:bg-red-700 text-white text-xs font-bold py-1 px-2 rounded">
                                    Hapus
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal for Add Location -->
    <div id="addLocationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-lg relative">
            <button onclick="toggleAddLocationForm()" class="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-2xl font-bold focus:outline-none">
                &times;
            </button>
            <h2 class="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600">Form Tambah Lokasi Baru</h2>
            <form action="kelola_lokasi.php" method="POST" enctype="multipart/form-data">
                <div class="mb-4">
                    <label for="name" class="block text-gray-700 text-sm font-bold mb-2">Nama Lokasi (Tempat Barber):</label>
                    <input type="text" id="name" name="name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="Contoh: Pixel Barbershop Ciaul" required>
                </div>
                <div class="mb-4">
                    <label for="address" class="block text-gray-700 text-sm font-bold mb-2">Alamat Lengkap:</label>
                    <textarea id="address" name="address" rows="3" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" placeholder="Alamat lengkap lokasi" required></textarea>
                </div>
                <div class="mb-4">
                    <label for="location_image" class="block text-gray-700 text-sm font-bold mb-2">Gambar Lokasi:</label>
                    <input type="file" id="location_image" name="location_image" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                </div>
                <button type="submit" name="add_location" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                    Simpan Lokasi
                </button>
            </form>
        </div>
    </div>

    <!-- Modal for Edit Location -->
    <div id="editLocationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-lg relative">
            <button onclick="closeEditLocationModal()" class="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-2xl font-bold focus:outline-none">
                &times;
            </button>
            <h2 class="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600">Edit Lokasi</h2>
            <form id="editLocationForm" action="kelola_lokasi.php" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="update_location" value="1">
                <input type="hidden" id="edit_id" name="id">
                <input type="hidden" id="edit_old_image_filename" name="old_image_filename">
                
                <div class="mb-4">
                    <label for="edit_name" class="block text-gray-700 text-sm font-bold mb-2">Nama Lokasi (Tempat Barber):</label>
                    <input type="text" id="edit_name" name="name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                </div>
                <div class="mb-4">
                    <label for="edit_address" class="block text-gray-700 text-sm font-bold mb-2">Alamat Lengkap:</label>
                    <textarea id="edit_address" name="address" rows="3" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required></textarea>
                </div>
                <div class="mb-4">
                    <label for="edit_location_image" class="block text-gray-700 text-sm font-bold mb-2">Gambar Lokasi (Biarkan kosong jika tidak ingin mengubah):</label>
                    <input type="file" id="edit_location_image" name="location_image" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    <p class="text-sm text-gray-500 mt-1" id="current_image_text"></p>
                </div>
                <div class="flex justify-end mt-4">
                    <button type="button" onclick="closeEditLocationModal()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">Batal</button>
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function toggleBookingSubmenu() {
            const submenu = document.getElementById('bookingSubmenu');
            const icon = document.getElementById('arrowBookingIcon');
            submenu.classList.toggle('hidden');
            icon.classList.toggle('rotate-180');
        }

        function toggleSubmenu() {
            const submenu = document.getElementById('submenu');
            const icon = document.getElementById('arrowIcon');
            submenu.classList.toggle('hidden');
            icon.classList.toggle('rotate-180');
        }

        function toggleAddLocationForm() {
            const modal = document.getElementById('addLocationModal');
            modal.classList.toggle('hidden');
        }

        // Script for Edit Location Modal
        const editLocationModal = document.getElementById('editLocationModal');

        window.openEditLocationModal = function(location) {
            document.getElementById('edit_id').value = location.id;
            document.getElementById('edit_name').value = location.name;
            document.getElementById('edit_address').value = location.address;
            document.getElementById('edit_old_image_filename').value = location.image_filename || '';

            const currentImageText = document.getElementById('current_image_text');
            if (location.image_filename) {
                currentImageText.textContent = `Gambar saat ini: ${location.image_filename}`;
            } else {
                currentImageText.textContent = 'Tidak ada gambar saat ini';
            }
            
            editLocationModal.classList.remove('hidden');
        };

        window.closeEditLocationModal = function() {
            editLocationModal.classList.add('hidden');
            // Clear form fields after closing
            document.getElementById('edit_id').value = '';
            document.getElementById('edit_name').value = '';
            document.getElementById('edit_address').value = '';
            document.getElementById('edit_old_image_filename').value = '';
            document.getElementById('edit_location_image').value = ''; // Clear file input
            document.getElementById('current_image_text').textContent = '';
        };

        // Close edit modal when clicking outside of it
        editLocationModal.addEventListener('click', function(e) {
            if (e.target === editLocationModal) {
                closeEditLocationModal();
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Auto hide success/error messages after 5 seconds if they exist
            const successAlert = document.querySelector('.bg-green-100');
            const errorAlert = document.querySelector('.bg-red-100');

            if (successAlert) {
                setTimeout(() => {
                    successAlert.style.transition = 'opacity 0.5s ease-out';
                    successAlert.style.opacity = '0';
                    setTimeout(() => successAlert.remove(), 500);
                }, 5000);
            }

            if (errorAlert) {
                // Add a click listener for the close button if it exists
                const closeButton = errorAlert.querySelector('svg');
                if (closeButton) {
                    closeButton.addEventListener('click', () => errorAlert.remove());
                }
                // Auto-hide after 5 seconds as well
                setTimeout(() => {
                    errorAlert.style.transition = 'opacity 0.5s ease-out';
                    errorAlert.style.opacity = '0';
                    setTimeout(() => errorAlert.remove(), 500);
                }, 5000);
            }

            // Set active class for current page in sidebar
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar nav ul li a');
            navLinks.forEach(link => {
                link.closest('li').classList.remove('active');
                if (currentPath.includes(link.getAttribute('href'))) {
                    link.closest('li').classList.add('active');
                    const parentUl = link.closest('ul');
                    if (parentUl && (parentUl.id === 'submenu' || parentUl.id === 'bookingSubmenu')) {
                        parentUl.classList.remove('hidden');
                        const parentButton = parentUl.previousElementSibling;
                        if (parentButton) {
                            parentButton.querySelector('i').classList.add('rotate-180');
                            const parentLiWithButton = parentButton.closest('li');
                            if (parentLiWithButton) {
                                parentLiWithButton.classList.remove('active');
                            }
                        }
                    }
                }
            });
        });

        // Profile dropdown functionality
        const profileDropdown = document.getElementById('profileDropdown');
        const profileDropdownMenu = document.getElementById('profileDropdownMenu');

        if (profileDropdown && profileDropdownMenu) {
            profileDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdownMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdownMenu.classList.add('hidden');
                }
            });
        }
    </script>
</body>
</html>