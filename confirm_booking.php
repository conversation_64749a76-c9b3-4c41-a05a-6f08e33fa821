<?php
session_start();
require_once 'admin/db_config.php'; // Ensure this path is correct

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $service_id = $_POST['service_id'] ?? null;
    $barber_id = $_POST['barber_id'] ?? null;
    $location_id = $_POST['location_id'] ?? null;
    $selected_date = $_POST['selected_date'] ?? null;
    $selected_time = $_POST['selected_time'] ?? null;
    $user_name = $_POST['user_name'] ?? null;
    $user_phone = $_POST['user_phone'] ?? null;

    // Get service details directly from POST
    $service_name = $_POST['service_name'] ?? null;
    $service_price = $_POST['service_price'] ?? null;
    $service_duration = $_POST['service_duration'] ?? null;

    // Validate inputs (add more robust validation as needed)
    if (!$service_id || !$barber_id || !$location_id || !$selected_date || !$selected_time || !$user_name || !$user_phone || !$service_name || !$service_price || !$service_duration) {
        $_SESSION['error_message'] = "Please fill all required booking details.";
        header("Location: booking.php");
        exit();
    }

    try {
        // Fetch barber name for display purposes on the success page
        $stmt_barber = $conn->prepare("SELECT name FROM barbers WHERE id = :barber_id");
        $stmt_barber->execute(['barber_id' => $barber_id]);
        $barber = $stmt_barber->fetch(PDO::FETCH_ASSOC);
        $barber_name = $barber ? $barber['name'] : 'Unknown Barber';

        // Combine date and time for the 'booking_time' datetime column
        $booking_datetime = $selected_date . ' ' . $selected_time;

        // Prepare the final, correct INSERT statement based on your database schema
        $stmt_insert = $conn->prepare(
            "INSERT INTO bookings (location_id, barber_id, user_name, user_phone, service_name, service_price, booking_time, status) VALUES (:location_id, :barber_id, :user_name, :user_phone, :service_name, :service_price, :booking_time, :status)"
        );

        $stmt_insert->execute([
            'location_id' => $location_id,
            'barber_id' => $barber_id,
            'user_name' => $user_name,
            'user_phone' => $user_phone,
            'service_name' => $service_name,
            'service_price' => $service_price,
            'booking_time' => $booking_datetime,
            'status' => 'Confirmed' // Using the default value from your schema
        ]);

        $last_insert_id = $conn->lastInsertId();

        // Store booking details in session for the success page
        $_SESSION['last_booking'] = [
            'id' => $last_insert_id,
            'booking_id' => 'BOOK-' . $last_insert_id, // Generate a user-friendly booking ID
            'user_name' => $user_name,
            'user_phone' => $user_phone,
            'service_name' => $service_name,
            'service_price' => $service_price,
            'service_duration' => $service_duration,
            'barber_name' => $barber_name,
            'booking_date' => $selected_date,
            'booking_time_slot' => $selected_time,
            'total_price' => $service_price,
            'payment_method' => 'Cash on Delivery',
            'order_status' => 'Confirmed'
        ];

        // Redirect to success page
        header("Location: berhasilbooking.php");
        exit();

    } catch (PDOException $e) {
        error_log("Booking error: " . $e->getMessage());
        $_SESSION['error_message'] = "Database error during booking. Please try again.";
        header("Location: booking.php");
        exit();
    }
} else {
    // If accessed directly without POST data
    $_SESSION['error_message'] = "Terjadi kesalahan saat mengirim formulir. Silakan coba lagi.";
    header("Location: booking.php");
    exit();
}
?>