# Cara Mengaktifkan Kembali Proteksi Login

Setelah Anda berhasil membuat admin pertama, i<PERSON>ti langkah berikut untuk mengaktifkan kembali proteksi login:

## Langkah 1: Edit file kelola_admin.php

Buka file `admin/kelola_admin.php` dan cari baris berikut (sekitar baris 6-10):

```php
// Temporarily disabled login check for initial admin creation
// if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
//     header("Location: login.php");
//     exit();
// }
```

## Langkah 2: Hapus komentar

Ubah kode tersebut menjadi:

```php
// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header("Location: login.php");
    exit();
}
```

## Langkah 3: Hapus pesan setup awal (opsional)

Cari dan hapus bagian berikut di file yang sama:

```php
<!-- Initial Setup Notice -->
<div class="mb-6 p-4 rounded-lg bg-blue-100 text-blue-700 border border-blue-300">
    <div class="flex items-center">
        <i class="fas fa-info-circle mr-2"></i>
        <div>
            <h3 class="font-semibold">Setup Awal Admin</h3>
            <p class="text-sm">Halaman ini sementara dapat diakses tanpa login untuk membuat admin pertama. Setelah membuat admin, aktifkan kembali pengecekan login.</p>
        </div>
    </div>
</div>
```

## Langkah 4: Ubah header (opsional)

Ubah bagian header dari:

```php
<span class="mr-4 text-gray-700">
    Setup Admin
    <span class="text-sm text-gray-500">(Initial Setup)</span>
</span>
```

Menjadi:

```php
<span class="mr-4 text-gray-700">
    <?= htmlspecialchars($_SESSION['admin_full_name'] ?? $_SESSION['admin_username']) ?>
    <span class="text-sm text-gray-500">(<?= ucfirst($_SESSION['admin_role']) ?>)</span>
</span>
```

## Setelah Selesai

Setelah mengikuti langkah-langkah di atas:

1. Halaman kelola_admin.php akan memerlukan login
2. Anda dapat login menggunakan admin yang baru dibuat
3. Sistem admin sudah aman dan siap digunakan

## Kredensial Default

Jika Anda menjalankan setup_database.php, admin default yang tersedia:

- **Super Admin:** username: `admin` / password: `password`
- **Manager:** username: `manager` / password: `password`  
- **Staff:** username: `staff` / password: `password`

**PENTING:** Segera ganti password default setelah login pertama kali!
