<?php
session_start();
require_once 'db_config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$message = '';
$message_type = '';

// Handle profile photo upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_photo'])) {
    $upload_dir = 'uploads/profiles/';
    
    // Create directory if not exists
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['profile_photo'];
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $max_size = 5 * 1024 * 1024; // 5MB
        
        // Validate file type
        if (!in_array($file['type'], $allowed_types)) {
            $message = 'Hanya file gambar (JPEG, PNG, GIF, WebP) yang diizinkan!';
            $message_type = 'error';
        }
        // Validate file size
        elseif ($file['size'] > $max_size) {
            $message = 'Ukuran file terlalu besar! Maksimal 5MB.';
            $message_type = 'error';
        }
        else {
            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'profile_' . $_SESSION['admin_id'] . '_' . time() . '.' . $extension;
            $filepath = $upload_dir . $filename;
            
            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                // Delete old profile photo if exists
                $stmt = $conn->prepare("SELECT profile_photo FROM admin WHERE id = ?");
                $stmt->execute([$_SESSION['admin_id']]);
                $old_photo = $stmt->fetchColumn();
                
                if ($old_photo && file_exists($upload_dir . $old_photo)) {
                    unlink($upload_dir . $old_photo);
                }
                
                // Update database
                $stmt = $conn->prepare("UPDATE admin SET profile_photo = ? WHERE id = ?");
                if ($stmt->execute([$filename, $_SESSION['admin_id']])) {
                    $_SESSION['admin_profile_photo'] = $filename;
                    $message = 'Foto profil berhasil diupload!';
                    $message_type = 'success';
                } else {
                    $message = 'Gagal menyimpan ke database!';
                    $message_type = 'error';
                }
            } else {
                $message = 'Gagal mengupload file!';
                $message_type = 'error';
            }
        }
    } else {
        $message = 'Pilih file gambar terlebih dahulu!';
        $message_type = 'error';
    }
}

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    try {
        // Verify current password if changing password
        if (!empty($new_password)) {
            $stmt = $conn->prepare("SELECT password FROM admin WHERE id = ?");
            $stmt->execute([$_SESSION['admin_id']]);
            $stored_password = $stmt->fetchColumn();
            
            if (!password_verify($current_password, $stored_password)) {
                throw new Exception('Password saat ini salah!');
            }
            
            if ($new_password !== $confirm_password) {
                throw new Exception('Konfirmasi password tidak cocok!');
            }
            
            if (strlen($new_password) < 6) {
                throw new Exception('Password baru minimal 6 karakter!');
            }
            
            // Update with new password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare("UPDATE admin SET full_name = ?, email = ?, password = ? WHERE id = ?");
            $stmt->execute([$full_name, $email, $hashed_password, $_SESSION['admin_id']]);
        } else {
            // Update without password change
            $stmt = $conn->prepare("UPDATE admin SET full_name = ?, email = ? WHERE id = ?");
            $stmt->execute([$full_name, $email, $_SESSION['admin_id']]);
        }
        
        // Update session
        $_SESSION['admin_full_name'] = $full_name;
        $_SESSION['admin_email'] = $email;
        
        $message = 'Profil berhasil diperbarui!';
        $message_type = 'success';
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $message_type = 'error';
    }
}

// Get current admin data
$stmt = $conn->prepare("SELECT * FROM admin WHERE id = ?");
$stmt->execute([$_SESSION['admin_id']]);
$admin_data = $stmt->fetch();
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Settings - Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-4xl mx-auto px-4">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-3xl font-bold text-gray-800">👤 Profile Settings</h1>
                    <a href="data_pelamar.php" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                        <i class="fas fa-arrow-left mr-2"></i>Kembali
                    </a>
                </div>

                <!-- Success/Error Messages -->
                <?php if ($message): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Profile Photo Section -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4">📸 Foto Profil</h2>
                        
                        <div class="text-center mb-6">
                            <div class="relative inline-block">
                                <?php if (!empty($admin_data['profile_photo']) && file_exists('uploads/profiles/' . $admin_data['profile_photo'])): ?>
                                    <img src="uploads/profiles/<?= htmlspecialchars($admin_data['profile_photo']) ?>" 
                                         alt="Profile Photo" 
                                         class="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg">
                                <?php else: ?>
                                    <div class="w-32 h-32 rounded-full bg-gray-300 flex items-center justify-center border-4 border-white shadow-lg">
                                        <i class="fas fa-user text-4xl text-gray-600"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <form method="POST" enctype="multipart/form-data" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Upload Foto Baru:</label>
                                <input type="file" name="profile_photo" accept="image/*" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="text-xs text-gray-500 mt-1">Format: JPEG, PNG, GIF, WebP. Maksimal 5MB.</p>
                            </div>
                            <button type="submit" name="upload_photo" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                <i class="fas fa-upload mr-2"></i>Upload Foto
                            </button>
                        </form>
                    </div>

                    <!-- Profile Information Section -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4">ℹ️ Informasi Profil</h2>
                        
                        <form method="POST" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Nama Lengkap:</label>
                                <input type="text" name="full_name" value="<?= htmlspecialchars($admin_data['full_name']) ?>" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email:</label>
                                <input type="email" name="email" value="<?= htmlspecialchars($admin_data['email']) ?>" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Username:</label>
                                <input type="text" value="<?= htmlspecialchars($admin_data['username']) ?>" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100" readonly>
                                <p class="text-xs text-gray-500 mt-1">Username tidak dapat diubah</p>
                            </div>

                            <hr class="my-4">

                            <h3 class="text-lg font-medium text-gray-800 mb-3">🔒 Ubah Password</h3>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Password Saat Ini:</label>
                                <input type="password" name="current_password" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Password Baru:</label>
                                <input type="password" name="new_password" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Konfirmasi Password Baru:</label>
                                <input type="password" name="confirm_password" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <p class="text-xs text-gray-500">Kosongkan jika tidak ingin mengubah password</p>

                            <button type="submit" name="update_profile" class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                                <i class="fas fa-save mr-2"></i>Simpan Perubahan
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
