<?php
require_once 'db_config.php';

if (isset($_GET['id'])) {
    $pembeli_id = $_GET['id'];

    try {
        // Start a transaction
        $conn->beginTransaction();

        // Delete the buyer. Due to ON DELETE CASCADE on orders and user_addresses,
        // related records in those tables will also be deleted automatically.
        $stmt = $conn->prepare("DELETE FROM pembeli WHERE id = :id");
        $stmt->execute(['id' => $pembeli_id]);

        $conn->commit();
        
        // Redirect back to the buyer management page after successful deletion
        header('Location: kelola_pembeli.php?status=deleted');
        exit();

    } catch (PDOException $e) {
        $conn->rollBack();
        error_log("Error deleting pembeli: " . $e->getMessage());
        // Redirect with an error message
        header('Location: kelola_pembeli.php?status=error&message=' . urlencode($e->getMessage()));
        exit();
    }
} else {
    // Redirect if no ID is provided
    header('Location: kelola_pembeli.php?status=error&message=No ID provided');
    exit();
}
?> 