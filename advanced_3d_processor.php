<?php
header('Content-Type: application/json');

function processAdvanced3DModeling($userImagePath, $modelImagePath, $hairStyle, $faceShape) {
    try {
        // Validate inputs
        if (!file_exists($userImagePath) || !file_exists($modelImagePath)) {
            return ['success' => false, 'error' => 'Image files not found'];
        }
        
        // Prepare Python command
        $pythonScript = __DIR__ . '/advanced_3d_face_processor.py';
        $command = sprintf(
            'python "%s" "%s" "%s" "%s" "%s"',
            $pythonScript,
            $userImagePath,
            $modelImagePath,
            $hairStyle,
            $faceShape
        );
        
        // Execute Python script
        $output = shell_exec($command . ' 2>&1');
        
        if (!$output) {
            return ['success' => false, 'error' => 'Python processing failed'];
        }
        
        // Parse JSON output
        $result = json_decode($output, true);
        
        if (!$result) {
            // Log raw output for debugging
            error_log("Python output: " . $output);
            return ['success' => false, 'error' => 'Invalid processing output', 'debug' => $output];
        }
        
        return $result;
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function checkPythonDependencies() {
    $dependencies = ['cv2', 'mediapipe', 'dlib', 'numpy', 'scipy'];
    $missing = [];
    
    foreach ($dependencies as $dep) {
        $check = shell_exec("python -c \"import $dep\" 2>&1");
        if (strpos($check, 'ModuleNotFoundError') !== false || strpos($check, 'ImportError') !== false) {
            $missing[] = $dep;
        }
    }
    
    return $missing;
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $userImage = $input['user_image'] ?? '';
    $modelImage = $input['model_image'] ?? '';
    $hairStyle = $input['hair_style'] ?? '';
    $faceShape = $input['face_shape'] ?? 'oval';
    
    if (empty($userImage) || empty($modelImage)) {
        echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
        exit;
    }
    
    // Check dependencies first
    $missingDeps = checkPythonDependencies();
    if (!empty($missingDeps)) {
        echo json_encode([
            'success' => false, 
            'error' => 'Missing Python dependencies: ' . implode(', ', $missingDeps),
            'install_command' => 'pip install ' . implode(' ', $missingDeps)
        ]);
        exit;
    }
    
    $result = processAdvanced3DModeling($userImage, $modelImage, $hairStyle, $faceShape);
    echo json_encode($result);
    exit;
}

// Handle GET request for dependency check
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['check_deps'])) {
    $missing = checkPythonDependencies();
    echo json_encode([
        'dependencies_ok' => empty($missing),
        'missing_dependencies' => $missing,
        'install_command' => empty($missing) ? null : 'pip install ' . implode(' ', $missing)
    ]);
    exit;
}

echo json_encode(['success' => false, 'error' => 'Invalid request method']);
?>