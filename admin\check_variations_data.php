<?php
require_once 'db_config.php';

try {
    $stmt = $conn->query("SELECT id, produk_id, ukuran_ml, harga, stok, gambar_varian FROM variasi_produk");
    $variations = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($variations)) {
        echo "Tidak ada data di tabel variasi_produk.\n";
    } else {
        echo "Data dari tabel variasi_produk:\n";
        foreach ($variations as $row) {
            echo "ID: " . $row['id'] . ", Produk ID: " . $row['produk_id'] . ", Ukuran (ml): " . $row['ukuran_ml'] . ", Harga: " . $row['harga'] . ", Stok: " . $row['stok'] . ", Gambar <PERSON>: " . ($row['gambar_varian'] ?? 'NULL') . "\n";
        }
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?> 