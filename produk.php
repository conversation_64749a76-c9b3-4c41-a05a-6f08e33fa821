<?php
// Sertakan file konfigurasi database
// Pastikan path ini benar relatif terhadap produk.php
require_once 'Admin/db_config.php'; 

// Path untuk folder uploads (digunakan untuk menampilkan gambar)
$upload_dir_display = '/aplikasi-pixel/uploads/'; // Path absolut dari root ke folder uploads

// Ambil semua produk dari database dengan gambar tambahan
$daftar_produk = [];
try {
    // Ambil produk utama
    $sql_select_produk = "SELECT 
        p.id, 
        p.nama_produk, 
        p.deskripsi, 
        p.harga, 
        p.gambar_utama, 
        p.kategori_id, 
        k.nama_kategori
    FROM produk p 
    LEFT JOIN kategori_produk k ON p.kategori_id = k.id 
    ORDER BY p.created_at DESC";
    
    $stmt = $conn->prepare($sql_select_produk);
    $stmt->execute();
    $result_produk = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Debug: Tampilkan data produk untuk debugging
    error_log("Produk data: " . print_r($result_produk, true));
    
    if (!empty($result_produk)) {
        $daftar_produk = $result_produk;
    }
} catch(PDOException $e) {
    // Log the full error message for debugging
    error_log("Database error: " . $e->getMessage());
    error_log("SQL State: " . $e->getCode());
    error_log("Error Info: " . print_r($stmt->errorInfo(), true));
    die("Error: " . $e->getMessage());
}


?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium Grooming Products - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
        }
        .active-filter {
            background-color: #0A5144;
            color: white;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
        }
        .discount-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #EF4444;
            color: white;
            font-size: 10px;
            font-weight: bold;
            padding: 3px 6px;
            border-radius: 4px;
        }
        .no-scrollbar::-webkit-scrollbar {
            display: none;
        }
        .no-scrollbar {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
        .line-clamp-2 {
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            line-clamp: 2; /* Standard property */
        }
        .nav-active i,
        .nav-active span {
            color: #0A5144 !important; /* Ensure active color overrides */
        }
        /* Ensure other nav items are default gray */
        nav a:not(.nav-active) i,
        nav a:not(.nav-active) span {
            color: #4B5563; /* text-gray-600 */
        }
        nav a:not(.nav-active):hover i,
        nav a:not(.nav-active):hover span {
            color: #0A5144; /* hover:text-[#0A5144] */
        }

    </style>
</head>
<body class="pb-24">
    <!-- Header -->
    <header class="sticky top-0 z-10 gradient-bg text-white p-4 shadow-md">
        <div class="flex items-center justify-between">
            <a href="javascript:history.back()" class="text-white">
                <i class="fas fa-arrow-left text-lg"></i>
            </a>
            <h1 class="text-xl font-bold text-center">Grooming Products</h1>
            <div class="flex items-center space-x-4">
                <a href="#" class="text-white relative">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span> 
                </a>
            </div>
        </div>
    </header>

    <!-- Categories (Statis untuk contoh) -->
    <div class="px-4 py-3 bg-white sticky top-16 z-10 shadow-sm">
        <div class="flex space-x-2 overflow-x-auto pb-2 no-scrollbar">
            <button class="category-btn active-filter px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">
                All Products
            </button>
            <button class="category-btn bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">
                Hair Styling
            </button>
            <button class="category-btn bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">
                Hair Care
            </button>
            <button class="category-btn bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">
                Face Care
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <main class="px-4 py-4">
        <!-- Sort/Filter Bar (Statis untuk contoh) -->
        <div class="flex justify-between items-center mb-4">
            <button class="flex items-center text-sm text-gray-600">
                <i class="fas fa-sliders-h mr-1"></i> Filter
            </button>
            <div class="flex items-center text-sm text-gray-600">
                <span class="mr-1">Sort by:</span>
                <select class="border-0 bg-transparent text-gray-900 font-medium focus:ring-0 focus:outline-none p-0">
                    <option>Popular</option>
                    <option>Newest</option>
                    <option>Price: Low to High</option>
                    <option>Price: High to Low</option>
                </select>
            </div>
        </div>

        <!-- Product Grid -->
        <div class="grid grid-cols-2 gap-4">
            <?php if (!empty($daftar_produk)): ?>
                <?php foreach ($daftar_produk as $produk): ?>
                    <a href="pdp.php?id=<?php echo htmlspecialchars($produk['id']); ?>" class="block no-underline text-current focus:outline-none">
                        <div class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 ease-in-out hover:-translate-y-1" data-category="All Products"> 
                        <div class="relative">
                            <img src="<?php 
                                $gambarPath = '/aplikasi-pixel/uploads/' . htmlspecialchars($produk['gambar_utama']);
                                
                                // Debug: Tampilkan path gambar untuk debugging
                                error_log("Path gambar: " . $gambarPath);
                                
                                // Cek apakah file gambar ada
                                if (!file_exists($_SERVER['DOCUMENT_ROOT'] . '/aplikasi-pixel/uploads/' . htmlspecialchars($produk['gambar_utama']))) {
                                    // Coba dengan ekstensi jpeg
                                    $fileParts = pathinfo($produk['gambar_utama']);
                                    $baseName = $fileParts['filename'];
                                    $gambarPath = '/aplikasi-pixel/uploads/' . $baseName . '_23.jpeg';
                                }
                                
                                // Debug: Tampilkan path alternatif untuk debugging
                                error_log("Path alternatif: " . $gambarPath);
                                
                                // Gunakan path absolut dari root
                                echo !file_exists($_SERVER['DOCUMENT_ROOT'] . '/aplikasi-pixel/uploads/' . htmlspecialchars($produk['gambar_utama'])) ? 'https://via.placeholder.com/300x200.png?text=No+Image' : $gambarPath;
                                ?>" 
                                 alt="<?php echo htmlspecialchars($produk['nama_produk']); ?>" 
                                 class="w-full h-40 object-cover">
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 text-base line-clamp-2 mb-1 h-10"><?php echo htmlspecialchars($produk['nama_produk']); ?></h3>
                            <p class="text-gray-600 text-sm line-clamp-2 mb-2 h-8"><?php echo nl2br(htmlspecialchars($produk['deskripsi'])); ?></p>
                            <div class="flex items-center mt-1 mb-2">
                                <div class="flex text-yellow-400 text-xs">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <span class="text-gray-500 text-xs ml-1">(Reviews)</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-[#0A5144] font-bold text-sm">Rp <?php echo number_format($produk['harga'], 0, ',', '.'); ?></span>
                                <button class="bg-[#0A5144] text-white text-xs p-2 rounded-md hover:bg-[#006A63]" title="Add to cart">
                                    <i class="fas fa-cart-plus"></i>
                                </button>
                            </div>
                        </div>
                        </div>
                    </a>
                <?php endforeach; ?>
            <?php else: ?>
                <p class="col-span-2 text-center text-gray-500 py-10">Saat ini belum ada produk yang tersedia.</p>
            <?php endif; ?>
        </div>
    </main>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white shadow-lg flex justify-around py-3 z-50 border-t border-gray-100">
      <a href="index.php" class="flex flex-col items-center text-xs <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php') ? 'nav-active' : ''; ?>">
        <i class="fas fa-home text-lg mb-1"></i>
        <span>Home</span>
      </a>
      <a href="service.php" class="flex flex-col items-center text-xs <?php echo (basename($_SERVER['PHP_SELF']) == 'service.php' || basename($_SERVER['PHP_SELF']) == 'treatment.php') ? 'nav-active' : ''; ?>">
        <i class="fas fa-cut text-lg mb-1"></i>
        <span>Services</span>
      </a>
      <a href="produk.php" class="flex flex-col items-center text-xs <?php echo (basename($_SERVER['PHP_SELF']) == 'produk.php') ? 'nav-active' : ''; ?>">
        <i class="fas fa-box-open text-lg mb-1"></i>
        <span>Produk</span>
      </a>
      <a href="location.php" class="flex flex-col items-center text-xs <?php echo (basename($_SERVER['PHP_SELF']) == 'location.php') ? 'nav-active' : ''; ?>">
        <i class="fas fa-calendar-alt text-lg mb-1"></i>
        <span>Book</span>
      </a>
    </nav>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const categoryButtons = document.querySelectorAll('.category-btn');
        const productCards = document.querySelectorAll('.bg-white.rounded-lg.overflow-hidden.shadow-md.hover\:shadow-lg.transition-all.duration-300.ease-in-out.hover\:-translate-y-1');

        categoryButtons.forEach(button => {
            button.addEventListener('click', function() {
                categoryButtons.forEach(btn => {
                    btn.classList.remove('active-filter', 'text-white');
                    btn.classList.add('bg-gray-100', 'hover:bg-gray-200');
                });
                button.classList.add('active-filter', 'text-white');
                button.classList.remove('bg-gray-100', 'hover:bg-gray-200');

                const filter = button.textContent.trim();
                productCards.forEach(card => {
                    // Untuk saat ini, filter hanya akan menampilkan semua produk jika 'All Products' dipilih.
                    // Anda dapat memperluas ini dengan menambahkan atribut data-category ke produk di PHP
                    // dan mencocokkannya di sini.
                    if (filter === 'All Products') {
                        card.style.display = '';
                    } else {
                        // Jika Anda ingin filter berdasarkan kategori lain, Anda perlu logika tambahan di sini
                        // yang mencocokkan 'filter' dengan 'card.getAttribute('data-category')'
                        // Untuk contoh ini, kategori selain 'All Products' akan menyembunyikan semua kartu.
                        // card.style.display = 'none'; 
                        // Untuk sementara, kita tampilkan semua saja sampai ada data kategori di produk
                        card.style.display = ''; 
                    }
                });
            });
        });
    });
    </script>
</body>
</html>