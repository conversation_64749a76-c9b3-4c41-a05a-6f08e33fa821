<?php
session_start();
require_once 'db_config.php';

if (isset($_GET['id'])) {
    $id = $_GET['id'];
    
    try {
        // Start transaction
        $conn->beginTransaction();
        
        // Get product details
        $stmt = $conn->prepare("SELECT gambar FROM produk WHERE id = ?");
        $stmt->execute([$id]);
        $product = $stmt->fetch();
        
        // Delete related images
        $stmt = $conn->prepare("SELECT gambar FROM gambar_produk WHERE produk_id = ?");
        $stmt->execute([$id]);
        $images = $stmt->fetchAll();
        
        // Delete all related data
        $stmt = $conn->prepare("DELETE FROM gambar_produk WHERE produk_id = ?");
        $stmt->execute([$id]);
        
        // Update produk table to remove variations
        $stmt = $conn->prepare("UPDATE produk SET variasi = NULL WHERE id = ?");
        $stmt->execute([$id]);
        
        // Delete main product
        $stmt = $conn->prepare("DELETE FROM produk WHERE id = ?");
        $stmt->execute([$id]);
        
        // Delete image files
        if ($product && file_exists("../uploads/" . $product['gambar'])) {
            unlink("../uploads/" . $product['gambar']);
        }
        
        foreach ($images as $image) {
            if (file_exists("../uploads/" . $image['gambar'])) {
                unlink("../uploads/" . $image['gambar']);
            }
        }
        
        // Commit transaction
        $conn->commit();
        
        header("Location: kelola_produk.php?success=Produk berhasil dihapus");
        exit();
        
    } catch(PDOException $e) {
        // Rollback transaction if error occurs
        $conn->rollBack();
        die("Error: " . $e->getMessage());
    }
} else {
    header("Location: kelola_produk.php");
    exit();
}
?>
