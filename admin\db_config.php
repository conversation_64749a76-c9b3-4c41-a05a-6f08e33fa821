<?php
ini_set('display_errors', 1); // Enable PHP errors for debugging
error_reporting(E_ALL); // Show all errors for debugging
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'pixel';

try {
    // Connect to MySQL with database selected
    $conn = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $conn->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);

    // Check if produk table exists and has all required columns
    $stmt = $conn->prepare("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'produk'");
    $stmt->execute([$database]);
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Check if gambar_utama column exists
    if (!in_array('gambar_utama', $columns)) {
        // Add the missing column
        $conn->exec("ALTER TABLE produk ADD COLUMN gambar_utama VARCHAR(255) DEFAULT NULL");
        error_log("Added missing gambar_utama column to produk table");
    }

    // Check if variasi column exists in produk table
    $stmt = $conn->prepare("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS \n                           WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'produk' AND COLUMN_NAME = 'variasi'");
    $stmt->execute([$database]);
    if (!$stmt->fetch()) {
        // Add the missing variasi column
        $conn->exec("ALTER TABLE produk ADD COLUMN variasi JSON DEFAULT NULL");
        error_log("Added missing variasi column to produk table");
    }

    // Check if face_analysis table exists
    $stmt = $conn->query("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '$database' AND TABLE_NAME = 'face_analysis'");
    if ($stmt->fetchColumn() == 0) {
        // Create face_analysis table
        $sql_face_analysis = "CREATE TABLE IF NOT EXISTS face_analysis (\n            id INT AUTO_INCREMENT PRIMARY KEY,\n            image_path VARCHAR(255) NOT NULL,\n            face_shape VARCHAR(50) NOT NULL,\n            description TEXT,\n            recommendation TEXT,\n            hairline_analysis TEXT,\n            hair_type_analysis TEXT,\n            tips TEXT,\n            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        try {
            $conn->exec($sql_face_analysis);
            error_log("Created face_analysis table");
        } catch (PDOException $e) {
            error_log("Failed to create face_analysis table: " . $e->getMessage());
            throw $e;
        }
    }

    // Ensure core tables exist - always check and create if not present
    $core_tables_sql = [
        "CREATE TABLE IF NOT EXISTS kategori_produk (\n            id INT AUTO_INCREMENT PRIMARY KEY,\n            nama_kategori VARCHAR(100) NOT NULL,\n            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        "CREATE TABLE IF NOT EXISTS produk (\n            id INT AUTO_INCREMENT PRIMARY KEY,\n            nama_produk VARCHAR(255) NOT NULL,\n            deskripsi TEXT,\n            harga DECIMAL(10,2) NOT NULL,\n            gambar_utama VARCHAR(255) DEFAULT NULL,\n            kategori_id INT,\n            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (kategori_id) REFERENCES kategori_produk(id)\n        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        "CREATE TABLE IF NOT EXISTS variasi_produk (\n            id INT AUTO_INCREMENT PRIMARY KEY,\n            produk_id INT NOT NULL,\n            ukuran_ml VARCHAR(50) NOT NULL,\n            harga DECIMAL(10,2) NOT NULL,\n            stok INT NOT NULL,\n            gambar_varian VARCHAR(255) DEFAULT NULL,\n            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (produk_id) REFERENCES produk(id) ON DELETE CASCADE\n        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        "CREATE TABLE IF NOT EXISTS gambar_produk (\n            id INT AUTO_INCREMENT PRIMARY KEY,\n            produk_id INT,\n            gambar VARCHAR(255),\n            label VARCHAR(100),\n            FOREIGN KEY (produk_id) REFERENCES produk(id)\n        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;"
    ];
    
    foreach ($core_tables_sql as $query) {
        try {
            $conn->exec($query);
        } catch (PDOException $e) {
            error_log("Failed to create or verify table: " . $query . "; Error: " . $e->getMessage());
            throw $e;
        }
    }

    // Insert sample categories if they don't exist
    $stmt = $conn->prepare("SELECT COUNT(*) FROM kategori_produk");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $categories = [
            'Perawatan Rambut',
            'Perawatan Wajah',
            'Perawatan Badan',
            'Aksesoris'
        ];
        
        $stmt = $conn->prepare("INSERT INTO kategori_produk (nama_kategori) VALUES (?)");
        foreach ($categories as $category) {
            $stmt->execute([$category]);
        }
    }

    // Create notifications table if not exists
    $conn->exec("CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        order_id INT,
        message TEXT,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES pembeli(id),
        FOREIGN KEY (order_id) REFERENCES orders(id)
    )");

    // Create jobs and applications tables if they don't exist
    $job_tables_sql = [
        "CREATE TABLE IF NOT EXISTS locations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            address TEXT NOT NULL,
            phone VARCHAR(20),
            image_filename VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",

        "CREATE TABLE IF NOT EXISTS barbers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            location_id INT,
            phone VARCHAR(20),
            email VARCHAR(255),
            specialization TEXT,
            image_filename VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",

        "CREATE TABLE IF NOT EXISTS jobs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            position VARCHAR(255) NOT NULL,
            location VARCHAR(255) NOT NULL,
            type VARCHAR(100) NOT NULL,
            salary VARCHAR(255) NOT NULL,
            description TEXT,
            requirements TEXT,
            benefits TEXT,
            experience_level VARCHAR(100),
            education VARCHAR(100),
            image VARCHAR(255),
            posted TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",

        "CREATE TABLE IF NOT EXISTS applications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            job_id INT NOT NULL,
            full_name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            address TEXT,
            city VARCHAR(100),
            experience TEXT,
            license VARCHAR(255),
            skills TEXT,
            start_date DATE,
            days_available JSON,
            cover_letter TEXT,
            resume_file VARCHAR(255),
            status ENUM('pending', 'reviewed', 'interview', 'accepted', 'rejected') DEFAULT 'pending',
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",

        "CREATE TABLE IF NOT EXISTS admin (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
            is_active BOOLEAN DEFAULT TRUE,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;"
    ];

    foreach ($job_tables_sql as $query) {
        try {
            $conn->exec($query);
        } catch (PDOException $e) {
            error_log("Failed to create job-related table: " . $query . "; Error: " . $e->getMessage());
        }
    }

    // Insert default admin accounts if admin table is empty
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM admin");
        $stmt->execute();
        if ($stmt->fetchColumn() == 0) {
            // Default password is "password" for all accounts
            $default_password = password_hash('password', PASSWORD_DEFAULT);

            $default_admins = [
                ['admin', $default_password, 'Super Administrator', '<EMAIL>', 'super_admin'],
                ['manager', $default_password, 'Manager', '<EMAIL>', 'admin'],
                ['staff', $default_password, 'Staff Admin', '<EMAIL>', 'moderator']
            ];

            $stmt = $conn->prepare("INSERT INTO admin (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
            foreach ($default_admins as $admin) {
                $stmt->execute($admin);
            }
            error_log("Default admin accounts created");
        }
    } catch (PDOException $e) {
        error_log("Failed to create default admin accounts: " . $e->getMessage());
    }

} catch(PDOException $e) {
    error_log("Database connection error: " . $e->getMessage());
    die("Database connection failed: " . $e->getMessage());
}