import cv2
import numpy as np
import json
import sys
import os
import base64

class TransparentHairApplier:
    def __init__(self):
        pass
    
    def apply_transparent_hair(self, user_image_path, hair_overlay_path):
        """Apply transparent hair overlay directly to user face"""
        try:
            # Load user image
            user_img = cv2.imread(user_image_path)
            if user_img is None:
                return {'success': False, 'error': 'Failed to load user image'}
            
            # Load hair overlay with transparency
            hair_overlay = cv2.imread(hair_overlay_path, cv2.IMREAD_UNCHANGED)
            if hair_overlay is None:
                return {'success': False, 'error': 'Failed to load hair overlay'}
            
            # Get dimensions
            user_h, user_w = user_img.shape[:2]
            
            # Resize hair overlay to match user image
            hair_resized = cv2.resize(hair_overlay, (user_w, user_h))
            
            # Check if hair has alpha channel
            if hair_resized.shape[2] == 4:
                # Extract alpha channel
                hair_rgb = hair_resized[:, :, :3]
                alpha = hair_resized[:, :, 3] / 255.0
                
                # Apply hair overlay using alpha blending
                result = user_img.copy().astype(np.float32)
                
                for c in range(3):
                    result[:, :, c] = (alpha * hair_rgb[:, :, c] + 
                                     (1 - alpha) * result[:, :, c])
                
                result = np.clip(result, 0, 255).astype(np.uint8)
            else:
                # If no alpha channel, use simple overlay
                result = cv2.addWeighted(user_img, 0.3, hair_resized, 0.7, 0)
            
            # Create output directory
            output_dir = 'uploads/3d_results/'
            os.makedirs(output_dir, exist_ok=True)
            
            # Save result
            output_path = os.path.join(output_dir, f'hair_applied_{hash(user_image_path)}.png')
            cv2.imwrite(output_path, result)
            
            # Convert to base64
            _, buffer = cv2.imencode('.png', result)
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return {
                'success': True,
                'result_url': output_path,
                'result_base64': f"data:image/png;base64,{img_base64}"
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

def main():
    if len(sys.argv) < 3:
        print(json.dumps({'success': False, 'error': 'Missing parameters'}))
        return
    
    user_image = sys.argv[1]
    hair_overlay = sys.argv[2]
    
    applier = TransparentHairApplier()
    result = applier.apply_transparent_hair(user_image, hair_overlay)
    
    print(json.dumps(result))

if __name__ == "__main__":
    main()