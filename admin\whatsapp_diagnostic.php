<?php
session_start();
require_once 'db_config.php';
require_once 'whatsapp_notification.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$diagnostic_results = [];

// Run diagnostics
function runDiagnostics() {
    global $diagnostic_results;
    
    // 1. Check configuration
    $config = include __DIR__ . '/whatsapp_config.php';
    $provider = $config['api_provider'];
    $api_key = $config[$provider]['api_key'] ?? '';
    
    $diagnostic_results['config'] = [
        'provider' => $provider,
        'api_key_set' => !empty($api_key) && strpos($api_key, 'YOUR_') !== 0,
        'api_key_preview' => $api_key ? substr($api_key, 0, 10) . '...' : 'Not set',
        'api_url' => $config[$provider]['api_url'] ?? ''
    ];
    
    // 2. Test API connection
    if ($diagnostic_results['config']['api_key_set']) {
        try {
            $whatsapp = new WhatsAppNotification();
            
            // Test with a dummy number to check API response
            $test_result = $whatsapp->sendMessage('+6281234567890', 'Test diagnostic message');
            
            $diagnostic_results['api_test'] = [
                'success' => $test_result['success'],
                'http_code' => $test_result['http_code'],
                'response' => $test_result['response']
            ];
        } catch (Exception $e) {
            $diagnostic_results['api_test'] = [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    // 3. Check recent logs
    try {
        $conn = getDbConnection();
        $stmt = $conn->prepare("SELECT * FROM notification_logs ORDER BY sent_at DESC LIMIT 5");
        $stmt->execute();
        $diagnostic_results['recent_logs'] = $stmt->fetchAll();
    } catch (Exception $e) {
        $diagnostic_results['recent_logs'] = [];
        $diagnostic_results['logs_error'] = $e->getMessage();
    }
    
    // 4. Check phone number format
    $diagnostic_results['phone_format'] = [
        'country_code' => $config['default_country_code'],
        'examples' => [
            'correct' => ['+6281234567890', '081234567890'],
            'incorrect' => ['81234567890', '+62-812-3456-7890']
        ]
    ];
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_diagnostic'])) {
    runDiagnostics();
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Diagnostic - Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-4xl mx-auto px-4">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-3xl font-bold text-gray-800">🔍 WhatsApp Diagnostic</h1>
                    <a href="whatsapp_manager.php" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                        <i class="fas fa-arrow-left mr-2"></i>Kembali
                    </a>
                </div>

                <!-- Run Diagnostic Button -->
                <div class="mb-8 text-center">
                    <form method="POST">
                        <button type="submit" name="run_diagnostic" class="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-lg">
                            <i class="fas fa-play mr-2"></i>Jalankan Diagnostic
                        </button>
                    </form>
                </div>

                <?php if (!empty($diagnostic_results)): ?>
                <!-- Diagnostic Results -->
                <div class="space-y-6">
                    
                    <!-- 1. Configuration Check -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4">⚙️ Konfigurasi API</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-600">Provider:</p>
                                <p class="font-semibold text-gray-800"><?= ucfirst($diagnostic_results['config']['provider']) ?></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">API Key Status:</p>
                                <p class="font-semibold <?= $diagnostic_results['config']['api_key_set'] ? 'text-green-600' : 'text-red-600' ?>">
                                    <?= $diagnostic_results['config']['api_key_set'] ? '✅ Configured' : '❌ Not Configured' ?>
                                </p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">API Key Preview:</p>
                                <p class="font-mono text-sm text-gray-800"><?= htmlspecialchars($diagnostic_results['config']['api_key_preview']) ?></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">API URL:</p>
                                <p class="font-mono text-sm text-gray-800"><?= htmlspecialchars($diagnostic_results['config']['api_url']) ?></p>
                            </div>
                        </div>
                        
                        <?php if (!$diagnostic_results['config']['api_key_set']): ?>
                        <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                            <p class="text-red-700 font-medium">⚠️ API Key belum dikonfigurasi!</p>
                            <p class="text-red-600 text-sm mt-1">
                                Silakan setup API key terlebih dahulu: 
                                <a href="whatsapp_setup_guide.php" class="underline">Setup API WhatsApp</a>
                            </p>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- 2. API Connection Test -->
                    <?php if (isset($diagnostic_results['api_test'])): ?>
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4">🌐 Test Koneksi API</h2>
                        
                        <div class="mb-4">
                            <p class="text-sm text-gray-600">Status Koneksi:</p>
                            <p class="font-semibold <?= $diagnostic_results['api_test']['success'] ? 'text-green-600' : 'text-red-600' ?>">
                                <?= $diagnostic_results['api_test']['success'] ? '✅ Berhasil' : '❌ Gagal' ?>
                            </p>
                        </div>

                        <?php if (isset($diagnostic_results['api_test']['http_code'])): ?>
                        <div class="mb-4">
                            <p class="text-sm text-gray-600">HTTP Response Code:</p>
                            <p class="font-mono text-sm text-gray-800"><?= $diagnostic_results['api_test']['http_code'] ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if (isset($diagnostic_results['api_test']['response'])): ?>
                        <div class="mb-4">
                            <p class="text-sm text-gray-600">API Response:</p>
                            <pre class="bg-gray-100 p-3 rounded text-xs overflow-x-auto"><?= htmlspecialchars(json_encode($diagnostic_results['api_test']['response'], JSON_PRETTY_PRINT)) ?></pre>
                        </div>
                        <?php endif; ?>

                        <?php if (isset($diagnostic_results['api_test']['error'])): ?>
                        <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                            <p class="text-red-700 font-medium">Error:</p>
                            <p class="text-red-600 text-sm"><?= htmlspecialchars($diagnostic_results['api_test']['error']) ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <!-- 3. Recent Logs -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4">📋 Log Notifikasi Terbaru</h2>
                        
                        <?php if (isset($diagnostic_results['logs_error'])): ?>
                        <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                            <p class="text-red-700 font-medium">Error mengakses logs:</p>
                            <p class="text-red-600 text-sm"><?= htmlspecialchars($diagnostic_results['logs_error']) ?></p>
                        </div>
                        <?php elseif (empty($diagnostic_results['recent_logs'])): ?>
                        <p class="text-gray-600">Belum ada log notifikasi.</p>
                        <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full table-auto">
                                <thead>
                                    <tr class="bg-gray-200">
                                        <th class="px-4 py-2 text-left text-xs">Waktu</th>
                                        <th class="px-4 py-2 text-left text-xs">Penerima</th>
                                        <th class="px-4 py-2 text-left text-xs">Status</th>
                                        <th class="px-4 py-2 text-left text-xs">Response</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($diagnostic_results['recent_logs'] as $log): ?>
                                    <tr class="border-b">
                                        <td class="px-4 py-2 text-xs"><?= date('d/m/Y H:i', strtotime($log['sent_at'])) ?></td>
                                        <td class="px-4 py-2 text-xs"><?= htmlspecialchars($log['recipient']) ?></td>
                                        <td class="px-4 py-2 text-xs">
                                            <?= $log['success'] ? '<span class="text-green-600">✅ Success</span>' : '<span class="text-red-600">❌ Failed</span>' ?>
                                        </td>
                                        <td class="px-4 py-2 text-xs max-w-xs truncate">
                                            <?= htmlspecialchars(substr($log['response'] ?? '', 0, 50)) ?>...
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- 4. Phone Format Guide -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4">📱 Format Nomor Telepon</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="font-medium text-green-700 mb-2">✅ Format Yang Benar:</h3>
                                <ul class="space-y-1 text-sm">
                                    <?php foreach ($diagnostic_results['phone_format']['examples']['correct'] as $example): ?>
                                    <li class="font-mono bg-green-50 px-2 py-1 rounded"><?= $example ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <div>
                                <h3 class="font-medium text-red-700 mb-2">❌ Format Yang Salah:</h3>
                                <ul class="space-y-1 text-sm">
                                    <?php foreach ($diagnostic_results['phone_format']['examples']['incorrect'] as $example): ?>
                                    <li class="font-mono bg-red-50 px-2 py-1 rounded"><?= $example ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                            <p class="text-blue-700 text-sm">
                                <strong>Country Code:</strong> +<?= $diagnostic_results['phone_format']['country_code'] ?> (Indonesia)<br>
                                Sistem otomatis mengkonversi format nasional ke international.
                            </p>
                        </div>
                    </div>

                    <!-- Recommendations -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-yellow-800 mb-4">💡 Rekomendasi</h2>
                        
                        <div class="space-y-3 text-sm">
                            <?php if (!$diagnostic_results['config']['api_key_set']): ?>
                            <div class="flex items-start">
                                <span class="text-yellow-600 mr-2">1.</span>
                                <span>Setup API key terlebih dahulu di <a href="whatsapp_setup_guide.php" class="underline">WhatsApp Setup Guide</a></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (isset($diagnostic_results['api_test']) && !$diagnostic_results['api_test']['success']): ?>
                            <div class="flex items-start">
                                <span class="text-yellow-600 mr-2">2.</span>
                                <span>Periksa API key dan pastikan akun provider memiliki saldo/quota yang cukup</span>
                            </div>
                            <?php endif; ?>
                            
                            <div class="flex items-start">
                                <span class="text-yellow-600 mr-2">3.</span>
                                <span>Pastikan nomor WhatsApp yang digunakan untuk test sudah terdaftar dan aktif</span>
                            </div>
                            
                            <div class="flex items-start">
                                <span class="text-yellow-600 mr-2">4.</span>
                                <span>Untuk Fonnte: pastikan device WhatsApp sudah di-scan dan online</span>
                            </div>
                            
                            <div class="flex items-start">
                                <span class="text-yellow-600 mr-2">5.</span>
                                <span>Cek spam/folder lain di WhatsApp jika pesan tidak muncul di chat utama</span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
