import urllib.request
import os

def download_dlib_model():
    """Download dlib shape predictor model"""
    model_url = "http://dlib.net/files/shape_predictor_68_face_landmarks.dat.bz2"
    model_path = "shape_predictor_68_face_landmarks.dat.bz2"
    
    if not os.path.exists(model_path):
        print("Downloading dlib face landmarks model...")
        urllib.request.urlretrieve(model_url, model_path)
        
        # Extract bz2 file
        import bz2
        with bz2.BZ2File(model_path, 'rb') as f_in:
            with open('shape_predictor_68_face_landmarks.dat', 'wb') as f_out:
                f_out.write(f_in.read())
        
        # Remove compressed file
        os.remove(model_path)
        print("Model downloaded and extracted successfully!")
    else:
        print("Model already exists!")

if __name__ == "__main__":
    download_dlib_model()