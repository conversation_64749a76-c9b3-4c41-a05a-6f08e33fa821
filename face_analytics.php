<?php
session_start();
require_once 'Admin/db_config.php';

// Get analytics data
$analytics = [];

try {
    // Total detections
    $stmt = $conn->query("SELECT COUNT(*) as total FROM face_analysis");
    $analytics['total_detections'] = $stmt->fetch()['total'];
    
    // Face shape distribution
    $stmt = $conn->query("SELECT face_shape, COUNT(*) as count FROM face_analysis GROUP BY face_shape ORDER BY count DESC");
    $analytics['shape_distribution'] = $stmt->fetchAll();
    
    // Recent detections (last 7 days)
    $stmt = $conn->query("SELECT COUNT(*) as count FROM face_analysis WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $analytics['recent_detections'] = $stmt->fetch()['count'];
    
    // Average confidence
    $stmt = $conn->query("SELECT AVG(confidence) as avg_confidence FROM face_analysis WHERE confidence IS NOT NULL");
    $analytics['avg_confidence'] = round($stmt->fetch()['avg_confidence'] ?? 0, 1);
    
    // Daily detections (last 30 days)
    $stmt = $conn->query("
        SELECT DATE(created_at) as date, COUNT(*) as count 
        FROM face_analysis 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at) 
        ORDER BY date DESC
    ");
    $analytics['daily_detections'] = $stmt->fetchAll();
    
} catch (PDOException $e) {
    error_log("Analytics error: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Detection Analytics - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-7xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-800">Face Detection Analytics</h1>
                        <p class="text-gray-600 mt-2">Dashboard analisis hasil deteksi bentuk wajah</p>
                    </div>
                    <div class="text-right">
                        <a href="face.php" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg inline-flex items-center">
                            <i class="fas fa-camera mr-2"></i>
                            Deteksi Wajah Baru
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-users text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-700">Total Deteksi</h3>
                            <p class="text-3xl font-bold text-blue-600"><?php echo $analytics['total_detections'] ?? 0; ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-calendar-week text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-700">7 Hari Terakhir</h3>
                            <p class="text-3xl font-bold text-green-600"><?php echo $analytics['recent_detections'] ?? 0; ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-percentage text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-700">Rata-rata Confidence</h3>
                            <p class="text-3xl font-bold text-yellow-600"><?php echo $analytics['avg_confidence'] ?? 0; ?>%</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-shapes text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-700">Bentuk Terpopuler</h3>
                            <p class="text-xl font-bold text-purple-600">
                                <?php 
                                if (!empty($analytics['shape_distribution'])) {
                                    echo strtoupper($analytics['shape_distribution'][0]['face_shape']);
                                } else {
                                    echo 'N/A';
                                }
                                ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Face Shape Distribution -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Distribusi Bentuk Wajah</h2>
                    <canvas id="shapeDistributionChart" width="400" height="300"></canvas>
                </div>

                <!-- Daily Detections -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Deteksi Harian (30 Hari Terakhir)</h2>
                    <canvas id="dailyDetectionsChart" width="400" height="300"></canvas>
                </div>
            </div>

            <!-- Recent Detections Table -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Deteksi Terbaru</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full table-auto">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bentuk Wajah</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Confidence</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="recentDetectionsTable">
                            <!-- Data akan dimuat via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Data untuk charts
        const shapeDistribution = <?php echo json_encode($analytics['shape_distribution'] ?? []); ?>;
        const dailyDetections = <?php echo json_encode($analytics['daily_detections'] ?? []); ?>;

        // Face Shape Distribution Chart
        const shapeCtx = document.getElementById('shapeDistributionChart').getContext('2d');
        new Chart(shapeCtx, {
            type: 'doughnut',
            data: {
                labels: shapeDistribution.map(item => item.face_shape.toUpperCase()),
                datasets: [{
                    data: shapeDistribution.map(item => item.count),
                    backgroundColor: [
                        '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Daily Detections Chart
        const dailyCtx = document.getElementById('dailyDetectionsChart').getContext('2d');
        new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: dailyDetections.map(item => item.date).reverse(),
                datasets: [{
                    label: 'Deteksi per Hari',
                    data: dailyDetections.map(item => item.count).reverse(),
                    borderColor: '#10B981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        // Load recent detections
        async function loadRecentDetections() {
            try {
                const response = await fetch('get_face_history.php');
                const data = await response.json();
                
                const tableBody = document.getElementById('recentDetectionsTable');
                
                if (data.success && data.data.length > 0) {
                    tableBody.innerHTML = data.data.map(item => `
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.id}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    ${item.face_shape.toUpperCase()}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.confidence || 'N/A'}%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${new Date(item.created_at).toLocaleString('id-ID')}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewDetail(${item.id})" class="text-indigo-600 hover:text-indigo-900">
                                    Lihat Detail
                                </button>
                            </td>
                        </tr>
                    `).join('');
                } else {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                Belum ada data deteksi wajah
                            </td>
                        </tr>
                    `;
                }
            } catch (error) {
                console.error('Error loading recent detections:', error);
            }
        }

        function viewDetail(id) {
            window.open(`output.php?history_id=${id}`, '_blank');
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', loadRecentDetections);
    </script>
</body>
</html>
