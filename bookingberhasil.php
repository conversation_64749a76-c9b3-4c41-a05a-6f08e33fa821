<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <title>Booking Berhasil</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .checkmark {
            width: 80px;
            height: 80px;
            display: block;
            margin: 0 auto 20px auto;
        }
        .checkmark__circle {
            stroke: #1cc88a;
            stroke-width: 5;
            fill: none;
            animation: circle 0.6s ease-in-out;
        }
        .checkmark__check {
            stroke: #1cc88a;
            stroke-width: 5;
            fill: none;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: check 0.4s 0.6s forwards;
        }
        @keyframes circle {
            0% { stroke-dasharray: 0 251.2; }
            100% { stroke-dasharray: 251.2 0; }
        }
        @keyframes check {
            to { stroke-dashoffset: 0; }
        }
    </style>
</head>
<body class="bg-[#f8f8f8] min-h-screen flex flex-col justify-center items-center">
    <div class="bg-white rounded-xl shadow-lg p-8 max-w-md w-full mt-24 text-center animate-fade-in">
        <svg class="checkmark" viewBox="0 0 52 52">
            <circle class="checkmark__circle" cx="26" cy="26" r="24"/>
            <path class="checkmark__check" fill="none" d="M14 27l8 8 16-16"/>
        </svg>
        <h2 class="text-2xl font-bold text-[#0A5144] mb-2">Terima Kasih!</h2>
        <p class="text-gray-700 mb-6">Booking Anda berhasil.<br>Kami akan segera memproses pesanan Anda.</p>
        <a href="location.php" class="inline-block bg-[#0A5144] text-white px-6 py-2 rounded-lg font-semibold shadow hover:bg-[#179b6b] transition">
            Kembali ke Halaman Utama
        </a>
    </div>
    <script>
        // Fade in animation
        document.querySelector('.animate-fade-in').style.opacity = 0;
        setTimeout(() => {
            document.querySelector('.animate-fade-in').style.transition = 'opacity 0.7s';
            document.querySelector('.animate-fade-in').style.opacity = 1;
        }, 200);
    </script>
</body>
</html>