<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Face Detection System</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">Test Face Detection System</h1>
        
        <?php
        require_once 'Admin/db_config.php';
        
        echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>";
        echo "<h2 class='text-xl font-bold mb-4'>Database Connection Test</h2>";
        
        try {
            if (isset($conn) && ($conn instanceof PDO)) {
                echo "<p class='text-green-600'>✅ Database connection: SUCCESS</p>";
                
                // Test face_analysis table
                $stmt = $conn->query("SHOW TABLES LIKE 'face_analysis'");
                if ($stmt->rowCount() > 0) {
                    echo "<p class='text-green-600'>✅ Table face_analysis: EXISTS</p>";
                    
                    // Show table structure
                    $stmt = $conn->query("DESCRIBE face_analysis");
                    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    echo "<p class='text-blue-600'>📋 Table structure: " . count($columns) . " columns</p>";
                    echo "<ul class='ml-4 text-sm text-gray-600'>";
                    foreach ($columns as $column) {
                        echo "<li>• " . $column['Field'] . " (" . $column['Type'] . ")</li>";
                    }
                    echo "</ul>";
                    
                    // Count records
                    $stmt = $conn->query("SELECT COUNT(*) as count FROM face_analysis");
                    $count = $stmt->fetch()['count'];
                    echo "<p class='text-blue-600 mt-2'>📊 Total records: " . $count . "</p>";
                    
                    // Show recent records
                    if ($count > 0) {
                        $stmt = $conn->query("SELECT * FROM face_analysis ORDER BY created_at DESC LIMIT 5");
                        $records = $stmt->fetchAll();
                        
                        echo "<h3 class='font-bold mt-4 mb-2'>Recent Records:</h3>";
                        echo "<div class='overflow-x-auto'>";
                        echo "<table class='min-w-full table-auto border-collapse border border-gray-300'>";
                        echo "<thead><tr class='bg-gray-100'>";
                        echo "<th class='border border-gray-300 px-2 py-1 text-xs'>ID</th>";
                        echo "<th class='border border-gray-300 px-2 py-1 text-xs'>Face Shape</th>";
                        echo "<th class='border border-gray-300 px-2 py-1 text-xs'>Confidence</th>";
                        echo "<th class='border border-gray-300 px-2 py-1 text-xs'>Created At</th>";
                        echo "</tr></thead><tbody>";
                        
                        foreach ($records as $record) {
                            echo "<tr>";
                            echo "<td class='border border-gray-300 px-2 py-1 text-xs'>" . $record['id'] . "</td>";
                            echo "<td class='border border-gray-300 px-2 py-1 text-xs'>" . strtoupper($record['face_shape']) . "</td>";
                            echo "<td class='border border-gray-300 px-2 py-1 text-xs'>" . ($record['confidence'] ?? 'N/A') . "%</td>";
                            echo "<td class='border border-gray-300 px-2 py-1 text-xs'>" . $record['created_at'] . "</td>";
                            echo "</tr>";
                        }
                        
                        echo "</tbody></table>";
                        echo "</div>";
                    }
                    
                } else {
                    echo "<p class='text-red-600'>❌ Table face_analysis: NOT EXISTS</p>";
                }
                
            } else {
                echo "<p class='text-red-600'>❌ Database connection: FAILED</p>";
            }
        } catch (Exception $e) {
            echo "<p class='text-red-600'>❌ Database error: " . $e->getMessage() . "</p>";
        }
        
        echo "</div>";
        ?>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-bold mb-4">File System Test</h2>
            
            <?php
            $directories = [
                'uploads/face_detection/',
                'rekomendasi/',
                'sketsa/',
                'models/'
            ];
            
            foreach ($directories as $dir) {
                if (is_dir($dir)) {
                    $files = scandir($dir);
                    $fileCount = count($files) - 2; // Exclude . and ..
                    echo "<p class='text-green-600'>✅ Directory '$dir': EXISTS ($fileCount files)</p>";
                } else {
                    echo "<p class='text-red-600'>❌ Directory '$dir': NOT EXISTS</p>";
                }
            }
            
            // Check Python files
            $pythonFiles = ['face_analysis.py', 'face_overlay.py'];
            foreach ($pythonFiles as $file) {
                if (file_exists($file)) {
                    echo "<p class='text-green-600'>✅ Python file '$file': EXISTS</p>";
                } else {
                    echo "<p class='text-red-600'>❌ Python file '$file': NOT EXISTS</p>";
                }
            }
            ?>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-bold mb-4">SessionStorage Test</h2>
            <button id="testSessionStorage" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Test SessionStorage
            </button>
            <div id="sessionStorageResult" class="mt-4"></div>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold mb-4">Quick Actions</h2>
            <div class="space-x-4">
                <a href="face.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded inline-block">
                    Start Face Detection
                </a>
                <a href="output.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded inline-block">
                    View Results
                </a>
                <a href="get_face_history.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded inline-block" target="_blank">
                    View History API
                </a>
            </div>
        </div>
    </div>
    
    <script>
        document.getElementById('testSessionStorage').addEventListener('click', function() {
            const resultDiv = document.getElementById('sessionStorageResult');
            
            // Test data
            const testData = {
                shape: 'oval',
                confidence: 95,
                alt_shape: 'round',
                alt_confidence: 5,
                recommendations: ['Textured Crop', 'Pompadour', 'Man Bun'],
                timestamp: new Date().toISOString()
            };
            
            try {
                // Save to sessionStorage
                sessionStorage.setItem('testFaceAnalysis', JSON.stringify(testData));
                
                // Read from sessionStorage
                const retrieved = JSON.parse(sessionStorage.getItem('testFaceAnalysis'));
                
                resultDiv.innerHTML = `
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                        <p><strong>✅ SessionStorage Test: SUCCESS</strong></p>
                        <p class="text-sm mt-2">Data saved and retrieved successfully:</p>
                        <pre class="text-xs mt-2 bg-gray-100 p-2 rounded">${JSON.stringify(retrieved, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        <p><strong>❌ SessionStorage Test: FAILED</strong></p>
                        <p class="text-sm mt-2">Error: ${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
