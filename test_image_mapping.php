<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Mapping - Face Detection</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">🔗 Test Image Mapping</h1>
                <p class="text-gray-600">Test mapping antara rekomendasi Python dan file gambar yang tersedia</p>
                <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 class="font-bold text-green-800">✅ Perbaikan yang Diterapkan:</h3>
                    <ul class="text-sm text-green-700 mt-2 space-y-1">
                        <li>• Mapping eksplisit antara nama rekomendasi dan file gambar</li>
                        <li>• Multiple fallback paths untuk cross-shape compatibility</li>
                        <li>• Smart placeholder generation untuk missing images</li>
                        <li>• Enhanced error handling dengan console logging</li>
                    </ul>
                </div>
            </div>

            <!-- Test Controls -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🧪 Test Controls</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="testAllMappings()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-test-tube mr-2"></i>Test All Mappings
                    </button>
                    <button onclick="simulateRecommendations()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-play mr-2"></i>Simulate Recommendations
                    </button>
                    <button onclick="testFallbackPaths()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-route mr-2"></i>Test Fallback Paths
                    </button>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">📊 Test Results</h2>
                <div id="testResults" class="space-y-4">
                    <p class="text-gray-500">Klik tombol test untuk memulai...</p>
                </div>
            </div>

            <!-- Live Simulation -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🎬 Live Simulation</h2>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Pilih Bentuk Wajah:</label>
                    <select id="faceShapeSelect" class="border border-gray-300 rounded-md px-3 py-2 w-full">
                        <option value="oval">Oval</option>
                        <option value="round">Round</option>
                        <option value="square">Square</option>
                        <option value="heart">Heart</option>
                        <option value="rectangular">Rectangular</option>
                    </select>
                </div>
                <button onclick="simulateOutputPage()" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded mb-4">
                    <i class="fas fa-eye mr-2"></i>Simulate Output Page
                </button>
                <div id="simulationResults" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Simulation results will appear here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Image mapping yang sama dengan output.php
        const imageMapping = {
            // OVAL mappings
            'Textured Crop': 'Textured Crop.jpg',
            'Pompadour': 'Pompadour.jpg',
            'Man Bun': 'Man Bun.jpg',
            'Long Shaggy': 'Long Shaggy.jpg',
            'Long Layered': 'Long Layered.jpg',
            'Haircut': 'Haircut.jpg',
            'Classic Undercut': 'Classic Undercut.jpg',
            'Caesar Cut': 'Caesar Cut.jpg',
            'Buzz Haircut': 'Buzz Haircut.jpg',
            
            // ROUND mappings
            'Two Block Hair': 'Two Block Hair.jpg',
            'Taper Fade': 'Taper Fade.jpg',
            'Fringe Haircut': 'Fringe Haircut.jpg',
            'Fluffy with Low Fade': 'Fluffy with Low Fade.jpg',
            'Crop Cut': 'Crop Cut.jpg',
            'Comma Hair': 'Comma Hair.jpg',
            'Buzz Cut': 'Buzz Cut.jpg',
            
            // SQUARE mappings
            'Wajah Persegi (Square)': 'Wajah Persegi (Square).jpg',
            'Slick Back': 'Slick Back.jpg',
            'Side Swept': 'Side Swept.jpg',
            'Quiff': 'Quiff.jpg',
            'Gaya Rambut Spike': 'Gaya Rambut Spike.jpg',
            'Crew Cut': 'Crew Cut.jpg',
            'Faux Hawk': 'Faux Hawk.jpg',
            'Comb Over': 'Comb Over.jpg',
            
            // HEART mappings
            'Taper Fade': 'Taper Fade.jpg',
            'Slick Back': 'Slick Back.jpg',
            'Side Part Fringe': 'Side Part Fringe.jpg',
            'Short Faux Hawk': 'Short Faux Hawk.jpg',
            'Long hair side part': 'Long hair side part.jpg',
            'Long Fringe': 'Long Fringe.jpg',
            'Classic Side Part': 'Classic Side Part.jpg',
            'Classic Quiff': 'Classic Quiff.jpg',
            
            // RECTANGULAR mappings
            'Textured Crop': 'Textured Crop.jpg',
            'Side Part': 'Side Part.jpg',
            'Messy Fringe Haircuts for Men': 'Messy Fringe Haircuts for Men.jpg',
            'Short Spiky': 'Short Spiky.jpg',
            'Crew Cut': 'Crew Cut.jpg',
            'Slick Back Hairstyles for Men': 'Slick Back Hairstyles for Men.jpg'
        };

        // Rekomendasi dari Python (sama dengan face_analysis.py)
        const pythonRecommendations = {
            'oval': [
                'Textured Crop - Gaya modern dengan tekstur alami',
                'Pompadour - Klasik dengan volume di atas',
                'Man Bun - Trendy untuk rambut panjang',
                'Long Shaggy - Kasual dengan layer bertingkat',
                'Classic Undercut - Bersih dan profesional'
            ],
            'round': [
                'Two Block Hair - Korean style dengan volume atas',
                'Taper Fade - Gradasi halus di samping',
                'Fringe Haircut - Poni untuk menyeimbangkan wajah',
                'Fluffy with Low Fade - Volume atas dengan fade rendah',
                'Crop Cut - Pendek dan rapi'
            ],
            'square': [
                'Slick Back - Rapi ke belakang',
                'Long Layers - Layer panjang untuk melembutkan',
                'French Crop - Pendek dengan tekstur',
                'Brush Up - Volume ke atas',
                'Bro Flow - Alami dengan gelombang'
            ],
            'heart': [
                'Undercut - Bersih di samping',
                'Textured Crop - Tekstur untuk keseimbangan',
                'Quiff - Volume depan yang stylish',
                'Pompadour - Klasik dengan volume',
                'Fringe - Poni untuk menyeimbangkan dahi'
            ],
            'rectangular': [
                'Slick Back Hairstyles for Men - Rapi dan profesional',
                'Short Sides Long Top - Kontras panjang',
                'Quiff - Volume untuk menambah lebar',
                'Man Bun - Untuk rambut panjang',
                'French Crop - Pendek dengan style'
            ]
        };

        function testAllMappings() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<p class="text-blue-600">🧪 Testing all image mappings...</p>';
            
            let totalTests = 0;
            let passedTests = 0;
            let failedTests = [];
            
            Object.keys(pythonRecommendations).forEach(shape => {
                pythonRecommendations[shape].forEach(rec => {
                    totalTests++;
                    const hairCutName = rec.split(' - ')[0].trim();
                    const mappedFileName = imageMapping[hairCutName] || `${hairCutName}.jpg`;
                    const imagePath = `rekomendasi/${shape}/${mappedFileName}`;
                    
                    // Test image availability
                    const img = new Image();
                    img.onload = function() {
                        passedTests++;
                        updateTestResults(resultsDiv, totalTests, passedTests, failedTests);
                    };
                    img.onerror = function() {
                        failedTests.push({
                            shape: shape,
                            haircut: hairCutName,
                            path: imagePath,
                            mapped: mappedFileName
                        });
                        updateTestResults(resultsDiv, totalTests, passedTests, failedTests);
                    };
                    img.src = imagePath;
                });
            });
        }

        function updateTestResults(resultsDiv, total, passed, failed) {
            if (passed + failed.length === total) {
                const passRate = Math.round((passed / total) * 100);
                
                resultsDiv.innerHTML = `
                    <div class="space-y-4">
                        <div class="p-4 ${passRate >= 80 ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'} border rounded-lg">
                            <h3 class="font-bold ${passRate >= 80 ? 'text-green-800' : 'text-yellow-800'}">
                                📊 Test Results Summary
                            </h3>
                            <p class="${passRate >= 80 ? 'text-green-700' : 'text-yellow-700'}">
                                Total Tests: ${total}<br>
                                Passed: ${passed} (${passRate}%)<br>
                                Failed: ${failed.length}
                            </p>
                        </div>
                        
                        ${failed.length > 0 ? `
                            <div class="p-4 bg-red-50 border-red-200 border rounded-lg">
                                <h3 class="font-bold text-red-800">❌ Failed Mappings:</h3>
                                <div class="mt-2 space-y-2">
                                    ${failed.slice(0, 5).map(item => `
                                        <div class="text-sm text-red-700 bg-white p-2 rounded border">
                                            <strong>${item.shape}/${item.haircut}</strong><br>
                                            Path: ${item.path}<br>
                                            Mapped: ${item.mapped}
                                        </div>
                                    `).join('')}
                                    ${failed.length > 5 ? `<p class="text-sm text-red-600">... and ${failed.length - 5} more</p>` : ''}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }
        }

        function simulateRecommendations() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<p class="text-green-600">🎬 Simulating recommendation display...</p>';
            
            // Simulate the exact process from output.php
            const faceShape = 'square'; // Example
            const recommendations = pythonRecommendations[faceShape];
            
            let html = '<div class="space-y-4">';
            html += `<h3 class="font-bold text-gray-800">Simulating ${faceShape.toUpperCase()} recommendations:</h3>`;
            
            recommendations.forEach((rec, index) => {
                const hairCutName = rec.split(' - ')[0].trim();
                const mappedFileName = imageMapping[hairCutName] || `${hairCutName}.jpg`;
                const modelImagePaths = [
                    `rekomendasi/${faceShape}/${mappedFileName}`,
                    `rekomendasi/${faceShape}/${hairCutName}.jpg`,
                    `rekomendasi/oval/${mappedFileName}`,
                    `rekomendasi/square/${mappedFileName}`
                ];
                
                html += `
                    <div class="p-3 border rounded-lg bg-gray-50">
                        <strong>${hairCutName}</strong><br>
                        <small class="text-gray-600">Mapped: ${mappedFileName}</small><br>
                        <small class="text-blue-600">Primary path: ${modelImagePaths[0]}</small><br>
                        <small class="text-gray-500">Fallbacks: ${modelImagePaths.length - 1} paths</small>
                    </div>
                `;
            });
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        function testFallbackPaths() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<p class="text-purple-600">🔄 Testing fallback path system...</p>';
            
            // Test cross-shape fallbacks
            const testCases = [
                { shape: 'square', haircut: 'Textured Crop' },
                { shape: 'heart', haircut: 'Quiff' },
                { shape: 'rectangular', haircut: 'Crew Cut' }
            ];
            
            let html = '<div class="space-y-4">';
            html += '<h3 class="font-bold text-gray-800">Testing cross-shape fallback paths:</h3>';
            
            testCases.forEach(testCase => {
                const { shape, haircut } = testCase;
                const mappedFileName = imageMapping[haircut] || `${haircut}.jpg`;
                const fallbackPaths = [
                    `rekomendasi/${shape}/${mappedFileName}`,
                    `rekomendasi/oval/${mappedFileName}`,
                    `rekomendasi/square/${mappedFileName}`,
                    `rekomendasi/round/${mappedFileName}`,
                    `rekomendasi/heart/${mappedFileName}`,
                    `rekomendasi/rectangular/${mappedFileName}`
                ];
                
                html += `
                    <div class="p-4 border rounded-lg">
                        <h4 class="font-bold">${shape.toUpperCase()} - ${haircut}</h4>
                        <div class="mt-2 space-y-1">
                            ${fallbackPaths.map((path, index) => `
                                <div class="text-sm ${index === 0 ? 'text-blue-600 font-medium' : 'text-gray-600'}">
                                    ${index === 0 ? '🎯 Primary:' : `🔄 Fallback ${index}:`} ${path}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        function simulateOutputPage() {
            const faceShape = document.getElementById('faceShapeSelect').value;
            const simulationDiv = document.getElementById('simulationResults');
            
            simulationDiv.innerHTML = '<p class="text-indigo-600 col-span-full">🎬 Simulating output page display...</p>';
            
            const recommendations = pythonRecommendations[faceShape] || [];
            
            setTimeout(() => {
                simulationDiv.innerHTML = '';
                
                recommendations.forEach((rec, index) => {
                    const hairCutName = rec.split(' - ')[0].trim();
                    const description = rec.split(' - ')[1] || 'Gaya rambut yang cocok';
                    const mappedFileName = imageMapping[hairCutName] || `${hairCutName}.jpg`;
                    const modelImagePath = `rekomendasi/${faceShape}/${mappedFileName}`;
                    const score = Math.max(10 - index, 7);
                    
                    const cardHtml = `
                        <div class="bg-white rounded-lg shadow-md p-4 flex flex-col items-center text-center border">
                            <h3 class="text-sm font-semibold text-gray-800 mb-2">${hairCutName}</h3>
                            <p class="text-xs text-gray-600 mb-3">${description}</p>
                            <div class="flex justify-center items-center gap-2 mb-3">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 rounded-lg overflow-hidden border-2 border-green-500 mb-1">
                                        <img src="${modelImagePath}" alt="Model ${hairCutName}" class="w-full h-full object-cover"
                                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjMjJjNTVlIi8+Cjx0ZXh0IHg9IjMyIiB5PSIyOCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE2IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+8J+RpDwvdGV4dD4KPHRleHQgeD0iMzIiIHk9IjQ0IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iOCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPk1vZGVsPC90ZXh0Pgo8L3N2Zz4K'">
                                    </div>
                                    <p class="text-gray-600 text-xs font-medium">Model</p>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 rounded-lg overflow-hidden border-2 border-blue-500 mb-1 bg-blue-100 flex items-center justify-center">
                                        <span class="text-blue-600 text-xs">You</span>
                                    </div>
                                    <p class="text-gray-600 text-xs font-medium">Anda</p>
                                </div>
                            </div>
                            <div class="w-full">
                                <p class="text-gray-700 font-medium text-xs">Skor <span class="text-green-600">${score}/10</span></p>
                                <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                    <div class="bg-green-500 h-1.5 rounded-full" style="width: ${score * 10}%"></div>
                                </div>
                                <span class="inline-block bg-yellow-400 text-gray-800 text-xs font-semibold px-2 py-0.5 rounded-full mt-2">Rekomendasi</span>
                            </div>
                        </div>
                    `;
                    simulationDiv.insertAdjacentHTML('beforeend', cardHtml);
                });
            }, 1000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔗 Image mapping test system initialized');
            console.log('📊 Total mappings:', Object.keys(imageMapping).length);
        });
    </script>
</body>
</html>
