<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Manajemen Booking - Admin Panel</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <style>
    .sidebar {
      background: #2d3748;
      color: #fff;
    }
    .sidebar .logo {
      background: #1a202c;
      padding: 1rem;
      border-bottom: 1px solid #4a5568;
    }
    .sidebar .logo h1 {
      font-size: 1.5rem;
      font-weight: 700;
    }
    .sidebar nav ul li {
      transition: all 0.3s ease;
    }
    .sidebar nav ul li a {
      color: #fff;
    }
    .sidebar nav ul li.active {
      background: #4299e1;
    }
    .sidebar nav ul li.active a {
      color: #fff;
    }
    .sidebar nav ul li i {
      color: #4299e1;
    }
    .sidebar nav ul li.active i {
      color: #fff;
    }
    .rotate-180 {
      transform: rotate(180deg);
    }
  </style>
</head>
<body class="bg-gray-900">
  <div class="flex h-screen">
    <!-- Sidebar -->
    <div class="w-64 sidebar">
      <div class="p-4">
        <h1 class="text-xl font-bold mb-4">Admin Panel</h1>
        <nav>
          <ul class="space-y-1">

            <!-- Manajemen Booking -->
            <li>
              <button onclick="toggleBookingSubmenu()" class="main-menu-btn w-full flex items-center justify-between px-4 py-3 bg-gray-800 rounded-lg focus:outline-none transition-colors duration-200">
                <span class="flex items-center space-x-3">
                  <i class="fas fa-calendar-check"></i>
                  <span>Manajemen Booking</span>
                </span>
                <i id="arrowBookingIcon" class="fas fa-chevron-down transition-transform"></i>
              </button>

              <!-- Submenu -->
              <ul id="bookingSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                <li>
                  <a href="data_booking.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Data Booking</span>
                  </a>
                </li>
                <li>
                  <a href="kelola_lokasi.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>Kelola Lokasi</span>
                  </a>
                </li>
                <li>
                  <a href="kelola_barber.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                    <i class="fas fa-cut"></i>
                    <span>Kelola Barber</span>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Kelola Produk -->
            <li class="mb-2">
      <button onclick="toggleSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
        <span class="flex items-center space-x-3">
          <i class="fas fa-box"></i>
          <span>Manajemen Produk</span>
        </span>
        <i id="arrowIcon" class="fas fa-chevron-down transition-transform"></i>
      </button>

      <!-- Submenu -->
      <ul id="submenu" class="ml-8 mt-2 space-y-2 hidden">
        <li>
          <a href="kelola_produk.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
            <i class="fas fa-cube"></i>
            <span>Produk</span>
          </a>
        </li>
        <li>
          <a href="kelola_pembeli.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
            <i class="fas fa-users"></i>
            <span>Data Pembeli</span>
          </a>
        </li>
        <li>
          <a href="kelola_metode_pembayaran.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
            <i class="fas fa-money-check-alt"></i>
            <span>Metode Pembayaran</span>
          </a>
        </li>
      </ul>
    </li>
    <script>
  function toggleSubmenu() {
    const submenu = document.getElementById('submenu');
    const icon = document.getElementById('arrowIcon');
    submenu.classList.toggle('hidden');
    icon.classList.toggle('rotate-180');
  }
</script>
            <!-- Kelola Admin -->
            <li class="px-4 py-3">
              <a href="kelola_admin.php" class="flex items-center space-x-3">
                <i class="fas fa-user-shield"></i>
                <span>Kelola Admin</span>
              </a>
            </li>

            <!-- Kelola Lowongan -->
            <li class="px-4 py-3">
              <a href="kelola_lowongan.php" class="flex items-center space-x-3">
                <i class="fas fa-briefcase"></i>
                <span>Kelola Lowongan</span>
              </a>
            </li>

            <!-- Logout -->
            <li class="px-4 py-3">
              <a href="Logout.php" class="flex items-center space-x-3 text-red-400 hover:text-red-600">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
              </a>
            </li>

          </ul>
        </nav>
      </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 p-8">
      <h1 class="text-2xl font-bold mb-6">Manajemen Booking</h1>
      <!-- Add your content here -->
    </div>
  </div>

  <script>
    function toggleBookingSubmenu() {
      const submenu = document.getElementById('bookingSubmenu');
      const arrow = document.getElementById('arrowBookingIcon');
      
      submenu.classList.toggle('hidden');
      arrow.classList.toggle('rotate-180');
    }

    // Menu activation logic
    document.addEventListener('DOMContentLoaded', function () {
      const menuButtons = document.querySelectorAll('.main-menu-btn');
      const currentPage = window.location.href;

      // Reset all menu items
      menuButtons.forEach(button => {
        button.classList.remove('bg-blue-600', 'text-white');
        button.classList.add('bg-gray-800');
      });

      // Check current page and activate corresponding menu
      if (currentPage.includes("kelola_booking") || 
          currentPage.includes("data_booking") || 
          currentPage.includes("kelola_lokasi") || 
          currentPage.includes("kelola_barber")) {
        const bookingBtn = document.querySelector('button[onclick="toggleBookingSubmenu()"]');
        if (bookingBtn) {
          bookingBtn.classList.add('bg-blue-600', 'text-white');
          bookingBtn.classList.remove('bg-gray-800');
          
          // Open submenu if on booking-related pages
          if (currentPage.includes("data_booking") || 
              currentPage.includes("kelola_lokasi") || 
              currentPage.includes("kelola_barber")) {
            document.getElementById('bookingSubmenu').classList.remove('hidden');
            document.getElementById('arrowBookingIcon').classList.add('rotate-180');
          }
        }
      }
      else if (currentPage.includes("kelola_produk")) {
        const produkLink = document.querySelector('a[href="kelola_produk.php"]');
        if (produkLink) {
          produkLink.classList.add('bg-blue-600', 'text-white');
          produkLink.classList.remove('bg-gray-800');
        }
      }
      else if (currentPage.includes("kelola_admin")) {
        const adminLink = document.querySelector('a[href="kelola_admin.php"]');
        if (adminLink) {
          adminLink.classList.add('bg-blue-600', 'text-white');
          adminLink.classList.remove('bg-gray-800');
        }
      }
    });
  </script>
</body>
</html>
