<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'db_config.php';

// Log POST data for debugging
file_put_contents('debug.log', 'POST Data: ' . print_r($_POST, true) . PHP_EOL, FILE_APPEND);

// Initialize response
$response = ['success' => false, 'message' => ''];

try {
    // Validate required fields
    if (!isset($_POST['nama_produk']) || !isset($_POST['deskripsi']) || 
        !isset($_POST['harga']) || !isset($_POST['kategori_id'])) {
        throw new Exception('Semua field wajib diisi!');
    }

    // Log field values
    file_put_contents('debug.log', 'Field Values: ' . print_r([
        'nama_produk' => $_POST['nama_produk'],
        'deskripsi' => $_POST['deskripsi'],
        'harga' => $_POST['harga'],
        'kategori_id' => $_POST['kategori_id']
    ], true) . PHP_EOL, FILE_APPEND);

    // Start transaction
    try {
        $conn->beginTransaction();
        file_put_contents('debug.log', 'Transaction started successfully' . PHP_EOL, FILE_APPEND);
    } catch (PDOException $e) {
        throw new Exception('Failed to start transaction: ' . $e->getMessage());
    }

    // Upload main image
    // Define upload directories
    $upload_dir = '../uploads/';
    $upload_dir = str_replace('/', DIRECTORY_SEPARATOR, $upload_dir);
    $upload_dir = str_replace('\\', DIRECTORY_SEPARATOR, $upload_dir);
    
    // Create upload directory if it doesn't exist
    if (!file_exists($upload_dir)) {
        if (!mkdir($upload_dir, 0777, true)) {
            throw new Exception('Gagal membuat direktori upload');
        }
    }

    // Check directory permissions
    if (!is_writable($upload_dir)) {
        throw new Exception('Direktori upload tidak dapat ditulis');
    }

    $gambar_utama = '';
    if (isset($_FILES['gambar_utama'])) {
        file_put_contents('debug.log', 'File upload status: ' . print_r($_FILES['gambar_utama'], true) . PHP_EOL, FILE_APPEND);
        
        // Validate file upload
        if ($_FILES['gambar_utama']['error'] !== UPLOAD_ERR_OK) {
            $errors = [
                UPLOAD_ERR_INI_SIZE => 'File terlalu besar (di atas batas server)',
                UPLOAD_ERR_FORM_SIZE => 'File terlalu besar (di atas batas form)',
                UPLOAD_ERR_PARTIAL => 'File hanya terupload sebagian',
                UPLOAD_ERR_NO_FILE => 'Tidak ada file yang diupload',
                UPLOAD_ERR_NO_TMP_DIR => 'Direktori temp tidak tersedia',
                UPLOAD_ERR_CANT_WRITE => 'Gagal menulis file ke disk',
                UPLOAD_ERR_EXTENSION => 'Ekstensi file tidak diizinkan'
            ];
            
            $error_msg = $errors[$_FILES['gambar_utama']['error']] ?? 'Error tidak diketahui';
            throw new Exception($error_msg);
        }

        // Validate file type
        $allowed_types = ['image/jpeg', 'image/png', 'image/webp'];
        if (!in_array($_FILES['gambar_utama']['type'], $allowed_types)) {
            throw new Exception('Format file tidak diizinkan. Hanya JPG, PNG, dan WEBP yang diizinkan.');
        }

        // Generate unique filename
        $file_ext = pathinfo($_FILES['gambar_utama']['name'], PATHINFO_EXTENSION);
        $file_name = uniqid() . '.' . $file_ext;
        $file_path = $upload_dir . DIRECTORY_SEPARATOR . $file_name;

        // Move uploaded file
        if (!move_uploaded_file($_FILES['gambar_utama']['tmp_name'], $file_path)) {
            throw new Exception('Gagal mengunggah gambar utama');
        }

        // Set proper permissions
        if (!chmod($file_path, 0644)) {
            throw new Exception('Gagal mengatur izin file');
        }

        $gambar_utama = $file_name;
    }

    // Log data yang akan dimasukkan
    file_put_contents('debug.log', 'Data yang akan dimasukkan ke database: ' . PHP_EOL);
    file_put_contents('debug.log', 'nama_produk: ' . $_POST['nama_produk'] . PHP_EOL);
    file_put_contents('debug.log', 'deskripsi: ' . $_POST['deskripsi'] . PHP_EOL);
    file_put_contents('debug.log', 'harga: ' . $_POST['harga'] . PHP_EOL);
    file_put_contents('debug.log', 'gambar_utama: ' . ($gambar_utama ?? 'NULL') . PHP_EOL);
    file_put_contents('debug.log', 'kategori_id: ' . $_POST['kategori_id'] . PHP_EOL);

    // Insert main product
    try {
        $stmt = $conn->prepare("INSERT INTO produk (nama_produk, deskripsi, harga, gambar_utama, kategori_id) 
                              VALUES (:nama_produk, :deskripsi, :harga, :gambar_utama, :kategori_id)");
        file_put_contents('debug.log', 'Query prepared: ' . $stmt->queryString . PHP_EOL);
        
        $stmt->execute([
            'nama_produk' => $_POST['nama_produk'],
            'deskripsi' => $_POST['deskripsi'],
            'harga' => $_POST['harga'],
            'gambar_utama' => $gambar_utama,
            'kategori_id' => $_POST['kategori_id']
        ]);
        file_put_contents('debug.log', 'Data berhasil dimasukkan ke database' . PHP_EOL);
    } catch (PDOException $e) {
        file_put_contents('debug.log', 'Error saat insert: ' . $e->getMessage() . PHP_EOL);
        throw $e;
    }
    $produk_id = $conn->lastInsertId();

    // Store variations in JSON column
    if (isset($_POST['ukuran_ml']) && isset($_POST['harga_variasi'])) {
        $variasi = array();
        foreach ($_POST['ukuran_ml'] as $index => $ukuran) {
            if (!empty($ukuran) && isset($_POST['harga_variasi'][$index])) {
                $variasi[] = array(
                    'ukuran_ml' => $ukuran,
                    'harga' => $_POST['harga_variasi'][$index]
                );
            }
        }
        
        if (!empty($variasi)) {
            $variasi_json = json_encode($variasi);
            $stmt = $conn->prepare("UPDATE produk SET variasi = :variasi WHERE id = :id");
            $stmt->execute([
                ':variasi' => $variasi_json,
                ':id' => $produk_id
            ]);
        }
    }

    // Log data gambar tambahan
    file_put_contents('debug.log', 'Data gambar tambahan: ' . PHP_EOL);
    if (isset($_FILES['gambar_tambahan'])) {
        file_put_contents('debug.log', 'Jumlah gambar tambahan: ' . count($_FILES['gambar_tambahan']['name']) . PHP_EOL);
        foreach ($_FILES['gambar_tambahan']['name'] as $index => $name) {
            file_put_contents('debug.log', 'Gambar ' . $index . ': ' . $name . PHP_EOL);
            file_put_contents('debug.log', 'Error: ' . $_FILES['gambar_tambahan']['error'][$index] . PHP_EOL);
        }
    }

    // Insert additional images
    if (isset($_FILES['gambar_tambahan']) && is_array($_FILES['gambar_tambahan']['name'])) {
        foreach ($_FILES['gambar_tambahan']['name'] as $index => $file_name) {
            if ($_FILES['gambar_tambahan']['error'][$index] == 0) {
                $file_name = uniqid() . '_' . $file_name;
                $file_path = $upload_dir . $file_name;
                
                // Log path file
                file_put_contents('debug.log', 'Mengupload gambar: ' . $file_name . PHP_EOL);
                file_put_contents('debug.log', 'Path tujuan: ' . $file_path . PHP_EOL);
                
                if (!move_uploaded_file($_FILES['gambar_tambahan']['tmp_name'][$index], $file_path)) {
                    file_put_contents('debug.log', 'Gagal mengupload gambar: ' . $file_name . PHP_EOL);
                    throw new Exception('Gagal mengunggah gambar tambahan');
                }
                
                // Set proper permissions
                if (!chmod($file_path, 0644)) {
                    file_put_contents('debug.log', 'Gagal mengatur izin file: ' . $file_name . PHP_EOL);
                    throw new Exception('Gagal mengatur izin file gambar tambahan');
                }

                try {
                    $stmt = $conn->prepare("INSERT INTO gambar_produk (produk_id, gambar, label) 
                                          VALUES (:produk_id, :gambar, :label)");
                    $stmt->execute([
                        'produk_id' => $produk_id,
                        'gambar' => $file_name,
                        'label' => $_POST['label_gambar'][$index] ?? ''
                    ]);
                    file_put_contents('debug.log', 'Berhasil menyimpan gambar tambahan: ' . $file_name . PHP_EOL);
                } catch (PDOException $e) {
                    file_put_contents('debug.log', 'Error menyimpan gambar tambahan: ' . $e->getMessage() . PHP_EOL);
                    throw $e;
                }
            } else {
                file_put_contents('debug.log', 'Error upload gambar ' . $index . ': ' . $_FILES['gambar_tambahan']['error'][$index] . PHP_EOL);
            }
        }
    }



    $conn->commit();
    $response = ['success' => true, 'message' => 'Produk berhasil ditambahkan'];
} catch (Exception $e) {
    try {
        $conn->rollBack();
        file_put_contents('debug.log', 'Transaction rolled back' . PHP_EOL, FILE_APPEND);
    } catch (PDOException $e2) {
        file_put_contents('debug.log', 'Failed to rollback transaction: ' . $e2->getMessage() . PHP_EOL, FILE_APPEND);
    }
    $response = ['success' => false, 'message' => $e->getMessage()];
}

header('Content-Type: application/json');
http_response_code($response['success'] ? 200 : 400);
echo json_encode($response);
?>
