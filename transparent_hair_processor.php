<?php
header('Content-Type: application/json');

function applyTransparentHair($userImagePath, $hairOverlayPath) {
    try {
        if (!file_exists($userImagePath)) {
            return ['success' => false, 'error' => 'User image not found'];
        }
        
        if (!file_exists($hairOverlayPath)) {
            return ['success' => false, 'error' => 'Hair overlay not found'];
        }
        
        // Call Python script
        $pythonScript = __DIR__ . '/transparent_hair_applier.py';
        $command = sprintf('python "%s" "%s" "%s"', $pythonScript, $userImagePath, $hairOverlayPath);
        
        $output = shell_exec($command . ' 2>&1');
        
        if (!$output) {
            return ['success' => false, 'error' => 'Hair application failed'];
        }
        
        $result = json_decode($output, true);
        
        if (!$result) {
            return ['success' => false, 'error' => 'Invalid processing output', 'debug' => $output];
        }
        
        return $result;
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $userImage = $input['user_image'] ?? '';
    $hairOverlay = $input['hair_overlay'] ?? '';
    
    if (empty($userImage) || empty($hairOverlay)) {
        echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
        exit;
    }
    
    $result = applyTransparentHair($userImage, $hairOverlay);
    echo json_encode($result);
    exit;
}

echo json_encode(['success' => false, 'error' => 'Invalid request method']);
?>