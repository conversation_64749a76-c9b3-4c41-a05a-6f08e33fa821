@echo off
echo Installing Python dependencies for 3D Face Modeling...
echo.

pip install opencv-python==********
pip install mediapipe==0.10.7
pip install numpy==1.24.3
pip install scipy==1.11.4
pip install Pillow==10.0.1

echo.
echo Attempting to install dlib (may require Visual Studio Build Tools)...
pip install dlib==19.24.2

echo.
echo Installation complete!
echo.
echo Testing imports...
python -c "import cv2; print('OpenCV: OK')"
python -c "import mediapipe; print('MediaPipe: OK')"
python -c "import numpy; print('NumPy: OK')"
python -c "import scipy; print('SciPy: OK')"
python -c "import PIL; print('Pillow: OK')"
python -c "import dlib; print('dlib: OK')" 2>nul || echo "dlib: FAILED (install Visual Studio Build Tools)"

pause