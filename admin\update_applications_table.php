<?php
/**
 * <PERSON><PERSON><PERSON> to update applications table with new columns for quiz functionality
 * Run this once to add the new columns
 */

require_once 'db_config.php';

// Set content type to HTML for better display
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Update</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .container { max-width: 800px; margin: 0 auto; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Database Update for Quiz System</h1>
        <p>Updating applications table to support quiz functionality...</p>
        <hr>

<?php

try {
    // Add new columns to applications table
    $alterQueries = [
        "ALTER TABLE applications ADD COLUMN IF NOT EXISTS quiz_score DECIMAL(5,2) DEFAULT NULL COMMENT 'Quiz score percentage'",
        "ALTER TABLE applications ADD COLUMN IF NOT EXISTS quiz_answers JSON DEFAULT NULL COMMENT 'Quiz answers in JSON format'",
        "ALTER TABLE applications ADD COLUMN IF NOT EXISTS status_reason TEXT DEFAULT NULL COMMENT 'Reason for automatic status assignment'",
        "ALTER TABLE applications ADD COLUMN IF NOT EXISTS evaluation_score DECIMAL(5,2) DEFAULT NULL COMMENT 'Comprehensive evaluation score'",
        "ALTER TABLE applications ADD COLUMN IF NOT EXISTS evaluation_details JSON DEFAULT NULL COMMENT 'Detailed evaluation breakdown'"
    ];
    
    foreach ($alterQueries as $query) {
        try {
            $conn->exec($query);
            echo "<p class='success'>✅ Successfully executed: " . htmlspecialchars(substr($query, 0, 50)) . "...</p>";
        } catch (PDOException $e) {
            // Check if error is because column already exists
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<p class='info'>ℹ️  Column already exists: " . htmlspecialchars(substr($query, 0, 50)) . "...</p>";
            } else {
                echo "<p class='error'>❌ Error executing: " . htmlspecialchars(substr($query, 0, 50)) . "...</p>";
                echo "<p class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    }
    
    // Create uploads directory if it doesn't exist
    $uploadDirs = [
        '../uploads/resumes/',
        '../uploads/portfolios/'
    ];
    
    foreach ($uploadDirs as $dir) {
        if (!file_exists($dir)) {
            if (mkdir($dir, 0777, true)) {
                echo "<p class='success'>✅ Created directory: " . htmlspecialchars($dir) . "</p>";
            } else {
                echo "<p class='error'>❌ Failed to create directory: " . htmlspecialchars($dir) . "</p>";
            }
        } else {
            echo "<p class='info'>ℹ️  Directory already exists: " . htmlspecialchars($dir) . "</p>";
        }
    }

    echo "<hr>";
    echo "<h2 class='success'>🎉 Database update completed successfully!</h2>";
    echo "<h3>New features added:</h3>";
    echo "<ul>";
    echo "<li>✅ Quiz score tracking</li>";
    echo "<li>✅ Quiz answers storage</li>";
    echo "<li>✅ Automatic status assignment with reasons</li>";
    echo "<li>✅ File upload directories</li>";
    echo "</ul>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ol>";
    echo "<li>Test the quiz system by applying for a job</li>";
    echo "<li>Check the admin panel to see quiz results</li>";
    echo "<li>Verify automatic status assignment works</li>";
    echo "</ol>";

} catch (PDOException $e) {
    echo "<p class='error'>❌ Database connection error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
        </div>
    </body>
</html>
