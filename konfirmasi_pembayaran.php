<?php
require_once 'Admin/db_config.php';

$order_id = isset($_GET['order_id']) ? $_GET['order_id'] : null;

$order_data = null;
$buyer_data = null;
$address_data = null;
$payment_method_data = null;
$order_items = [];

if ($order_id) {
    try {
        // Fetch order details
        $stmt_order = $conn->prepare("
            SELECT o.*, pm.name as payment_method_name, pm.type as payment_method_type, pm.qr_code_image
            FROM orders o
            LEFT JOIN payment_methods pm ON o.payment_method_id = pm.id
            WHERE o.id = :order_id
        ");
        $stmt_order->execute(['order_id' => $order_id]);
        $order_data = $stmt_order->fetch(PDO::FETCH_ASSOC);

        if ($order_data) {
            // Fetch buyer data
            $stmt_buyer = $conn->prepare("SELECT nama_pembeli, phone FROM pembeli WHERE id = :pembeli_id");
            $stmt_buyer->execute(['pembeli_id' => $order_data['pembeli_id']]);
            $buyer_data = $stmt_buyer->fetch(PDO::FETCH_ASSOC);

            // Fetch address data
            $stmt_address = $conn->prepare("SELECT address_name, full_address, phone_number FROM user_addresses WHERE id = :address_id");
            $stmt_address->execute(['address_id' => $order_data['address_id']]);
            $address_data = $stmt_address->fetch(PDO::FETCH_ASSOC);

            // Fetch order items
            $stmt_items = $conn->prepare("SELECT product_name_at_purchase, variant_name_at_purchase, quantity, price_per_unit, total_price FROM order_items WHERE order_id = :order_id");
            $stmt_items->execute(['order_id' => $order_id]);
            $raw_order_items = $stmt_items->fetchAll(PDO::FETCH_ASSOC);

            // Group items by product_name and variant_name for consolidated display
            $grouped_items = [];
            foreach ($raw_order_items as $item) {
                $key = $item['product_name_at_purchase'] . '-' . ($item['variant_name_at_purchase'] ?? 'null');
                if (isset($grouped_items[$key])) {
                    $grouped_items[$key]['quantity'] += $item['quantity'];
                    $grouped_items[$key]['total_price'] += $item['total_price'];
                } else {
                    $grouped_items[$key] = $item;
                }
            }
            $order_items = $grouped_items;

        } else {
            // Order not found
            header('Location: index.php?status=order_not_found'); // Redirect to homepage or error page
            exit();
        }

    } catch (PDOException $e) {
        error_log("Database Error on konfirmasi_pembayaran.php: " . $e->getMessage());
        // Optionally, redirect to an error page or display a message
        header('Location: index.php?status=db_error');
        exit();
    }
}

// Determine payment status display
$payment_status_text = 'Pending';
$payment_status_class = 'text-yellow-600';
if ($order_data) {
    switch ($order_data['payment_status']) {
        case 'paid':
            $payment_status_text = 'Lunas';
            $payment_status_class = 'text-green-600';
            break;
        case 'failed':
            $payment_status_text = 'Gagal';
            $payment_status_class = 'text-red-600';
            break;
        case 'refunded':
            $payment_status_text = 'Dikembalikan';
            $payment_status_class = 'text-blue-600';
            break;
        default:
            $payment_status_text = 'Pending';
            $payment_status_class = 'text-yellow-600';
            break;
    }
}

if ($order_data) {
    // Create notification for successful payment
    try {
        $stmt_notif = $conn->prepare("
            INSERT INTO notifications (user_id, order_id, message) 
            VALUES (:user_id, :order_id, :message)
        ");
        
        $message = "Pembayaran untuk Order #" . $order_data['id'] . " telah dikonfirmasi";
        
        $stmt_notif->execute([
            'user_id' => $order_data['pembeli_id'],
            'order_id' => $order_data['id'],
            'message' => $message
        ]);
    } catch (PDOException $e) {
        error_log("Error creating notification: " . $e->getMessage());
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Konfirmasi Pembayaran - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0A5144 0%, #006A63 100%);
        }
        /* For print */
        @media print {
            body {
                background-color: #fff;
            }
            .no-print {
                display: none !important;
            }
            .receipt-card {
                border: 1px solid #ccc;
                box-shadow: none;
                margin: 0;
                padding: 1rem;
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="gradient-bg fixed top-0 left-0 right-0 z-20 p-4 shadow-md">
        <div class="flex items-center justify-between">
            <a href="javascript:history.back()" class="text-white">
                <i class="fas fa-arrow-left text-lg"></i>
            </a>
            <h1 class="text-xl font-bold text-white">Status Transaksi</h1>
            <div class="w-6"></div> <!-- Spacer for balance -->
        </div>
    </header>

    <!-- Transaction Status Section -->
    <div class="gradient-bg pt-20 pb-8 px-4 text-white text-center relative">
        <div class="flex items-center justify-center mb-2">
            <div class="bg-white rounded-full p-2">
                <i class="fas fa-check-circle text-[#28a745] text-4xl"></i> <!-- Checkmark icon -->
            </div>
        </div>
        <p class="text-lg font-semibold">"Terima kasih! Transaksi berhasil. Nikmati layanan Pixel Barbershop."</p>
    </div>

    <!-- Main Content Area -->
    <div class="relative -mt-8 z-10 px-4">
        <!-- Transaction ID Card (existing content will go here, adapted) -->
        <?php if ($order_data): ?>
        <div class="bg-white rounded-lg shadow-lg p-6 mb-4 relative">
            <!-- Original Receipt Header Re-integrated -->
            <div class="text-center mb-6 relative z-10">
                <div class="mb-2">
                    <i class="fas fa-store text-4xl text-gray-800"></i>
                </div>
                <h2 class="text-xl font-bold text-gray-800">PIXEL TONSORIUM</h2>
                <p class="text-gray-700">Jl. R.A. Kosasih No.278, Subangjaya,<br>Kec. Cikole, Kota Sukabumi, Jawa Barat</p>
                <p class="text-gray-700 font-bold">No. Telp 08997573730</p>
                <p class="text-gray-700 mt-1 font-semibold">Order ID: #<?php echo htmlspecialchars($order_data['id']); ?></p>
            </div>

            <div class="text-center mb-6">
                <h2 class="text-xl font-bold text-gray-900 mb-2">Transaksi Berhasil!</h2>
                <p class="text-sm text-gray-600 mb-4"><?php echo htmlspecialchars(date('d F Y H:i', strtotime($order_data['created_at']))); ?> WIB</p>
                <p class="text-4xl font-bold text-gray-900">Rp<?php echo number_format($order_data['total_amount'], 0, ',', '.'); ?></p>
            </div>

            <div class="space-y-3">
                <div class="flex justify-between border-b border-dashed border-gray-200 pb-2">
                    <span class="text-gray-600">Metode Pembayaran</span>
                    <span class="text-gray-800 font-medium"><?php echo htmlspecialchars($order_data['payment_method_name'] ?? 'N/A'); ?></span>
                </div>
                <div class="flex justify-between border-b border-dashed border-gray-200 pb-2">
                    <span class="text-gray-600">Biaya Pengiriman</span>
                    <span class="text-gray-800 font-medium">Rp <?php echo number_format($order_data['delivery_cost'], 0, ',', '.'); ?></span>
                </div>
                <div class="flex justify-between border-b border-dashed border-gray-200 pb-2">
                    <span class="text-gray-600">Biaya Layanan</span>
                    <span class="text-gray-800 font-medium">Rp <?php echo number_format($order_data['service_fee'], 0, ',', '.'); ?></span>
                </div>
            </div>

            <!-- Pembeli and Address Details -->
            <div class="border-t border-b border-dashed border-gray-400 py-3 mt-4 mb-4">
                <p>Pembeli: <?php echo htmlspecialchars($buyer_data['nama_pembeli'] ?? 'N/A'); ?></p>
                <p>Telp: <?php echo htmlspecialchars($buyer_data['phone'] ?? 'N/A'); ?></p>
                <p class="mt-2">Alamat Pengiriman:</p>
                <p class="ml-4"><?php echo htmlspecialchars($address_data['address_name'] ?? 'N/A'); ?></p>
                <p class="ml-4"><?php echo nl2br(htmlspecialchars($address_data['full_address'] ?? 'N/A')); ?></p>
            </div>

            <!-- Order Items -->
            <h4 class="sr-only">Item Pesanan:</h4>
            <div class="overflow-x-auto mb-4">
                <table class="w-full border-collapse">
                    <thead>
                        <tr class="border-b border-dashed border-gray-400">
                            <th class="pb-1 text-left">Produk</th>
                            <th class="pb-1 text-right">Qty</th>
                            <th class="pb-1 text-right">Harga Satuan</th>
                            <th class="pb-1 text-right">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                            $total_qty = 0; 
                            $item_no = 1;
                            if (!empty($order_items)): 
                                foreach ($order_items as $item): 
                                    $total_qty += $item['quantity'];
                        ?>
                                <tr>
                                    <td class="pt-1 text-left"><?php echo $item_no++; ?>. <?php echo htmlspecialchars($item['product_name_at_purchase']); ?><?php echo !empty($item['variant_name_at_purchase']) ? ' (' . htmlspecialchars($item['variant_name_at_purchase']) . ')' : ''; ?></td>
                                    <td class="pt-1 text-right"><?php echo htmlspecialchars($item['quantity']); ?></td>
                                    <td class="pt-1 text-right">Rp <?php echo number_format($item['price_per_unit'], 0, ',', '.'); ?></td>
                                    <td class="pt-1 text-right">Rp <?php echo number_format($item['total_price'], 0, ',', '.'); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" class="py-2 text-center text-gray-500">
                                    Tidak ada item untuk pesanan ini.
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Total Summary (subtotal, delivery, service, grand total) -->
            <div class="border-t border-dashed border-gray-400 pt-3 mb-4 space-y-1">
                <p class="flex justify-between"><span>Jumlah Item :</span> <span><?php echo $total_qty; ?></span></p>
                <p class="flex justify-between"><span>Subtotal Produk</span> <span>Rp <?php echo number_format($order_data['total_amount'] - $order_data['delivery_cost'] - $order_data['service_fee'], 0, ',', '.'); ?></span></p>
                <p class="flex justify-between"><span>Biaya Pengiriman</span> <span>Rp <?php echo number_format($order_data['delivery_cost'], 0, ',', '.'); ?></span></p>
                <p class="flex justify-between"><span>Biaya Layanan</span> <span>Rp <?php echo number_format($order_data['service_fee'], 0, ',', '.'); ?></span></p>
                <p class="flex justify-between font-bold text-lg"><span>Total Pembayaran</span> <span>Rp <?php echo number_format($order_data['total_amount'], 0, ',', '.'); ?></span></p>
            </div>

            <!-- Payment Method QR/Account Number -->
            <?php if ($order_data['qr_code_image'] && $order_data['payment_method_type'] !== 'cod'): // Display QR code for non-COD methods ?>
                <div class="text-center mt-4 border-t border-dashed border-gray-400 pt-3">
                    <p>Scan QR Code untuk Pembayaran:</p>
                    <img src="/aplikasi-pixel/uploads/qr_codes/<?php echo htmlspecialchars($order_data['qr_code_image']); ?>" alt="QR Code" class="mx-auto w-32 h-32 object-contain mt-2">
                </div>
            <?php endif; ?>

            <?php if ($order_data['payment_method_type'] === 'bank_transfer' && $order_data['qr_code_image']): // Assuming qr_code_image stores account number for bank transfer ?>
                <div class="text-center mt-4 border-t border-dashed border-gray-400 pt-3">
                    <p>Nomor Rekening Tujuan:</p>
                    <p class="font-bold text-lg text-gray-800"><?php echo htmlspecialchars($order_data['qr_code_image']); ?></p>
                    <button onclick="navigator.clipboard.writeText('<?php echo htmlspecialchars($order_data['qr_code_image']); ?>')" class="mt-2 bg-blue-500 hover:bg-blue-600 text-white text-sm py-1 px-3 rounded-md no-print">Salin Rekening</button>
                </div>
            <?php endif; ?>
        </div>

        <!-- Additional Info Card (Removed as per new design focus) -->
        <!-- <div class="bg-white rounded-lg shadow-lg p-4 text-center text-sm text-gray-700 mb-4">
            <i class="fas fa-shield-alt text-blue-600 text-lg mr-2"></i> Transaksi Anda aman dan terlindungi.
        </div> -->

        <!-- Action Buttons -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <button onclick="window.print()" class="flex items-center justify-between p-4 border-b border-gray-100 w-full text-left hover:bg-gray-50 transition">
                <div class="flex items-center">
                    <i class="fas fa-print text-gray-600 mr-3"></i>
                    <span class="font-medium text-gray-800">Cetak Bukti Pembayaran</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </button>
            <a href="index.php" class="flex items-center justify-between p-4 hover:bg-gray-50 transition">
                <div class="flex items-center">
                    <i class="fas fa-home text-gray-600 mr-3"></i>
                    <span class="font-medium text-gray-800">Kembali ke Beranda</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </a>
        </div>

        <?php else: ?>
            <div class="bg-white shadow-lg rounded-lg p-6 text-center">
                <p class="text-red-500 font-bold mb-4">Pesanan Tidak Ditemukan</p>
                <p class="text-gray-600">Maaf, detail pesanan untuk ID ini tidak dapat ditemukan. Silakan cek kembali tautan Anda.</p>
                <a href="index.php" class="mt-4 inline-block gradient-bg text-white py-2 px-4 rounded-lg hover:opacity-90">Kembali ke Beranda</a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Persistent Floating Button (Placeholder for now) -->
    <div class="fixed bottom-0 left-0 right-0 bg-white p-4 shadow-lg border-t border-gray-100 flex justify-center space-x-4 no-print hidden">
        <a href="index.php" class="gradient-bg text-white font-bold py-3 rounded-lg shadow-md hover:opacity-90 transition w-full text-center">
            Kembali ke Beranda
        </a>
    </div>

</body>
</html>
