<?php
header('Content-Type: application/json');
require_once '../admin/db_config.php';
session_start();

$response = [
    'success' => false,
    'data' => null,
    'message' => ''
];

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $response['message'] = 'User not logged in';
    echo json_encode($response);
    exit();
}

$user_id = $_SESSION['user_id'];

try {
    // First ensure the table has the required columns
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS alamat TEXT");
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS password VARCHAR(255)");
    $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(255)");

    // Get user profile data
    $stmt = $conn->prepare("SELECT * FROM pembeli WHERE id = ?");
    $stmt->execute([$user_id]);
    $user_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user_data) {
        // Remove sensitive data if any
        unset($user_data['password']); // if password field exists

        // Add full URL for profile picture if exists
        if (!empty($user_data['profile_picture'])) {
            $user_data['profile_picture_url'] = 'uploads/profile/' . $user_data['profile_picture'];
        } else {
            $user_data['profile_picture_url'] = '';
        }

        $response['success'] = true;
        $response['data'] = $user_data;
        $response['message'] = 'Profile data retrieved successfully';
    } else {
        $response['message'] = 'User not found';
    }
    
} catch (PDOException $e) {
    $response['message'] = 'Database error: ' . $e->getMessage();
    error_log("Error in get_profile.php: " . $e->getMessage());
}

echo json_encode($response);
?>
