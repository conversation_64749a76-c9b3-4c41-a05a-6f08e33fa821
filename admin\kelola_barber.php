<?php
session_start();
require_once 'db_config.php';

$message = '';
$message_type = ''; // 'success' or 'error'

// Check for and display messages from session after redirect
if (isset($_SESSION['message'])) {
    $message = $_SESSION['message'];
    $message_type = $_SESSION['message_type'] ?? '';
    unset($_SESSION['message']);
    unset($_SESSION['message_type']);
}

// Path for image uploads
$upload_dir = "../uploads/barbers/";

// Ensure upload directory exists
if (!is_dir($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

// Fetch all locations
$locations = [];
try {
    $stmt = $conn->query("SELECT id, name, address FROM locations ORDER BY name ASC");
    $locations = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Fetch locations error: " . $e->getMessage());
    // Optionally set a message for the user
}

// Handle Add Barber
if (isset($_POST['add_barber'])) {
    $name = trim($_POST['name'] ?? '');
    $services_data = $_POST['services'] ?? []; // Array of service data
    $location_id = $_POST['location_id'] ?? null;
    $image_filename = null;

    // Validate services data and reconstruct into a comma-separated string
    $services = [];
    $has_empty_service_field = false;
    foreach ($services_data as $index => $svc) {
        $svc_name = trim($svc['name'] ?? '');
        $svc_duration = trim($svc['duration'] ?? '');
        $svc_price = trim($svc['price'] ?? '');

        if (empty($svc_name) || empty($svc_duration) || $svc_price === '') {
            $has_empty_service_field = true;
            break; // Stop and flag if any service field is empty
        }
        $services[] = "{$svc_name}|{$svc_duration}|{$svc_price}";
    }
    $services_string = implode(',', $services);

    if (empty($name) || empty($services_string) || empty($location_id) || $has_empty_service_field) {
        $_SESSION['message'] = 'Nama barber, lokasi, dan semua detail layanan tidak boleh kosong.';
        $_SESSION['message_type'] = 'error';
    } else {
        // Handle image upload
        if (isset($_FILES['barber_image']) && $_FILES['barber_image']['error'] == UPLOAD_ERR_OK) {
            $file_extension = pathinfo($_FILES['barber_image']['name'], PATHINFO_EXTENSION);
            $unique_filename = uniqid() . '.' . $file_extension;
            $target_file = $upload_dir . $unique_filename;

            if (move_uploaded_file($_FILES['barber_image']['tmp_name'], $target_file)) {
                $image_filename = $unique_filename;
            } else {
                $_SESSION['message'] = 'Gagal mengunggah gambar barber.';
                $_SESSION['message_type'] = 'error';
            }
        }

        if (!isset($_SESSION['message_type']) || $_SESSION['message_type'] !== 'error') { 
            try {
                $stmt = $conn->prepare("INSERT INTO barbers (name, services_offered, image_filename, location_id) VALUES (:name, :services, :image_filename, :location_id)");
                $stmt->execute([
                    'name' => $name,
                    'services' => $services_string, // Use the reconstructed string
                    'image_filename' => $image_filename,
                    'location_id' => $location_id
                ]);
                $_SESSION['message'] = 'Barber berhasil ditambahkan!';
                $_SESSION['message_type'] = 'success';
            } catch (PDOException $e) {
                error_log("Add barber error: " . $e->getMessage());
                $_SESSION['message'] = 'Gagal menambahkan barber. Silakan coba lagi.';
                $_SESSION['message_type'] = 'error';

                if ($image_filename && file_exists($upload_dir . $image_filename)) {
                    unlink($upload_dir . $image_filename);
                }
            }
        }
    }
    header("Location: kelola_barber.php");
    exit();
}

// Handle Update Barber
if (isset($_POST['update_barber'])) {
    $id = $_POST['id'] ?? '';
    $name = trim($_POST['name'] ?? '');
    $services_data = $_POST['services'] ?? []; // Array of service data
    $location_id = $_POST['location_id'] ?? null;
    $old_image_filename = $_POST['old_image_filename'] ?? null;
    $image_filename = $old_image_filename;

    // Validate services data and reconstruct into a comma-separated string
    $services = [];
    $has_empty_service_field = false;
    foreach ($services_data as $index => $svc) {
        $svc_name = trim($svc['name'] ?? '');
        $svc_duration = trim($svc['duration'] ?? '');
        $svc_price = trim($svc['price'] ?? '');

        if (empty($svc_name) || empty($svc_duration) || $svc_price === '') {
            $has_empty_service_field = true;
            break; // Stop and flag if any service field is empty
        }
        $services[] = "{$svc_name}|{$svc_duration}|{$svc_price}";
    }
    $services_string = implode(',', $services);

    if (empty($name) || empty($services_string) || empty($location_id) || $has_empty_service_field) {
        $_SESSION['message'] = 'Nama barber, lokasi, dan semua detail layanan tidak boleh kosong.';
        $_SESSION['message_type'] = 'error';
    } else {
        // Handle image upload if new image is provided
        if (isset($_FILES['barber_image']) && $_FILES['barber_image']['error'] == UPLOAD_ERR_OK) {
            $file_extension = pathinfo($_FILES['barber_image']['name'], PATHINFO_EXTENSION);
            $unique_filename = uniqid() . '.' . $file_extension;
            $target_file = $upload_dir . $unique_filename;

            if (move_uploaded_file($_FILES['barber_image']['tmp_name'], $target_file)) {
                $image_filename = $unique_filename;
                // Delete old image if a new one is successfully uploaded
                if ($old_image_filename && file_exists($upload_dir . $old_image_filename)) {
                    unlink($upload_dir . $old_image_filename);
                }
            } else {
                $_SESSION['message'] = 'Gagal mengunggah gambar barber baru.';
                $_SESSION['message_type'] = 'error';
            }
        }

        if (!isset($_SESSION['message_type']) || $_SESSION['message_type'] !== 'error') {
            try {
                $stmt = $conn->prepare("UPDATE barbers SET name = :name, services_offered = :services, image_filename = :image_filename, location_id = :location_id WHERE id = :id");
                $stmt->execute([
                    'id' => $id,
                    'name' => $name,
                    'services' => $services_string, // Use the reconstructed string
                    'image_filename' => $image_filename,
                    'location_id' => $location_id
                ]);
                $_SESSION['message'] = 'Barber berhasil diperbarui!';
                $_SESSION['message_type'] = 'success';
            } catch (PDOException $e) {
                error_log("Update barber error: " . $e->getMessage());
                $_SESSION['message'] = 'Gagal memperbarui barber. Silakan coba lagi.';
                $_SESSION['message_type'] = 'error';
            }
        }
    }
    header("Location: kelola_barber.php");
    exit();
}

// Handle Delete Barber
if (isset($_GET['delete_id'])) {
    $delete_id = $_GET['delete_id'];

    try {
        // Get image filename before deleting barber from DB
        $stmt_get_image = $conn->prepare("SELECT image_filename FROM barbers WHERE id = :id");
        $stmt_get_image->execute(['id' => $delete_id]);
        $barber_to_delete = $stmt_get_image->fetch(PDO::FETCH_ASSOC);

        $stmt = $conn->prepare("DELETE FROM barbers WHERE id = :id");
        $stmt->execute(['id' => $delete_id]);
        $_SESSION['message'] = 'Barber berhasil dihapus!';
        $_SESSION['message_type'] = 'success';

        // Delete the associated image file if it exists
        if ($barber_to_delete && !empty($barber_to_delete['image_filename'])) {
            $image_path = $upload_dir . $barber_to_delete['image_filename'];
            if (file_exists($image_path)) {
                unlink($image_path);
            }
        }

    } catch (PDOException $e) {
        error_log("Delete barber error: " . $e->getMessage());
        $_SESSION['message'] = 'Gagal menghapus barber. Pastikan tidak ada data terkait yang bergantung pada barber ini.';
        $_SESSION['message_type'] = 'error';
    }
    header("Location: kelola_barber.php");
    exit();
}

// Fetch all barbers
$barbers = [];
try {
    $stmt = $conn->query("SELECT b.*, l.name AS location_name FROM barbers b LEFT JOIN locations l ON b.location_id = l.id ORDER BY b.id DESC");
    $barbers = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Fetch barbers error: " . $e->getMessage());
    $message = 'Gagal mengambil data barber.';
    $message_type = 'error';
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Barber - Pixel Barbershop Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f0f2f5;
        }
        .sidebar {
            background: #2d3748;
            color: #fff;
            transition: transform 0.3s ease-in-out;
        }
        .sidebar .logo {
            background: #1a202c;
            padding: 1rem;
        }
        .sidebar .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .sidebar nav ul li {
            transition: all 0.3s ease;
        }
        .sidebar nav ul li a {
            color: #fff;
            text-decoration: none;
        }
        .sidebar nav ul li.active {
            background: rgb(35, 71, 250);
        }
        .sidebar nav ul li.active a,
        .sidebar nav ul li.active i {
            color: #fff;
        }
        .sidebar nav ul li i {
            color: #4299e1;
        }
        .sidebar nav ul li.active i {
            color: #fff;
        }
        .sidebar nav ul li:hover {
            background: rgba(66, 153, 225, 0.1);
        }
        .sidebar nav ul li.active:hover {
            background: rgb(35, 71, 250);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 50;
                transform: translateX(-100%);
            }
            .sidebar.open {
                transform: translateX(0);
            }
            .main-content {
                width: 100%;
            }
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 40;
                display: none;
            }
            .overlay.show {
                display: block;
            }
        }

        @media (min-width: 769px) {
            .sidebar {
                position: relative;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile Overlay -->
    <div id="overlay" class="overlay"></div>

    <div class="flex min-h-screen bg-gray-100"> <!-- Ubah h-screen menjadi min-h-screen -->
        <!-- Sidebar -->
        <aside id="sidebar" class="w-64 sidebar min-h-screen overflow-y-auto"> <!-- Tambah min-h-screen dan overflow-y-auto -->
            <div class="logo">
                <h1 class="text-2xl font-bold">Admin Panel</h1>
            </div>
            <nav class="mt-4">
                <ul>
                    <li class="px-4 py-3">
                        <a href="index.php" class="flex items-center space-x-3">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <!-- Manajemen Booking Menu -->
                    <li>
                        <button onclick="toggleBookingSubmenu()" class="w-full flex items-center justify-between px-4 py-3 rounded-lg text-white focus:outline-none">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-calendar-check"></i>
                                <span>Manajemen Booking</span>
                            </span>
                            <i id="arrowBookingIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <!-- Submenu -->
                        <ul id="bookingSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="data_booking.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Data Booking</span>
                                </a>
                            </li>
                            <li>
                                <a href="kelola_lokasi.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Kelola Lokasi</span>
                                </a>
                            </li>
                            <li class="active">
                                <a href="kelola_barber.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cut"></i>
                                    <span>Kelola Barber</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <button onclick="toggleSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-box"></i>
                                <span>Manajemen Produk</span>
                            </span>
                            <i id="arrowIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="submenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_produk.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-cube"></i>
                                    <span>Produk</span>
                                </a>
                            </li>
                            <li>
                                <a href="data_pembeli.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pembeli</span>
                                </a>
                            </li>
                            <li>
                                <a href="metode_pembayaran.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-money-check-alt"></i>
                                    <span>Metode Pembayaran</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                            <li class="mb-2">
                        <button onclick="toggleLowonganSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
<span class="flex items-center space-x-3 text-left">
    <i class="fas fa-briefcase"></i>
    <span>Kelola Rekrutmen</span>
</span>
                            <i id="arrowLowonganIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="lowonganSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_lowongan.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Kelola Lowongan</span>
                                </a>
                            </li>
                            <li>
                                <a href="data_pelamar.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-user-tie"></i>
                                    <span>Data Pelamar</span>
                                </a>
                            </li>
                            <li>
                                <a href="whatsapp_manager.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>WhatsApp Manager</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="px-4 py-3">
                        <a href="kelola_admin.php" class="flex items-center space-x-3">
                            <i class="fas fa-user-shield"></i>
                            <span>Kelola Admin</span>
                        </a>
                    </li>

                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col main-content">
            <header class="flex items-center justify-between px-6 py-4 bg-white border-b-4 border-indigo-600">
                <div class="flex items-center">
                    <button id="sidebarToggle" class="text-gray-500 focus:outline-none md:hidden mr-4">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <h1 class="text-xl font-semibold">Kelola Barber</h1>
                </div>
                <div class="flex items-center">
                    <span class="mr-4 text-gray-700 hidden sm:block"><?php echo $_SESSION['admin_full_name'] ?? 'Admin'; ?></span>
                    <div class="relative">
                        <button id="profileDropdown" class="flex items-center focus:outline-none">
                            <?php
                            // Get admin profile photo
                            $stmt = $conn->prepare("SELECT profile_photo FROM admin WHERE id = ?");
                            $stmt->execute([$_SESSION['admin_id']]);
                            $profile_photo = $stmt->fetchColumn();

                            if (!empty($profile_photo) && file_exists('uploads/profiles/' . $profile_photo)):
                            ?>
                                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-300 hover:border-blue-500 transition-colors"
                                     src="uploads/profiles/<?= htmlspecialchars($profile_photo) ?>"
                                     alt="Admin avatar">
                            <?php else: ?>
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-300 hover:border-blue-500 transition-colors">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                            <?php endif; ?>
                            <i class="fas fa-chevron-down ml-2 text-gray-500 text-sm"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="profileDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="profile_settings.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-3 text-gray-500"></i>
                                Profile Settings
                            </a>
                            <hr class="my-1">
                            <a href="logout.php" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-3 text-red-500"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 p-6">
                <div class="max-w-full lg:max-w-screen-xl mx-auto">
                    <?php if ($message): ?>
                        <div id="notification" class="<?php echo $message_type === 'success' ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700'; ?> border px-4 py-3 rounded relative mb-4 transition-all duration-500 ease-in-out" role="alert">
                            <div class="flex items-center justify-between">
                                <div>
                                    <strong class="font-bold">Pesan!</strong>
                                    <span class="block sm:inline"><?php echo htmlspecialchars($message); ?></span>
                                </div>
                                <button onclick="hideNotification()" class="ml-4 text-gray-500 hover:text-gray-700 font-bold text-lg">&times;</button>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Tombol Tambah Barber Baru -->
                    <a href="javascript:void(0)" onclick="openBarberModal()" class="inline-block mb-6 px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-bold text-lg shadow transition">
                        <i class="fas fa-plus mr-2"></i> Tambah Barber Baru
                    </a>

                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600">Daftar Barber</h2>
                        <?php if (empty($barbers)): ?>
                            <p class="text-gray-600 text-center">Belum ada barber yang ditambahkan.</p>
                        <?php else: ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full leading-normal">
                                    <thead>
                                        <tr>
                                            <th class="py-2 px-4 border text-left text-xs font-semibold text-white bg-blue-800">ID</th>
                                            <th class="py-2 px-4 border text-left text-xs font-semibold text-white bg-blue-800">Nama</th>
                                            <th class="py-2 px-4 border text-left text-xs font-semibold text-white bg-blue-800">Layanan</th>
                                            <th class="py-2 px-4 border text-left text-xs font-semibold text-white bg-blue-800">Lokasi</th>
                                            <th class="py-2 px-4 border text-left text-xs font-semibold text-white bg-blue-800">Gambar</th>
                                            <th class="py-2 px-4 border text-center text-xs font-semibold text-white bg-blue-800">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $counter = 1; // Initialize counter ?>
                                        <?php foreach ($barbers as $barber): ?>
                                        <tr>
                                            <td class="py-2 px-4 border border-gray-200 bg-white text-sm text-left"><?php echo $counter++; ?></td>
                                            <td class="py-2 px-4 border border-gray-200 bg-white text-sm text-left"><?php echo htmlspecialchars($barber['name']); ?></td>
                                            <td class="py-2 px-4 border border-gray-200 bg-white text-sm">
                                                <?php if (!empty($barber['services_offered'])): ?>
                                                    <?php
                                                    $services = explode(',', $barber['services_offered']);
                                                    foreach ($services as $service):
                                                        $parts = explode('|', trim($service));
                                                        if (count($parts) === 3):
                                                    ?>
                                                        <div class="mb-2 p-2 bg-gray-50 rounded border-l-4 border-indigo-500">
                                                            <div class="font-semibold text-gray-800"><?php echo htmlspecialchars(trim($parts[0])); ?></div>
                                                            <div class="text-xs text-gray-600 flex items-center space-x-3 mt-1">
                                                                <span class="flex items-center">
                                                                    <i class="fas fa-clock mr-1"></i>
                                                                    <?php echo htmlspecialchars(trim($parts[1])); ?> menit
                                                                </span>
                                                                <span class="flex items-center">
                                                                    <i class="fas fa-tag mr-1"></i>
                                                                    Rp <?php echo number_format((int)trim($parts[2]), 0, ',', '.'); ?>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    <?php
                                                        else:
                                                    ?>
                                                        <div class="mb-1 text-gray-600"><?php echo htmlspecialchars(trim($service)); ?></div>
                                                    <?php
                                                        endif;
                                                    endforeach;
                                                    ?>
                                                <?php else: ?>
                                                    <span class="text-gray-400 italic">Belum ada layanan</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="py-2 px-4 border border-gray-200 bg-white text-sm text-left"><?php echo htmlspecialchars($barber['location_name'] ?? 'N/A'); ?></td>
                                            <td class="py-2 px-4 border border-gray-200 bg-white text-sm text-center">
                                                <?php if (!empty($barber['image_filename'])): ?>
                                                    <img src="../uploads/barbers/<?php echo htmlspecialchars($barber['image_filename']); ?>" alt="Gambar Barber" class="w-20 h-20 object-cover rounded-md">
                                                <?php else: ?>
                                                    Tidak ada gambar
                                                <?php endif; ?>
                                            </td>
                                            <td class="py-2 px-4 border border-gray-200 bg-white text-sm text-center">
                                                <button onclick="openEditBarberModal(<?php echo htmlspecialchars(json_encode($barber)); ?>)" class="bg-yellow-500 hover:bg-yellow-700 text-white text-xs font-bold py-1 px-2 rounded mr-1">Edit</button>
                                                <a href="kelola_barber.php?delete_id=<?php echo $barber['id']; ?>" onclick="return confirm('Anda yakin ingin menghapus barber ini?');" class="bg-red-500 hover:bg-red-700 text-white text-xs font-bold py-1 px-2 rounded">
                                                    Hapus
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal for Add Barber -->
    <div id="addBarberModal" class="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-xl shadow-lg w-full max-w-2xl relative p-0 max-h-[95vh] overflow-hidden">
            <button onclick="closeBarberModal()" class="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-2xl font-bold focus:outline-none">&times;</button>
            <h2 class="text-xl font-bold mb-4 border-b-2 border-indigo-600 pb-2 px-6 pt-6">Tambah Barber Baru</h2>
            <div class="px-6 pb-6 max-h-[80vh] overflow-y-auto">
                <form action="kelola_barber.php" method="POST" enctype="multipart/form-data">
                    <!-- Detail Barber -->
                    <div class="mb-4">
                        <label class="block mb-1 font-semibold">Nama Barber</label>
                        <input type="text" name="name" class="w-full border rounded px-3 py-2" required placeholder="Contoh: Ahmad Rizki">
                    </div>

                    <div class="mb-4">
                        <label class="block mb-1 font-semibold">Lokasi Barbershop</label>
                        <select name="location_id" class="w-full border rounded px-3 py-2" required>
                            <option value="">Pilih Lokasi</option>
                            <?php foreach ($locations as $location): ?>
                                <option value="<?php echo htmlspecialchars($location['id']); ?>">
                                    <?php echo htmlspecialchars($location['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block mb-1 font-semibold">Foto Barber (Opsional)</label>
                        <input type="file" name="barber_image" class="w-full border rounded px-3 py-2" accept="image/*">
                        <small class="text-gray-500">Format: JPG, PNG, GIF. Maksimal 2MB</small>
                    </div>
                    <!-- Layanan yang Ditawarkan -->
                    <div class="mb-4">
                        <label class="block mb-2 font-semibold">Layanan yang Ditawarkan</label>
                        <div class="mb-2">
                            <small class="text-gray-600">Layanan populer barbershop:</small>
                            <div class="flex flex-wrap gap-2 mt-1">
                                <button type="button" onclick="addPresetService('Potong Rambut Pria', '30', '25000')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">Potong Rambut</button>
                                <button type="button" onclick="addPresetService('Cukur Jenggot', '20', '15000')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">Cukur Jenggot</button>
                                <button type="button" onclick="addPresetService('Hair Wash & Styling', '45', '35000')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">Hair Wash</button>
                                <button type="button" onclick="addPresetService('Facial Treatment', '60', '50000')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">Facial</button>
                                <button type="button" onclick="addPresetService('Hair Coloring', '90', '75000')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">Hair Coloring</button>
                            </div>
                        </div>
                        <div id="servicesContainer" class="space-y-3 mb-4">
                            <!-- Service input fields will be added here dynamically -->
                        </div>
                        <button type="button" id="addServiceBtn" class="mt-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                            <i class="fas fa-plus mr-1"></i> Tambah Layanan Manual
                        </button>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" onclick="closeBarberModal()" class="mr-2 px-4 py-2 rounded bg-gray-300 hover:bg-gray-400 text-gray-700">Batal</button>
                        <button type="submit" name="add_barber" class="px-4 py-2 rounded bg-indigo-600 hover:bg-indigo-700 text-white font-bold">Simpan Barber</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for Edit Barber -->
    <div id="editBarberModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-lg relative max-h-[90vh] overflow-y-auto">
            <button onclick="closeEditBarberModal()" class="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-2xl font-bold focus:outline-none">
                &times;
            </button>
            <h2 class="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600">Edit Barber</h2>
            <form id="editBarberForm" action="kelola_barber.php" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="update_barber" value="1">
                <input type="hidden" id="edit_id" name="id">
                <input type="hidden" id="edit_old_image_filename" name="old_image_filename">
                
                <div class="p-4 border border-gray-200 rounded-lg bg-gray-50 mb-4">
                    <h4 class="font-semibold text-gray-800 mb-2">Detail Barber Utama</h4>
                    <div class="mb-4">
                        <label for="edit_name" class="block text-gray-700 text-sm font-bold mb-2">Nama Barber:</label>
                        <input type="text" id="edit_name" name="name" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    </div>
                    <div class="mb-4">
                        <label for="edit_barber_image" class="block text-gray-700 text-sm font-bold mb-2">Gambar Barber (Biarkan kosong jika tidak ingin mengubah):</label>
                        <input type="file" id="edit_barber_image" name="barber_image" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <p class="text-sm text-gray-500 mt-1" id="current_image_text"></p>
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Layanan yang Ditawarkan:</label>
                    <div id="editServicesContainer" class="space-y-4 mb-4">
                        <!-- Service input fields will be added here dynamically -->
                    </div>
                    <button type="button" id="addEditServiceBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        Tambah Layanan Baru
                    </button>
                </div>
                <div class="mb-4">
                    <label for="edit_location_id" class="block text-gray-700 text-sm font-bold mb-2">Lokasi:</label>
                    <select id="edit_location_id" name="location_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                        <option value="">Pilih Lokasi</option>
                        <?php foreach ($locations as $location): ?>
                            <option value="<?php echo htmlspecialchars($location['id']); ?>">
                                <?php echo htmlspecialchars($location['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="flex justify-end mt-4">
                    <button type="button" onclick="closeEditBarberModal()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">Batal</button>
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function toggleBookingSubmenu() {
            const submenu = document.getElementById('bookingSubmenu');
            const icon = document.getElementById('arrowBookingIcon');
            submenu.classList.toggle('hidden');
            icon.classList.toggle('rotate-180');
        }

        function toggleSubmenu() {
            const submenu = document.getElementById('submenu');
            const icon = document.getElementById('arrowIcon');
            submenu.classList.toggle('hidden');
            icon.classList.toggle('rotate-180');
        }

        function openBarberModal() {
            document.getElementById('addBarberModal').classList.remove('hidden');
        }

        function closeBarberModal() {
            document.getElementById('addBarberModal').classList.add('hidden');
        }

        // Functions for preset services
        function addPresetService(name, duration, price) {
            const container = document.getElementById('servicesContainer');
            addServiceField(container, name, duration, price);
        }

        const editBarberModal = document.getElementById('editBarberModal');

        let timeFlatpickrInstance;

        // Auto-hide notification function
        function hideNotification() {
            const notification = document.getElementById('notification');
            if (notification) {
                notification.style.opacity = '0';
                notification.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    notification.style.display = 'none';
                }, 500);
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Auto-hide notification after 4 seconds
            const notification = document.getElementById('notification');
            if (notification) {
                setTimeout(() => {
                    hideNotification();
                }, 4000); // Hide after 4 seconds
            }

            // Set active class for current page in sidebar
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar nav ul li a');
            navLinks.forEach(link => {
                link.closest('li').classList.remove('active');
                if (currentPath.includes(link.getAttribute('href'))) {
                    link.closest('li').classList.add('active');
                    const parentUl = link.closest('ul');
                    if (parentUl && (parentUl.id === 'submenu' || parentUl.id === 'bookingSubmenu')) {
                        parentUl.classList.remove('hidden');
                        const parentButton = parentUl.previousElementSibling;
                        if (parentButton) {
                            parentButton.querySelector('i').classList.add('rotate-180');
                            const parentLiWithButton = parentButton.closest('li');
                            if (parentLiWithButton) {
                                parentLiWithButton.classList.remove('active');
                            }
                        }
                    }
                }
            });

            // Service management for Add Modal
            const servicesContainer = document.getElementById('servicesContainer');
            const addServiceBtn = document.getElementById('addServiceBtn');

            addServiceBtn.addEventListener('click', function() {
                addServiceField(servicesContainer);
            });

            // Service management for Edit Modal
            const editServicesContainer = document.getElementById('editServicesContainer');
            const addEditServiceBtn = document.getElementById('addEditServiceBtn');

            addEditServiceBtn.addEventListener('click', function() {
                addServiceField(editServicesContainer);
            });

            // Add at least one service field by default for Add Modal
            if (servicesContainer.children.length === 0) {
                addServiceField(servicesContainer);
            }

            // Auto-open booking submenu since this is kelola_barber.php
            const bookingSubmenu = document.getElementById('bookingSubmenu');
            const bookingIcon = document.getElementById('arrowBookingIcon');
            if (bookingSubmenu && bookingIcon) {
                bookingSubmenu.classList.remove('hidden');
                bookingIcon.classList.add('rotate-180');
            }

            // Sidebar Toggle for Mobile
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('overlay');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('open');
                    overlay.classList.toggle('show');
                });
            }

            if (overlay) {
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('open');
                    overlay.classList.remove('show');
                });
            }
        });

        function addServiceField(container, serviceName = '', duration = '', price = '') {
            const serviceIndex = container.children.length;
            const serviceDiv = document.createElement('div');
            serviceDiv.className = 'p-4 border border-gray-300 rounded-lg bg-gray-50 relative';
            serviceDiv.innerHTML = `
                <div class="flex justify-between items-center mb-3">
                    <h4 class="font-semibold text-gray-800">
                        <i class="fas fa-cut text-indigo-600 mr-2"></i>
                        Layanan #${serviceIndex + 1}
                    </h4>
                    <button type="button" class="delete-service-btn text-red-500 hover:text-red-700 transition-colors duration-200">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nama Layanan</label>
                        <input type="text" name="services[${serviceIndex}][name]" class="w-full border rounded px-3 py-2 text-sm" placeholder="Contoh: Potong Rambut" value="${serviceName}" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Durasi (menit)</label>
                        <input type="number" name="services[${serviceIndex}][duration]" class="w-full border rounded px-3 py-2 text-sm" placeholder="30" value="${duration}" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Harga (Rp)</label>
                        <input type="number" name="services[${serviceIndex}][price]" class="w-full border rounded px-3 py-2 text-sm" placeholder="25000" value="${price}" step="1000" required>
                    </div>
                </div>
            `;
            serviceDiv.querySelector('.delete-service-btn').addEventListener('click', function() {
                if (container.children.length > 1) {
                    serviceDiv.remove();
                    updateServiceNumbers(container);
                } else {
                    alert('Minimal harus ada satu layanan!');
                }
            });
            container.appendChild(serviceDiv);
            updateServiceNumbers(container);
        }

        function updateServiceNumbers(container) {
            Array.from(container.children).forEach((div, index) => {
                const h4 = div.querySelector('h4');
                if (h4) {
                    h4.innerHTML = `<i class="fas fa-cut text-indigo-600 mr-2"></i>Layanan #${index + 1}`;
                }
                const nameInput = div.querySelector(`[name^="services["][name$="[name]"]`);
                const durationInput = div.querySelector(`[name^="services["][name$="[duration]"]`);
                const priceInput = div.querySelector(`[name^="services["][name$="[price]"]`);

                if (nameInput) nameInput.name = `services[${index}][name]`;
                if (durationInput) durationInput.name = `services[${index}][duration]`;
                if (priceInput) priceInput.name = `services[${index}][price]`;
            });
        }

        // Adjust openEditBarberModal to populate multiple services
        window.openEditBarberModal = function(barber) {
            document.getElementById('edit_id').value = barber.id;
            document.getElementById('edit_name').value = barber.name;
            document.getElementById('edit_old_image_filename').value = barber.image_filename || '';
            document.getElementById('edit_location_id').value = barber.location_id;

            // Clear existing service fields in edit modal
            editServicesContainer.innerHTML = '';

            // Populate service fields from barber.services_offered
            if (barber.services_offered) {
                const servicesArray = barber.services_offered.split(',').map(s => s.trim());
                servicesArray.forEach(serviceRaw => {
                    const parts = serviceRaw.split('|');
                    if (parts.length === 3) {
                        addServiceField(editServicesContainer, parts[0], parts[1], parts[2]);
                    } else if (parts.length === 1) { // Handle old format if it still exists
                        addServiceField(editServicesContainer, parts[0], '', '');
                    }
                });
            } else { // Add at least one empty field if no services or old format
                addServiceField(editServicesContainer);
            }

            const currentImageText = document.getElementById('current_image_text');
            if (barber.image_filename) {
                currentImageText.textContent = `Gambar saat ini: ${barber.image_filename}`;
            } else {
                currentImageText.textContent = 'Tidak ada gambar saat ini';
            }
            
            editBarberModal.classList.remove('hidden');
        };

        window.closeEditBarberModal = function() {
            editBarberModal.classList.add('hidden');
            // Clear form fields after closing
            document.getElementById('edit_id').value = '';
            document.getElementById('edit_name').value = '';
            document.getElementById('edit_old_image_filename').value = '';
            document.getElementById('edit_location_id').value = '';
            document.getElementById('edit_barber_image').value = ''; // Clear file input
            document.getElementById('current_image_text').textContent = '';
            const servicesContainer = document.getElementById('servicesContainer');
            const editServicesContainer = document.getElementById('editServicesContainer');
            servicesContainer.innerHTML = ''; // Clear add services
            editServicesContainer.innerHTML = ''; // Clear edit services
            addServiceField(servicesContainer); // Add default field back for add modal
        };

        // Close modals when clicking outside of them
        const addBarberModal = document.getElementById('addBarberModal');
        addBarberModal.addEventListener('click', function(e) {
            if (e.target === addBarberModal) {
                closeBarberModal();
            }
        });

        editBarberModal.addEventListener('click', function(e) {
            if (e.target === editBarberModal) {
                closeEditBarberModal();
            }
        });

        // Profile dropdown functionality
        const profileDropdown = document.getElementById('profileDropdown');
        const profileDropdownMenu = document.getElementById('profileDropdownMenu');

        if (profileDropdown && profileDropdownMenu) {
            profileDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdownMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdownMenu.classList.add('hidden');
                }
            });
        }
    </script>
</body>
</html>

