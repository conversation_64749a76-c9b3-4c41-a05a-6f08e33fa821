<?php
session_start();

// Clear all session data
$_SESSION = array();

// Delete session cookies
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Clear custom cookies
setcookie('user_id', '', time() - 3600, '/');
setcookie('user_login', '', time() - 3600, '/');

// Destroy session
session_destroy();

// Redirect to login page with logout message
header('Location: login.php?logout=success');
exit();
?>
