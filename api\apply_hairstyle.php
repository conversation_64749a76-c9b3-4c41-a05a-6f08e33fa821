<?php
// api/apply_hairstyle.php
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
if (!$data || !isset($data['user_image']) || !isset($data['hairstyle'])) {
    echo json_encode(['success' => false, 'error' => 'Missing parameters']);
    exit;
}

$user_image_base64 = $data['user_image'];
$hairstyle = $data['hairstyle'];
$score = isset($data['score']) ? intval($data['score']) : 9;
$hairstyle_name = $hairstyle;

// Simpan gambar user sementara
$upload_dir = __DIR__ . '/../uploads/face_detection/';
if (!is_dir($upload_dir)) mkdir($upload_dir, 0777, true);
$user_image_path = $upload_dir . 'user_tmp_' . uniqid() . '.png';
file_put_contents($user_image_path, base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $user_image_base64)));

// Path hairstyle overlay dan referensi model
$hairstyle_overlay_path = __DIR__ . '/../hair_styles/' . $hairstyle . '.png';
if (!file_exists($hairstyle_overlay_path)) {
    $hairstyle_overlay_path = __DIR__ . '/../hair_styles/' . $hairstyle . '.jpeg';
    if (!file_exists($hairstyle_overlay_path)) {
        echo json_encode(['success' => false, 'error' => 'Overlay rambut tidak ditemukan']);
        @unlink($user_image_path);
        exit;
    }
}
$model_reference_path = __DIR__ . '/../hair_styles/' . $hairstyle . '.png';
if (!file_exists($model_reference_path)) {
    $model_reference_path = __DIR__ . '/../hair_styles/' . $hairstyle . '.jpeg';
}

// Dummy face box (x, y, w, h) - seharusnya dari face detector
$face_box = '60,80,220,220';

// Path hasil output
$output_path = $upload_dir . 'result_' . uniqid() . '.png';

// Jalankan script Python
$cmd = escapeshellcmd("python ../apply_hairstyle.py \"$user_image_path\" \"$hairstyle_overlay_path\" \"$face_box\" $score \"$hairstyle_name\" \"$model_reference_path\" \"$output_path\"");
exec($cmd, $output, $return_var);

if ($return_var === 0 && file_exists($output_path)) {
    $result_base64 = base64_encode(file_get_contents($output_path));
    echo json_encode([
        'success' => true,
        'result_base64' => 'data:image/png;base64,' . $result_base64
    ]);
} else {
    echo json_encode(['success' => false, 'error' => 'Failed to process overlay']);
}
// Optional: hapus file sementara
@unlink($user_image_path);
@unlink($output_path); 