<?php
ini_set('display_errors', 0); // Temporary: Hide PHP errors from output
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING); // Temporary: Suppress notices and warnings
require_once dirname(__FILE__) . '/../vendor/autoload.php';
require_once dirname(__FILE__) . '/../Admin/db_config.php';

use Midtrans\Snap;
use Midtrans\Config;

Config::$serverKey = 'SB-Mid-server-YOUR_SERVER_KEY'; // GANTI DENGAN SERVER KEY ANDA YANG SEBENARNYA
Config::$isProduction = false;
Config::$isSanitized = true;
Config::$is3ds = true;

header('Content-Type: application/json');

$response = ['status' => 'error', 'message' => 'Invalid request'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);

    $product_id = $input['product_id'] ?? null;
    $variant_id = $input['variant_id'] ?? null;
    $quantity = $input['quantity'] ?? 0;
    $delivery_cost = $input['delivery_cost'] ?? 0;
    $service_fee = $input['service_fee'] ?? 0;
    $total_amount = $input['total_amount'] ?? 0;
    $address_id = $input['address_id'] ?? null;
    $selectedPaymentMethodId = $input['payment_method_id'] ?? null; // ID dari metode pembayaran yang dipilih

    $product_name = $input['product_name'] ?? 'Produk';
    $variant_ml = $input['variant_ml'] ?? '0';
    $product_price = $input['product_price'] ?? 0;
    $customer_name = $input['customer_name'] ?? 'Guest User';
    $customer_email = $input['customer_email'] ?? '<EMAIL>';
    $customer_phone = $input['customer_phone'] ?? '08123456789';

    if (!$product_id || !$variant_id || $quantity <= 0 || !$address_id || !$selectedPaymentMethodId || !$total_amount) {
        $response['message'] = 'Data pesanan tidak lengkap atau jumlah total tidak valid.';
        echo json_encode($response);
        exit();
    }

    try {
        $conn->beginTransaction();

        // 1. Determine pembeli_id (buyer ID)
        // Menggunakan nama dan email pelanggan dari data yang dikirim
        $pembeli_id = null;
        
        $stmt_check_buyer = $conn->prepare("SELECT id FROM pembeli WHERE nama_pembeli = :nama_pembeli OR email = :email LIMIT 1");
        $stmt_check_buyer->execute([
            ':nama_pembeli' => $customer_name,
            ':email' => $customer_email
        ]);
        $existing_buyer = $stmt_check_buyer->fetch(PDO::FETCH_ASSOC);

        if ($existing_buyer) {
            $pembeli_id = $existing_buyer['id'];
        } else {
            // Create new buyer if not exists
            $stmt_insert_buyer = $conn->prepare("INSERT INTO pembeli (nama_pembeli, email) VALUES (:nama_pembeli, :email)");
            $stmt_insert_buyer->execute([
                ':nama_pembeli' => $customer_name,
                ':email' => $customer_email
            ]);
            $pembeli_id = $conn->lastInsertId();
        }

        if (!$pembeli_id) {
            throw new Exception("Gagal mendapatkan ID pembeli.");
        }

        // 2. Insert into orders table
        $created_at = date('Y-m-d H:i:s');
        $order_status = 'pending'; // Initial status for Midtrans orders
        $payment_status = 'pending'; // Initial payment status

        $stmt_order = $conn->prepare("
            INSERT INTO orders (
                pembeli_id, total_amount, created_at, order_status, payment_status, 
                delivery_cost, service_fee, address_id, payment_method_id
            ) VALUES (
                :pembeli_id, :total_amount, :created_at, :order_status, :payment_status, 
                :delivery_cost, :service_fee, :address_id, :payment_method_id
            )
        ");

        $stmt_order->execute([
            ':pembeli_id' => $pembeli_id,
            ':total_amount' => $total_amount,
            ':created_at' => $created_at,
            ':order_status' => $order_status,
            ':payment_status' => $payment_status,
            ':delivery_cost' => $delivery_cost,
            ':service_fee' => $service_fee,
            ':address_id' => $address_id,
            ':payment_method_id' => $selectedPaymentMethodId
        ]);

        $order_id_from_db = $conn->lastInsertId();

        // 3. Insert into order_items table
        $item_total = $product_price * $quantity;
        $stmt_item = $conn->prepare("
            INSERT INTO order_items (
                order_id, product_id, variant_id, quantity, price_per_unit, total_price, 
                product_name_at_purchase, variant_name_at_purchase
            ) VALUES (
                :order_id, :product_id, :variant_id, :quantity, :price_per_unit, :total_price, 
                :product_name_at_purchase, :variant_name_at_purchase
            )
        ");
        $stmt_item->execute([
            ':order_id' => $order_id_from_db,
            ':product_id' => $product_id,
            ':variant_id' => $variant_id,
            ':quantity' => $quantity,
            ':price_per_unit' => $product_price,
            ':total_price' => $item_total,
            ':product_name_at_purchase' => $product_name,
            ':variant_name_at_purchase' => $variant_ml . ' ml'
        ]);

        $conn->commit();

        // 4. Generate Midtrans Snap Token
        $transaction_details = array(
            'order_id' => $order_id_from_db, // Use the ID from our database
            'gross_amount' => $total_amount,
        );

        $item_details_midtrans = [
            [
                'id' => $product_id . '-' . $variant_id,
                'price' => $product_price,
                'quantity' => $quantity,
                'name' => $product_name . ' ' . $variant_ml . 'ml',
            ],
        ];

        if ($delivery_cost > 0) {
            $item_details_midtrans[] = [
                'id' => 'DELIVERY_FEE',
                'price' => $delivery_cost,
                'quantity' => 1,
                'name' => 'Delivery Fee',
            ];
        }
        if ($service_fee > 0) {
            $item_details_midtrans[] = [
                'id' => 'SERVICE_FEE',
                'price' => $service_fee,
                'quantity' => 1,
                'name' => 'Service Fee',
            ];
        }

        $customer_details = array(
            'first_name' => explode(' ', $customer_name)[0],
            'last_name'  => implode(' ', array_slice(explode(' ', $customer_name), 1)),
            'email'      => $customer_email,
            'phone'      => $customer_phone,
        );

        $params = array(
            'transaction_details' => $transaction_details,
            'item_details' => $item_details_midtrans,
            'customer_details' => $customer_details,
            'enabled_payments' => ['bca_va', 'bca_klikpay', 'echannel', 'permata_va', 'other_va', 'gopay', 'ovo', 'shopeepay'], // Example enabled payments
            'callbacks' => [
                'finish' => 'http://localhost/aplikasi-pixel/konfirmasi_pembayaran.php?order_id=' . $order_id_from_db,
                'error' => 'http://localhost/aplikasi-pixel/error_pembayaran.php?order_id=' . $order_id_from_db,
                'pending' => 'http://localhost/aplikasi-pixel/pending_pembayaran.php?order_id=' . $order_id_from_db,
            ],
        );

        $snapToken = Snap::getSnapToken($params);

        $response = ['status' => 'success', 'snap_token' => $snapToken, 'order_id' => $order_id_from_db];

    } catch (Exception $e) {
        $conn->rollBack();
        $response['message'] = 'Terjadi kesalahan saat memproses pesanan: ' . $e->getMessage();
        error_log("Midtrans Order Processing Error: " . $e->getMessage());
    }
} else {
    $response['message'] = 'Metode request tidak diizinkan.';
}

echo json_encode($response);
?> 