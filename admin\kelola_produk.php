<?php
session_start();
require_once 'db_config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Cek koneksi database
if (!isset($conn)) {
    die("Koneksi database gagal. Silakan periksa konfigurasi database di db_config.php");
}

// Path untuk folder uploads (digunakan untuk menyimpan gambar)
$upload_dir = "../uploads/";
$variant_upload_dir = "../uploads/variants/"; // Direktori untuk gambar varian

// Pastikan direktori varian ada
if (!is_dir($variant_upload_dir)) {
    mkdir($variant_upload_dir, 0777, true);
}

// Handle add product form
if (isset($_POST['tambah_produk'])) {
    try {
        // Start transaction
        $conn->beginTransaction();
        
        // Validate required fields
        if (!isset($_POST['nama_produk']) || !isset($_POST['deskripsi']) || 
            !isset($_POST['harga']) || !isset($_POST['kategori_id'])) {
            throw new Exception("Semua field wajib diisi!");
        }

        // Get form data
        $nama_produk = $_POST['nama_produk'];
        $deskripsi = $_POST['deskripsi'];
        $harga = $_POST['harga'];
        $kategori_id = $_POST['kategori_id'];
        
        // Handle main image upload
        // Check if image was uploaded
        if (!isset($_FILES['gambar_utama']) || $_FILES['gambar_utama']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception("Gambar utama harus diunggah");
        }
        
        $file_name = basename($_FILES['gambar_utama']['name']);
        $target_file = $upload_dir . $file_name;
        
        if (move_uploaded_file($_FILES['gambar_utama']['tmp_name'], $target_file)) {
            // Insert product to database
            $stmt = $conn->prepare("INSERT INTO produk (nama_produk, deskripsi, harga, gambar_utama, kategori_id) 
                                VALUES (:nama_produk, :deskripsi, :harga, :gambar_utama, :kategori_id)");
            $stmt->execute([
                ':nama_produk' => $nama_produk,
                ':deskripsi' => $deskripsi,
                ':harga' => $harga,
                ':gambar_utama' => $file_name,
                ':kategori_id' => $kategori_id
            ]);
            
            $produk_id = $conn->lastInsertId();
            
            // Handle variasi produk
            if (isset($_POST['ukuran_ml']) && is_array($_POST['ukuran_ml'])) {
                $ukuran_ml_arr = $_POST['ukuran_ml'];
                $harga_variasi_arr = $_POST['harga_variasi'];
                $stok_arr = $_POST['stok'];
                
                // Process variant images if uploaded
                $gambar_varian_uploaded_filenames = [];
                if (isset($_FILES['gambar_varian']) && is_array($_FILES['gambar_varian']['name'])) {
                    foreach ($_FILES['gambar_varian']['name'] as $index => $name) {
                        if ($_FILES['gambar_varian']['error'][$index] == UPLOAD_ERR_OK) {
                            $unique_filename = uniqid() . '_' . basename($name); // Generate unique filename
                            $target_variant_file = $variant_upload_dir . $unique_filename;
                            if (move_uploaded_file($_FILES['gambar_varian']['tmp_name'][$index], $target_variant_file)) {
                                $gambar_varian_uploaded_filenames[$index] = $unique_filename;
                            } else {
                                error_log("Failed to upload variant image: " . $name);
                                $gambar_varian_uploaded_filenames[$index] = null;
                            }
                        } else {
                            $gambar_varian_uploaded_filenames[$index] = null;
                        }
                    }
                }

                foreach ($ukuran_ml_arr as $index => $ukuran) {
                    if (!empty($ukuran)) {
                        $variant_gambar = $gambar_varian_uploaded_filenames[$index] ?? null;
                        
                        $stmt = $conn->prepare("INSERT INTO variasi_produk (produk_id, ukuran_ml, harga, stok, gambar_varian) 
                                                VALUES (:produk_id, :ukuran_ml, :harga, :stok, :gambar_varian)");
                        $stmt->execute([
                            ':produk_id' => $produk_id,
                            ':ukuran_ml' => $ukuran,
                            ':harga' => $harga_variasi_arr[$index],
                            ':stok' => $stok_arr[$index],
                            ':gambar_varian' => $variant_gambar
                        ]);
                    }
                }
            }
            
            // Handle additional images
            if (isset($_FILES['gambar_tambahan']) && !empty($_FILES['gambar_tambahan']['name'][0])) {
                $label_gambar = $_POST['label_gambar'];
                
                foreach ($_FILES['gambar_tambahan']['name'] as $index => $name) {
                    if (!empty($name)) {
                        $file_name = basename($name);
                        $target_file = $upload_dir . $file_name;
                        
                        if (move_uploaded_file($_FILES['gambar_tambahan']['tmp_name'][$index], $target_file)) {
                            $stmt = $conn->prepare("INSERT INTO gambar_produk (produk_id, gambar, label) 
                                                VALUES (:produk_id, :gambar, :label)");
                            $stmt->execute([
                                ':produk_id' => $produk_id,
                                ':gambar' => $file_name,
                                ':label' => $label_gambar[$index]
                            ]);
                        }
                    }
                }
            }
            
            // Commit transaction if everything is successful
            $conn->commit();
            
            // Redirect back to page with success message
            header("Location: kelola_produk.php?success=1");
            exit();
        } else {
            throw new Exception("Gagal mengunggah gambar utama");
        }
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollBack();
        
        // Redirect back to page with error message
        header("Location: kelola_produk.php?error=" . urlencode($e->getMessage()));
        exit();
    }
}

// Handle update product form
if (isset($_POST['update_produk'])) {
    try {
        $conn->beginTransaction();

        $product_id = $_POST['product_id'];
        $nama_produk = $_POST['nama_produk_edit'];
        $deskripsi = $_POST['deskripsi_edit'];
        $harga = $_POST['harga_edit'];
        $kategori_id = $_POST['kategori_id_edit'];

        $sql = "UPDATE produk SET nama_produk = :nama_produk, deskripsi = :deskripsi, harga = :harga, kategori_id = :kategori_id";
        $params = [
            ':nama_produk' => $nama_produk,
            ':deskripsi' => $deskripsi,
            ':harga' => $harga,
            ':kategori_id' => $kategori_id
        ];

        // Handle main image update
        if (isset($_FILES['gambar_utama_edit']) && $_FILES['gambar_utama_edit']['error'] == UPLOAD_ERR_OK) {
            $old_gambar_utama = $_POST['old_gambar_utama'];
            if ($old_gambar_utama && file_exists($upload_dir . $old_gambar_utama)) {
                unlink($upload_dir . $old_gambar_utama);
            }

            $file_name = basename($_FILES['gambar_utama_edit']['name']);
            $target_file = $upload_dir . $file_name;
            if (move_uploaded_file($_FILES['gambar_utama_edit']['tmp_name'], $target_file)) {
                $sql .= ", gambar_utama = :gambar_utama";
                $params[':gambar_utama'] = $file_name;
            } else {
                throw new Exception("Gagal mengunggah gambar utama baru");
            }
        }

        $sql .= " WHERE id = :id";
        $params[':id'] = $product_id;

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);

        // Handle existing variasi produk update
        if (isset($_POST['edit_variant_id']) && is_array($_POST['edit_variant_id'])) {
            $edit_variant_ids = $_POST['edit_variant_id'];
            $edit_ukuran_ml = $_POST['edit_ukuran_ml'];
            $edit_harga_variasi = $_POST['edit_harga_variasi'];
            $edit_stok = $_POST['edit_stok'];
            $old_gambar_varian_edit = $_POST['old_gambar_varian_edit'];

            foreach ($edit_variant_ids as $index => $variant_id) {
                $variant_gambar = $old_gambar_varian_edit[$index] ?? null;
                
                // Handle variant image update if new file is uploaded
                if (isset($_FILES['edit_gambar_varian']) && isset($_FILES['edit_gambar_varian']['name'][$index]) && $_FILES['edit_gambar_varian']['error'][$index] == UPLOAD_ERR_OK) {
                    // Delete old variant image if exists
                    if ($variant_gambar && file_exists($variant_upload_dir . $variant_gambar)) {
                        unlink($variant_upload_dir . $variant_gambar);
                    }
                    $unique_filename = uniqid() . '_' . basename($_FILES['edit_gambar_varian']['name'][$index]);
                    $target_variant_file = $variant_upload_dir . $unique_filename;
                    if (move_uploaded_file($_FILES['edit_gambar_varian']['tmp_name'][$index], $target_variant_file)) {
                        $variant_gambar = $unique_filename;
                    } else {
                        error_log("Failed to upload new variant image for variant ID: " . $variant_id);
                    }
                }

                $stmt = $conn->prepare("UPDATE variasi_produk SET ukuran_ml = :ukuran_ml, harga = :harga, stok = :stok, gambar_varian = :gambar_varian WHERE id = :id AND produk_id = :produk_id");
                $stmt->execute([
                    ':id' => $variant_id,
                    ':produk_id' => $product_id,
                    ':ukuran_ml' => $edit_ukuran_ml[$index],
                    ':harga' => $edit_harga_variasi[$index],
                    ':stok' => $edit_stok[$index],
                    ':gambar_varian' => $variant_gambar
                ]);
            }
        }

        // Handle new variasi produk additions during update
        if (isset($_POST['new_ukuran_ml']) && is_array($_POST['new_ukuran_ml'])) {
            $new_ukuran_ml_arr = $_POST['new_ukuran_ml'];
            $new_harga_variasi_arr = $_POST['new_harga_variasi'];
            $new_stok_arr = $_POST['new_stok'];

            $new_gambar_varian_uploaded_filenames = [];
            if (isset($_FILES['new_gambar_varian']) && is_array($_FILES['new_gambar_varian']['name'])) {
                foreach ($_FILES['new_gambar_varian']['name'] as $index => $name) {
                    if ($_FILES['new_gambar_varian']['error'][$index] == UPLOAD_ERR_OK) {
                        $unique_filename = uniqid() . '_' . basename($name);
                        $target_variant_file = $variant_upload_dir . $unique_filename;
                        if (move_uploaded_file($_FILES['new_gambar_varian']['tmp_name'][$index], $target_variant_file)) {
                            $new_gambar_varian_uploaded_filenames[$index] = $unique_filename;
                        } else {
                            error_log("Failed to upload new variant image: " . $name);
                            $new_gambar_varian_uploaded_filenames[$index] = null;
                        }
                    }
                }
            }

            foreach ($new_ukuran_ml_arr as $index => $ukuran) {
                if (!empty($ukuran)) {
                    $new_variant_gambar = $new_gambar_varian_uploaded_filenames[$index] ?? null;

                    $stmt = $conn->prepare("INSERT INTO variasi_produk (produk_id, ukuran_ml, harga, stok, gambar_varian) 
                                            VALUES (:produk_id, :ukuran_ml, :harga, :stok, :gambar_varian)");
                    $stmt->execute([
                        ':produk_id' => $product_id,
                        ':ukuran_ml' => $ukuran,
                        ':harga' => $new_harga_variasi_arr[$index],
                        ':stok' => $new_stok_arr[$index],
                        ':gambar_varian' => $new_variant_gambar
                    ]);
                }
            }
        }

        // Handle additional images during update
        if (isset($_FILES['gambar_tambahan_edit']) && !empty($_FILES['gambar_tambahan_edit']['name'][0])) {
            foreach ($_FILES['gambar_tambahan_edit']['name'] as $index => $name) {
                if (!empty($name) && $_FILES['gambar_tambahan_edit']['error'][$index] == UPLOAD_ERR_OK) {
                    $file_name = uniqid() . '_' . basename($name);
                    $target_file = $upload_dir . $file_name;
                    if (move_uploaded_file($_FILES['gambar_tambahan_edit']['tmp_name'][$index], $target_file)) {
                        $stmt = $conn->prepare("INSERT INTO gambar_produk (produk_id, gambar, label) 
                                            VALUES (:produk_id, :gambar, :label)");
                        $stmt->execute([
                            ':produk_id' => $product_id,
                            ':gambar' => $file_name,
                            ':label' => $_POST['label_gambar_edit'][$index] ?? ''
                        ]);
                    }
                }
            }
        }

        // Handle deletion of additional images
        if (isset($_POST['delete_image_ids']) && is_array($_POST['delete_image_ids'])) {
            foreach ($_POST['delete_image_ids'] as $image_id) {
                // Fetch image filename to delete from disk
                $stmt = $conn->prepare("SELECT gambar FROM gambar_produk WHERE id = :id");
                $stmt->execute([':id' => $image_id]);
                $image_to_delete = $stmt->fetchColumn();

                if ($image_to_delete && file_exists($upload_dir . $image_to_delete)) {
                    unlink($upload_dir . $image_to_delete);
                }

                $stmt = $conn->prepare("DELETE FROM gambar_produk WHERE id = :id");
                $stmt->execute([':id' => $image_id]);
            }
        }

        $conn->commit();
        header("Location: kelola_produk.php?success=2");
        exit();
    } catch (Exception $e) {
        $conn->rollBack();
        header("Location: kelola_produk.php?error=" . urlencode($e->getMessage()));
        exit();
    }
}

// Handle delete product
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    try {
        $conn->beginTransaction();

        $product_id = $_GET['id'];

        // Hapus gambar utama
        $stmt = $conn->prepare("SELECT gambar_utama FROM produk WHERE id = :id");
        $stmt->execute(['id' => $product_id]);
        $gambar_utama = $stmt->fetchColumn();
        if ($gambar_utama && file_exists($upload_dir . $gambar_utama)) {
            unlink($upload_dir . $gambar_utama);
        }

        // Hapus gambar varian (jika ada)
        $stmt = $conn->prepare("SELECT gambar_varian FROM variasi_produk WHERE produk_id = :produk_id");
        $stmt->execute([':produk_id' => $product_id]);
        $variant_images = $stmt->fetchAll(PDO::FETCH_COLUMN);
        foreach ($variant_images as $img) {
            if ($img && file_exists($variant_upload_dir . $img)) {
                unlink($variant_upload_dir . $img);
            }
        }
        $stmt = $conn->prepare("DELETE FROM variasi_produk WHERE produk_id = :produk_id");
        $stmt->execute([':produk_id' => $product_id]);

        // Hapus gambar tambahan
        $stmt = $conn->prepare("SELECT gambar FROM gambar_produk WHERE produk_id = :id");
        $stmt->execute(['id' => $product_id]);
        $additional_images = $stmt->fetchAll(PDO::FETCH_COLUMN);
        foreach ($additional_images as $img) {
            if ($img && file_exists($upload_dir . $img)) {
                unlink($upload_dir . $img);
            }
        }
        $stmt = $conn->prepare("DELETE FROM gambar_produk WHERE produk_id = :id");
        $stmt->execute(['id' => $product_id]);

        // Hapus produk utama
        $stmt = $conn->prepare("DELETE FROM produk WHERE id = :id");
        $stmt->execute(['id' => $product_id]);

        $conn->commit();
        header("Location: kelola_produk.php?success=3");
        exit();
    } catch (Exception $e) {
        $conn->rollBack();
        header("Location: kelola_produk.php?error=" . urlencode($e->getMessage()));
        exit();
    }
}

// Fetch products with their category names
$stmt = $conn->prepare("SELECT p.*, k.nama_kategori as kategori 
                        FROM produk p 
                        LEFT JOIN kategori_produk k ON p.kategori_id = k.id");
$stmt->execute();
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch categories for dropdown
$stmt = $conn->prepare("SELECT id, nama_kategori FROM kategori_produk");
$stmt->execute();
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Produk - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            background: #2d3748;
            color: #fff;
        }
        .sidebar .logo {
            background: #1a202c;
            padding: 1rem;
        }
        .sidebar .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .sidebar nav ul li {
            transition: all 0.3s ease;
        }
        .sidebar nav ul li a {
            color: #fff;
        }
        .sidebar nav ul li.active {
            background:rgb(35, 71, 250);
        }
        .sidebar nav ul li.active a {
            color: #fff;
        }
        .sidebar nav ul li i {
            color: #4299e1;
        }
        .sidebar nav ul li.active i {
            color: #fff;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <aside class="w-64 sidebar">
            <div class="logo">
                <h1 class="text-2xl font-bold">Admin Panel</h1>
            </div>
            <nav class="mt-4">
                <ul>
                    <li class="px-4 py-3">
                        <a href="index.php" class="flex items-center space-x-3">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <!-- Manajemen Booking Menu -->
<li>
  <button onclick="toggleBookingSubmenu()" class="w-full flex items-center justify-between px-4 py-3 rounded-lg text-white focus:outline-none">
    <span class="flex items-center space-x-3">
      <i class="fas fa-calendar-check"></i>
      <span>Manajemen Booking</span>
    </span>
    <i id="arrowBookingIcon" class="fas fa-chevron-down transition-transform"></i>
  </button>

  <!-- Submenu -->
  <ul id="bookingSubmenu" class="ml-8 mt-2 space-y-2 hidden">
    <li>
      <a href="data_booking.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
        <i class="fas fa-calendar-alt"></i>
        <span>Data Booking</span>
      </a>
    </li>
    <li>
      <a href="kelola_lokasi.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
        <i class="fas fa-map-marker-alt"></i>
        <span>Kelola Lokasi</span>
      </a>
    </li>
    <li>
      <a href="kelola_barber.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
        <i class="fas fa-cut"></i>
        <span>Kelola Barber</span>
      </a>
    </li>
  </ul>
</li>
<script>
  function toggleBookingSubmenu() {
    const submenu = document.getElementById('bookingSubmenu');
    const icon = document.getElementById('arrowBookingIcon');
    submenu.classList.toggle('hidden');
    icon.classList.toggle('rotate-180');
  }
</script>

                    <li class="mb-2">
      <button onclick="toggleSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
        <span class="flex items-center space-x-3">
          <i class="fas fa-box"></i>
          <span>Manajemen Produk</span>
        </span>
        <i id="arrowIcon" class="fas fa-chevron-down transition-transform"></i>
      </button>
      <ul id="submenu" class="ml-8 mt-2 space-y-2 hidden">
        <li>
          <a href="kelola_produk.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
            <i class="fas fa-cube"></i>
            <span>Produk</span>
          </a>
        </li>
        <li>
          <a href="kelola_pembeli.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
            <i class="fas fa-users"></i>
            <span>Data Pembeli</span>
          </a>
        </li>
        <li>
          <a href="kelola_metode_pembayaran.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
            <i class="fas fa-money-check-alt"></i>
            <span>Metode Pembayaran</span>
          </a>
        </li>
      </ul>
    </li>
                    <li class="mb-2">
                        <button onclick="toggleLowonganSubmenu()" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-briefcase"></i>
                                <span>Manajemen Lowongan</span>
                            </span>
                            <i id="arrowLowonganIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="lowonganSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li>
                                <a href="kelola_lowongan.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Kelola Lowongan</span>
                                </a>
                            </li>
                            <li>
                                <a href="data_pelamar.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fas fa-user-tie"></i>
                                    <span>Data Pelamar</span>
                                </a>
                            </li>
                            <li>
                                <a href="whatsapp_manager.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>WhatsApp Manager</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="px-4 py-3">
                        <a href="kelola_admin.php" class="flex items-center space-x-3">
                            <i class="fas fa-user-shield"></i>
                            <span>Kelola Admin</span>
                        </a>
                    </li>

                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col">
            <header class="flex items-center justify-between px-6 py-4 bg-white border-b-4 border-indigo-600">
                <div class="flex items-center">
                    <button class="text-gray-500 focus:outline-none">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-xl font-semibold ml-4">Kelola Produk</h1>
                </div>
                <div class="flex items-center">
                    <span class="mr-4 text-gray-700 hidden sm:block"><?php echo $_SESSION['admin_full_name'] ?? 'Admin'; ?></span>
                    <div class="relative">
                        <button id="profileDropdown" class="flex items-center focus:outline-none">
                            <?php
                            // Get admin profile photo
                            $stmt = $conn->prepare("SELECT profile_photo FROM admin WHERE id = ?");
                            $stmt->execute([$_SESSION['admin_id']]);
                            $profile_photo = $stmt->fetchColumn();

                            if (!empty($profile_photo) && file_exists('uploads/profiles/' . $profile_photo)):
                            ?>
                                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-300 hover:border-blue-500 transition-colors"
                                     src="uploads/profiles/<?= htmlspecialchars($profile_photo) ?>"
                                     alt="Admin avatar">
                            <?php else: ?>
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-300 hover:border-blue-500 transition-colors">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                            <?php endif; ?>
                            <i class="fas fa-chevron-down ml-2 text-gray-500 text-sm"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="profileDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="profile_settings.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-3 text-gray-500"></i>
                                Profile Settings
                            </a>
                            <hr class="my-1">
                            <a href="logout.php" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-3 text-red-500"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200 p-6">
                <!-- Success/Error Message Display -->
                <?php if (isset($_GET['success'])): ?>
                    <div id="success-message" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                        <span class="block sm:inline">Produk berhasil ditambahkan!</span>
                    </div>
                    <script>
                        // Remove success parameter from URL without refreshing
                        window.history.replaceState({}, document.title, window.location.pathname);
                        
                        // Auto hide success message after 5 seconds
                        setTimeout(function() {
                            const successMessage = document.getElementById('success-message');
                            if (successMessage) {
                                successMessage.style.transition = 'opacity 0.5s ease-in-out';
                                successMessage.style.opacity = '0';
                                setTimeout(() => successMessage.remove(), 500);
                            }
                        }, 5000);
                    </script>
                <?php elseif (isset($_GET['error'])): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                        <strong class="font-bold">Error!</strong>
                        <span class="block sm:inline"><?php echo htmlspecialchars($_GET['error']); ?></span>
                        <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                            <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.03a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.03a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                        </span>
                    </div>
                <?php endif; ?>

                <!-- Add Product Button -->
                <div class="mb-4">
                    <button id="openAddProductModalBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg flex items-center space-x-2">
                        <i class="fas fa-plus"></i>
                        <span>Tambah Produk</span>
                    </button>
                </div>

                <!-- Add Product Modal -->
                <div id="addProductModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
                    <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 relative overflow-y-auto max-h-[90vh]">
                        <button onclick="closeAddProductModal()" class="absolute top-3 right-3 text-gray-600 hover:text-gray-900 text-2xl font-bold">&times;</button>
                        <h2 class="text-2xl font-semibold mb-4">Tambah Produk Baru</h2>
                        <form action="kelola_produk.php" method="POST" enctype="multipart/form-data">
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="nama_produk">Nama Produk</label>
                                <input type="text" id="nama_produk" name="nama_produk" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="deskripsi">Deskripsi</label>
                                <textarea id="deskripsi" name="deskripsi" rows="4" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required></textarea>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="harga">Harga</label>
                                <input type="number" id="harga" name="harga" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="gambar_utama">Gambar Utama</label>
                                <input type="file" id="gambar_utama" name="gambar_utama" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" accept="image/*" required>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="kategori_id">Kategori</label>
                                <select id="kategori_id" name="kategori_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                                    <option value="">Pilih Kategori</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['nama_kategori']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Variasi Produk Section -->
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold mb-2">Variasi Produk</h3>
                                <div id="variasi-container">
                                    <!-- Existing variasi items will be loaded here, or new ones added -->
                                </div>
                                <button type="button" id="addVariasiBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                    Tambah Varian
                                </button>
                            </div>

                            <!-- Gambar Tambahan Section -->
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold mb-2">Gambar Tambahan (Opsional)</h3>
                                <div id="gambar-tambahan-container">
                                    <div class="gambar-tambahan-item mb-4 p-4 border rounded-lg bg-gray-50">
                                        <div class="form-group mb-2">
                                            <label class="block text-gray-700 text-sm font-bold mb-2">Pilih Gambar</label>
                                            <input type="file" name="gambar_tambahan[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" accept="image/*">
                                        </div>
                                        <div class="form-group">
                                            <label class="block text-gray-700 text-sm font-bold mb-2">Label Gambar (opsional)</label>
                                            <input type="text" name="label_gambar[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                                        </div>
                                        <button type="button" class="remove-gambar-btn bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded mt-2">Hapus Gambar</button>
                                    </div>
                                </div>
                                <button type="button" id="addGambarTambahanBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                    Tambah Gambar Tambahan
                                </button>
                            </div>

                            <div class="flex items-center justify-between">
                                <button type="submit" name="tambah_produk" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                    Tambah Produk
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Edit Product Modal -->
                <div id="editProductModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
                    <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 relative overflow-y-auto max-h-[90vh]">
                        <button onclick="closeEditModal()" class="absolute top-3 right-3 text-gray-600 hover:text-gray-900 text-2xl font-bold">&times;</button>
                        <h2 class="text-2xl font-semibold mb-4">Edit Produk</h2>
                        <form id="editProductForm" action="kelola_produk.php" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="update_produk" value="1">
                            <input type="hidden" id="edit_product_id" name="product_id">
                            <input type="hidden" id="edit_old_gambar_utama" name="old_gambar_utama">

                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="nama_produk_edit">Nama Produk</label>
                                <input type="text" id="nama_produk_edit" name="nama_produk_edit" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="deskripsi_edit">Deskripsi</label>
                                <textarea id="deskripsi_edit" name="deskripsi_edit" rows="4" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required></textarea>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="harga_edit">Harga</label>
                                <input type="number" id="harga_edit" name="harga_edit" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2">Gambar Utama Saat Ini</label>
                                <img id="current_gambar_utama" src="" alt="Gambar Utama" class="w-32 h-32 object-cover mb-2 rounded">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="gambar_utama_edit">Ganti Gambar Utama</label>
                                <input type="file" id="gambar_utama_edit" name="gambar_utama_edit" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" accept="image/*">
                            </div>
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-bold mb-2" for="kategori_id_edit">Kategori</label>
                                <select id="kategori_id_edit" name="kategori_id_edit" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['nama_kategori']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Edit Existing Variasi Produk Section -->
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold mb-2">Edit Variasi Produk</h3>
                                <div id="edit-variasi-container">
                                    <!-- Existing variasi items will be loaded here by JS -->
                                </div>
                                <!-- Add New Variant in Edit Modal -->
                                <button type="button" id="addNewVariasiEditBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                    Tambah Varian Baru
                                </button>
                            </div>

                            <!-- Edit Additional Images Section -->
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold mb-2">Gambar Tambahan</h3>
                                <div id="edit-gambar-tambahan-container">
                                    <!-- Existing additional images will be loaded here by JS -->
                                </div>
                                <button type="button" id="addGambarTambahanEditBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                    Tambah Gambar Tambahan Baru
                                </button>
                            </div>

                            <div class="flex justify-end mt-4">
                                <button type="button" onclick="closeEditModal()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">Batal</button>
                                <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">Simpan Perubahan</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Product List Section -->
                <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
                <h2 class="text-xl font-bold text-gray-800 mb-4 pb-2 border-b-2 border-blue-600">Daftar Produk</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border-collapse border">
                            <thead>
                                <tr>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">NO</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Gambar</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Nama Produk</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Harga</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Kategori</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Varian</th>
                                    <th class="py-2 px-4 border text-center text-sm font-semibold text-white bg-blue-800">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($products)): ?>
                                    <?php $no = 1; // Initialize counter ?>
                                    <?php foreach ($products as $product): ?>
                                        <tr>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo $no++; ?></td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700">
                                                <img src="../uploads/<?php echo htmlspecialchars($product['gambar_utama']); ?>" alt="<?php echo htmlspecialchars($product['nama_produk']); ?>" class="w-16 h-16 object-cover rounded mx-auto">
                                            </td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo htmlspecialchars($product['nama_produk']); ?></td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700">Rp <?php echo number_format($product['harga'], 0, ',', '.'); ?></td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700"><?php echo htmlspecialchars($product['kategori']); ?></td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700">
                                                <button type="button" onclick="viewVariasi(<?php echo $product['id']; ?>)" class="bg-blue-500 hover:bg-blue-700 text-white text-xs sm:text-sm font-bold py-1 sm:py-2 px-2 sm:px-4 rounded">
                                                    Lihat Varian
                                                </button>
                                            </td>
                                            <td class="py-2 px-4 border text-center text-sm text-gray-700">
                                                <button onclick="openEditModal(<?php echo htmlspecialchars(json_encode($product)); ?>)" class="bg-yellow-500 hover:bg-yellow-700 text-white text-xs font-bold py-1 px-2 rounded mr-1">Edit</button>
                                                <a href="kelola_produk.php?action=delete&id=<?php echo $product['id']; ?>" onclick="return confirm('Apakah Anda yakin ingin menghapus produk ini?');" class="bg-red-500 hover:bg-red-700 text-white text-xs font-bold py-1 px-2 rounded">Hapus</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="py-4 text-center text-gray-500 border">
                                            Tidak ada produk yang tersedia.
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- View Variasi Modal -->
    <div id="viewVariasiModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg p-6 w-full max-w-xl mx-4">
            <h2 class="text-2xl font-semibold mb-4">Detail Variasi Produk</h2>
            <div id="variasi-details" class="space-y-4">
                <!-- Variasi details will be loaded here by JS -->
            </div>
            <div class="flex justify-end mt-4">
                <button type="button" onclick="closeVariasiModal()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Tutup</button>
            </div>
        </div>
    </div>

<script>
    function toggleSubmenu() {
        const submenu = document.getElementById('submenu');
        const icon = document.getElementById('arrowIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleLowonganSubmenu() {
        const submenu = document.getElementById('lowonganSubmenu');
        const icon = document.getElementById('arrowLowonganIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Handle success/error message close
        const alertCloseButtons = document.querySelectorAll('.bg-green-100 svg, .bg-red-100 svg');
        alertCloseButtons.forEach(button => {
            button.addEventListener('click', function() {
                this.closest('[role="alert"]').remove();
            });
        });

        // Auto-hide success/error messages after 5 seconds
        const successAlert = document.getElementById('success-message');
        const errorAlert = document.getElementById('error-message');

        if (successAlert) {
            setTimeout(() => {
                successAlert.style.transition = 'opacity 0.5s ease-out';
                successAlert.style.opacity = '0';
                setTimeout(() => successAlert.remove(), 500); // Remove after transition
            }, 5000);
        }

        if (errorAlert) {
            setTimeout(() => {
                errorAlert.style.transition = 'opacity 0.5s ease-out';
                errorAlert.style.opacity = '0';
                setTimeout(() => errorAlert.remove(), 500); // Remove after transition
            }, 5000);
        }

        // Set active class for current page in sidebar
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.sidebar nav ul li a');
        navLinks.forEach(link => {
            // Remove active class from all direct links initially to ensure only one is active
            link.closest('li').classList.remove('active');

            if (currentPath.includes(link.getAttribute('href'))) { // Use includes for partial match
                link.closest('li').classList.add('active'); // Make the specific link active

                // If this active link is inside a submenu, open the submenu and activate its parent button's LI
                const parentUl = link.closest('ul');
                if (parentUl && (parentUl.id === 'submenu' || parentUl.id === 'bookingSubmenu' || parentUl.id === 'lowonganSubmenu')) {
                    parentUl.classList.remove('hidden'); // Show the submenu

                    // Get the button element that controls this submenu
                    const parentButton = parentUl.previousElementSibling;
                    if (parentButton) {
                        parentButton.querySelector('i').classList.add('rotate-180'); // Rotate arrow for parent button
                        // IMPORTANT: Ensure the parent LI (which contains the button) does NOT get the 'active' background
                        const parentLiWithButton = parentButton.closest('li');
                        if (parentLiWithButton) {
                            parentLiWithButton.classList.remove('active');
                        }
                    }
                }
            }
        });

        // Add Product Modal Logic
        const addProductModal = document.getElementById('addProductModal');
        const openAddProductModalBtn = document.getElementById('openAddProductModalBtn');
        
        openAddProductModalBtn.addEventListener('click', function() {
            addProductModal.classList.remove('hidden');
        });

        window.closeAddProductModal = function() {
            addProductModal.classList.add('hidden');
        };

        // Close modal when clicking outside of it
        addProductModal.addEventListener('click', function(e) {
            if (e.target === addProductModal) {
                closeAddProductModal();
            }
        });

        let variasiCount = 0;
        document.getElementById('addVariasiBtn').addEventListener('click', function() {
            variasiCount++;
            const variasiContainer = document.getElementById('variasi-container');
            const newVariasi = `
                <div class="variasi-item border p-4 mb-4 rounded-lg bg-gray-50">
                    <h4 class="font-semibold mb-2">Varian #${variasiCount}</h4>
                    <div class="form-group mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Ukuran (ml)</label>
                        <input type="text" name="ukuran_ml[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    </div>
                    <div class="form-group mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Harga Variasi</label>
                        <input type="number" name="harga_variasi[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    </div>
                    <div class="form-group mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Stok</label>
                        <input type="number" name="stok[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    </div>
                    <div class="form-group mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Gambar Varian</label>
                        <input type="file" name="gambar_varian[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" accept="image/*">
                    </div>
                    <button type="button" class="remove-variasi-btn bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded mt-2">Hapus Varian</button>
                </div>
            `;
            variasiContainer.insertAdjacentHTML('beforeend', newVariasi);
            attachRemoveListeners();
        });

        function attachRemoveListeners() {
            document.querySelectorAll('.remove-variasi-btn').forEach(button => {
                button.onclick = function() {
                    this.closest('.variasi-item').remove();
                    updateVariasiIndices();
                };
            });

            document.querySelectorAll('.remove-gambar-btn').forEach(button => {
                button.onclick = function() {
                    this.closest('.gambar-tambahan-item').remove();
                };
            });
        }

        function updateVariasiIndices() {
            document.querySelectorAll('#variasi-container .variasi-item').forEach((item, index) => {
                item.querySelector('h4').textContent = `Varian #${index + 1}`;
            });
        }

        let gambarTambahanCount = 0;
        document.getElementById('addGambarTambahanBtn').addEventListener('click', function() {
            gambarTambahanCount++;
            const container = document.getElementById('gambar-tambahan-container');
            const newItem = `
                <div class="gambar-tambahan-item mb-4 p-4 border rounded-lg bg-gray-50">
                    <div class="form-group mb-2">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Pilih Gambar</label>
                        <input type="file" name="gambar_tambahan[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" accept="image/*">
                    </div>
                    <div class="form-group">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Label Gambar (opsional)</label>
                        <input type="text" name="label_gambar[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    </div>
                    <button type="button" class="remove-gambar-btn bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded mt-2">Hapus Gambar</button>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', newItem);
            attachRemoveListeners();
        });

        // Edit Product Modal Logic
        const editProductModal = document.getElementById('editProductModal');
        const editVariasiContainer = document.getElementById('edit-variasi-container');
        const addNewVariasiEditBtn = document.getElementById('addNewVariasiEditBtn');
        let newVariasiEditCount = 0;

        addNewVariasiEditBtn.addEventListener('click', function() {
            newVariasiEditCount++;
            const newVariasiEditItem = `
                <div class="variasi-item border p-4 mb-4 rounded-lg bg-gray-50">
                    <h4 class="font-semibold mb-2">Varian Baru #${newVariasiEditCount}</h4>
                    <div class="form-group mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Ukuran (ml)</label>
                        <input type="text" name="new_ukuran_ml[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    </div>
                    <div class="form-group mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Harga Variasi</label>
                        <input type="number" name="new_harga_variasi[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    </div>
                    <div class="form-group mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Stok</label>
                        <input type="number" name="new_stok[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    </div>
                    <div class="form-group mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">Gambar Varian Baru</label>
                        <input type="file" name="new_gambar_varian[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" accept="image/*">
                    </div>
                    <button type="button" class="remove-variasi-btn-edit bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded mt-2">Hapus Varian Baru</button>
                </div>
            `;
            editVariasiContainer.insertAdjacentHTML('beforeend', newVariasiEditItem);
            attachRemoveEditListeners();
        });

        function attachRemoveEditListeners() {
            document.querySelectorAll('.remove-variasi-btn-edit').forEach(button => {
                button.onclick = function() {
                    this.closest('.variasi-item').remove();
                };
            });
            document.querySelectorAll('.remove-gambar-btn-edit').forEach(button => {
                button.onclick = function() {
                    this.closest('.gambar-tambahan-item').remove();
                    const imageId = this.dataset.imageId;
                    if (imageId) {
                        // Add to a hidden input to mark for deletion on submit
                        const deleteInput = document.createElement('input');
                        deleteInput.type = 'hidden';
                        deleteInput.name = 'delete_image_ids[]';
                        deleteInput.value = imageId;
                        document.getElementById('editProductForm').appendChild(deleteInput);
                    }
                };
            });
        }

        const addGambarTambahanEditBtn = document.getElementById('addGambarTambahanEditBtn');
        if (addGambarTambahanEditBtn) {
            addGambarTambahanEditBtn.addEventListener('click', function() {
                const container = document.getElementById('edit-gambar-tambahan-container');
                const newItem = `
                    <div class="gambar-tambahan-item mb-4 p-4 border rounded-lg bg-gray-50">
                        <div class="form-group mb-2">
                            <label class="block text-gray-700 text-sm font-bold mb-2">Pilih Gambar Baru</label>
                            <input type="file" name="existing_gambar_tambahan[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" accept="image/*">
                        </div>
                        <div class="form-group">
                            <label class="block text-gray-700 text-sm font-bold mb-2">Label Gambar</label>
                            <input type="text" name="existing_label_gambar[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                        <button type="button" class="remove-gambar-btn-edit bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded mt-2" data-image-id="${image.id}">Hapus Gambar Ini</button>
                    </div>
                `;
                container.insertAdjacentHTML('beforeend', newItem);
                attachRemoveEditListeners();
            });
        }

        window.openEditModal = function(product) {
            document.getElementById('edit_product_id').value = product.id;
            document.getElementById('nama_produk_edit').value = product.nama_produk;
            document.getElementById('deskripsi_edit').value = product.deskripsi;
            document.getElementById('harga_edit').value = product.harga;
            document.getElementById('kategori_id_edit').value = product.kategori_id;
            document.getElementById('current_gambar_utama').src = '../uploads/' + product.gambar_utama;
            document.getElementById('edit_old_gambar_utama').value = product.gambar_utama;
            
            // Clear existing variasi and additional images in modal
            editVariasiContainer.innerHTML = '';
            document.getElementById('edit-gambar-tambahan-container').innerHTML = '';
            
            // Fetch and populate existing variations for edit
            fetch('get_product_variations.php?product_id=' + product.id)
                .then(response => response.json())
                .then(variations => {
                    if (variations.length > 0) {
                        variations.forEach(variant => {
                            const variantItem = `
                                <div class="variasi-item border p-4 mb-4 rounded-lg bg-gray-50">
                                    <input type="hidden" name="edit_variant_id[]" value="${variant.id}">
                                    <input type="hidden" name="old_gambar_varian_edit[]" value="${variant.gambar_varian || ''}">
                                    <h4 class="font-semibold mb-2">Varian Existing</h4>
                                    ${variant.gambar_varian ? `<img src="${'../uploads/variants/' + variant.gambar_varian}" alt="Gambar Varian" class="w-20 h-20 object-cover mb-2 rounded">` : ''}
                                    <div class="form-group mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2">Ukuran (ml)</label>
                                        <input type="text" name="edit_ukuran_ml[]" value="${variant.ukuran_ml}" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                                    </div>
                                    <div class="form-group mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2">Harga Variasi</label>
                                        <input type="number" name="edit_harga_variasi[]" value="${variant.harga}" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                                    </div>
                                    <div class="form-group mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2">Stok</label>
                                        <input type="number" name="edit_stok[]" value="${variant.stok}" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                                    </div>
                                    <div class="form-group mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2">Ganti Gambar Varian</label>
                                        <input type="file" name="edit_gambar_varian[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" accept="image/*">
                                    </div>
                                    <button type="button" class="remove-variasi-btn-edit bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded mt-2">Hapus Varian Ini</button>
                                </div>
                            `;
                            editVariasiContainer.insertAdjacentHTML('beforeend', variantItem);
                        });
                        attachRemoveEditListeners();
                    }
                })
                .catch(error => console.error('Error fetching variations:', error));

            // Fetch and populate additional images for edit
            fetch('get_additional_images.php?product_id=' + product.id)
                .then(response => response.json())
                .then(images => {
                    if (images.length > 0) {
                        images.forEach(image => {
                            const imageItem = `
                                <div class="gambar-tambahan-item mb-4 p-4 border rounded-lg bg-gray-50">
                                    <input type="hidden" name="existing_image_id[]" value="${image.id}">
                                    <img src="${'../uploads/' + image.gambar}" alt="${image.label}" class="w-20 h-20 object-cover mb-2 rounded">
                                    <div class="form-group mb-2">
                                        <label class="block text-gray-700 text-sm font-bold mb-2">Pilih Gambar Baru</label>
                                        <input type="file" name="existing_gambar_tambahan[]" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" accept="image/*">
                                    </div>
                                    <div class="form-group">
                                        <label class="block text-gray-700 text-sm font-bold mb-2">Label Gambar</label>
                                        <input type="text" name="existing_label_gambar[]" value="${image.label}" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                                    </div>
                                    <button type="button" class="remove-gambar-btn-edit bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded mt-2" data-image-id="${image.id}">Hapus Gambar Ini</button>
                                </div>
                            `;
                            document.getElementById('edit-gambar-tambahan-container').insertAdjacentHTML('beforeend', imageItem);
                        });
                        attachRemoveEditListeners();
                    }
                })
                .catch(error => console.error('Error fetching additional images:', error));

            editProductModal.classList.remove('hidden');
        };

        window.closeEditModal = function() {
            editProductModal.classList.add('hidden');
            // Clear deletion markers
            document.querySelectorAll('#editProductForm input[name="delete_image_ids[]"]').forEach(input => input.remove());
        };

        window.viewVariasi = function(productId) {
            console.log('Fetching variations for product ID:', productId);
            fetch('get_product_variations.php?product_id=' + productId)
                .then(response => {
                    if (!response.ok) {
                        console.error('Network response was not ok', response.statusText);
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Received variations data:', data);
                    const variasiDetailsContainer = document.getElementById('variasi-details');
                    variasiDetailsContainer.innerHTML = ''; // Clear previous details

                    if (data.status === 'success' && data.data.length > 0) {
                        data.data.forEach(variant => {
                            console.log('Processing variant:', variant);
                            const variasiHtml = `
                                <div class="border p-3 rounded-lg bg-gray-50 flex items-center space-x-4">
                                    ${variant.gambar_varian ? `<img src="../uploads/variants/${variant.gambar_varian}" alt="Gambar Varian" class="w-16 h-16 object-cover rounded">` : ''}
                                    <div>
                                        <p class="font-semibold text-gray-800">Ukuran: ${variant.ukuran_ml} ml</p>
                                        <p class="text-gray-600">Harga: Rp ${new Intl.NumberFormat('id-ID').format(variant.harga)}</p>
                                        <p class="text-gray-600">Stok: ${variant.stok}</p>
                                    </div>
                                </div>
                            `;
                            variasiDetailsContainer.insertAdjacentHTML('beforeend', variasiHtml);
                        });
                    } else if (data.status === 'error') {
                        variasiDetailsContainer.innerHTML = `<p class="text-red-500">Error loading variations: ${data.message}</p>`;
                    } else {
                        variasiDetailsContainer.innerHTML = '<p class="text-gray-500">Tidak ada variasi untuk produk ini.</p>';
                    }
                    document.getElementById('viewVariasiModal').classList.remove('hidden');
                })
                .catch(error => console.error('Error fetching variations:', error));
        };

        window.closeVariasiModal = function() {
            document.getElementById('viewVariasiModal').classList.add('hidden');
        };

        // Profile dropdown functionality
        const profileDropdown = document.getElementById('profileDropdown');
        const profileDropdownMenu = document.getElementById('profileDropdownMenu');

        if (profileDropdown && profileDropdownMenu) {
            profileDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdownMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdownMenu.classList.add('hidden');
                }
            });
        }
    });
</script>
</body>
</html>
