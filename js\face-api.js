/**
 * Minified by jsDelivr using Terser v5.19.2.
 * Original file: /npm/face-api.js@0.22.2/build/commonjs/index.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var tslib_1=require("tslib"),tf=require("@tensorflow/tfjs-core");exports.tf=tf;var draw=require("./draw");exports.draw=draw;var utils=require("./utils");exports.utils=utils,tslib_1.__exportStar(require("./ageGenderNet/index"),exports),tslib_1.__exportStar(require("./classes/index"),exports),tslib_1.__exportStar(require("./dom/index"),exports),tslib_1.__exportStar(require("./env/index"),exports),tslib_1.__exportStar(require("./faceExpressionNet/index"),exports),tslib_1.__exportStar(require("./faceLandmarkNet/index"),exports),tslib_1.__exportStar(require("./faceRecognitionNet/index"),exports),tslib_1.__exportStar(require("./factories/index"),exports),tslib_1.__exportStar(require("./globalApi/index"),exports),tslib_1.__exportStar(require("./mtcnn/index"),exports),tslib_1.__exportStar(require("./ops/index"),exports),tslib_1.__exportStar(require("./ssdMobilenetv1/index"),exports),tslib_1.__exportStar(require("./tinyFaceDetector/index"),exports),tslib_1.__exportStar(require("./tinyYolov2/index"),exports),tslib_1.__exportStar(require("./euclideanDistance"),exports),tslib_1.__exportStar(require("./NeuralNetwork"),exports),tslib_1.__exportStar(require("./resizeResults"),exports);
//# sourceMappingURL=/sm/cbae0ac721f092c35bfebb7c763068fd3f75e829f0a653d108584e53a2141723.map