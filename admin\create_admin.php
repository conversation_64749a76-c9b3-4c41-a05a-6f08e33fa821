<?php
require_once 'db_config.php';

// Script untuk membuat admin baru atau reset password
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create') {
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        $full_name = $_POST['full_name'] ?? '';
        $email = $_POST['email'] ?? '';
        $role = $_POST['role'] ?? 'admin';
        
        if (!empty($username) && !empty($password) && !empty($full_name) && !empty($email)) {
            try {
                // Check if username already exists
                $stmt = $conn->prepare("SELECT id FROM admin WHERE username = ?");
                $stmt->execute([$username]);
                
                if ($stmt->fetch()) {
                    $message = "Username sudah ada!";
                    $message_type = "error";
                } else {
                    // Hash password
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Insert new admin
                    $stmt = $conn->prepare("INSERT INTO admin (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([$username, $hashed_password, $full_name, $email, $role]);
                    
                    $message = "Admin baru berhasil dibuat!";
                    $message_type = "success";
                }
            } catch (PDOException $e) {
                $message = "Error: " . $e->getMessage();
                $message_type = "error";
            }
        } else {
            $message = "Semua field harus diisi!";
            $message_type = "error";
        }
    }
    
    if ($action === 'reset') {
        $admin_id = $_POST['admin_id'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        
        if (!empty($admin_id) && !empty($new_password)) {
            try {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $conn->prepare("UPDATE admin SET password = ? WHERE id = ?");
                $stmt->execute([$hashed_password, $admin_id]);
                
                $message = "Password berhasil direset!";
                $message_type = "success";
            } catch (PDOException $e) {
                $message = "Error: " . $e->getMessage();
                $message_type = "error";
            }
        } else {
            $message = "ID Admin dan password baru harus diisi!";
            $message_type = "error";
        }
    }
}

// Get all admins
try {
    $stmt = $conn->query("SELECT id, username, full_name, email, role, is_active, last_login, created_at FROM admin ORDER BY created_at DESC");
    $admins = $stmt->fetchAll();
} catch (PDOException $e) {
    $admins = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Admin - Pixel Barbershop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-8">Kelola Admin</h1>
            
            <?php if (isset($message)): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <!-- Create New Admin Form -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-8">
                <h2 class="text-xl font-bold mb-4">Buat Admin Baru</h2>
                <form method="POST" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <input type="hidden" name="action" value="create">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                        <input type="text" name="username" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <input type="password" name="password" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nama Lengkap</label>
                        <input type="text" name="full_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                        <select name="role" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="admin">Admin</option>
                            <option value="super_admin">Super Admin</option>
                            <option value="moderator">Moderator</option>
                        </select>
                    </div>
                    
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-plus mr-2"></i>Buat Admin
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Admin List -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h2 class="text-xl font-bold mb-4">Daftar Admin</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full border border-gray-300">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-2 border border-gray-300 text-left">Username</th>
                                <th class="px-4 py-2 border border-gray-300 text-left">Nama Lengkap</th>
                                <th class="px-4 py-2 border border-gray-300 text-left">Email</th>
                                <th class="px-4 py-2 border border-gray-300 text-left">Role</th>
                                <th class="px-4 py-2 border border-gray-300 text-left">Status</th>
                                <th class="px-4 py-2 border border-gray-300 text-left">Last Login</th>
                                <th class="px-4 py-2 border border-gray-300 text-left">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($admins as $admin): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 border border-gray-300"><?= htmlspecialchars($admin['username']) ?></td>
                                <td class="px-4 py-2 border border-gray-300"><?= htmlspecialchars($admin['full_name']) ?></td>
                                <td class="px-4 py-2 border border-gray-300"><?= htmlspecialchars($admin['email']) ?></td>
                                <td class="px-4 py-2 border border-gray-300">
                                    <span class="px-2 py-1 rounded text-xs font-semibold
                                        <?php 
                                        switch($admin['role']) {
                                            case 'super_admin': echo 'bg-red-100 text-red-800'; break;
                                            case 'admin': echo 'bg-blue-100 text-blue-800'; break;
                                            case 'moderator': echo 'bg-green-100 text-green-800'; break;
                                            default: echo 'bg-gray-100 text-gray-800';
                                        }
                                        ?>">
                                        <?= ucfirst(str_replace('_', ' ', $admin['role'])) ?>
                                    </span>
                                </td>
                                <td class="px-4 py-2 border border-gray-300">
                                    <span class="px-2 py-1 rounded text-xs font-semibold <?= $admin['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                        <?= $admin['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                                <td class="px-4 py-2 border border-gray-300">
                                    <?= $admin['last_login'] ? date('d/m/Y H:i', strtotime($admin['last_login'])) : 'Never' ?>
                                </td>
                                <td class="px-4 py-2 border border-gray-300">
                                    <button onclick="resetPassword(<?= $admin['id'] ?>, '<?= htmlspecialchars($admin['username']) ?>')" 
                                            class="px-3 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600">
                                        Reset Password
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Reset Password Modal -->
            <div id="resetModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
                <div class="bg-white rounded-xl shadow-lg w-full max-w-md p-6">
                    <h3 class="text-xl font-bold mb-4">Reset Password</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="reset">
                        <input type="hidden" id="resetAdminId" name="admin_id">
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Username:</label>
                            <p id="resetUsername" class="font-semibold"></p>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Password Baru:</label>
                            <input type="password" name="new_password" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div class="flex justify-end space-x-2">
                            <button type="button" onclick="closeResetModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">Batal</button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Reset Password</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="mt-8 text-center">
                <a href="login.php" class="text-blue-600 hover:text-blue-800">← Kembali ke Login</a>
            </div>
        </div>
    </div>
    
    <script>
    function resetPassword(adminId, username) {
        document.getElementById('resetAdminId').value = adminId;
        document.getElementById('resetUsername').textContent = username;
        document.getElementById('resetModal').classList.remove('hidden');
    }
    
    function closeResetModal() {
        document.getElementById('resetModal').classList.add('hidden');
    }
    </script>
</body>
</html>
