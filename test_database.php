<?php
// Test file to check database structure and functionality
require_once 'Admin/db_config.php';

echo "<h2>Database Structure Test</h2>";

try {
    // Test 1: Check if pembeli table exists and has required columns
    echo "<h3>1. Checking pembeli table structure:</h3>";
    $stmt = $conn->query("DESCRIBE pembeli");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test 2: Check required columns exist
    echo "<h3>2. Required columns check:</h3>";
    $required_columns = ['id', 'nama_pembeli', 'email', 'phone', 'password', 'alamat', 'created_at'];
    $existing_columns = array_column($columns, 'Field');
    
    foreach ($required_columns as $col) {
        if (in_array($col, $existing_columns)) {
            echo "✅ Column '$col' exists<br>";
        } else {
            echo "❌ Column '$col' missing<br>";
        }
    }
    
    // Test 3: Try to add missing columns
    echo "<h3>3. Adding missing columns:</h3>";
    try {
        $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS password VARCHAR(255)");
        echo "✅ Password column added/exists<br>";
        
        $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS alamat TEXT");
        echo "✅ Alamat column added/exists<br>";
        
        $conn->exec("ALTER TABLE pembeli ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
        echo "✅ Updated_at column added/exists<br>";
        
    } catch (PDOException $e) {
        echo "❌ Error adding columns: " . $e->getMessage() . "<br>";
    }
    
    // Test 4: Test sample user operations
    echo "<h3>4. Testing user operations:</h3>";
    
    // Count existing users
    $stmt = $conn->query("SELECT COUNT(*) as count FROM pembeli");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "📊 Total users in database: $count<br>";
    
    // Test insert (if no test user exists)
    $stmt = $conn->prepare("SELECT id FROM pembeli WHERE email = '<EMAIL>'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        try {
            $test_password = password_hash('test123', PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO pembeli (nama_pembeli, email, phone, password, alamat, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute(['Test User', '<EMAIL>', '08123456789', $test_password, 'Test Address']);
            echo "✅ Test user created successfully<br>";
        } catch (PDOException $e) {
            echo "❌ Error creating test user: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "ℹ️ Test user already exists<br>";
    }
    
    echo "<h3>5. Final table structure:</h3>";
    $stmt = $conn->query("DESCRIBE pembeli");
    $final_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($final_columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage();
}

echo "<br><br><a href='index.php'>← Back to Home</a>";
?>
