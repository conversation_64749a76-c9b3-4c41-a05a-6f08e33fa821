<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Konsistensi Fixed - Face Detection</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">🎯 Test Konsistensi FIXED</h1>
                <p class="text-gray-600">Verifikasi konsistensi antara face.php dan output.php setelah perbaikan</p>
                <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 class="font-bold text-green-800">✅ Perbaikan yang Diterapkan:</h3>
                    <ul class="text-sm text-green-700 mt-2 space-y-1">
                        <li>• Real-time detection sebagai prioritas utama</li>
                        <li>• Konsistensi data dari face.php ke output.php</li>
                        <li>• Server response sebagai alternatif/fallback</li>
                        <li>• Logging detail untuk tracking konsistensi</li>
                    </ul>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🧪 Test Results</h2>
                <div id="testResults" class="space-y-4">
                    <p class="text-gray-500">Klik tombol test untuk memulai pengujian...</p>
                </div>
            </div>

            <!-- Test Controls -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🎮 Test Controls</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button onclick="testRealTimeConsistency()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-eye mr-2"></i>Test Real-time Consistency
                    </button>
                    <button onclick="testDataFlow()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-flow-chart mr-2"></i>Test Data Flow
                    </button>
                    <button onclick="simulateDetection()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-robot mr-2"></i>Simulate Detection
                    </button>
                    <button onclick="verifyOutput()" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-check-double mr-2"></i>Verify Output
                    </button>
                </div>
            </div>

            <!-- Mock Detection Simulator -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🎭 Mock Detection Simulator</h2>
                <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                    <button onclick="simulateShape('oval')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                        OVAL
                    </button>
                    <button onclick="simulateShape('round')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                        ROUND
                    </button>
                    <button onclick="simulateShape('square')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                        SQUARE
                    </button>
                    <button onclick="simulateShape('heart')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                        HEART
                    </button>
                    <button onclick="simulateShape('rectangular')" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm">
                        RECTANGULAR
                    </button>
                </div>
                <div class="flex gap-4">
                    <a href="output.php" target="_blank" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-external-link-alt mr-2"></i>Test di Output.php
                    </a>
                    <button onclick="clearAllData()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                        <i class="fas fa-trash mr-2"></i>Clear All Data
                    </button>
                </div>
            </div>

            <!-- Current Data Display -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">📊 Current SessionStorage Data</h2>
                <div id="currentData" class="bg-gray-100 p-4 rounded-lg font-mono text-sm">
                    <p class="text-gray-500">No data in sessionStorage</p>
                </div>
                <button onclick="refreshCurrentData()" class="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                    <i class="fas fa-sync mr-2"></i>Refresh
                </button>
            </div>
        </div>
    </div>

    <script>
        // Test functions
        function testRealTimeConsistency() {
            addTestResult('🎯 Testing Real-time Consistency...', 'info');
            
            const testShapes = ['oval', 'round', 'square', 'heart', 'rectangular'];
            let allTestsPassed = true;
            
            testShapes.forEach(shape => {
                // Simulate real-time detection
                window.currentDetectedShape = shape;
                
                // Simulate server response (different shape)
                const serverShape = getRandomShape(shape);
                
                // Simulate the priority logic
                const finalShape = window.currentDetectedShape; // Real-time has priority
                
                if (finalShape === shape) {
                    addTestResult(`✅ ${shape.toUpperCase()}: Real-time priority working correctly`, 'success');
                } else {
                    addTestResult(`❌ ${shape.toUpperCase()}: Priority logic failed`, 'error');
                    allTestsPassed = false;
                }
                
                // Log the simulation
                addTestResult(`   📊 Real-time: ${shape}, Server: ${serverShape}, Final: ${finalShape}`, 'info');
            });
            
            if (allTestsPassed) {
                addTestResult('🎉 All real-time consistency tests passed!', 'success');
            } else {
                addTestResult('⚠️ Some real-time consistency tests failed!', 'warning');
            }
        }
        
        function testDataFlow() {
            addTestResult('🔄 Testing Data Flow...', 'info');
            
            // Test 1: Real-time detection flow
            const realTimeShape = 'square';
            const serverShape = 'oval';
            
            // Simulate face.php detection
            window.currentDetectedShape = realTimeShape;
            
            // Simulate complete result creation
            const completeResult = {
                shape: realTimeShape, // Priority: real-time
                confidence: 95,
                alt_shape: serverShape !== realTimeShape ? serverShape : null,
                alt_confidence: serverShape !== realTimeShape ? 85 : 0,
                detection_source: 'real_time_priority',
                real_time_shape: realTimeShape,
                server_shape: serverShape,
                timestamp: new Date().toISOString()
            };
            
            // Test data integrity
            if (completeResult.shape === realTimeShape) {
                addTestResult('✅ Data flow: Real-time shape preserved', 'success');
            } else {
                addTestResult('❌ Data flow: Real-time shape lost', 'error');
            }
            
            if (completeResult.detection_source === 'real_time_priority') {
                addTestResult('✅ Data flow: Detection source marked correctly', 'success');
            } else {
                addTestResult('❌ Data flow: Detection source not marked', 'error');
            }
            
            if (completeResult.alt_shape === serverShape) {
                addTestResult('✅ Data flow: Server shape preserved as alternative', 'success');
            } else {
                addTestResult('❌ Data flow: Server shape not preserved', 'error');
            }
            
            addTestResult('📋 Data flow test completed', 'info');
        }
        
        function simulateDetection() {
            addTestResult('🤖 Simulating Full Detection Process...', 'info');
            
            const shapes = ['oval', 'round', 'square', 'heart', 'rectangular'];
            const randomShape = shapes[Math.floor(Math.random() * shapes.length)];
            
            addTestResult(`🎯 Simulating detection of: ${randomShape.toUpperCase()}`, 'info');
            
            // Step 1: Real-time detection
            window.currentDetectedShape = randomShape;
            addTestResult(`   1️⃣ Real-time detection: ${randomShape}`, 'success');
            
            // Step 2: Server response (might be different)
            const serverResponse = Math.random() > 0.7 ? getRandomShape(randomShape) : randomShape;
            addTestResult(`   2️⃣ Server response: ${serverResponse}`, 'info');
            
            // Step 3: Priority logic
            const finalShape = window.currentDetectedShape; // Real-time priority
            addTestResult(`   3️⃣ Final shape (real-time priority): ${finalShape}`, 'success');
            
            // Step 4: Create complete result
            simulateShape(finalShape);
            addTestResult(`   4️⃣ Data saved to sessionStorage`, 'success');
            
            addTestResult('🎉 Full detection simulation completed!', 'success');
        }
        
        function verifyOutput() {
            addTestResult('🔍 Verifying Output Consistency...', 'info');
            
            const storedData = sessionStorage.getItem('faceAnalysisResult');
            
            if (!storedData) {
                addTestResult('❌ No data found in sessionStorage', 'error');
                return;
            }
            
            try {
                const result = JSON.parse(storedData);
                
                // Check required fields
                const requiredFields = ['shape', 'detection_source', 'real_time_shape'];
                let allFieldsPresent = true;
                
                requiredFields.forEach(field => {
                    if (result[field]) {
                        addTestResult(`✅ Field '${field}': ${result[field]}`, 'success');
                    } else {
                        addTestResult(`❌ Missing field: ${field}`, 'error');
                        allFieldsPresent = false;
                    }
                });
                
                // Check consistency
                if (result.detection_source === 'real_time_priority') {
                    if (result.shape === result.real_time_shape) {
                        addTestResult('✅ Consistency: Shape matches real-time detection', 'success');
                    } else {
                        addTestResult('❌ Consistency: Shape does not match real-time detection', 'error');
                    }
                }
                
                if (allFieldsPresent) {
                    addTestResult('🎉 Output verification passed!', 'success');
                } else {
                    addTestResult('⚠️ Output verification found issues', 'warning');
                }
                
            } catch (e) {
                addTestResult('❌ Error parsing stored data: ' + e.message, 'error');
            }
        }
        
        function simulateShape(shape) {
            // Simulate real-time detection
            window.currentDetectedShape = shape;
            
            // Create mock data with real-time priority
            const mockData = {
                shape: shape.toLowerCase(),
                confidence: 95,
                alt_shape: getRandomShape(shape),
                alt_confidence: Math.floor(Math.random() * 30) + 70,
                description: `Bentuk wajah ${shape} terdeteksi dengan akurat menggunakan analisis real-time`,
                shape_description: `Bentuk wajah ${shape} dengan proporsi yang seimbang`,
                recommendations: getRecommendationsForShape(shape),
                detection_source: 'real_time_priority',
                real_time_shape: shape.toLowerCase(),
                server_shape: getRandomShape(shape),
                timestamp: new Date().toISOString()
            };
            
            // Create mock image
            const canvas = document.createElement('canvas');
            canvas.width = 200;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, 200, 200);
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`${shape.toUpperCase()}`, 100, 90);
            ctx.fillText('Real-time', 100, 110);
            ctx.fillText('Detection', 100, 130);
            
            const mockImage = canvas.toDataURL('image/jpeg', 0.8);
            
            // Save to sessionStorage
            sessionStorage.setItem('faceAnalysisResult', JSON.stringify(mockData));
            sessionStorage.setItem('capturedImage', mockImage);
            
            addTestResult(`✅ Simulated ${shape.toUpperCase()} with real-time priority`, 'success');
            refreshCurrentData();
        }
        
        function getRandomShape(excludeShape) {
            const shapes = ['oval', 'round', 'square', 'heart', 'rectangular'];
            const filtered = shapes.filter(s => s !== excludeShape);
            return filtered[Math.floor(Math.random() * filtered.length)];
        }
        
        function getRecommendationsForShape(shape) {
            const recommendations = {
                'oval': ['Textured Crop', 'Pompadour', 'Man Bun'],
                'round': ['Two Block Hair', 'Taper Fade', 'Fringe Haircut'],
                'square': ['Slick Back', 'Long Layers', 'French Crop'],
                'heart': ['Undercut', 'Textured Crop', 'Quiff'],
                'rectangular': ['Slick Back Hairstyles', 'Short Sides Long Top', 'Quiff']
            };
            return recommendations[shape] || recommendations['oval'];
        }
        
        function clearAllData() {
            sessionStorage.removeItem('faceAnalysisResult');
            sessionStorage.removeItem('capturedImage');
            window.currentDetectedShape = null;
            addTestResult('🗑️ All data cleared', 'info');
            refreshCurrentData();
        }
        
        function refreshCurrentData() {
            const currentDataDiv = document.getElementById('currentData');
            const storedData = sessionStorage.getItem('faceAnalysisResult');
            
            if (storedData) {
                try {
                    const parsed = JSON.parse(storedData);
                    currentDataDiv.innerHTML = `
                        <div class="space-y-2">
                            <div><strong>Shape:</strong> <span class="text-green-600">${parsed.shape}</span></div>
                            <div><strong>Detection Source:</strong> <span class="text-blue-600">${parsed.detection_source || 'Unknown'}</span></div>
                            <div><strong>Real-time Shape:</strong> <span class="text-purple-600">${parsed.real_time_shape || 'N/A'}</span></div>
                            <div><strong>Server Shape:</strong> <span class="text-orange-600">${parsed.server_shape || 'N/A'}</span></div>
                            <div><strong>Confidence:</strong> ${parsed.confidence}%</div>
                            <div><strong>Alt Shape:</strong> ${parsed.alt_shape || 'None'}</div>
                            <div><strong>Timestamp:</strong> ${parsed.timestamp || 'Unknown'}</div>
                        </div>
                    `;
                } catch (e) {
                    currentDataDiv.innerHTML = '<p class="text-red-500">Error parsing stored data</p>';
                }
            } else {
                currentDataDiv.innerHTML = '<p class="text-gray-500">No data in sessionStorage</p>';
            }
        }
        
        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultItem = document.createElement('div');
            
            let colorClass = 'text-gray-700';
            let bgClass = 'bg-gray-100';
            
            switch (type) {
                case 'success':
                    colorClass = 'text-green-700';
                    bgClass = 'bg-green-100';
                    break;
                case 'error':
                    colorClass = 'text-red-700';
                    bgClass = 'bg-red-100';
                    break;
                case 'warning':
                    colorClass = 'text-yellow-700';
                    bgClass = 'bg-yellow-100';
                    break;
                case 'info':
                    colorClass = 'text-blue-700';
                    bgClass = 'bg-blue-100';
                    break;
            }
            
            resultItem.className = `p-3 rounded-lg ${bgClass} ${colorClass} font-mono text-sm`;
            resultItem.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            // Clear "no results" message if it exists
            if (resultsDiv.children.length === 1 && resultsDiv.children[0].textContent.includes('Klik tombol test')) {
                resultsDiv.innerHTML = '';
            }
            
            resultsDiv.appendChild(resultItem);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            refreshCurrentData();
            addTestResult('🎯 Consistency test system initialized', 'info');
            addTestResult('✅ Real-time detection priority system active', 'success');
        });
    </script>
</body>
</html>
