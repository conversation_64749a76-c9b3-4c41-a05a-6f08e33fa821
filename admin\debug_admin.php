<?php
require_once 'db_config.php';

echo "<h2>Debug Admin Login System</h2>";

try {
    // Check if admin table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'admin'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ Tabel admin tidak ditemukan!</p>";
        exit;
    } else {
        echo "<p style='color: green;'>✅ Tabel admin ditemukan</p>";
    }

    // Check admin table structure
    echo "<h3>Struktur Tabel Admin:</h3>";
    $stmt = $conn->query("DESCRIBE admin");
    $columns = $stmt->fetchAll();
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Check all admin accounts
    echo "<h3>Data Admin yang Ada:</h3>";
    $stmt = $conn->query("SELECT id, username, full_name, email, role, is_active, last_login, created_at FROM admin ORDER BY id");
    $admins = $stmt->fetchAll();
    
    if (empty($admins)) {
        echo "<p style='color: red;'>❌ Tidak ada data admin!</p>";
        
        // Create default admin
        echo "<h3>Membuat Admin Default...</h3>";
        $default_password = password_hash('password', PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("INSERT INTO admin (username, password, full_name, email, role, is_active) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['admin', $default_password, 'Super Administrator', '<EMAIL>', 'super_admin', 1]);
        
        echo "<p style='color: green;'>✅ Admin default berhasil dibuat!</p>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> password</p>";
        
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Email</th><th>Role</th><th>Active</th><th>Last Login</th><th>Created</th></tr>";
        foreach ($admins as $admin) {
            echo "<tr>";
            echo "<td>" . $admin['id'] . "</td>";
            echo "<td>" . $admin['username'] . "</td>";
            echo "<td>" . $admin['full_name'] . "</td>";
            echo "<td>" . $admin['email'] . "</td>";
            echo "<td>" . $admin['role'] . "</td>";
            echo "<td>" . ($admin['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "<td>" . ($admin['last_login'] ?? 'Never') . "</td>";
            echo "<td>" . $admin['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Test password verification for existing admins
    echo "<h3>Test Password Verification:</h3>";
    foreach ($admins as $admin) {
        $stmt = $conn->prepare("SELECT password FROM admin WHERE username = ?");
        $stmt->execute([$admin['username']]);
        $stored_password = $stmt->fetchColumn();
        
        $test_password = 'password';
        $is_valid = password_verify($test_password, $stored_password);
        
        echo "<p><strong>" . $admin['username'] . ":</strong> ";
        if ($is_valid) {
            echo "<span style='color: green;'>✅ Password 'password' valid</span>";
        } else {
            echo "<span style='color: red;'>❌ Password 'password' tidak valid</span>";
        }
        echo "</p>";
    }

    // Fix inactive admins
    echo "<h3>Mengaktifkan Admin yang Tidak Aktif:</h3>";
    $stmt = $conn->prepare("UPDATE admin SET is_active = 1 WHERE is_active = 0 OR is_active IS NULL");
    $affected = $stmt->execute();
    $count = $stmt->rowCount();
    
    if ($count > 0) {
        echo "<p style='color: green;'>✅ $count admin berhasil diaktifkan</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Semua admin sudah aktif</p>";
    }

    // Reset password for all admins to 'password'
    echo "<h3>Reset Password Semua Admin ke 'password':</h3>";
    $new_password = password_hash('password', PASSWORD_DEFAULT);
    $stmt = $conn->prepare("UPDATE admin SET password = ?");
    $stmt->execute([$new_password]);
    $count = $stmt->rowCount();
    
    echo "<p style='color: green;'>✅ Password $count admin berhasil direset ke 'password'</p>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Informasi Login:</h3>";
echo "<p><strong>URL Login:</strong> <a href='login.php'>login.php</a></p>";
echo "<p><strong>Username yang bisa digunakan:</strong></p>";
echo "<ul>";
echo "<li>admin (Super Administrator)</li>";
echo "<li>manager (Manager)</li>";
echo "<li>staff (Staff Admin)</li>";
echo "</ul>";
echo "<p><strong>Password untuk semua akun:</strong> password</p>";
?>
