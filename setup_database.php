<?php
// Database setup script
error_reporting(E_ALL);
ini_set('display_errors', 1);

$host = 'localhost';
$username = 'root';
$password = '';
$database = 'pixel';

try {
    // Connect to MySQL server (without database)
    $conn = new PDO("mysql:host=$host", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if not exists
    $conn->exec("CREATE DATABASE IF NOT EXISTS $database");
    echo "Database '$database' created or already exists.<br>";
    
    // Connect to the specific database
    $conn = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Read and execute the database.sql file
    $sql_file = 'database.sql';
    if (file_exists($sql_file)) {
        $sql_content = file_get_contents($sql_file);
        
        // Split SQL statements by semicolon and execute each one
        $statements = explode(';', $sql_content);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                try {
                    $conn->exec($statement);
                } catch (PDOException $e) {
                    // Skip errors for statements that might already exist
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "Warning: " . $e->getMessage() . "<br>";
                    }
                }
            }
        }
        
        echo "Database tables created successfully!<br>";
    } else {
        echo "database.sql file not found!<br>";
    }
    
    // Insert some sample data if tables are empty
    
    // Sample locations
    $stmt = $conn->prepare("SELECT COUNT(*) FROM locations");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $locations = [
            ['Pixel Barbershop Jakarta Selatan', 'Jl. Kemang Raya No. 123, Jakarta Selatan', '021-12345678'],
            ['Pixel Barbershop Jakarta Pusat', 'Jl. Sudirman No. 456, Jakarta Pusat', '021-87654321'],
            ['Pixel Barbershop Bandung', 'Jl. Braga No. 789, Bandung', '022-11223344']
        ];
        
        $stmt = $conn->prepare("INSERT INTO locations (name, address, phone) VALUES (?, ?, ?)");
        foreach ($locations as $location) {
            $stmt->execute($location);
        }
        echo "Sample locations inserted.<br>";
    }
    
    // Sample jobs
    $stmt = $conn->prepare("SELECT COUNT(*) FROM jobs");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $jobs = [
            [
                'Senior Barber',
                'Jakarta Selatan',
                'Full Time',
                'Rp 4.000.000 - 6.000.000',
                'Mencari barber berpengalaman untuk melayani pelanggan dengan berbagai gaya rambut modern dan klasik.',
                'Minimal 3 tahun pengalaman sebagai barber, memiliki sertifikat barbering, komunikasi yang baik',
                'Gaji kompetitif, bonus performa, asuransi kesehatan, cuti tahunan',
                'Senior',
                'SMA/SMK'
            ],
            [
                'Junior Barber',
                'Jakarta Pusat',
                'Full Time',
                'Rp 2.500.000 - 3.500.000',
                'Kesempatan untuk barber pemula yang ingin mengembangkan karir di industri barbering.',
                'Fresh graduate atau minimal 1 tahun pengalaman, antusias belajar, attitude positif',
                'Training intensif, mentoring dari senior barber, jenjang karir yang jelas',
                'Junior',
                'SMA/SMK'
            ],
            [
                'Barber Freelance',
                'Bandung',
                'Part Time',
                'Rp 150.000 - 300.000/hari',
                'Mencari barber freelance untuk weekend dan hari-hari sibuk.',
                'Fleksibel, bisa bekerja weekend, memiliki skill dasar barbering',
                'Jadwal fleksibel, bayaran harian, bonus tip dari customer',
                'Fresh Graduate',
                'SMA/SMK'
            ]
        ];
        
        $stmt = $conn->prepare("INSERT INTO jobs (position, location, type, salary, description, requirements, benefits, experience_level, education) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        foreach ($jobs as $job) {
            $stmt->execute($job);
        }
        echo "Sample jobs inserted.<br>";
    }

    // Sample admin accounts
    $stmt = $conn->prepare("SELECT COUNT(*) FROM admin");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        // Default password is "password" for all accounts
        $default_password = password_hash('password', PASSWORD_DEFAULT);

        $admins = [
            ['admin', $default_password, 'Super Administrator', '<EMAIL>', 'super_admin'],
            ['manager', $default_password, 'Manager', '<EMAIL>', 'admin'],
            ['staff', $default_password, 'Staff Admin', '<EMAIL>', 'moderator']
        ];

        $stmt = $conn->prepare("INSERT INTO admin (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
        foreach ($admins as $admin) {
            $stmt->execute($admin);
        }
        echo "Default admin accounts created.<br>";
        echo "<div class='bg-blue-100 p-4 rounded mt-4'>";
        echo "<h3 class='font-bold'>Default Admin Credentials:</h3>";
        echo "<ul class='mt-2'>";
        echo "<li><strong>Super Admin:</strong> admin / password</li>";
        echo "<li><strong>Manager:</strong> manager / password</li>";
        echo "<li><strong>Staff:</strong> staff / password</li>";
        echo "</ul>";
        echo "</div>";
    }

    echo "<br><strong>Database setup completed successfully!</strong><br>";
    echo "<a href='admin/login.php'>Go to Admin Login</a> | <a href='admin/create_admin.php'>Manage Admins</a> | <a href='index.php'>Go to Homepage</a>";
    
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
