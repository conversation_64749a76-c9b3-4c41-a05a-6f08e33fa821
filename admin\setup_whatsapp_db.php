<?php
/**
 * WhatsApp Database Setup Script
 * Run this script to create necessary database tables for WhatsApp notifications
 */

require_once 'db_config.php';

echo "<h2>WhatsApp Database Setup</h2>";

try {
    // Create notification_logs table
    $sql = "CREATE TABLE IF NOT EXISTS notification_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        application_id INT NOT NULL,
        type ENUM('whatsapp', 'email', 'sms') NOT NULL DEFAULT 'whatsapp',
        recipient VARCHAR(255) NOT NULL,
        message_sent TEXT NOT NULL,
        success BOOLEAN NOT NULL DEFAULT FALSE,
        response JSON NULL,
        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_application_id (application_id),
        INDEX idx_sent_at (sent_at),
        INDEX idx_success (success),
        FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($sql);
    echo "<p style='color: green;'>✅ Tabel 'notification_logs' berhasil dibuat!</p>";
    
    // Add evaluation columns to applications table if not exists
    $alterQueries = [
        "ALTER TABLE applications ADD COLUMN IF NOT EXISTS evaluation_score DECIMAL(5,2) DEFAULT NULL COMMENT 'Comprehensive evaluation score'",
        "ALTER TABLE applications ADD COLUMN IF NOT EXISTS evaluation_details JSON DEFAULT NULL COMMENT 'Detailed evaluation breakdown'"
    ];
    
    foreach ($alterQueries as $query) {
        try {
            $conn->exec($query);
            echo "<p style='color: green;'>✅ Kolom evaluasi berhasil ditambahkan!</p>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<p style='color: orange;'>⚠️ Kolom evaluasi sudah ada.</p>";
            } else {
                throw $e;
            }
        }
    }
    
    // Test insert (optional)
    echo "<h3>Test Database Connection</h3>";
    $testQuery = "SELECT COUNT(*) as count FROM notification_logs";
    $stmt = $conn->prepare($testQuery);
    $stmt->execute();
    $result = $stmt->fetch();
    
    echo "<p style='color: blue;'>📊 Jumlah log notifikasi saat ini: " . $result['count'] . "</p>";
    
    echo "<h3>Setup Selesai!</h3>";
    echo "<p>Database WhatsApp notification sudah siap digunakan.</p>";
    echo "<p><a href='whatsapp_manager.php' style='background: #3b82f6; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Buka WhatsApp Manager</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Pastikan:</p>";
    echo "<ul>";
    echo "<li>Database connection sudah benar</li>";
    echo "<li>User database memiliki permission CREATE TABLE</li>";
    echo "<li>Tabel 'applications' sudah ada</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Unexpected error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>WhatsApp Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        h2 { color: #333; border-bottom: 2px solid #3b82f6; padding-bottom: 10px; }
        h3 { color: #555; margin-top: 30px; }
        p { line-height: 1.6; }
        ul { margin-left: 20px; }
        li { margin-bottom: 5px; }
    </style>
</head>
<body>
    <!-- Content is generated by PHP above -->
</body>
</html>
