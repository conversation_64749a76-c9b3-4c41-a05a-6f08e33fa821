<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header("Location: login.php");
    exit();
}

require_once 'db_config.php'; // Pastikan path sudah benar

// Proses hapus lowongan
if (isset($_POST['delete_id'])) {
    $delete_id = $_POST['delete_id'];
    try {
        $stmt = $conn->prepare("DELETE FROM jobs WHERE id = ?");
        $stmt->execute([$delete_id]);
        $_SESSION['message'] = 'Lowongan berhasil dihapus!';
        $_SESSION['message_type'] = 'success';
    } catch (PDOException $e) {
        $_SESSION['message'] = 'Gagal menghapus lowongan: ' . $e->getMessage();
        $_SESSION['message_type'] = 'error';
    }
    header("Location: kelola_lowongan.php");
    exit;
}

// Proses tambah lowongan
if (isset($_POST['add_job'])) {
    $position = $_POST['position'] ?? '';
    $location = $_POST['location'] ?? '';
    $type = $_POST['type'];
    $salary = $_POST['salary'];
    $description = $_POST['description'];

    // Process requirements array
    $requirements_array = $_POST['requirements'] ?? [];
    $requirements_array = array_filter($requirements_array, function($item) {
        return !empty(trim($item));
    });
    $requirements = !empty($requirements_array) ? json_encode($requirements_array) : '';

    // Process benefits array
    $benefits_array = $_POST['benefits'] ?? [];
    $benefits_array = array_filter($benefits_array, function($item) {
        return !empty(trim($item));
    });
    $benefits = !empty($benefits_array) ? json_encode($benefits_array) : '';

    $experience_level = $_POST['experience_level'] ?? $_POST['experience'] ?? '';
    $education = $_POST['education'] ?? '';
    $image = null;

    // Proses upload gambar jika ada
    if (!empty($_FILES['image']['name'])) {
        $target_dir = "../assets/";
        if (!is_dir($target_dir)) {
            mkdir($target_dir, 0755, true);
        }
        $image = uniqid() . '_' . basename($_FILES["image"]["name"]);
        if (move_uploaded_file($_FILES["image"]["tmp_name"], $target_dir . $image)) {
            // Upload berhasil
        } else {
            $image = null;
        }
    }

    try {
        // Simpan ke database
        $stmt = $conn->prepare("INSERT INTO jobs (position, location, type, salary, description, requirements, benefits, experience_level, education, image) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$position, $location, $type, $salary, $description, $requirements, $benefits, $experience_level, $education, $image]);
        $_SESSION['message'] = 'Lowongan berhasil ditambahkan!';
        $_SESSION['message_type'] = 'success';
    } catch (PDOException $e) {
        $_SESSION['message'] = 'Gagal menambahkan lowongan: ' . $e->getMessage();
        $_SESSION['message_type'] = 'error';
    }
    header("Location: kelola_lowongan.php");
    exit;
}

// Proses edit lowongan
if (isset($_POST['save_edit_lowongan'])) {
    $id = $_POST['edit_id'];
    $position = $_POST['edit_position'];
    $location = $_POST['edit_location'];
    $type = $_POST['edit_type'];
    $salary = $_POST['edit_salary'];
    $experience_level = $_POST['edit_experience_level'];
    $education = $_POST['edit_education'];
    try {
        $stmt = $conn->prepare("UPDATE jobs SET position=?, location=?, type=?, salary=?, experience_level=?, education=? WHERE id=?");
        $stmt->execute([$position, $location, $type, $salary, $experience_level, $education, $id]);
        $_SESSION['message'] = 'Lowongan berhasil diupdate!';
        $_SESSION['message_type'] = 'success';
    } catch (PDOException $e) {
        $_SESSION['message'] = 'Gagal update lowongan: ' . $e->getMessage();
        $_SESSION['message_type'] = 'error';
    }
    header("Location: kelola_lowongan.php");
    exit;
}

// Logic tambah pertanyaan quiz
if (isset($_POST['add_quiz_question'])) {
    $question = trim($_POST['quiz_question']);
    $job_id = isset($_POST['quiz_job_id']) ? intval($_POST['quiz_job_id']) : null;
    $answers = $_POST['quiz_answers'];
    $correct = $_POST['correct_answer'] ?? -1;
    if ($question && !empty($answers)) {
        $stmt = $conn->prepare("INSERT INTO quiz_questions (question, job_id) VALUES (?, ?)");
        $stmt->execute([$question, $job_id]);
        $question_id = $conn->lastInsertId();
        foreach ($answers as $i => $ans) {
            if (trim($ans) !== '') {
                $is_correct = ($i == $correct) ? 1 : 0;
                $stmt2 = $conn->prepare("INSERT INTO quiz_answers (question_id, answer, is_correct) VALUES (?, ?, ?)");
                $stmt2->execute([$question_id, trim($ans), $is_correct]);
            }
        }
        $_SESSION['message'] = 'Pertanyaan quiz berhasil ditambahkan!';
        $_SESSION['message_type'] = 'success';
    }
    header("Location: kelola_lowongan.php#quiz");
    exit;
}

// Logic hapus pertanyaan quiz
if (isset($_POST['delete_quiz_question'])) {
    $qid = intval($_POST['delete_quiz_question']);
    $conn->prepare("DELETE FROM quiz_questions WHERE id = ?")->execute([$qid]);
    $conn->prepare("DELETE FROM quiz_answers WHERE question_id = ?")->execute([$qid]);
    $_SESSION['message'] = 'Pertanyaan quiz berhasil dihapus!';
    $_SESSION['message_type'] = 'success';
    header("Location: kelola_lowongan.php#quiz");
    exit;
}

// Ambil data lowongan dari database
$jobs = [];
try {
    $stmt = $conn->query("SELECT * FROM jobs ORDER BY posted DESC");
    $jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $jobs = [];
    error_log("Error fetching jobs: " . $e->getMessage());
}

// Ambil quiz dari database
$quiz_questions = $conn->query("SELECT * FROM quiz_questions ORDER BY id DESC")->fetchAll(PDO::FETCH_ASSOC);
$quiz_answers = [];
foreach ($quiz_questions as $q) {
    $quiz_answers[$q['id']] = $conn->query("SELECT * FROM quiz_answers WHERE question_id = " . intval($q['id']))->fetchAll(PDO::FETCH_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Produk - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            background: #2d3748;
            color: #fff;
            transition: transform 0.3s ease-in-out;
        }
        .sidebar .logo {
            background: #1a202c;
            padding: 1rem;
        }
        .sidebar .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .sidebar nav ul li {
            transition: all 0.3s ease;
        }
        .sidebar nav ul li a {
            color: #fff;
            text-decoration: none;
        }
        .sidebar nav ul li.active {
            background: rgb(35, 71, 250);
        }
        .sidebar nav ul li.active a,
        .sidebar nav ul li.active i {
            color: #fff;
        }
        .sidebar nav ul li i {
            color: #4299e1;
        }
        .sidebar nav ul li.active i {
            color: #fff;
        }
        .sidebar nav ul li:hover {
            background: rgba(66, 153, 225, 0.1);
        }
        .sidebar nav ul li.active:hover {
            background: rgb(35, 71, 250);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 50;
                transform: translateX(-100%);
            }
            .sidebar.open {
                transform: translateX(0);
            }
            .main-content {
                width: 100%;
            }
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 40;
                display: none;
            }
            .overlay.show {
                display: block;
            }
        }

        @media (min-width: 769px) {
            .sidebar {
                position: relative;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile Overlay -->
    <div id="overlay" class="overlay"></div>

    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <aside id="sidebar" class="w-64 sidebar">
            <div class="logo">
                <h1 class="text-2xl font-bold">Admin Panel</h1>
            </div>
            <nav class="mt-4">
                <ul>
                    <li class="px-4 py-3 hover:bg-gray-600 rounded-lg mx-2">
                        <a href="index.php" class="flex items-center space-x-3">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <!-- Manajemen Booking -->
                    <li class="mb-2 mx-2">
                        <button onclick="toggleBookingSubmenu(event)" class="w-full flex items-center justify-between px-4 py-3 rounded-lg text-white focus:outline-none hover:bg-gray-600 transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-calendar-check"></i>
                                <span>Manajemen Booking</span>
                            </span>
                            <i id="arrowBookingIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="bookingSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'data_booking.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="data_booking.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Data Booking</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_lokasi.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_lokasi.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Kelola Lokasi</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_barber.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_barber.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-cut"></i>
                                    <span>Kelola Barber</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- Manajemen Produk -->
                    <li class="mb-2 mx-2">
                        <button onclick="toggleProdukSubmenu(event)" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none hover:bg-gray-600 transition-colors duration-200">
                            <span class="flex items-center space-x-3">
                                <i class="fas fa-box"></i>
                                <span>Manajemen Produk</span>
                            </span>
                            <i id="arrowProdukIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="produkSubmenu" class="ml-8 mt-2 space-y-2 hidden">
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_produk.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_produk.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-cube"></i>
                                    <span>Produk</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_pembeli.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_pembeli.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pembeli</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_metode_pembayaran.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_metode_pembayaran.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-money-check-alt"></i>
                                    <span>Metode Pembayaran</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- Manajemen Lowongan -->
                    <li class="mb-2 mx-2">
                        <button onclick="toggleLowonganSubmenu(event)" class="w-full flex items-center justify-between px-4 py-3 text-white rounded-lg focus:outline-none hover:bg-gray-600 transition-colors duration-200">
                        <span class="flex items-center space-x-3">
                            <i class="fas fa-briefcase"></i>
                            <span>Manajemen Lowongan</span>
                        </span>
                            <i id="arrowLowonganIcon" class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <ul id="lowonganSubmenu" class="ml-8 mt-2 space-y-2 <?php echo (basename($_SERVER['PHP_SELF']) === 'kelola_lowongan.php' || basename($_SERVER['PHP_SELF']) === 'data_pelamar.php' || basename($_SERVER['PHP_SELF']) === 'whatsapp_manager.php') ? '' : 'hidden'; ?>">
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'kelola_lowongan.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="kelola_lowongan.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-tasks"></i>
                                    <span>Kelola Lowongan</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'data_pelamar.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="data_pelamar.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fas fa-users"></i>
                                    <span>Data Pelamar</span>
                                </a>
                            </li>
                            <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'whatsapp_manager.php' ? 'bg-blue-600 rounded-md' : ''; ?>">
                                <a href="whatsapp_manager.php" class="flex items-center space-x-2 px-2 py-2 rounded-md hover:bg-blue-500 text-white transition-colors duration-200">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>WhatsApp Manager</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="px-4 py-3 hover:bg-gray-600 rounded-lg mx-2 <?php echo basename($_SERVER['PHP_SELF']) === 'kelola_admin.php' ? 'bg-blue-600' : ''; ?>">
                        <a href="kelola_admin.php" class="flex items-center space-x-3">
                            <i class="fas fa-user-shield"></i>
                            <span>Kelola Admin</span>
                        </a>
                    </li>

                </ul>
            </nav>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col main-content">
            <header class="flex items-center justify-between px-6 py-4 bg-white border-b-4 border-indigo-600">
                <div class="flex items-center">
                    <button id="sidebarToggle" class="text-gray-500 focus:outline-none md:hidden mr-4">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <h1 class="text-xl font-semibold">Kelola Lowongan</h1>
                </div>

                <div class="flex items-center">
                    <span class="mr-4 text-gray-700 hidden sm:block"><?php echo $_SESSION['admin_full_name'] ?? 'Admin'; ?></span>
                    <div class="relative">
                        <button id="profileDropdown" class="flex items-center focus:outline-none">
                            <?php
                            // Get admin profile photo
                            $stmt = $conn->prepare("SELECT profile_photo FROM admin WHERE id = ?");
                            $stmt->execute([$_SESSION['admin_id']]);
                            $profile_photo = $stmt->fetchColumn();

                            if (!empty($profile_photo) && file_exists('uploads/profiles/' . $profile_photo)):
                            ?>
                                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-300 hover:border-blue-500 transition-colors"
                                     src="uploads/profiles/<?= htmlspecialchars($profile_photo) ?>"
                                     alt="Admin avatar">
                            <?php else: ?>
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-300 hover:border-blue-500 transition-colors">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                            <?php endif; ?>
                            <i class="fas fa-chevron-down ml-2 text-gray-500 text-sm"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="profileDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="profile_settings.php" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-cog mr-3 text-gray-500"></i>
                                Profile Settings
                            </a>
                            <hr class="my-1">
                            <a href="logout.php" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-3 text-red-500"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 p-6">
                <!-- Success/Error Messages -->
                <?php if (isset($_SESSION['message'])): ?>
                    <div id="notification" class="mb-4 p-4 rounded-lg <?php echo $_SESSION['message_type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?> transition-all duration-500 ease-in-out">
                        <div class="flex items-center justify-between">
                            <span><?php echo htmlspecialchars($_SESSION['message']); ?></span>
                            <button onclick="hideNotification()" class="ml-4 text-gray-500 hover:text-gray-700 font-bold text-lg">&times;</button>
                        </div>
                    </div>
                    <?php unset($_SESSION['message'], $_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Tombol Tambah Lowongan Baru dan Tambah Pertanyaan sejajar -->
                <div class="flex flex-row items-center mb-6 gap-4">
                    <a href="javascript:void(0)" onclick="openLowonganModal()" class="inline-block px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-bold text-lg shadow transition">
                        <i class="fas fa-plus mr-2"></i> Tambah Lowongan Baru
                    </a>
                    <button id="openQuizModal" class="inline-block px-6 py-3 rounded-lg bg-blue-600 hover:bg-blue-700 text-white font-bold text-lg shadow transition flex items-center">
                        <i class="fas fa-plus mr-2"></i> Tambah Pertanyaan
                    </button>
                </div>

                <!-- Modal Pop-up Scrollable -->
                <div id="tambahLowonganModal" class="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50 hidden p-4">
                    <div class="bg-white rounded-xl shadow-lg w-full max-w-2xl relative p-0 max-h-[95vh] overflow-hidden">
                        <button onclick="closeLowonganModal()" class="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-2xl font-bold focus:outline-none">&times;</button>
                        <h2 class="text-xl font-bold mb-4 border-b-2 border-indigo-600 pb-2 px-6 pt-6">Tambah Lowongan Baru</h2>
                        <div class="px-6 pb-6 max-h-[80vh] overflow-y-auto">
                            <form action="" method="POST" enctype="multipart/form-data">
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Posisi Pekerjaan</label>
                                    <input type="text" name="position" class="w-full border rounded px-3 py-2" required placeholder="Contoh: Barber Senior">
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Lokasi</label>
                                    <input type="text" name="location" class="w-full border rounded px-3 py-2" required placeholder="Contoh: Jakarta Selatan">
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Tipe Pekerjaan</label>
                                    <select name="type" class="w-full border rounded px-3 py-2" required>
                                        <option value="">Pilih Tipe Pekerjaan</option>
                                        <option value="Full Time">Full Time</option>
                                        <option value="Part Time">Part Time</option>
                                        <option value="Contract">Contract</option>
                                        <option value="Freelance">Freelance</option>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Gaji</label>
                                    <input type="text" name="salary" class="w-full border rounded px-3 py-2" required placeholder="Contoh: Rp 3.000.000 - 5.000.000">
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Deskripsi Pekerjaan</label>
                                    <textarea name="description" class="w-full border rounded px-3 py-2" rows="3" required placeholder="Jelaskan tugas dan tanggung jawab pekerjaan"></textarea>
                                </div>
                                <!-- Kualifikasi/Persyaratan -->
                                <div class="mb-4">
                                    <label class="block mb-2 font-semibold">Kualifikasi/Persyaratan</label>
                                    <div class="mb-2">
                                        <small class="text-gray-600">Contoh kualifikasi umum:</small>
                                        <div class="flex flex-wrap gap-2 mt-1">
                                            <button type="button" onclick="addPresetRequirement('Minimal SMA/SMK sederajat')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">SMA/SMK</button>
                                            <button type="button" onclick="addPresetRequirement('Pengalaman minimal 1 tahun di bidang barbershop')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">Pengalaman 1 tahun</button>
                                            <button type="button" onclick="addPresetRequirement('Memiliki sertifikat barbering')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">Sertifikat</button>
                                            <button type="button" onclick="addPresetRequirement('Komunikasi yang baik dengan pelanggan')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">Komunikasi baik</button>
                                        </div>
                                    </div>
                                    <div id="requirementsContainer">
                                        <div class="requirement-item flex items-center mb-2">
                                            <input type="text" name="requirements[]" class="flex-1 border rounded px-3 py-2 mr-2" placeholder="Contoh: Minimal SMA/SMK sederajat">
                                            <button type="button" onclick="removeRequirement(this)" class="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 hidden">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <button type="button" onclick="addRequirement()" class="mt-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                                        <i class="fas fa-plus mr-1"></i> Tambah Kualifikasi
                                    </button>
                                </div>

                                <!-- Benefit/Fasilitas -->
                                <div class="mb-4">
                                    <label class="block mb-2 font-semibold">Benefit/Fasilitas</label>
                                    <div class="mb-2">
                                        <small class="text-gray-600">Contoh benefit umum:</small>
                                        <div class="flex flex-wrap gap-2 mt-1">
                                            <button type="button" onclick="addPresetBenefit('Gaji pokok + komisi')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">Gaji + Komisi</button>
                                            <button type="button" onclick="addPresetBenefit('BPJS Kesehatan & Ketenagakerjaan')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">BPJS</button>
                                            <button type="button" onclick="addPresetBenefit('Bonus performance bulanan')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">Bonus Performance</button>
                                            <button type="button" onclick="addPresetBenefit('Training dan pengembangan skill')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">Training</button>
                                            <button type="button" onclick="addPresetBenefit('Seragam kerja disediakan')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">Seragam</button>
                                        </div>
                                    </div>
                                    <div id="benefitsContainer">
                                        <div class="benefit-item flex items-center mb-2">
                                            <input type="text" name="benefits[]" class="flex-1 border rounded px-3 py-2 mr-2" placeholder="Contoh: Gaji pokok + komisi">
                                            <button type="button" onclick="removeBenefit(this)" class="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 hidden">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <button type="button" onclick="addBenefit()" class="mt-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                                        <i class="fas fa-plus mr-1"></i> Tambah Benefit
                                    </button>
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Level Pengalaman</label>
                                    <select name="experience_level" class="w-full border rounded px-3 py-2">
                                        <option value="">Pilih Level Pengalaman</option>
                                        <option value="Fresh Graduate">Fresh Graduate</option>
                                        <option value="Junior">Junior (1-2 tahun)</option>
                                        <option value="Senior">Senior (3-5 tahun)</option>
                                        <option value="Expert">Expert (5+ tahun)</option>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Pendidikan Minimal</label>
                                    <select name="education" class="w-full border rounded px-3 py-2">
                                        <option value="">Pilih Pendidikan Minimal</option>
                                        <option value="SMA/SMK">SMA/SMK</option>
                                        <option value="D3">Diploma 3</option>
                                        <option value="S1">Sarjana (S1)</option>
                                        <option value="S2">Magister (S2)</option>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Gambar/Logo (Opsional)</label>
                                    <input type="file" name="image" class="w-full border rounded px-3 py-2" accept="image/*">
                                    <small class="text-gray-500">Format: JPG, PNG, GIF. Maksimal 2MB</small>
                                </div>
                                <div class="flex justify-end">
                                    <button type="button" onclick="closeLowonganModal()" class="mr-2 px-4 py-2 rounded bg-gray-300 hover:bg-gray-400 text-gray-700 w-full block md:inline-block md:w-auto mb-2 md:mb-0 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2">Batal</button>
                                    <button type="submit" name="add_job" class="px-4 py-2 rounded bg-indigo-600 hover:bg-indigo-700 text-white font-bold">Simpan</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Edit Lowongan Modal -->
                <div id="editLowonganModal" class="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50 hidden p-4">
                    <div class="bg-white rounded-xl shadow-lg w-full max-w-2xl relative p-0 max-h-[95vh] overflow-hidden">
                        <button onclick="closeEditLowonganModal()" class="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-2xl font-bold focus:outline-none w-10 h-10 flex items-center justify-center transition md:w-10 md:h-10 w-12 h-12">&times;</button>
                        <h2 class="text-xl font-bold mb-4 border-b-2 border-indigo-600 pb-2 px-6 pt-6">Edit Lowongan</h2>
                        <div class="px-6 pb-6 max-h-[80vh] overflow-y-auto">
                            <form action="" method="POST" enctype="multipart/form-data">
                                <input type="hidden" name="id" id="edit_id">
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Posisi Pekerjaan</label>
                                    <input type="text" name="position" id="edit_position" class="w-full border rounded px-3 py-2" required placeholder="Contoh: Barber Senior">
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Lokasi</label>
                                    <input type="text" name="location" id="edit_location" class="w-full border rounded px-3 py-2" required placeholder="Contoh: Jakarta Selatan">
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Tipe Pekerjaan</label>
                                    <select name="type" id="edit_type" class="w-full border rounded px-3 py-2" required>
                                        <option value="">Pilih Tipe Pekerjaan</option>
                                        <option value="Full Time">Full Time</option>
                                        <option value="Part Time">Part Time</option>
                                        <option value="Contract">Contract</option>
                                        <option value="Freelance">Freelance</option>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Gaji</label>
                                    <input type="text" name="salary" id="edit_salary" class="w-full border rounded px-3 py-2" required placeholder="Contoh: Rp 3.000.000 - 5.000.000">
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Deskripsi Pekerjaan</label>
                                    <textarea name="description" class="w-full border rounded px-3 py-2" rows="3" required placeholder="Jelaskan tugas dan tanggung jawab pekerjaan"></textarea>
                                </div>
                                <!-- Kualifikasi/Persyaratan -->
                                <div class="mb-4">
                                    <label class="block mb-2 font-semibold">Kualifikasi/Persyaratan</label>
                                    <div class="mb-2">
                                        <small class="text-gray-600">Contoh kualifikasi umum:</small>
                                        <div class="flex flex-wrap gap-2 mt-1">
                                            <button type="button" onclick="addPresetRequirement('Minimal SMA/SMK sederajat')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">SMA/SMK</button>
                                            <button type="button" onclick="addPresetRequirement('Pengalaman minimal 1 tahun di bidang barbershop')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">Pengalaman 1 tahun</button>
                                            <button type="button" onclick="addPresetRequirement('Memiliki sertifikat barbering')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">Sertifikat</button>
                                            <button type="button" onclick="addPresetRequirement('Komunikasi yang baik dengan pelanggan')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">Komunikasi baik</button>
                                        </div>
                                    </div>
                                    <div id="requirementsContainer">
                                        <div class="requirement-item flex items-center mb-2">
                                            <input type="text" name="requirements[]" class="flex-1 border rounded px-3 py-2 mr-2" placeholder="Contoh: Minimal SMA/SMK sederajat">
                                            <button type="button" onclick="removeRequirement(this)" class="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 hidden">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <button type="button" onclick="addRequirement()" class="mt-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                                        <i class="fas fa-plus mr-1"></i> Tambah Kualifikasi
                                    </button>
                                </div>

                                <!-- Benefit/Fasilitas -->
                                <div class="mb-4">
                                    <label class="block mb-2 font-semibold">Benefit/Fasilitas</label>
                                    <div class="mb-2">
                                        <small class="text-gray-600">Contoh benefit umum:</small>
                                        <div class="flex flex-wrap gap-2 mt-1">
                                            <button type="button" onclick="addPresetBenefit('Gaji pokok + komisi')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">Gaji + Komisi</button>
                                            <button type="button" onclick="addPresetBenefit('BPJS Kesehatan & Ketenagakerjaan')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">BPJS</button>
                                            <button type="button" onclick="addPresetBenefit('Bonus performance bulanan')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">Bonus Performance</button>
                                            <button type="button" onclick="addPresetBenefit('Training dan pengembangan skill')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">Training</button>
                                            <button type="button" onclick="addPresetBenefit('Seragam kerja disediakan')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">Seragam</button>
                                        </div>
                                    </div>
                                    <div id="benefitsContainer">
                                        <div class="benefit-item flex items-center mb-2">
                                            <input type="text" name="benefits[]" class="flex-1 border rounded px-3 py-2 mr-2" placeholder="Contoh: Gaji pokok + komisi">
                                            <button type="button" onclick="removeBenefit(this)" class="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 hidden">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <button type="button" onclick="addBenefit()" class="mt-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                                        <i class="fas fa-plus mr-1"></i> Tambah Benefit
                                    </button>
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Level Pengalaman</label>
                                    <select name="experience_level" id="edit_experience_level" class="w-full border rounded px-3 py-2">
                                        <option value="">Pilih Level Pengalaman</option>
                                        <option value="Fresh Graduate">Fresh Graduate</option>
                                        <option value="Junior">Junior (1-2 tahun)</option>
                                        <option value="Senior">Senior (3-5 tahun)</option>
                                        <option value="Expert">Expert (5+ tahun)</option>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Pendidikan Minimal</label>
                                    <select name="education" id="edit_education" class="w-full border rounded px-3 py-2">
                                        <option value="">Pilih Pendidikan Minimal</option>
                                        <option value="SMA/SMK">SMA/SMK</option>
                                        <option value="D3">Diploma 3</option>
                                        <option value="S1">Sarjana (S1)</option>
                                        <option value="S2">Magister (S2)</option>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label class="block mb-1 font-semibold">Gambar/Logo (Opsional)</label>
                                    <input type="file" name="image" class="w-full border rounded px-3 py-2" accept="image/*">
                                    <small class="text-gray-500">Format: JPG, PNG, GIF. Maksimal 2MB</small>
                                </div>
                                <div class="flex justify-end">
                                    <button type="button" onclick="closeEditLowonganModal()" class="mr-2 px-4 py-2 rounded bg-gray-300 hover:bg-gray-400 text-gray-700 w-full block md:inline-block md:w-auto mb-2 md:mb-0 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2">Batal</button>
                                    <button type="submit" name="edit_job" class="px-4 py-2 rounded bg-indigo-600 hover:bg-indigo-700 text-white font-bold">Simpan Perubahan</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <section id="quiz" class="mb-8">
                    <div class="">
                        <!-- Modal tetap di sini, tombol sudah dipindah ke atas -->
                        <div id="quizModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 hidden">
                            <div class="bg-white rounded-lg shadow-lg w-full max-w-lg p-6 relative">
                                <button id="closeQuizModal" class="absolute top-2 right-2 text-gray-500 hover:text-red-600 text-2xl">&times;</button>
                                <h3 class="text-lg font-bold mb-4">Tambah Pertanyaan Quiz</h3>
                                <form method="POST" id="quizForm">
                                    <div class="mb-2">
                                        <label class="block font-semibold mb-1">Job/Role</label>
                                        <select name="quiz_job_id" class="w-full border rounded px-3 py-2">
                                            <option value="">Umum (Semua Job)</option>
                                            <?php foreach ($jobs as $job): ?>
                                                <option value="<?= $job['id'] ?>"><?= htmlspecialchars($job['position']) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="mb-2">
                                        <label class="block font-semibold mb-1">Pertanyaan</label>
                                        <input type="text" name="quiz_question" class="w-full border rounded px-3 py-2" required>
                                    </div>
                                    <div class="mb-2">
                                        <label class="block font-semibold mb-1">Jawaban</label>
                                        <?php for ($i=0; $i<4; $i++): ?>
                                            <div class="flex items-center mb-1">
                                                <input type="text" name="quiz_answers[]" class="border rounded px-2 py-1 flex-1 mr-2" required>
                                                <label class="flex items-center text-sm">
                                                    <input type="radio" name="correct_answer" value="<?= $i ?>" class="mr-1"> Benar
                                                </label>
                                            </div>
                                        <?php endfor; ?>
                                    </div>
                                    <div class="flex justify-end">
                                        <button type="submit" name="add_quiz_question" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Tambah Pertanyaan</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Card Daftar Lowongan -->
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <h2 class="text-2xl font-bold mb-4 border-b-2 border-blue-700 pb-2">Daftar Lowongan</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full border border-gray-400 border-collapse">
                            <thead>
                                <tr class="bg-blue-700 text-white border border-gray-400">
                                    <th class="py-3 px-4 border border-gray-400 text-center">ID</th>
                                    <th class="py-3 px-4 border border-gray-400 text-center">Posisi</th>
                                    <th class="py-3 px-4 border border-gray-400 text-center">Lokasi</th>
                                    <th class="py-3 px-4 border border-gray-400 text-center">Tipe</th>
                                    <th class="py-3 px-4 border border-gray-400 text-center">Gaji</th>
                                    <th class="py-3 px-4 border border-gray-400 text-center">Level</th>
                                    <th class="py-3 px-4 border border-gray-400 text-center">Pendidikan</th>
                                    <th class="py-3 px-4 border border-gray-400 text-center">Tanggal</th>
                                    <th class="py-3 px-4 border border-gray-400 text-center">Gambar</th>
                                    <th class="py-3 px-4 border border-gray-400 text-center">Pertanyaan Quiz</th>
                                    <th class="py-3 px-4 border border-gray-400 text-center">Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="text-gray-900">
                                <?php foreach ($jobs as $i => $job): ?>
                                <tr class="border border-gray-400">
                                    <td class="py-3 px-4 border border-gray-400 text-center"><?= $i+1 ?></td>
                                    <td class="py-3 px-4 border border-gray-400 text-center"><?= htmlspecialchars($job['position']) ?></td>
                                    <td class="py-3 px-4 border border-gray-400 text-center"><?= htmlspecialchars($job['location']) ?></td>
                                    <td class="py-3 px-4 border border-gray-400 text-center"><?= htmlspecialchars($job['type']) ?></td>
                                    <td class="py-3 px-4 border border-gray-400 text-center"><?= htmlspecialchars($job['salary']) ?></td>
                                    <td class="py-3 px-4 border border-gray-400 text-center"><?= htmlspecialchars($job['experience_level']) ?></td>
                                    <td class="py-3 px-4 border border-gray-400 text-center"><?= htmlspecialchars($job['education']) ?></td>
                                    <td class="py-3 px-4 border border-gray-400 text-center"><?= date('d/m/Y', strtotime($job['posted'])) ?></td>
                                    <td class="py-3 px-4 border border-gray-400 text-center">
                                        <?php if (!empty($job['image'])): ?>
                                            <img src="../assets/<?= htmlspecialchars($job['image']) ?>" class="w-20 h-20 object-cover rounded mx-auto" />
                                        <?php endif; ?>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-400 text-center">
                                        <button class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm" onclick="openQuizDetailModal(<?= $job['id'] ?>)">Detail</button>
                                    </td>
                                    <td class="py-3 px-4 border border-gray-400 text-center space-x-2">
                                        <a href="javascript:void(0);"
                                           onclick="openEditLowonganModal(<?= htmlspecialchars(json_encode($job), ENT_QUOTES, 'UTF-8'); ?>)"
                                           class="px-4 py-2 rounded bg-yellow-400 hover:bg-yellow-500 text-white font-semibold transition w-full block md:inline-block md:w-auto mb-2 md:mb-0 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2">
                                            Edit
                                        </a>
                                        <form action="" method="POST" class="inline" onsubmit="return confirm('Yakin ingin menghapus?');">
                                            <input type="hidden" name="delete_id" value="<?= $job['id'] ?>">
                                            <button type="submit" class="inline-block px-4 py-2 rounded bg-red-500 hover:bg-red-600 text-white font-semibold">Hapus</button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Modal Detail Quiz -->
                <div id="quizDetailModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 hidden">
                    <div class="bg-white rounded-lg shadow-lg w-full max-w-2xl p-6 relative max-h-[90vh] overflow-y-auto">
                        <button id="closeQuizDetailModal" class="absolute top-2 right-2 text-gray-500 hover:text-red-600 text-2xl">&times;</button>
                        <h3 class="text-lg font-bold mb-4">Daftar Pertanyaan Quiz</h3>
                        <div id="quizDetailContent">
                            <!-- Konten pertanyaan quiz akan diisi oleh JS -->
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
    // Sidebar Toggle for Mobile
    document.addEventListener('DOMContentLoaded', function() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('open');
                overlay.classList.toggle('show');
            });
        }

        if (overlay) {
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
            });
        }

        // Auto-open current submenu
        const currentPage = '<?php echo basename($_SERVER['PHP_SELF']); ?>';
        if (currentPage === 'kelola_lowongan.php' || currentPage === 'data_pelamar.php' || currentPage === 'whatsapp_manager.php') {
            const lowonganSubmenu = document.getElementById('lowonganSubmenu');
            const lowonganIcon = document.getElementById('arrowLowonganIcon');
            if (lowonganSubmenu && lowonganIcon) {
                lowonganSubmenu.classList.remove('hidden');
                lowonganIcon.classList.add('rotate-180');
            }
        }

        // Profile dropdown functionality
        const profileDropdown = document.getElementById('profileDropdown');
        const profileDropdownMenu = document.getElementById('profileDropdownMenu');

        if (profileDropdown && profileDropdownMenu) {
            profileDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdownMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdownMenu.classList.add('hidden');
                }
            });
        }
    });

    function toggleBookingSubmenu(event) {
        event.preventDefault();
        event.stopPropagation();
        const submenu = document.getElementById('bookingSubmenu');
        const icon = document.getElementById('arrowBookingIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleProdukSubmenu(event) {
        event.preventDefault();
        event.stopPropagation();
        const submenu = document.getElementById('produkSubmenu');
        const icon = document.getElementById('arrowProdukIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    function toggleLowonganSubmenu(event) {
        event.preventDefault();
        event.stopPropagation();
        const submenu = document.getElementById('lowonganSubmenu');
        const icon = document.getElementById('arrowLowonganIcon');
        submenu.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
    }

    // Auto-hide notification function
    function hideNotification() {
        const notification = document.getElementById('notification');
        if (notification) {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                notification.style.display = 'none';
            }, 500);
        }
    }

    // Auto-hide notification after 4 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const notification = document.getElementById('notification');
        if (notification) {
            setTimeout(() => {
                hideNotification();
            }, 4000); // Hide after 4 seconds
        }
    });

    // Modal logic
    const openBtn = document.getElementById('openQuizModal');
    const modal = document.getElementById('quizModal');
    const closeBtn = document.getElementById('closeQuizModal');
    if (openBtn && modal && closeBtn) {
        openBtn.onclick = () => { modal.classList.remove('hidden'); };
        closeBtn.onclick = () => { modal.classList.add('hidden'); };
        window.onclick = function(event) {
            if (event.target === modal) { modal.classList.add('hidden'); }
        };
    }
    // Setelah submit, modal tertutup otomatis (karena reload)

    // Data quiz PHP ke JS
    const allQuizQuestions = <?php echo json_encode($quiz_questions); ?>;
    const allQuizAnswers = <?php echo json_encode($quiz_answers); ?>;
    // Tampilkan modal detail quiz
    function openQuizDetailModal(jobId) {
        const modal = document.getElementById('quizDetailModal');
        const content = document.getElementById('quizDetailContent');
        // Filter pertanyaan untuk jobId
        const questions = allQuizQuestions.filter(q => String(q.job_id) === String(jobId));
        if (questions.length === 0) {
            content.innerHTML = '<p class="text-gray-500">Belum ada pertanyaan quiz untuk lowongan ini.</p>';
        } else {
            let html = '';
            questions.forEach(q => {
                html += `<div class="mb-4 border rounded p-3">
                    <div class="font-semibold mb-2">Q: ${q.question}</div>
                    <ul class="list-disc ml-6">`;
                (allQuizAnswers[q.id] || []).forEach(a => {
                    html += `<li class="${a.is_correct ? 'text-green-600 font-bold' : ''}">${a.answer}${a.is_correct ? ' <span class="ml-2 text-xs">(Benar)</span>' : ''}</li>`;
                });
                html += '</ul></div>';
            });
            content.innerHTML = html;
        }
        modal.classList.remove('hidden');
    }
    document.getElementById('closeQuizDetailModal').onclick = function() {
        document.getElementById('quizDetailModal').classList.add('hidden');
    };
    window.onclick = function(event) {
        const modal = document.getElementById('quizDetailModal');
        if (event.target === modal) {
            modal.classList.add('hidden');
        }
    };

    function openEditLowonganModal(job) {
        // Pastikan modal dan field ada
        const modal = document.getElementById('editLowonganModal');
        if (!modal) return;
        // Isi field modal
        document.getElementById('edit_id').value = job.id;
        document.getElementById('edit_position').value = job.position;
        document.getElementById('edit_location').value = job.location;
        document.getElementById('edit_type').value = job.type;
        document.getElementById('edit_salary').value = job.salary;
        document.getElementById('edit_experience_level').value = job.experience_level;
        document.getElementById('edit_education').value = job.education;
        // Tampilkan modal
        modal.classList.remove('hidden');
    }

    function closeEditLowonganModal() {
        const modal = document.getElementById('editLowonganModal');
        if (modal) modal.classList.add('hidden');
    }

    function openLowonganModal() {
        const modal = document.getElementById('tambahLowonganModal');
        if (modal) modal.classList.remove('hidden');
    }
    function closeLowonganModal() {
        const modal = document.getElementById('tambahLowonganModal');
        if (modal) modal.classList.add('hidden');
    }

    function addPresetRequirement(text) {
        const container = document.getElementById('requirementsContainer');
        const div = document.createElement('div');
        div.className = 'requirement-item flex items-center mb-2';
        div.innerHTML = `<input type="text" name="requirements[]" class="flex-1 border rounded px-3 py-2 mr-2" value="${text}" placeholder="Contoh: Minimal SMA/SMK sederajat">
            <button type="button" onclick="removeRequirement(this)" class="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600"><i class='fas fa-trash'></i></button>`;
        container.appendChild(div);
    }
    function addPresetBenefit(text) {
        const container = document.getElementById('benefitsContainer');
        const div = document.createElement('div');
        div.className = 'benefit-item flex items-center mb-2';
        div.innerHTML = `<input type="text" name="benefits[]" class="flex-1 border rounded px-3 py-2 mr-2" value="${text}" placeholder="Contoh: Gaji pokok + komisi">
            <button type="button" onclick="removeBenefit(this)" class="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600"><i class='fas fa-trash'></i></button>`;
        container.appendChild(div);
    }
    function addRequirement() {
        addPresetRequirement('');
    }
    function addBenefit() {
        addPresetBenefit('');
    }
    function removeRequirement(btn) {
        btn.parentElement.remove();
    }
    function removeBenefit(btn) {
        btn.parentElement.remove();
    }
    </script>
</body>
</html>
